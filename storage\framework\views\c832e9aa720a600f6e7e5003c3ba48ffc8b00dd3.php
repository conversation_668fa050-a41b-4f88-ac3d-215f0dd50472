<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Archived Kanban - <?php echo e($sprint->sprint_name); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
        }
        .kanban-board {
            display: flex;
            gap: 1rem;
            overflow-x: auto;
            padding: 1rem;
        }
        .swim-lane {
            flex: 0 0 300px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem;
        }
        .lane-header {
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #eee;
        }
        .task-card {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0.75rem;
            margin-bottom: 0.75rem;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        .archive-header {
            background: #fff;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .archive-info {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="archive-header">
            <h2 class="mb-2"><?php echo e($sprint->sprint_name); ?> - Archived Kanban Board</h2>
            <div class="archive-info">
                <p class="mb-1">
                    <i class="fas fa-calendar"></i> 
                    Sprint Period: <?php echo e(\Carbon\Carbon::parse($sprint->start_sprint)->format('j M Y')); ?> - 
                    <?php echo e(\Carbon\Carbon::parse($sprint->end_sprint)->format('j M Y')); ?>

                </p>
                <p class="mb-1">
                    <i class="fas fa-archive"></i>
                    Archived on: <?php echo e($archive->archived_at->format('j M Y, H:i')); ?>

                </p>
            </div>
        </div>

        <div class="kanban-board">
            <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="swim-lane">
                    <div class="lane-header">
                        <h4><?php echo e($status->title); ?></h4>
                    </div>
                    <div class="lane-content">
                        <?php if(isset($archive->kanban_state[$status->id])): ?>
                            <?php $__currentLoopData = $archive->kanban_state[$status->id]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="task-card">
                                    <h5><?php echo e($task['title']); ?></h5>
                                    <p class="mb-0"><?php echo e($task['description']); ?></p>
                                    <?php if(isset($task['user_names'])): ?>
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                <?php
                                                    $userNames = json_decode($task['user_names'], true);
                                                    $assignedUsers = is_array($userNames) && !empty($userNames) ? implode(', ', $userNames) : 'Unassigned';
                                                ?>
                                                Assigned to: <?php echo e($assignedUsers); ?>

                                            </small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="mt-4">
            <a href="<?php echo e(route('sprint.archives', ['proj_id' => $project->id])); ?>" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i>
                Back to Archives
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> <?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/sprint/archives/kanban.blade.php ENDPATH**/ ?>