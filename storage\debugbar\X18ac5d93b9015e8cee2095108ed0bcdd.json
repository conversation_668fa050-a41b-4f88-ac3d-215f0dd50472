{"__meta": {"id": "X18ac5d93b9015e8cee2095108ed0bcdd", "datetime": "2025-08-19 10:55:02", "utime": 1755572102.120096, "method": "POST", "uri": "/tasks", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[10:55:01] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572101.745825, "xdebug_link": null, "collector": "log"}, {"message": "[10:55:01] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/tasks", "message_html": null, "is_string": false, "label": "debug", "time": 1755572101.853124, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572101.231283, "end": 1755572102.120128, "duration": 0.8888449668884277, "duration_str": "889ms", "measures": [{"label": "Booting", "start": 1755572101.231283, "relative_start": 0, "end": 1755572101.710452, "relative_end": 1755572101.710452, "duration": 0.4791691303253174, "duration_str": "479ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572101.710473, "relative_start": 0.4791901111602783, "end": 1755572102.120131, "relative_end": 3.0994415283203125e-06, "duration": 0.40965795516967773, "duration_str": "410ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24572512, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST tasks", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\TaskController@store", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "tasks.store", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TaskController.php&line=558\">\\app\\Http\\Controllers\\TaskController.php:558-632</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.018539999999999997, "accumulated_duration_str": "18.54ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 3.83}, {"sql": "select * from `user_stories` where `u_id` = '49' limit 1", "type": "query", "params": [], "bindings": ["49"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 560}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:560", "connection": "sagile", "start_percent": 3.83, "width_percent": 4.423}, {"sql": "select * from `sprint` where `sprint_id` = '0' limit 1", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 567}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:567", "connection": "sagile", "start_percent": 8.252, "width_percent": 2.967}, {"sql": "select * from `projects` where `id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 570}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00061, "duration_str": "610μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:570", "connection": "sagile", "start_percent": 11.219, "width_percent": 3.29}, {"sql": "select count(*) as aggregate from `tasks` where `title` = 'task for test 2' and `userstory_id` = '49'", "type": "query", "params": [], "bindings": ["task for test 2", "49"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 447}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "sagile", "start_percent": 14.509, "width_percent": 3.398}, {"sql": "select max(`order`) as aggregate from `tasks` where `status_id` = '205' and `sprint_id` = '0'", "type": "query", "params": [], "bindings": ["205", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 601}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:601", "connection": "sagile", "start_percent": 17.907, "width_percent": 3.074}, {"sql": "insert into `tasks` (`userstory_id`, `title`, `description`, `user_names`, `status_id`, `proj_id`, `sprint_id`, `order`, `newTask_update`, `updated_at`, `created_at`) values ('49', 'task for test 2', 'task for test 2', '[\\\"ivlyn\\\"]', '205', 42, '0', 1, '2025-08-19', '2025-08-19 10:55:01', '2025-08-19 10:55:01')", "type": "query", "params": [], "bindings": ["49", "task for test 2", "task for test 2", "[&quot;ivlyn&quot;]", "205", "42", "0", "1", "2025-08-19", "2025-08-19 10:55:01", "2025-08-19 10:55:01"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 612}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.012119999999999999, "duration_str": "12.12ms", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:612", "connection": "sagile", "start_percent": 20.982, "width_percent": 65.372}, {"sql": "select * from `tasks` where `userstory_id` = '49'", "type": "query", "params": [], "bindings": ["49"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 614}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:614", "connection": "sagile", "start_percent": 86.354, "width_percent": 3.776}, {"sql": "select `team_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 617}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:617", "connection": "sagile", "start_percent": 90.129, "width_percent": 3.236}, {"sql": "select * from `projects` where `team_name` in ('iv<PERSON>\\'s team', 'iv<PERSON>\\'s team')", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team", "ivlyn&#039;s team"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 618}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:618", "connection": "sagile", "start_percent": 93.366, "width_percent": 3.182}, {"sql": "select * from `statuses` where `project_id` = '42'", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 619}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:619", "connection": "sagile", "start_percent": 96.548, "width_percent": 3.452}]}, "models": {"data": {"App\\Status": 4, "App\\Task": 1, "App\\Project": 2, "App\\UserStory": 1, "App\\User": 1}, "count": 9}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/task/49/create\"\n]", "_flash": "array:2 [\n  \"old\" => array:6 [\n    0 => \"title\"\n    1 => \"success\"\n    2 => \"task\"\n    3 => \"statuses\"\n    4 => \"userstory_id\"\n    5 => \"pros\"\n  ]\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "title": "Tasks for As a Project Manager, I am able to test2", "success": "Task has successfully been created!", "task": "Illuminate\\Database\\Eloquent\\Collection {#1829\n  #items: array:1 [\n    0 => App\\Task {#1828\n      #connection: \"mysql\"\n      #table: \"tasks\"\n      +primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:15 [\n        \"id\" => 107\n        \"title\" => \"task for test 2\"\n        \"description\" => \"task for test 2\"\n        \"order\" => 1\n        \"status_id\" => \"205\"\n        \"userstory_id\" => 49\n        \"sprint_id\" => 0\n        \"proj_id\" => 42\n        \"start_date\" => null\n        \"end_date\" => null\n        \"completion_date\" => null\n        \"created_at\" => \"2025-08-19 10:55:01\"\n        \"updated_at\" => \"2025-08-19 10:55:01\"\n        \"user_names\" => \"[\"ivlyn\"]\"\n        \"newTask_update\" => \"2025-08-19\"\n      ]\n      #original: array:15 [\n        \"id\" => 107\n        \"title\" => \"task for test 2\"\n        \"description\" => \"task for test 2\"\n        \"order\" => 1\n        \"status_id\" => \"205\"\n        \"userstory_id\" => 49\n        \"sprint_id\" => 0\n        \"proj_id\" => 42\n        \"start_date\" => null\n        \"end_date\" => null\n        \"completion_date\" => null\n        \"created_at\" => \"2025-08-19 10:55:01\"\n        \"updated_at\" => \"2025-08-19 10:55:01\"\n        \"user_names\" => \"[\"ivlyn\"]\"\n        \"newTask_update\" => \"2025-08-19\"\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:8 [\n        0 => \"title\"\n        1 => \"description\"\n        2 => \"user_names\"\n        3 => \"order\"\n        4 => \"start_date\"\n        5 => \"end_date\"\n        6 => \"proj_id\"\n        7 => \"newTask_update\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      +foreignKey: array:3 [\n        0 => \"userstory_id\"\n        1 => \"sprint_id\"\n        2 => \"status_id\"\n      ]\n    }\n  ]\n  #escapeWhenCastingToString: false\n}", "statuses": "Illuminate\\Database\\Eloquent\\Collection {#1827\n  #items: array:4 [\n    0 => App\\Status {#1826\n      #connection: \"mysql\"\n      #table: \"statuses\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 205\n        \"title\" => \"Backlog\"\n        \"slug\" => \"backlog\"\n        \"order\" => 1\n        \"project_id\" => 42\n        \"created_at\" => null\n        \"updated_at\" => null\n      ]\n      #original: array:7 [\n        \"id\" => 205\n        \"title\" => \"Backlog\"\n        \"slug\" => \"backlog\"\n        \"order\" => 1\n        \"project_id\" => 42\n        \"created_at\" => null\n        \"updated_at\" => null\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:4 [\n        0 => \"title\"\n        1 => \"slug\"\n        2 => \"order\"\n        3 => \"project_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    1 => App\\Status {#1825\n      #connection: \"mysql\"\n      #table: \"statuses\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 206\n        \"title\" => \"Up Next\"\n        \"slug\" => \"up-next\"\n        \"order\" => 2\n        \"project_id\" => 42\n        \"created_at\" => null\n        \"updated_at\" => null\n      ]\n      #original: array:7 [\n        \"id\" => 206\n        \"title\" => \"Up Next\"\n        \"slug\" => \"up-next\"\n        \"order\" => 2\n        \"project_id\" => 42\n        \"created_at\" => null\n        \"updated_at\" => null\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:4 [\n        0 => \"title\"\n        1 => \"slug\"\n        2 => \"order\"\n        3 => \"project_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    2 => App\\Status {#1824\n      #connection: \"mysql\"\n      #table: \"statuses\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 207\n        \"title\" => \"In Progress\"\n        \"slug\" => \"in-progress\"\n        \"order\" => 3\n        \"project_id\" => 42\n        \"created_at\" => null\n        \"updated_at\" => null\n      ]\n      #original: array:7 [\n        \"id\" => 207\n        \"title\" => \"In Progress\"\n        \"slug\" => \"in-progress\"\n        \"order\" => 3\n        \"project_id\" => 42\n        \"created_at\" => null\n        \"updated_at\" => null\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:4 [\n        0 => \"title\"\n        1 => \"slug\"\n        2 => \"order\"\n        3 => \"project_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    3 => App\\Status {#1770\n      #connection: \"mysql\"\n      #table: \"statuses\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 208\n        \"title\" => \"Done\"\n        \"slug\" => \"done\"\n        \"order\" => 4\n        \"project_id\" => 42\n        \"created_at\" => null\n        \"updated_at\" => null\n      ]\n      #original: array:7 [\n        \"id\" => 208\n        \"title\" => \"Done\"\n        \"slug\" => \"done\"\n        \"order\" => 4\n        \"project_id\" => 42\n        \"created_at\" => null\n        \"updated_at\" => null\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:4 [\n        0 => \"title\"\n        1 => \"slug\"\n        2 => \"order\"\n        3 => \"project_id\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n  ]\n  #escapeWhenCastingToString: false\n}", "userstory_id": "49", "pros": "Illuminate\\Database\\Eloquent\\Collection {#1822\n  #items: array:1 [\n    0 => App\\Project {#1835\n      #connection: \"mysql\"\n      #table: \"projects\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:9 [\n        \"id\" => 42\n        \"team_name\" => \"i<PERSON><PERSON>'s team\"\n        \"proj_name\" => \"Food Ordering System\"\n        \"proj_desc\" => \"A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.\"\n        \"start_date\" => \"2025-08-18\"\n        \"end_date\" => \"2026-01-25\"\n        \"shareable_slug\" => null\n        \"created_at\" => \"2025-08-18 23:02:05\"\n        \"updated_at\" => \"2025-08-18 23:02:05\"\n      ]\n      #original: array:9 [\n        \"id\" => 42\n        \"team_name\" => \"i<PERSON><PERSON>'s team\"\n        \"proj_name\" => \"Food Ordering System\"\n        \"proj_desc\" => \"A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.\"\n        \"start_date\" => \"2025-08-18\"\n        \"end_date\" => \"2026-01-25\"\n        \"shareable_slug\" => null\n        \"created_at\" => \"2025-08-18 23:02:05\"\n        \"updated_at\" => \"2025-08-18 23:02:05\"\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:7 [\n        0 => \"user_id\"\n        1 => \"team_name\"\n        2 => \"proj_name\"\n        3 => \"proj_desc\"\n        4 => \"start_date\"\n        5 => \"end_date\"\n        6 => \"shareable_slug\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n  ]\n  #escapeWhenCastingToString: false\n}", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/tasks", "status_code": "<pre class=sf-dump id=sf-dump-1857503898 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1857503898\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1645017712 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1645017712\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-799529582 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>userstory_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">49</span>\"\n  \"<span class=sf-dump-key>sprint_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"15 characters\">task for test 2</span>\"\n  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">task for test 2</span>\"\n  \"<span class=sf-dump-key>user_names</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">ivlyn</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>status_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">205</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-799529582\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">798</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarycu1RMaxYUnziaBiK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/task/49/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkI2WlNMNWdqdld1cEtzL1ZnTDJ5WXc9PSIsInZhbHVlIjoiOWZIWks0SnZyRGFldktiaWJLRVVWcWg3S3huTWtvYlB2bXZIQTZSdjZMS20yY3k0Zk96YUNuMkVJYTFwSTJYK05SZVF0elhFTnExeGVoODlkQ3RUcHAzRWtqRTkzME96Q294RGhaYXBEcVNQay80R2NRLzV1Nmp6OUFmYVB0ejAiLCJtYWMiOiJiYjc4MmM3ZDEwNzcyNTlhYWFhMTVjMWJiMDE5ZDJiMTJkMDk0YTNmNjhlOGI5MDViYjFiYzU1Mjg2NDU1NWZmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InJNUENoeE9WZ1Q2TVd0RXRKbXNtWHc9PSIsInZhbHVlIjoicUdzTXBvMmpzeGt4SGRXTDNKaTlEcjhId0dIQnVWeEhHakh4bG1CVzIzbldDc2J6M2xUSDJRZm5rZkw2bWVRVjBsVzI0aFRYK0J6ajFiaE9EaUprNVR0WUp0MVRlaStndkU1dzYyalFZMTZlWGQwS2JQdXEyMmNSSHhoYW1hT0wiLCJtYWMiOiIyZjU2OTM2ZjMxOWNjM2E5NDEwYjFhYzM4YjQ3NzFlZmIzMzkzZDg0M2ZjZDY1NWIwZWFjMDM3ZTA4YTVlMWQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1002839468 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51929</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/tasks</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/tasks</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/tasks</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">798</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">798</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarycu1RMaxYUnziaBiK</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarycu1RMaxYUnziaBiK</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/task/49/create</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkI2WlNMNWdqdld1cEtzL1ZnTDJ5WXc9PSIsInZhbHVlIjoiOWZIWks0SnZyRGFldktiaWJLRVVWcWg3S3huTWtvYlB2bXZIQTZSdjZMS20yY3k0Zk96YUNuMkVJYTFwSTJYK05SZVF0elhFTnExeGVoODlkQ3RUcHAzRWtqRTkzME96Q294RGhaYXBEcVNQay80R2NRLzV1Nmp6OUFmYVB0ejAiLCJtYWMiOiJiYjc4MmM3ZDEwNzcyNTlhYWFhMTVjMWJiMDE5ZDJiMTJkMDk0YTNmNjhlOGI5MDViYjFiYzU1Mjg2NDU1NWZmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InJNUENoeE9WZ1Q2TVd0RXRKbXNtWHc9PSIsInZhbHVlIjoicUdzTXBvMmpzeGt4SGRXTDNKaTlEcjhId0dIQnVWeEhHakh4bG1CVzIzbldDc2J6M2xUSDJRZm5rZkw2bWVRVjBsVzI0aFRYK0J6ajFiaE9EaUprNVR0WUp0MVRlaStndkU1dzYyalFZMTZlWGQwS2JQdXEyMmNSSHhoYW1hT0wiLCJtYWMiOiIyZjU2OTM2ZjMxOWNjM2E5NDEwYjFhYzM4YjQ3NzFlZmIzMzkzZDg0M2ZjZDY1NWIwZWFjMDM3ZTA4YTVlMWQzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572101.2313</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572101</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1002839468\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1457139424 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:55:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/task/49</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlMzRkVqbGFiaHhtVnh2THhqN0QrVUE9PSIsInZhbHVlIjoidDh5WlBXRmgrS1J1dU9UUWtCZ3AxS3BpSndhNUZLMHJzTTEvWUJPY1A2N2pMbzlPREdmTDBaeGNLV0M3ajdZRW5VVXluYVVoMUdSeGxqL2FXQWVpam1wdFhuZEdYSmVOU1loRjdqZWZ4Qlg0NER5ZFlvcE94bWhHbkZBb1d1OVkiLCJtYWMiOiI2ZDc1YTY5MWZiNGEyYWNhYWI3NmU3ZmU2NDUyYjU4OGNjNjAwMWE2MTc1ZWM0OTY0NGNiZDkwYmQ5MTg4Y2YyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:55:02 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjIydlk2SGdycThCSHRYYVNmV1UrYWc9PSIsInZhbHVlIjoiUmZQNWxJR09UdjZOVzJYOTZWTjVrQVhCTEpaayt0ZHRXYmNBcHBacEFHbmdXMDZxeUk1T25sSUJ2NVQyTC8yUmhkSlJMUkhrRi9wdURHUWxCdk5SdDFrYTBvWUliWVRkc0pCaXg2R2ZwMzY5OGMyZmRJTFpRNGNlRHYwaWZoOXQiLCJtYWMiOiI5YTFjYjYyN2MzYWQ0MzM3NTE1NTBhYmM1MWU2OWVjYjE0ZmRhZGI5YmQ2NzBiMThhYjJiZTUxZDQ1NjE2MmI3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:55:02 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlMzRkVqbGFiaHhtVnh2THhqN0QrVUE9PSIsInZhbHVlIjoidDh5WlBXRmgrS1J1dU9UUWtCZ3AxS3BpSndhNUZLMHJzTTEvWUJPY1A2N2pMbzlPREdmTDBaeGNLV0M3ajdZRW5VVXluYVVoMUdSeGxqL2FXQWVpam1wdFhuZEdYSmVOU1loRjdqZWZ4Qlg0NER5ZFlvcE94bWhHbkZBb1d1OVkiLCJtYWMiOiI2ZDc1YTY5MWZiNGEyYWNhYWI3NmU3ZmU2NDUyYjU4OGNjNjAwMWE2MTc1ZWM0OTY0NGNiZDkwYmQ5MTg4Y2YyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:55:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjIydlk2SGdycThCSHRYYVNmV1UrYWc9PSIsInZhbHVlIjoiUmZQNWxJR09UdjZOVzJYOTZWTjVrQVhCTEpaayt0ZHRXYmNBcHBacEFHbmdXMDZxeUk1T25sSUJ2NVQyTC8yUmhkSlJMUkhrRi9wdURHUWxCdk5SdDFrYTBvWUliWVRkc0pCaXg2R2ZwMzY5OGMyZmRJTFpRNGNlRHYwaWZoOXQiLCJtYWMiOiI5YTFjYjYyN2MzYWQ0MzM3NTE1NTBhYmM1MWU2OWVjYjE0ZmRhZGI5YmQ2NzBiMThhYjJiZTUxZDQ1NjE2MmI3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:55:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1457139424\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-607846918 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/task/49/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">task</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">statuses</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"12 characters\">userstory_id</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"4 characters\">pros</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Tasks for As a Project Manager, I am able to test2</span>\"\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Task has successfully been created!</span>\"\n  \"<span class=sf-dump-key>task</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#1829</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Task\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Task</span> {<a class=sf-dump-ref>#1828</a><samp data-depth=4 class=sf-dump-compact>\n        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">tasks</span>\"\n        +<span class=sf-dump-public title=\"Public property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:15</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>107</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"15 characters\">task for test 2</span>\"\n          \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">task for test 2</span>\"\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>status_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">205</span>\"\n          \"<span class=sf-dump-key>userstory_id</span>\" => <span class=sf-dump-num>49</span>\n          \"<span class=sf-dump-key>sprint_id</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>proj_id</span>\" => <span class=sf-dump-num>42</span>\n          \"<span class=sf-dump-key>start_date</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>end_date</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>completion_date</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 10:55:01</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 10:55:01</span>\"\n          \"<span class=sf-dump-key>user_names</span>\" => \"<span class=sf-dump-str title=\"9 characters\">[&quot;ivlyn&quot;]</span>\"\n          \"<span class=sf-dump-key>newTask_update</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-19</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:15</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>107</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"15 characters\">task for test 2</span>\"\n          \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">task for test 2</span>\"\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>status_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">205</span>\"\n          \"<span class=sf-dump-key>userstory_id</span>\" => <span class=sf-dump-num>49</span>\n          \"<span class=sf-dump-key>sprint_id</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>proj_id</span>\" => <span class=sf-dump-num>42</span>\n          \"<span class=sf-dump-key>start_date</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>end_date</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>completion_date</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 10:55:01</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 10:55:01</span>\"\n          \"<span class=sf-dump-key>user_names</span>\" => \"<span class=sf-dump-str title=\"9 characters\">[&quot;ivlyn&quot;]</span>\"\n          \"<span class=sf-dump-key>newTask_update</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-19</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">user_names</span>\"\n          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">start_date</span>\"\n          <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">end_date</span>\"\n          <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"7 characters\">proj_id</span>\"\n          <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"14 characters\">newTask_update</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n        </samp>]\n        +<span class=sf-dump-public title=\"Public property\">foreignKey</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">userstory_id</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">sprint_id</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"9 characters\">status_id</span>\"\n        </samp>]\n      </samp>}\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n  </samp>}\n  \"<span class=sf-dump-key>statuses</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#1827</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Status\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Status</span> {<a class=sf-dump-ref>#1826</a><samp data-depth=4 class=sf-dump-compact>\n        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">statuses</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>205</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Backlog</span>\"\n          \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">backlog</span>\"\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>project_id</span>\" => <span class=sf-dump-num>42</span>\n          \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>205</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Backlog</span>\"\n          \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">backlog</span>\"\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>project_id</span>\" => <span class=sf-dump-num>42</span>\n          \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">project_id</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n        </samp>]\n      </samp>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Status\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Status</span> {<a class=sf-dump-ref>#1825</a><samp data-depth=4 class=sf-dump-compact>\n        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">statuses</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>206</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Up Next</span>\"\n          \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">up-next</span>\"\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>project_id</span>\" => <span class=sf-dump-num>42</span>\n          \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>206</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Up Next</span>\"\n          \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">up-next</span>\"\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>project_id</span>\" => <span class=sf-dump-num>42</span>\n          \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">project_id</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n        </samp>]\n      </samp>}\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note title=\"App\\Status\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Status</span> {<a class=sf-dump-ref>#1824</a><samp data-depth=4 class=sf-dump-compact>\n        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">statuses</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>207</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"11 characters\">In Progress</span>\"\n          \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"11 characters\">in-progress</span>\"\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n          \"<span class=sf-dump-key>project_id</span>\" => <span class=sf-dump-num>42</span>\n          \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>207</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"11 characters\">In Progress</span>\"\n          \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"11 characters\">in-progress</span>\"\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n          \"<span class=sf-dump-key>project_id</span>\" => <span class=sf-dump-num>42</span>\n          \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">project_id</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n        </samp>]\n      </samp>}\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note title=\"App\\Status\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Status</span> {<a class=sf-dump-ref>#1770</a><samp data-depth=4 class=sf-dump-compact>\n        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">statuses</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>208</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n          \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"4 characters\">done</span>\"\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>4</span>\n          \"<span class=sf-dump-key>project_id</span>\" => <span class=sf-dump-num>42</span>\n          \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>208</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n          \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"4 characters\">done</span>\"\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>4</span>\n          \"<span class=sf-dump-key>project_id</span>\" => <span class=sf-dump-num>42</span>\n          \"<span class=sf-dump-key>created_at</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>updated_at</span>\" => <span class=sf-dump-const>null</span>\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">project_id</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n        </samp>]\n      </samp>}\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n  </samp>}\n  \"<span class=sf-dump-key>userstory_id</span>\" => <span class=sf-dump-num>49</span>\n  \"<span class=sf-dump-key>pros</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#1822</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Project\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Project</span> {<a class=sf-dump-ref>#1835</a><samp data-depth=4 class=sf-dump-compact>\n        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">projects</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>42</span>\n          \"<span class=sf-dump-key>team_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">ivlyn&#039;s team</span>\"\n          \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n          \"<span class=sf-dump-key>proj_desc</span>\" => \"<span class=sf-dump-str title=\"213 characters\">A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.</span>\"\n          \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n          \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2026-01-25</span>\"\n          \"<span class=sf-dump-key>shareable_slug</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-18 23:02:05</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-18 23:02:05</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>42</span>\n          \"<span class=sf-dump-key>team_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">ivlyn&#039;s team</span>\"\n          \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n          \"<span class=sf-dump-key>proj_desc</span>\" => \"<span class=sf-dump-str title=\"213 characters\">A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.</span>\"\n          \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n          \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2026-01-25</span>\"\n          \"<span class=sf-dump-key>shareable_slug</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-18 23:02:05</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-18 23:02:05</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">team_name</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"9 characters\">proj_name</span>\"\n          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">proj_desc</span>\"\n          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">start_date</span>\"\n          <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">end_date</span>\"\n          <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">shareable_slug</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n        </samp>]\n      </samp>}\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n  </samp>}\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-607846918\", {\"maxDepth\":0})</script>\n"}}