# SAgile Project Management Tool
SAgile is a project management tool built with Laravel 8. It allows teams to manage projects, tasks, and deadlines in a collaborative environment. The tool is designed to improve team productivity and collaboration, enabling you to easily manage your project from start to finish.

Features:
- SAgile comes with the following features:
- User authentication and authorization
- Project management
- Task management
- Collaborative environment
- Dashboard for overview
- Single Sign-On (SSO) integration


# Installation

To run SAgile on your local machine, you need to follow these steps:

**Prerequisites**

Before you start, make sure you have the following software installed on your system:

- XAMPP web server
- MySQL database
- Composer
- Git

Ensure PHP version installed in XAMPP is **v8.1.10**
- [XAMPP PHP Version Change Tutorial](https://www.youtube.com/watch?v=Uto36GI6HIg)
- [XAMPP Version 8.1.10](https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.1.10/)

The project must be installed in `C:\path\to\xampp\htdocs\SAgilePMT_UTM`

In your phpMyAdmin create a database `sagile` and import [`sagile.sql`](sagile.sql)


# Start the Server

To start the server, navigate to the root directory of your Laravel project and run the following commands:

```
composer install
```
```
composer update
```
```
cp .env.example .env
```
```
php artisan key:generate
```
```
php artisan serve
```

This will start the server on http://localhost:8000.

# If CIG Radar Chart not generated in CIG Report
run the command below:
```
php artisan storage:link
```
This command creates a symbolic link from public/storage to storage/app/public, allowing you to access files stored in storage/app/public via the public/storage URL.
After running this command, you can access files in the storage/app/public directory using the public/storage

# Email Notification for Task Comments
Configure Email Settings
Ensure your .env file is correctly configured for email sending. For Gmail, use the following settings:

.env

```
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Your Application Name"
```

Clear and Cache the Configuration

```
php artisan config:clear
```

```
php artisan config:cache
```
# Additional Documentation
The following documents provide details for developers:
- [Permission Control](docs/ADDING_PROJECT_PERMISSIONS.md)
- [Admin Settings](docs/SETTING_USER_AS_ADMIN.md)
- [Logic Overview](docs/PROJECT_ASSIGNMENTS_README.md)
# SSO Integration Setup

SAgilePMT supports Single Sign-On (SSO) integration, allowing users from other systems to seamlessly access the platform without separate authentication. The current implementation supports integration with the UTM Mock Portal.

## Configuring SSO in SAgilePMT

1. Configure your `.env` file with the SSO parameters:
```
MOCK_PORTAL_URL=http://localhost:8001
MOCK_PORTAL_KEY=your-secret-key
```
   Make sure to use the same secret key in both systems.

2. Ensure the necessary SSO routes are enabled in `routes/web.php`:
```php
// SSO Routes
Route::get('login/sso/direct', [App\Http\Controllers\Auth\SSOController::class, 'handleDirectLogin'])->name('sso.direct.login');
```

3. Configure the token verification API endpoint in `routes/api.php`:
```php
// SSO Routes
Route::post('/verify-sso-token', [SSOController::class, 'verifyToken']);
```

## How SSO Works in SAgilePMT

The SSO flow works as follows:

1. User initiates SSO login from an external system (e.g., Mock Portal)
2. External system redirects to SAgilePMT with token, timestamp, and user data
3. SAgilePMT verifies the token and timestamp
4. If valid, SAgilePMT creates or updates the user record
5. User is automatically logged in to SAgilePMT
6. User data is synced between systems

## SSO Security Considerations

- Both systems must use the same secret key for token validation
- Tokens include a timestamp to prevent replay attacks
- The SSO request must include a state parameter for CSRF protection
- User data is transferred securely using base64 encoding

## Testing SSO Integration

1. Start both SAgilePMT and the Mock Portal servers:
```bash
# Start SAgilePMT on port 8000
php artisan serve

# In another terminal, start Mock Portal on port 8001
php artisan serve --port=8001
```

2. Register or log in to the Mock Portal
3. Click "Access SAgilePMT" in the Mock Portal
4. You should be automatically logged in to SAgilePMT

## Supporting Other SSO Providers

To integrate with other SSO providers:

1. Add a new key-value pair in your `.env` file for the provider:
```
NEW_PROVIDER_URL=http://provider-url
NEW_PROVIDER_KEY=provider-secret-key
```

2. Add the provider configuration in `config/services.php`
3. Modify the `SSOController` to handle authentication from the new provider

# Conclusion

Congratulations! You have successfully installed SAgile Project Management Tool on your local machine. You can now use the tool to manage your projects and tasks, collaborate with your team members, and track deadlines and milestones.
