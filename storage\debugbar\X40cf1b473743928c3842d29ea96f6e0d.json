{"__meta": {"id": "X40cf1b473743928c3842d29ea96f6e0d", "datetime": "2025-08-19 11:08:13", "utime": 1755572893.21063, "method": "POST", "uri": "/upload-radar-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[11:08:13] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572893.092521, "xdebug_link": null, "collector": "log"}, {"message": "[11:08:13] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/upload-radar-chart", "message_html": null, "is_string": false, "label": "debug", "time": 1755572893.195887, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572892.545138, "end": 1755572893.210659, "duration": 0.6655211448669434, "duration_str": "666ms", "measures": [{"label": "Booting", "start": 1755572892.545138, "relative_start": 0, "end": 1755572893.056532, "relative_end": 1755572893.056532, "duration": 0.5113940238952637, "duration_str": "511ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572893.056553, "relative_start": 0.5114150047302246, "end": 1755572893.210662, "relative_end": 2.86102294921875e-06, "duration": 0.15410900115966797, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23504112, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST upload-radar-chart", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@uploadRadarChart", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=141\">\\app\\Http\\Controllers\\CIGController.php:141-151</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.0007, "accumulated_duration_str": "700μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/radar-data/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"path_info": "/upload-radar-chart", "status_code": "<pre class=sf-dump id=sf-dump-141959862 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-141959862\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1374929334 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"74290 characters\">data:image/png;base64,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</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1374929334\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1939096282 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">74302</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImlkdXBXY1FqWXJ3akRLUWRUMUhmNmc9PSIsInZhbHVlIjoic0dVS09XV2I1bnhpNmUxb2VKT0NoOWNHR21MSGRvUzZHZURRY2VSdUhyNEQ4MWsrbXAxQlozNHlCSSsxV2hjRWZKZG91QWYzUDErLzl6VEdPbWVDeitESFg4bzN0ZGttSWpLMng5RS9MSTJqVWpuTlpYV0ZxUkJxOWxOb1N5MmciLCJtYWMiOiI0ODUxYzA4YWJjNDQ3NGIxZTljNGQxNWNjM2EyMmNkMDQxMjIyYjI2YjM3MTJiN2UyYWFiOWMwYzA2ZTg2Yzg0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkF3MkpUeCtwNXFkNVBUMm5PcERzVUE9PSIsInZhbHVlIjoiUmZOZlR5bU5SVDR0ZkZ0L2NqbDJEUWljcHdDTGVqYUZQOUtlbkVwVWt5WFBzejFBOXozVDdCTDlUSitqQ01mNmtKcndUY20xWjR5OFdRSnVFbERtTEZCdFhUeXhpT04yVEV0c1QzQnllWjhnWWkzUTFablVsZUpQa28rN0xNSmIiLCJtYWMiOiJmODYxNjk4YmM0ZDY0MDY4MzkxZTRlZTQyZGU5ZmUwMDQ4NmQyZDk1NGU2NWY3YzMzMzFkZjBhZmVkNjE0NWI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1939096282\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1832048548 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">63867</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/index.php/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">74302</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">74302</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImlkdXBXY1FqWXJ3akRLUWRUMUhmNmc9PSIsInZhbHVlIjoic0dVS09XV2I1bnhpNmUxb2VKT0NoOWNHR21MSGRvUzZHZURRY2VSdUhyNEQ4MWsrbXAxQlozNHlCSSsxV2hjRWZKZG91QWYzUDErLzl6VEdPbWVDeitESFg4bzN0ZGttSWpLMng5RS9MSTJqVWpuTlpYV0ZxUkJxOWxOb1N5MmciLCJtYWMiOiI0ODUxYzA4YWJjNDQ3NGIxZTljNGQxNWNjM2EyMmNkMDQxMjIyYjI2YjM3MTJiN2UyYWFiOWMwYzA2ZTg2Yzg0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkF3MkpUeCtwNXFkNVBUMm5PcERzVUE9PSIsInZhbHVlIjoiUmZOZlR5bU5SVDR0ZkZ0L2NqbDJEUWljcHdDTGVqYUZQOUtlbkVwVWt5WFBzejFBOXozVDdCTDlUSitqQ01mNmtKcndUY20xWjR5OFdRSnVFbERtTEZCdFhUeXhpT04yVEV0c1QzQnllWjhnWWkzUTFablVsZUpQa28rN0xNSmIiLCJtYWMiOiJmODYxNjk4YmM0ZDY0MDY4MzkxZTRlZTQyZGU5ZmUwMDQ4NmQyZDk1NGU2NWY3YzMzMzFkZjBhZmVkNjE0NWI4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572892.5451</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572892</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1832048548\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-739379525 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739379525\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2070772839 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 03:08:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImlpYmxlQmhveXdKN1NZbElUdmxiYkE9PSIsInZhbHVlIjoiSEZrcld4MnpPdnB3bGl0TU1SZ25GTFBvc3hGdFBwTjNPQ05idnRyL1drbGVRWk0yOStEWGFxdVQwZktHRFo5R1F2MmRobFpMQ21aVDBxbjBlTjNjUUtad3RXMDhxM1N2eGJxU0tNeUIzWjl1SnBTbFRVb1ozcHMzVVZZbHVIaWYiLCJtYWMiOiI3YTIzMDIyM2Q1OWM2YTZhZGY1YjU1ZDkyNzE3N2QyNjkxNTZhNzNmOTNiM2JmYzIxMzc5YjRiYzExMDEwNGFlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:13 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IncvY0RJR1hFazJGN2JTSUR2RzFwaUE9PSIsInZhbHVlIjoiUTR3Ty9IMlRrUmE1VlV1c3VmMmZ6N0RJOGdVcDVIbGhXeVRBUkVqb3lXczhEZVZVRm90WkpnWlJVamFTOTRFcmZMdzJtejVoZ2dvaWNvNWQ2ZXJ1SlJDUGo0blJqM0pGRFF3aDQ1UWJTN2ZwYytCL29TcTFNcmk0bEg5dW5FaXoiLCJtYWMiOiJiMTEzOWIxNDJmMTU2NzM4MzU4ZGJjNmE4MjQzOTM4ZjVjMTIxNjgwMjc2M2ZlNzUzYmFlMGViMmRlMjVmOTU5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:13 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImlpYmxlQmhveXdKN1NZbElUdmxiYkE9PSIsInZhbHVlIjoiSEZrcld4MnpPdnB3bGl0TU1SZ25GTFBvc3hGdFBwTjNPQ05idnRyL1drbGVRWk0yOStEWGFxdVQwZktHRFo5R1F2MmRobFpMQ21aVDBxbjBlTjNjUUtad3RXMDhxM1N2eGJxU0tNeUIzWjl1SnBTbFRVb1ozcHMzVVZZbHVIaWYiLCJtYWMiOiI3YTIzMDIyM2Q1OWM2YTZhZGY1YjU1ZDkyNzE3N2QyNjkxNTZhNzNmOTNiM2JmYzIxMzc5YjRiYzExMDEwNGFlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IncvY0RJR1hFazJGN2JTSUR2RzFwaUE9PSIsInZhbHVlIjoiUTR3Ty9IMlRrUmE1VlV1c3VmMmZ6N0RJOGdVcDVIbGhXeVRBUkVqb3lXczhEZVZVRm90WkpnWlJVamFTOTRFcmZMdzJtejVoZ2dvaWNvNWQ2ZXJ1SlJDUGo0blJqM0pGRFF3aDQ1UWJTN2ZwYytCL29TcTFNcmk0bEg5dW5FaXoiLCJtYWMiOiJiMTEzOWIxNDJmMTU2NzM4MzU4ZGJjNmE4MjQzOTM4ZjVjMTIxNjgwMjc2M2ZlNzUzYmFlMGViMmRlMjVmOTU5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2070772839\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/radar-data/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}