{"__meta": {"id": "X5036f932501f281c017c66d5efff010d", "datetime": "2025-08-19 11:08:11", "utime": 1755572891.449049, "method": "POST", "uri": "/upload-radar-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[11:08:11] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572891.303583, "xdebug_link": null, "collector": "log"}, {"message": "[11:08:11] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/upload-radar-chart", "message_html": null, "is_string": false, "label": "debug", "time": 1755572891.428469, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572890.733385, "end": 1755572891.449104, "duration": 0.7157189846038818, "duration_str": "716ms", "measures": [{"label": "Booting", "start": 1755572890.733385, "relative_start": 0, "end": 1755572891.262592, "relative_end": 1755572891.262592, "duration": 0.5292069911956787, "duration_str": "529ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572891.262614, "relative_start": 0.529228925704956, "end": 1755572891.449109, "relative_end": 5.0067901611328125e-06, "duration": 0.18649506568908691, "duration_str": "186ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23504112, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST upload-radar-chart", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@uploadRadarChart", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=141\">\\app\\Http\\Controllers\\CIGController.php:141-151</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00083, "accumulated_duration_str": "830μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/radar-data/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"path_info": "/upload-radar-chart", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1923177 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"74522 characters\">data:image/png;base64,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</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1923177\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-405405257 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">74534</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkVkRUhDYTdFbDQrcHNDVjVvZDMvd2c9PSIsInZhbHVlIjoiend1SHFCVHBHNm95cnQvZ05vZnRqZUtDZnJiblNZY1N6ejBkT0g3M3NmUi9jSUdVL3N2YWRMTE1VL0NxcFhtV3JtdnlKOHVMTFVSUkpNQlp0YnJqcjlSRkF2MVpXV0RxRGRQTjhKbUw5R0U2QUovL2NTWiswbjU2dGZEb3M2bXkiLCJtYWMiOiIyNzg3NmM1NzcxMjk0NjgyZjc0OTYzNGEwNzI3NmE2ZTgwNzAwYWMzYjhmNDk1ZThjNTVkN2MwN2MwOTY0NmZhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IldDWktvNE9YMHNGdFNBWE1wSVQxcEE9PSIsInZhbHVlIjoiUFMvdXlmcnVIcWRpVisyazR5VDVmLzlJZGg3Qm0velBBODA5RTlvK1NxeDJCaVdweVg2c3VrdHlocW0rSndBMG9MNDBMODUyd0Q0Ym1LSTNUa3Z4Rm0rckF5KzZ3a0liTVZlRm1DQSt1aHNNT2NybGhDUkpRUm41dkRCR0xNSVIiLCJtYWMiOiJjZGMwNjg3NjA3MGE1MjlhMzQ4NzhkYzdmMzczNzQ1MmFjZDcyZWE0ZWU4NGM0MTJiYjE0MDBlZDZhOTYwNGU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-405405257\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1420732634 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60893</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/index.php/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">74534</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">74534</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkVkRUhDYTdFbDQrcHNDVjVvZDMvd2c9PSIsInZhbHVlIjoiend1SHFCVHBHNm95cnQvZ05vZnRqZUtDZnJiblNZY1N6ejBkT0g3M3NmUi9jSUdVL3N2YWRMTE1VL0NxcFhtV3JtdnlKOHVMTFVSUkpNQlp0YnJqcjlSRkF2MVpXV0RxRGRQTjhKbUw5R0U2QUovL2NTWiswbjU2dGZEb3M2bXkiLCJtYWMiOiIyNzg3NmM1NzcxMjk0NjgyZjc0OTYzNGEwNzI3NmE2ZTgwNzAwYWMzYjhmNDk1ZThjNTVkN2MwN2MwOTY0NmZhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IldDWktvNE9YMHNGdFNBWE1wSVQxcEE9PSIsInZhbHVlIjoiUFMvdXlmcnVIcWRpVisyazR5VDVmLzlJZGg3Qm0velBBODA5RTlvK1NxeDJCaVdweVg2c3VrdHlocW0rSndBMG9MNDBMODUyd0Q0Ym1LSTNUa3Z4Rm0rckF5KzZ3a0liTVZlRm1DQSt1aHNNT2NybGhDUkpRUm41dkRCR0xNSVIiLCJtYWMiOiJjZGMwNjg3NjA3MGE1MjlhMzQ4NzhkYzdmMzczNzQ1MmFjZDcyZWE0ZWU4NGM0MTJiYjE0MDBlZDZhOTYwNGU3IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572890.7334</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572890</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1420732634\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-616394423 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-616394423\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-637017499 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 03:08:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImlkdXBXY1FqWXJ3akRLUWRUMUhmNmc9PSIsInZhbHVlIjoic0dVS09XV2I1bnhpNmUxb2VKT0NoOWNHR21MSGRvUzZHZURRY2VSdUhyNEQ4MWsrbXAxQlozNHlCSSsxV2hjRWZKZG91QWYzUDErLzl6VEdPbWVDeitESFg4bzN0ZGttSWpLMng5RS9MSTJqVWpuTlpYV0ZxUkJxOWxOb1N5MmciLCJtYWMiOiI0ODUxYzA4YWJjNDQ3NGIxZTljNGQxNWNjM2EyMmNkMDQxMjIyYjI2YjM3MTJiN2UyYWFiOWMwYzA2ZTg2Yzg0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:11 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkF3MkpUeCtwNXFkNVBUMm5PcERzVUE9PSIsInZhbHVlIjoiUmZOZlR5bU5SVDR0ZkZ0L2NqbDJEUWljcHdDTGVqYUZQOUtlbkVwVWt5WFBzejFBOXozVDdCTDlUSitqQ01mNmtKcndUY20xWjR5OFdRSnVFbERtTEZCdFhUeXhpT04yVEV0c1QzQnllWjhnWWkzUTFablVsZUpQa28rN0xNSmIiLCJtYWMiOiJmODYxNjk4YmM0ZDY0MDY4MzkxZTRlZTQyZGU5ZmUwMDQ4NmQyZDk1NGU2NWY3YzMzMzFkZjBhZmVkNjE0NWI4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:11 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImlkdXBXY1FqWXJ3akRLUWRUMUhmNmc9PSIsInZhbHVlIjoic0dVS09XV2I1bnhpNmUxb2VKT0NoOWNHR21MSGRvUzZHZURRY2VSdUhyNEQ4MWsrbXAxQlozNHlCSSsxV2hjRWZKZG91QWYzUDErLzl6VEdPbWVDeitESFg4bzN0ZGttSWpLMng5RS9MSTJqVWpuTlpYV0ZxUkJxOWxOb1N5MmciLCJtYWMiOiI0ODUxYzA4YWJjNDQ3NGIxZTljNGQxNWNjM2EyMmNkMDQxMjIyYjI2YjM3MTJiN2UyYWFiOWMwYzA2ZTg2Yzg0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkF3MkpUeCtwNXFkNVBUMm5PcERzVUE9PSIsInZhbHVlIjoiUmZOZlR5bU5SVDR0ZkZ0L2NqbDJEUWljcHdDTGVqYUZQOUtlbkVwVWt5WFBzejFBOXozVDdCTDlUSitqQ01mNmtKcndUY20xWjR5OFdRSnVFbERtTEZCdFhUeXhpT04yVEV0c1QzQnllWjhnWWkzUTFablVsZUpQa28rN0xNSmIiLCJtYWMiOiJmODYxNjk4YmM0ZDY0MDY4MzkxZTRlZTQyZGU5ZmUwMDQ4NmQyZDk1NGU2NWY3YzMzMzFkZjBhZmVkNjE0NWI4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-637017499\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-931508124 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/radar-data/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931508124\", {\"maxDepth\":0})</script>\n"}}