{"__meta": {"id": "X081d59976a5457558357756efb036a86", "datetime": "2025-08-19 13:30:13", "utime": 1755581413.155944, "method": "GET", "uri": "/team-invitation/accept/hyBZCSMUM2lHOHUYxlA6dQwAcQa9LjHV", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[13:30:13] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755581413.002563, "xdebug_link": null, "collector": "log"}, {"message": "[13:30:13] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/team-invitation/accept/hyBZCSMUM2lHOHUYxlA6dQwAcQa9LjHV", "message_html": null, "is_string": false, "label": "debug", "time": 1755581413.103806, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755581412.342212, "end": 1755581413.155972, "duration": 0.8137600421905518, "duration_str": "814ms", "measures": [{"label": "Booting", "start": 1755581412.342212, "relative_start": 0, "end": 1755581412.959038, "relative_end": 1755581412.959038, "duration": 0.616826057434082, "duration_str": "617ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755581412.959055, "relative_start": 0.6168429851531982, "end": 1755581413.155975, "relative_end": 3.0994415283203125e-06, "duration": 0.19692015647888184, "duration_str": "197ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23572600, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET team-invitation/accept/{token}", "middleware": "web", "controller": "App\\Http\\Controllers\\TeamMappingController@acceptInvitation", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "team-invitation.accept", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TeamMappingController.php&line=550\">\\app\\Http\\Controllers\\TeamMappingController.php:550-553</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00564, "accumulated_duration_str": "5.64ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 7.979}, {"sql": "select * from `teammappings` where `invitation_token` = 'hyBZCSMUM2lHOHUYxlA6dQwAcQa9LjHV' limit 1", "type": "query", "params": [], "bindings": ["hyBZCSMUM2lHOHUYxlA6dQwAcQa9LjHV"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 523}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 552}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:523", "connection": "sagile", "start_percent": 7.979, "width_percent": 8.511}, {"sql": "update `teammappings` set `invitation_status` = 'accepted', `teammappings`.`updated_at` = '2025-08-19 13:30:13' where `teammapping_id` = 94", "type": "query", "params": [], "bindings": ["accepted", "2025-08-19 13:30:13", "94"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 536}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 552}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00471, "duration_str": "4.71ms", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:536", "connection": "sagile", "start_percent": 16.489, "width_percent": 83.511}]}, "models": {"data": {"App\\TeamMapping": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/team-invitation/accept/hyBZCSMUM2lHOHUYxlA6dQwAcQa9LjHV\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "success": "You have successfully accepted the team invitation.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/team-invitation/accept/hyBZCSMUM2lHOHUYxlA6dQwAcQa9LjHV", "status_code": "<pre class=sf-dump id=sf-dump-1249157707 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1249157707\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-574701004 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-574701004\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1878792361 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1878792361\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-755085610 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-browser-channel</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">stable</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-browser-year</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2025</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-browser-validation</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">XPdmRdCCj2OkELQ2uovjJFk6aKA=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-browser-copyright</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">Copyright 2025 Google LLC. All rights reserved.</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlpxR204RHB3WU1FOGNEWGJjTFY1dlE9PSIsInZhbHVlIjoiRml3N3dBa2N2Rk1zOTNlYnkwTGRIWFZqeC82cnorT3BOZmkvZTZ1V0psZ2dmbDBYZUFCMFk4MVpaTmtBTEVScmFqZ2xwRngwOXpKaE5KOW52VWhUQUJPRndTRXFSUzZzcE9FVlVsVmVGTXpVYXU2NDdjNitTaWpGWWIvVHJOamUiLCJtYWMiOiJlYTg1NTlhMTM5Njg3YjBjZjY4NjQ3OGQ3MmQzMzc3OTQyNjNkMGFkNmE2ZWMyZjNkOWY0NjFkNWM1NWM2ZGNjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Imttb2FkMVdvL0I5VTZjUlJGZUMzdHc9PSIsInZhbHVlIjoiNzdvbVdLaTRaTE9oeUluMXN6MWQxZEYyckJabHowZ0tJd09CcGF6Y3RxTVArcE8zWUxRRWtwQlB1U3hYNTV1SVNtazdpUXVSTGUzTGgrM2xmOWU2a2puN1d1aTRwOU92RjRiWmZ1RGFWZEdWd2Q0V3NUNHczSUVyeUptK0VwTWsiLCJtYWMiOiIxNTYwYTVjOGFmNDdkNTA2MTk1OTBiZDc5OWNmMTVlZGM4MDJjZjMwYzE0ZTZkMzE2ODkzOTkyYzI1ZThhMzI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755085610\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-154816494 data-indent-pad=\"  \"><span class=sf-dump-note>array:34</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55891</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"56 characters\">/team-invitation/accept/hyBZCSMUM2lHOHUYxlA6dQwAcQa9LjHV</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"56 characters\">/team-invitation/accept/hyBZCSMUM2lHOHUYxlA6dQwAcQa9LjHV</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"66 characters\">/index.php/team-invitation/accept/hyBZCSMUM2lHOHUYxlA6dQwAcQa9LjHV</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_X_BROWSER_CHANNEL</span>\" => \"<span class=sf-dump-str title=\"6 characters\">stable</span>\"\n  \"<span class=sf-dump-key>HTTP_X_BROWSER_YEAR</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2025</span>\"\n  \"<span class=sf-dump-key>HTTP_X_BROWSER_VALIDATION</span>\" => \"<span class=sf-dump-str title=\"28 characters\">XPdmRdCCj2OkELQ2uovjJFk6aKA=</span>\"\n  \"<span class=sf-dump-key>HTTP_X_BROWSER_COPYRIGHT</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Copyright 2025 Google LLC. All rights reserved.</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlpxR204RHB3WU1FOGNEWGJjTFY1dlE9PSIsInZhbHVlIjoiRml3N3dBa2N2Rk1zOTNlYnkwTGRIWFZqeC82cnorT3BOZmkvZTZ1V0psZ2dmbDBYZUFCMFk4MVpaTmtBTEVScmFqZ2xwRngwOXpKaE5KOW52VWhUQUJPRndTRXFSUzZzcE9FVlVsVmVGTXpVYXU2NDdjNitTaWpGWWIvVHJOamUiLCJtYWMiOiJlYTg1NTlhMTM5Njg3YjBjZjY4NjQ3OGQ3MmQzMzc3OTQyNjNkMGFkNmE2ZWMyZjNkOWY0NjFkNWM1NWM2ZGNjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Imttb2FkMVdvL0I5VTZjUlJGZUMzdHc9PSIsInZhbHVlIjoiNzdvbVdLaTRaTE9oeUluMXN6MWQxZEYyckJabHowZ0tJd09CcGF6Y3RxTVArcE8zWUxRRWtwQlB1U3hYNTV1SVNtazdpUXVSTGUzTGgrM2xmOWU2a2puN1d1aTRwOU92RjRiWmZ1RGFWZEdWd2Q0V3NUNHczSUVyeUptK0VwTWsiLCJtYWMiOiIxNTYwYTVjOGFmNDdkNTA2MTk1OTBiZDc5OWNmMTVlZGM4MDJjZjMwYzE0ZTZkMzE2ODkzOTkyYzI1ZThhMzI3IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755581412.3422</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755581412</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-154816494\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-273241401 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GKxRuD042xMQ4k3v5EHCZLsF1sfAXXnsz4rkTCog</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-273241401\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-341223706 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:30:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/teammapping/Team%20888</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImNlUkVMaXY4aEY5ZTdOazM3cSs1TkE9PSIsInZhbHVlIjoiRkdGMDN6ZWg0VDZpcFpZWVI2MW5MWkw4bldwQmd0MlFEOEI1cmlZSnMzVFlFZm1oV211ZDdOQWFldzFlNGUwNnlrZ1hhbXdEWlc1eGc5SHB6SHV3dlczbmp2VXEwV0FpamhnRjNpUWhoQlRVbmpTYU1ta1JURUI1VlRlQW5zYjYiLCJtYWMiOiJkOTg2MDAyYmU4NTJmMzU5OGNlOGMzYTgyMDFkNjgzNGJhODg1OTZkZDRlMzM2ZjBjZTdiMTM3NzYzZGYzZGM3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:30:13 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlZ6R21HakVxYzNxUFFmak10MXJJTHc9PSIsInZhbHVlIjoiUUxMQm5lSlk4L1VRNnRYTHcvY2wzeVdTT3RXeXQ4U0Q3Ym5jRmJEbkFyR2V3TkQwbHNIRXZpeXhXOWdzRmlSYWtEOXJYSnlGbXdiN1BpUGJvNFJ3Mm9IeGEwVW56WEp4UnJvQ3UwUU1Oamc2WTJrZlB2M1R4ZDhOQ25EdXYya3QiLCJtYWMiOiJlYjJkYTAzMmNiN2MwMmM0NTk2YzlhMjkyZjNhYzE0YTQ3NThiYTc5YjY0ZTYyYmFjOTdiZGM4MDA3ZGRlZGY4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:30:13 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImNlUkVMaXY4aEY5ZTdOazM3cSs1TkE9PSIsInZhbHVlIjoiRkdGMDN6ZWg0VDZpcFpZWVI2MW5MWkw4bldwQmd0MlFEOEI1cmlZSnMzVFlFZm1oV211ZDdOQWFldzFlNGUwNnlrZ1hhbXdEWlc1eGc5SHB6SHV3dlczbmp2VXEwV0FpamhnRjNpUWhoQlRVbmpTYU1ta1JURUI1VlRlQW5zYjYiLCJtYWMiOiJkOTg2MDAyYmU4NTJmMzU5OGNlOGMzYTgyMDFkNjgzNGJhODg1OTZkZDRlMzM2ZjBjZTdiMTM3NzYzZGYzZGM3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:30:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlZ6R21HakVxYzNxUFFmak10MXJJTHc9PSIsInZhbHVlIjoiUUxMQm5lSlk4L1VRNnRYTHcvY2wzeVdTT3RXeXQ4U0Q3Ym5jRmJEbkFyR2V3TkQwbHNIRXZpeXhXOWdzRmlSYWtEOXJYSnlGbXdiN1BpUGJvNFJ3Mm9IeGEwVW56WEp4UnJvQ3UwUU1Oamc2WTJrZlB2M1R4ZDhOQ25EdXYya3QiLCJtYWMiOiJlYjJkYTAzMmNiN2MwMmM0NTk2YzlhMjkyZjNhYzE0YTQ3NThiYTc5YjY0ZTYyYmFjOTdiZGM4MDA3ZGRlZGY4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:30:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-341223706\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-525908485 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"77 characters\">http://127.0.0.1:8000/team-invitation/accept/hyBZCSMUM2lHOHUYxlA6dQwAcQa9LjHV</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"51 characters\">You have successfully accepted the team invitation.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-525908485\", {\"maxDepth\":0})</script>\n"}}