{"__meta": {"id": "X5950fcee59683365dcc388e3bb7490e1", "datetime": "2025-08-19 10:56:44", "utime": 1755572204.702705, "method": "GET", "uri": "/cig/create/42", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[10:56:44] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572204.07587, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:44] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/cig/create/42", "message_html": null, "is_string": false, "label": "debug", "time": 1755572204.200038, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572203.503434, "end": 1755572204.702755, "duration": 1.1993210315704346, "duration_str": "1.2s", "measures": [{"label": "Booting", "start": 1755572203.503434, "relative_start": 0, "end": 1755572204.031535, "relative_end": 1755572204.031535, "duration": 0.5281009674072266, "duration_str": "528ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572204.031555, "relative_start": 0.5281209945678711, "end": 1755572204.702758, "relative_end": 3.0994415283203125e-06, "duration": 0.6712031364440918, "duration_str": "671ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25752848, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 18, "templates": [{"name": "cig.create (\\resources\\views\\cig\\create.blade.php)", "param_count": 7, "params": ["title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\resources\\views\\vendor\\pagination\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 26, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 26, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 26, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET cig/create/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@create", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "cig.create", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=81\">\\app\\Http\\Controllers\\CIGController.php:81-108</a>"}, "queries": {"nb_statements": 20, "nb_failed_statements": 0, "accumulated_duration": 0.014750000000000001, "accumulated_duration_str": "14.75ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0008900000000000001, "duration_str": "890μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 6.034}, {"sql": "select * from `projects` where `projects`.`id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Project.php", "line": 86}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Project.php:86", "connection": "sagile", "start_percent": 6.034, "width_percent": 5.085}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System'", "type": "query", "params": [], "bindings": ["Food Ordering System"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Sprint.php", "line": 62}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 85}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Sprint.php:62", "connection": "sagile", "start_percent": 11.119, "width_percent": 6.373}, {"sql": "select * from `user_stories` where `proj_id` = '42'", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 36}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 91}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:36", "connection": "sagile", "start_percent": 17.492, "width_percent": 5.831}, {"sql": "select * from `user_story_general_nfr` where `user_story_general_nfr`.`user_story_id` in (45, 46, 47, 48, 49)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 91}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:36", "connection": "sagile", "start_percent": 23.322, "width_percent": 4.61}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 91}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:36", "connection": "sagile", "start_percent": 27.932, "width_percent": 5.288}, {"sql": "select count(*) as aggregate from `user_stories` where `proj_id` = '42' and exists (select * from `user_story_general_nfr` where `user_stories`.`u_id` = `user_story_general_nfr`.`user_story_id`)", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\UserStory.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 94}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\UserStory.php:57", "connection": "sagile", "start_percent": 33.22, "width_percent": 6.237}, {"sql": "select `u_id`, `user_story`, `sprint_id` from `user_stories` where `proj_id` = '42' and exists (select * from `user_story_general_nfr` where `user_stories`.`u_id` = `user_story_general_nfr`.`user_story_id`) order by `u_id` asc limit 5 offset 0", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\UserStory.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 94}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\UserStory.php:57", "connection": "sagile", "start_percent": 39.458, "width_percent": 5.356}, {"sql": "select * from `user_story_general_nfr` where `user_story_general_nfr`.`user_story_id` = 45 and `user_story_general_nfr`.`user_story_id` is not null", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 44.814, "width_percent": 5.22}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 50.034, "width_percent": 4.407}, {"sql": "select * from `nfr` where `nfr`.`nfr_id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 54.441, "width_percent": 4.61}, {"sql": "select * from `user_story_general_nfr` where `user_story_general_nfr`.`user_story_id` = 47 and `user_story_general_nfr`.`user_story_id` is not null", "type": "query", "params": [], "bindings": ["47"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 59.051, "width_percent": 3.864}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 62.915, "width_percent": 3.932}, {"sql": "select * from `nfr` where `nfr`.`nfr_id` in (2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 66.847, "width_percent": 3.661}, {"sql": "select * from `user_story_general_nfr` where `user_story_general_nfr`.`user_story_id` = 48 and `user_story_general_nfr`.`user_story_id` is not null", "type": "query", "params": [], "bindings": ["48"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 70.508, "width_percent": 5.356}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 75.864, "width_percent": 4.203}, {"sql": "select * from `nfr` where `nfr`.`nfr_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 80.068, "width_percent": 3.864}, {"sql": "select * from `user_story_general_nfr` where `user_story_general_nfr`.`user_story_id` = 49 and `user_story_general_nfr`.`user_story_id` is not null", "type": "query", "params": [], "bindings": ["49"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 83.932, "width_percent": 4.407}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 88.339, "width_percent": 5.288}, {"sql": "select * from `nfr` where `nfr`.`nfr_id` in (2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 93.627, "width_percent": 6.373}]}, "models": {"data": {"App\\SpecificNFR": 8, "App\\GeneralNFR": 7, "App\\UserStoryGeneralNfr": 16, "App\\UserStory": 9, "App\\Sprint": 3, "App\\Project": 1, "App\\User": 1}, "count": 45}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/cig/create/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/cig/create/42", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1535166911 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1535166911\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2106969049 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2106969049\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-648887922 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://127.0.0.1:8000/cig</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkNkZXNTVkw5aHFHSkFNakk3RXlsdlE9PSIsInZhbHVlIjoiNzdYbS8rMGt6MDg1UzVsZUNKcEgvdzlUZWN3VlhVZU5jOWJhZWJRSFlzREZvMm0vR3VDT1B6RnF0U2krZDZXZWhacFJLZ3JOZ3VBQUZCR25ieG02SVJnSnR6V0dRSTJWT3A0eDlrUzB2d01TaEV5ZU9jRlNJWFBzMjFrdHlyRUQiLCJtYWMiOiIzYzgwMTQwNDgwNmFhMmQ2Y2JlOTljODhmNzM5OGYzZDU2ZTI1N2FlMDQ0OThiMDVmMGMyYWEzZWNkYTYzNWQyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InFOQkRQcUtUeDJTTXB1ay8wOGtUamc9PSIsInZhbHVlIjoiQXRjWmQ1Z3ZrNFR0bTJpRUVaWmdrL0dNN1h2UlozM2xpcXZWVlNDQi9DcVZmcGM2M0J2OS9iNE1DRDdVZjZkNmxMZmdVQ05vTE1VK0VsaGN1NDlxZFZlWlNpMGhzblVKeE90OGI5aEVSSUQvRGlranFkOWcvL0lEcnJYZWdFTUwiLCJtYWMiOiJjOWNlMzBmZGI0YzhkZGVmM2NlMWMzYzFjN2VhMjY0YzcxMzk5YmM1NWQ5ZWFiNDg3OGY0MjkxYjNiNzUwMTgyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-648887922\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1324606219 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">49823</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"14 characters\">/cig/create/42</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"14 characters\">/cig/create/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/index.php/cig/create/42</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://127.0.0.1:8000/cig</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkNkZXNTVkw5aHFHSkFNakk3RXlsdlE9PSIsInZhbHVlIjoiNzdYbS8rMGt6MDg1UzVsZUNKcEgvdzlUZWN3VlhVZU5jOWJhZWJRSFlzREZvMm0vR3VDT1B6RnF0U2krZDZXZWhacFJLZ3JOZ3VBQUZCR25ieG02SVJnSnR6V0dRSTJWT3A0eDlrUzB2d01TaEV5ZU9jRlNJWFBzMjFrdHlyRUQiLCJtYWMiOiIzYzgwMTQwNDgwNmFhMmQ2Y2JlOTljODhmNzM5OGYzZDU2ZTI1N2FlMDQ0OThiMDVmMGMyYWEzZWNkYTYzNWQyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InFOQkRQcUtUeDJTTXB1ay8wOGtUamc9PSIsInZhbHVlIjoiQXRjWmQ1Z3ZrNFR0bTJpRUVaWmdrL0dNN1h2UlozM2xpcXZWVlNDQi9DcVZmcGM2M0J2OS9iNE1DRDdVZjZkNmxMZmdVQ05vTE1VK0VsaGN1NDlxZFZlWlNpMGhzblVKeE90OGI5aEVSSUQvRGlranFkOWcvL0lEcnJYZWdFTUwiLCJtYWMiOiJjOWNlMzBmZGI0YzhkZGVmM2NlMWMzYzFjN2VhMjY0YzcxMzk5YmM1NWQ5ZWFiNDg3OGY0MjkxYjNiNzUwMTgyIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572203.5034</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572203</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1324606219\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-123696729 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-123696729\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:56:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlM4RHBISWN0cEhMQlVrc1ZpbjlaaUE9PSIsInZhbHVlIjoicEpwdU02SzU3VVM4d3I5RU93Y2JQVEc1cWZ4SEovQkc5OHZ1VHNTUVYyczI4NEI2YS9HNkxPc05JUUhLUElacnJCdXdUQmg2cUNTbjV1YjliQ3BGblJ2dkczSUFObHBtWjI0ZlR1Y1RXcUpWbElSYWczbkYzaUFyMjEzdENHdnEiLCJtYWMiOiI0NDkxMzI3OWMyY2Q5MmYyYzFlMjE4ZjZiOWVlYTViYTE0ODg0NjAzMGYwOTBjZTU4OTFiZjdhZmQ5NjQyNzk2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:44 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkxNRGtUdW9JUFFUM2JKQ1E0U0VRZnc9PSIsInZhbHVlIjoiNncrS1BLbTlNR2J4RDkxT2lyL085SThTSWYvNzlIN3JEMktndVM4ZFdidHJKV0wwVVV1VFRPSGFTZENBUG02YmF6bWlDSDFrSnhzd2dIZno4UVkyVDlWNlloVU9DcHM5dVdrcURWUFcrTVRHMTlrNTNYNG1IaTA1QmVhaHZJYVUiLCJtYWMiOiI5NGVlODkxNTIwMmNlNDA1YmNjZjhhZTJjNjcxZWI0ZDM1MWRlMTVjMjUyNjQ4NzE1ZDQyMDE0MmI5NjEwNDNhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:44 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlM4RHBISWN0cEhMQlVrc1ZpbjlaaUE9PSIsInZhbHVlIjoicEpwdU02SzU3VVM4d3I5RU93Y2JQVEc1cWZ4SEovQkc5OHZ1VHNTUVYyczI4NEI2YS9HNkxPc05JUUhLUElacnJCdXdUQmg2cUNTbjV1YjliQ3BGblJ2dkczSUFObHBtWjI0ZlR1Y1RXcUpWbElSYWczbkYzaUFyMjEzdENHdnEiLCJtYWMiOiI0NDkxMzI3OWMyY2Q5MmYyYzFlMjE4ZjZiOWVlYTViYTE0ODg0NjAzMGYwOTBjZTU4OTFiZjdhZmQ5NjQyNzk2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkxNRGtUdW9JUFFUM2JKQ1E0U0VRZnc9PSIsInZhbHVlIjoiNncrS1BLbTlNR2J4RDkxT2lyL085SThTSWYvNzlIN3JEMktndVM4ZFdidHJKV0wwVVV1VFRPSGFTZENBUG02YmF6bWlDSDFrSnhzd2dIZno4UVkyVDlWNlloVU9DcHM5dVdrcURWUFcrTVRHMTlrNTNYNG1IaTA1QmVhaHZJYVUiLCJtYWMiOiI5NGVlODkxNTIwMmNlNDA1YmNjZjhhZTJjNjcxZWI0ZDM1MWRlMTVjMjUyNjQ4NzE1ZDQyMDE0MmI5NjEwNDNhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}