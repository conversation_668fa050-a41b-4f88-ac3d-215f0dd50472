
<?php echo $__env->make('inc.success', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('inc.style', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('inc.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->startSection('navbar'); ?>
  <?php echo $__env->make('inc.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
  <?php echo $__env->make('inc.title', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

  <br>
  <?php if($specificNFR): ?>
      <p>Specific NFR: <?php echo e($specificNFR->specific_nfr); ?></p>
      <p>Description: <?php echo e($specificNFR->specific_nfr_desc); ?></p>


      <!-- Filter Bar Form -->
      <form action="<?php echo e(route('nfr.viewSpecific', ['general_nfr_id' => $specificNFR->general_nfr_id, 'nfr_id' => $specificNFR->nfr_id])); ?>" method="GET">
      <div class="form-group">
        <label for="project_filter">Filter by Project:</label>
            <select name="project_filter" id="project_filter" class="form-control" onchange="this.form.submit()">
                <option value="">Select a project</option>
                <?php $__currentLoopData = $pro; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($project->id); ?>" <?php echo e(request('project_filter') == $project->id ? 'selected' : ''); ?>>
                        <?php echo e($project->proj_name); ?>

                    </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
      </div>
      <br>
      <div class="form-group">
        <label for="sprint_filter">Filter by Sprint:</label>
            <select name="sprint_filter" id="sprint_filter" class="form-control" onchange="this.form.submit()">
                <option value="">Select a sprint</option>
                <?php $__currentLoopData = $sprints; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sprint): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($sprint->sprint_id); ?>" <?php echo e(request('sprint_filter') == $sprint->sprint_id ? 'selected' : ''); ?>>
                        <?php echo e($sprint->sprint_name); ?>

                    </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
      </div>
      </form>

      <br>
      <h3>Linked User Stories</h3>
      <table class="table table-bordered">
          <thead>
              <tr>
                <th>Project</th>
                <th>Sprint</th>
                <th>User Story</th>
              </tr>
          </thead>
          <tbody>
              <?php if(isset($linkedUserStories) && !$linkedUserStories->isEmpty()): ?>
                  <?php $__currentLoopData = $linkedUserStories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $story): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <tr>
                        <td><?php echo e($story->proj_name); ?></td>
                        <td><?php echo e($story->sprint_name); ?></td>
                        <td><?php echo e($story->user_story); ?></td>
                      </tr>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              <?php else: ?>
                  <tr>
                      <td>No linked user stories found.</td>
                  </tr>
              <?php endif; ?>
          </tbody>
      </table>

      <!-- Pagination -->
      <div class="pagination-container">
          <?php echo e($linkedUserStories->links('pagination::bootstrap-4')); ?>

      </div>

  <?php else: ?>
      <p>Specific NFR not found.</p>
  <?php endif; ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/nfr/viewSpecific.blade.php ENDPATH**/ ?>