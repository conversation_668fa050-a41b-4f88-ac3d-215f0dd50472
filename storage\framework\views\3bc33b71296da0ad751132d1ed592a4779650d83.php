

<?php $__env->startSection('title', 'Dashboard - Analytics'); ?>

<!-- <?php $__env->startSection('vendor-style'); ?>
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/apex-charts/apex-charts.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('vendor-script'); ?>
<script src="<?php echo e(asset('assets/vendor/libs/apex-charts/apexcharts.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-script'); ?>
<script src="<?php echo e(asset('assets/js/dashboards-analytics.js')); ?>"></script>
<?php $__env->stopSection(); ?> -->

<?php echo $__env->make('inc.apex-charts-css', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('inc.apex-charts-js', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('inc.dashboard-analytics-js', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->startSection('content'); ?>
<div class="row gy-4">
  <!-- Greetings card -->
  <div class="col-md-12 col-lg-4">
    <div class="card">
      <div class="card-body">
        <h2 class="card-title mb-2">Hi <strong> <?php echo e(auth()->user()->name); ?> </strong></h2>
        <p class="pb-0">Your next steps are just a click away!</p>
        <!--<h4 class="text-primary mb-1">$42.8k</h4> -->
        <!-- <a href="javascript:;" class="btn btn-sm btn-danger" style="background-color: #3f58b0;">View Profile</a> -->
      </div>
      <img src="<?php echo e(asset('assets/img/icons/misc/triangle-light.png')); ?>" class="scaleX-n1-rtl position-absolute bottom-0 end-0" width="166" alt="triangle background">
      <!-- <img src="<?php echo e(asset('assets/img/illustrations/trophy.png')); ?>" class="scaleX-n1-rtl position-absolute bottom-0 end-0 me-4 mb-4 pb-2" width="83" alt="view sales"> -->
    </div>
  </div>
  <!--/ Greetings card -->

  <div class="row gy-4">
    <!-- Event Count Over Time Chart -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-1">Calendar Events</h5>
            </div>
            <div class="card-body">
                <div id="eventChart" style="height: 350px;"></div>
                <script>
                    document.addEventListener('DOMContentLoaded', function () {
                        var eventOptions = {
                            chart: {
                                type: 'bar', // Changed to bar type
                                height: 350,
                                zoom: {
                                    enabled: false
                                }
                            },
                            series: [{
                                name: 'Event Count',
                                data: <?php echo json_encode(array_values($countsPerMonth), 15, 512) ?>
                            }],
                            xaxis: {
                                categories: <?php echo json_encode(array_keys($countsPerMonth), 15, 512) ?>,
                                labels: {
                                    rotate: -45,
                                    formatter: function (value) {
                                        return value.split(' ')[0]; // Display only the month part
                                    }
                                }
                            },
                            yaxis: {
                                title: {
                                    text: 'Total Events'
                                },
                                tickAmount: 5,
                                ticks: [1, 2, 3, 4, 5]
                            },
                        };

                        var eventChart = new ApexCharts(document.querySelector("#eventChart"), eventOptions);
                        eventChart.render();
                    });
                </script>
            </div>
        </div>
    </div>

 <!-- Bug Distribution Chart -->
 <div class="col-lg-6">
  <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-1">Bug Distribution</h5>
          <!-- Dropdown Menu for Chart Type -->
          <div class="dropdown">
              <button class="btn btn-secondary dropdown-toggle btn-sm" type="button" id="chartTypeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                  Bug Status
              </button>
              <ul class="dropdown-menu" aria-labelledby="chartTypeDropdown">
                  <li><a class="dropdown-item active" href="#" data-chart-type="status">Bug Status</a></li>
                  <li><a class="dropdown-item" href="#" data-chart-type="severity">Bug Severity</a></li>
              </ul>
          </div>
      </div>
      <div class="card-body">
          <div id="bugChart" style="height: 350px;"></div>
      </div>
  </div>
</div>

<!-- Include Bootstrap CSS (if not already included) -->
<link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" rel="stylesheet">

<!-- Include Bootstrap JS (if not already included) -->
<script src="https://code.jquery.com/jquery-3.2.1.slim.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.11.0/umd/popper.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>

<script>
  document.addEventListener('DOMContentLoaded', function () {
      var chartOptions = {
          chart: {
              type: 'pie',
              height: 350,
              toolbar: {
                  show: true,
                  tools: {
                      download: true,
                      selection: true,
                      zoom: true,
                      zoomin: true,
                      zoomout: true,
                      pan: true,
                      reset: true,
                  },
              },
          },
          series: <?php echo json_encode($bugChartData['status']['series'], 15, 512) ?>,
          labels: <?php echo json_encode($bugChartData['status']['labels'], 15, 512) ?>,
      };

      var chart = new ApexCharts(document.querySelector("#bugChart"), chartOptions);
      chart.render();

      document.querySelectorAll('.dropdown-item').forEach(function (item) {
          item.addEventListener('click', function (event) {
              event.preventDefault();
              var selectedType = this.getAttribute('data-chart-type');
              updateChart(selectedType);
              updateDropdownText(this.textContent);
          });
      });

      function updateChart(type) {
          var newOptions = {
              series: type === 'status' ? <?php echo json_encode($bugChartData['status']['series'], 15, 512) ?> : <?php echo json_encode($bugChartData['severity']['series'], 15, 512) ?>,
              labels: type === 'status' ? <?php echo json_encode($bugChartData['status']['labels'], 15, 512) ?> : <?php echo json_encode($bugChartData['severity']['labels'], 15, 512) ?>,
          };

          chart.updateOptions(newOptions);
      }

      function updateDropdownText(text) {
          var button = document.querySelector('#chartTypeDropdown');
          button.textContent = text;
      }

      // Initialize with default selection
      updateDropdownText('Bug Status');
  });
</script>
<div class="row gy-4">
  <!-- Project Progress Chart -->
  <div class="col-lg-12">
      <div class="card">
          <div class="card-header">
              <h5 class="mb-1">Project Progress</h5>
          </div>
          <div class="card-body">
              <div id="projectProgressChart" style="height: 350px;"></div>
              <script>
                  document.addEventListener('DOMContentLoaded', function () {                      // Real project progress data based on user stories
                      var projectProgressData = [];
                      var projectNames = <?php echo json_encode($pros->pluck('proj_name'), 15, 512) ?>;

                      // Calculate progress for each project
                      <?php $__currentLoopData = $pros; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                          projectProgressData.push(<?php echo e(\App\Http\Controllers\UserStoryController::calculateProjectProgress($project->id)); ?>);
                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                      // Define chart options
                      var projectProgressOptions = {
                          chart: {
                              type: 'bar',
                              height: 350,
                              stacked: true,
                              zoom: {
                                  enabled: false
                              }
                          },
                          series: [{
                              name: 'Progress',
                              data: projectProgressData
                          }],
                          xaxis: {
                              categories: projectNames,
                              labels: {
                                  rotate: -45
                              }
                          },
                          yaxis: {
                              title: {
                                  text: 'Progress (%)'
                              },
                              max: 100
                          },
                          plotOptions: {
                              bar: {
                                  horizontal: false,
                                  columnWidth: '55%'
                              }
                          },
                          fill: {
                              opacity: 1
                          },
                          legend: {
                              position: 'top'
                          }
                      };

                      // Initialize and render the chart
                      var projectProgressChart = new ApexCharts(document.querySelector("#projectProgressChart"), projectProgressOptions);
                      projectProgressChart.render();
                  });
              </script>
          </div>
      </div>
  </div>
</div>

</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts/contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/layouts/app3.blade.php ENDPATH**/ ?>