{"__meta": {"id": "X2b0085b48342431ea6d09a2ad63b0380", "datetime": "2025-08-19 14:09:28", "utime": 1755583768.612588, "method": "GET", "uri": "/teammapping/Team%20AD", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[14:09:28] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755583768.459421, "xdebug_link": null, "collector": "log"}, {"message": "[14:09:28] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/teammapping/Team%20AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583768.521969, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755583768.127244, "end": 1755583768.612613, "duration": 0.4853689670562744, "duration_str": "485ms", "measures": [{"label": "Booting", "start": 1755583768.127244, "relative_start": 0, "end": 1755583768.434989, "relative_end": 1755583768.434989, "duration": 0.30774497985839844, "duration_str": "308ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755583768.435002, "relative_start": 0.307758092880249, "end": 1755583768.612615, "relative_end": 2.1457672119140625e-06, "duration": 0.1776130199432373, "duration_str": "178ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25346088, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 15, "templates": [{"name": "teammapping.index (\\resources\\views\\teammapping\\index.blade.php)", "param_count": 3, "params": ["teammappings", "teams", "title"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET teammapping/{team_name}", "middleware": "web", "controller": "App\\Http\\Controllers\\TeamMappingController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "teammapping.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TeamMappingController.php&line=59\">\\app\\Http\\Controllers\\TeamMappingController.php:59-76</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0017, "accumulated_duration_str": "1.7ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 25.294}, {"sql": "select * from `teams` where `team_name` = 'Team AD' limit 1", "type": "query", "params": [], "bindings": ["Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 64}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:64", "connection": "sagile", "start_percent": 25.294, "width_percent": 25.882}, {"sql": "select * from `teammappings` where `project_id` is null and `team_name` = 'Team AD'", "type": "query", "params": [], "bindings": ["Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 70}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:70", "connection": "sagile", "start_percent": 51.176, "width_percent": 25.294}, {"sql": "select exists(select * from `teammappings` where `team_name` = 'Team AD' and `username` = 'ivlyn' and `role_name` = 'Team Manager' and `invitation_status` = 'accepted') as `exists`", "type": "query", "params": [], "bindings": ["Team AD", "ivlyn", "Team Manager", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": "view", "name": "4dd4546e8c5ba9283dc8de130df2adb90e812ffc", "line": 74}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 15, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "view::4dd4546e8c5ba9283dc8de130df2adb90e812ffc:74", "connection": "sagile", "start_percent": 76.471, "width_percent": 23.529}]}, "models": {"data": {"App\\TeamMapping": 1, "App\\Team": 1, "App\\User": 1}, "count": 3}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/teammapping/Team%20AD\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755581799\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/teammapping/Team%20AD", "status_code": "<pre class=sf-dump id=sf-dump-2046857772 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2046857772\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-998885036 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-998885036\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-534432319 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-534432319\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1728105088 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/team</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ilkrc2xQZXd0c3g2Mzgyb1BCdEdvSlE9PSIsInZhbHVlIjoiSkVyUTRpWXgwTEQ4RGJRVVdIbTBpejBxV3A4N1hRL0dZS05rUzJnOWQybGtRdGgvbFFEdnJnSnNwT3RsVWdTSjk4VGJiZmZ2QkpMOHY2bGhDVW5ubTIwRmVhUTNDazkwZ2Jzd2hEalBqMjcvcWQ4YUwvdmVOQ2FTR2hzaTI3THgiLCJtYWMiOiI3Y2FhOThiMTgyNmViMDM0YmMxNWEzOWNkNmIyMzEwMmY5ZDUxNDUyOTA4NTdiNDkyYmMyZTU4MzVkZDg1MTFiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjBERUhtSkYxcW1zUHFSNTRNbnNaTXc9PSIsInZhbHVlIjoiS253T1hvdjFsbkdEV3Z5RGJEU0VuRkZpMUhxMktQb3c1OHpqU2RlMEUrcEtqUFMyUktJUXA2TTRiREVZK2kzdUdYUi92Sml4aU9pamVxay9samh3L2tlUGFMM1o2NzRZekhsTUxTRVdjVVZhV2dhbzBLWnR3OHI5c3RKTmFkVlEiLCJtYWMiOiJiZTFlNDJiZjI0NWQ1ODY5MDY2ZDA5NzM5MjI2N2VhMzRmMjBhMjlmOTM0MWNkYjQ4OGI5NDQ5ZGQwNjU2MmZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1728105088\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-900086324 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">64218</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/teammapping/Team%20AD</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/teammapping/Team AD</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/index.php/teammapping/Team AD</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/team</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ilkrc2xQZXd0c3g2Mzgyb1BCdEdvSlE9PSIsInZhbHVlIjoiSkVyUTRpWXgwTEQ4RGJRVVdIbTBpejBxV3A4N1hRL0dZS05rUzJnOWQybGtRdGgvbFFEdnJnSnNwT3RsVWdTSjk4VGJiZmZ2QkpMOHY2bGhDVW5ubTIwRmVhUTNDazkwZ2Jzd2hEalBqMjcvcWQ4YUwvdmVOQ2FTR2hzaTI3THgiLCJtYWMiOiI3Y2FhOThiMTgyNmViMDM0YmMxNWEzOWNkNmIyMzEwMmY5ZDUxNDUyOTA4NTdiNDkyYmMyZTU4MzVkZDg1MTFiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjBERUhtSkYxcW1zUHFSNTRNbnNaTXc9PSIsInZhbHVlIjoiS253T1hvdjFsbkdEV3Z5RGJEU0VuRkZpMUhxMktQb3c1OHpqU2RlMEUrcEtqUFMyUktJUXA2TTRiREVZK2kzdUdYUi92Sml4aU9pamVxay9samh3L2tlUGFMM1o2NzRZekhsTUxTRVdjVVZhV2dhbzBLWnR3OHI5c3RKTmFkVlEiLCJtYWMiOiJiZTFlNDJiZjI0NWQ1ODY5MDY2ZDA5NzM5MjI2N2VhMzRmMjBhMjlmOTM0MWNkYjQ4OGI5NDQ5ZGQwNjU2MmZmIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755583768.1272</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755583768</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-900086324\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1130425840 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130425840\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1560044837 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:09:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlhuM1FwZDVOVlVyek1xS0pzSHdhQ2c9PSIsInZhbHVlIjoiUnBUY2RYeFpqNlZ1aDh2elN4UnN2M01zY0l2c2VPOVZFSWxOdnRuYjk3d3gvSGp4bFlnYW5kTUlINkVHQkZDMUg2V1ZWazh6YlVFZHhKOTZTK2xtQkFBL3dVWVB0ZmMyV2FVYjRRVnhOQzJqdzU5ZlF6MmF4NlZlL3hCSlpkVG0iLCJtYWMiOiIyMjI1NGMyNWY1NWQ4NmUyZTRlNjQ5MjdiNzdlZTMwZDJkMzljYWU5ZWY3ODUyNmY1ZjNhMzFhNWIzMjdkYzcwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:09:28 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjZsNzd6ZHE5TE1oSlcwMENFV25sWmc9PSIsInZhbHVlIjoiaktNd3huMi9ZVUYvMjNzT2JHNzg5RUNabGVZSFZPRXI5dVFkc1FmOXdnUjk1M3pTR0RkRm5md3lPWUFJakR4c0FtUjg1Q3JZa0NsVDliblU3akV1M2dmMlpJdG9Cd1hEOXlkb1haUVZCY1FRSldxZUxMYWN1dUdOMzh1aWI5Zm4iLCJtYWMiOiJlZGU5NTVlM2Y2ZTAxOWY2NjllMjU0M2IzZGRkZmY3OTYwMTI4MGQ1NTQzMzY3YzQwYzQ0ZjExYTBlMWJmN2Y3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:09:28 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlhuM1FwZDVOVlVyek1xS0pzSHdhQ2c9PSIsInZhbHVlIjoiUnBUY2RYeFpqNlZ1aDh2elN4UnN2M01zY0l2c2VPOVZFSWxOdnRuYjk3d3gvSGp4bFlnYW5kTUlINkVHQkZDMUg2V1ZWazh6YlVFZHhKOTZTK2xtQkFBL3dVWVB0ZmMyV2FVYjRRVnhOQzJqdzU5ZlF6MmF4NlZlL3hCSlpkVG0iLCJtYWMiOiIyMjI1NGMyNWY1NWQ4NmUyZTRlNjQ5MjdiNzdlZTMwZDJkMzljYWU5ZWY3ODUyNmY1ZjNhMzFhNWIzMjdkYzcwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:09:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjZsNzd6ZHE5TE1oSlcwMENFV25sWmc9PSIsInZhbHVlIjoiaktNd3huMi9ZVUYvMjNzT2JHNzg5RUNabGVZSFZPRXI5dVFkc1FmOXdnUjk1M3pTR0RkRm5md3lPWUFJakR4c0FtUjg1Q3JZa0NsVDliblU3akV1M2dmMlpJdG9Cd1hEOXlkb1haUVZCY1FRSldxZUxMYWN1dUdOMzh1aWI5Zm4iLCJtYWMiOiJlZGU5NTVlM2Y2ZTAxOWY2NjllMjU0M2IzZGRkZmY3OTYwMTI4MGQ1NTQzMzY3YzQwYzQ0ZjExYTBlMWJmN2Y3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:09:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1560044837\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1286976302 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/teammapping/Team%20AD</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755581799</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286976302\", {\"maxDepth\":0})</script>\n"}}