
<?php echo $__env->make('inc.success', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('inc.style', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->startSection('navbar'); ?>
  <?php echo $__env->make('inc.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<?php echo $__env->make('inc.title', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<br>
<?php if($errors->any()): ?>
    <div class="alert alert-danger">
        <ul>
            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li><?php echo e($error); ?></li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
<?php endif; ?>
<script>
    function confirmAction(event) {
        event.preventDefault();
        if (confirm("Are you sure you want to proceed?")) {
            event.target.closest('form').submit();
        }
    }
</script>

<?php if($user->user_type == 1): ?> 
<form action="<?php echo e(route('nfr.store')); ?>" method="post" enctype="multipart/form-data" onsubmit="confirmAction(event)">
    <?php echo csrf_field(); ?>
    <br><br>
        <label for="general_nfr">General Requirement:</label>
        <input type="text" name="general_nfr"  class="form-control" required>
        <br><br>
        <label for="general_nfr_desc">Description:</label>
        <input type="text" name="general_nfr_desc"  class="form-control">
        <br><br>

    <button type="submit" class="btn btn-success">Submit</button>
    <a href="<?php echo e(route('nfr.index')); ?>" class="btn btn-secondary">Cancel</a>
</form>

<?php else: ?>
<div>Only System Admin can access this page.</div>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/nfr/create.blade.php ENDPATH**/ ?>