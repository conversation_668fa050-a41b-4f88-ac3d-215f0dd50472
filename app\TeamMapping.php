<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class TeamMapping extends Model
{
    protected $table = 'teammappings';

    protected $fillable = [
        'username',
        'role_name',
        'team_name',
        'project_id',
        'invitation_status',
        'invitation_token'
    ];

    protected $primaryKey = 'teammapping_id';

    public function user()
    {
        return $this->belongsTo(User::class, 'username', 'username');
    }

    public function role()
    {
        return $this->belongsTo(Role::class, 'role_name', 'role_name');
    }

    public function team()
    {
        return $this->belongsTo(Team::class, 'team_name', 'team_name');
    }

    public function project()
    {
        return $this->belongsTo(Project::class, 'project_id', 'id');
    }

    /**
     * Scope for team-only memberships (no project assignment)
     */
    public function scopeTeamOnly($query)
    {
        return $query->whereNull('project_id');
    }

    /**
     * Scope for project assignments
     */
    public function scopeProjectAssignments($query)
    {
        return $query->whereNotNull('project_id');
    }


    /**
     * Check if this is a team-only membership
     */
    public function isTeamOnlyMembership()
    {
        return is_null($this->project_id);
    }

    public function isProjectManager(): bool
    {
        return $this->role_name === 'Project Manager';
    }

    public static function getProjectsByUser($username)
    {
        $teamNames = self::where('username', $username)->pluck('team_name')->toArray();
        return \App\Project::whereIn('team_name', $teamNames)->get();
    }

    public static function getTeamNamesByProject($proj_name)
    {
        return \App\Project::where('proj_name', $proj_name)->pluck('team_name')->toArray();
    }

    /**
     * Remove duplicate records for the same project_id
     * Keeps the first record and removes subsequent duplicates
     *
     * @param int|null $projectId - specific project ID to clean, or null for all projects
     * @return int - number of duplicates removed
     */
    public static function removeDuplicatesForProject($projectId = null)
    {
        $query = self::query();

        if ($projectId !== null) {
            $query->where('project_id', $projectId);
        } else {
            $query->whereNotNull('project_id');
        }

        // Group by the combination that should be unique
        $duplicates = $query->select('username', 'project_id', 'team_name')
            ->selectRaw('COUNT(*) as count, MIN(teammapping_id) as keep_id')
            ->groupBy('username', 'project_id', 'team_name')
            ->having('count', '>', 1)
            ->get();

        $removedCount = 0;

        foreach ($duplicates as $duplicate) {
            // Find all records for this combination
            $allRecords = self::where('username', $duplicate->username)
                ->where('project_id', $duplicate->project_id)
                ->where('team_name', $duplicate->team_name)
                ->orderBy('teammapping_id')
                ->get();

            // Remove all except the first one (keep the oldest record)
            foreach ($allRecords->skip(1) as $recordToDelete) {
                $recordToDelete->delete();
                $removedCount++;
            }
        }

        return $removedCount;
    }

    /**
     * Get duplicate records for analysis
     *
     * @param int|null $projectId - specific project ID to check, or null for all projects
     * @return \Illuminate\Support\Collection
     */
    public static function getDuplicatesForProject($projectId = null)
    {
        $query = self::query();

        if ($projectId !== null) {
            $query->where('project_id', $projectId);
        } else {
            $query->whereNotNull('project_id');
        }

        return $query->select('username', 'project_id', 'team_name')
            ->selectRaw('COUNT(*) as duplicate_count')
            ->groupBy('username', 'project_id', 'team_name')
            ->having('duplicate_count', '>', 1)
            ->with(['project:id,proj_name'])
            ->get();
    }

    public static function getTeamMembersByProject($proj_name)
    {
        $teamNames = self::getTeamNamesByProject($proj_name);
        $userTeams = [];

        foreach ($teamNames as $teamName) {
            $teamMembers = self::where('team_name', $teamName)->get();
            foreach ($teamMembers as $member) {
                $userTeams[] = [
                    'username' => $member->username,
                    'team_name' => $teamName,
                ];
            }
        }

        return $userTeams;
    }

    public static function getRolesByProject($proj_name)
    {
        $teamNames = self::getTeamNamesByProject($proj_name);

        return self::whereIn('team_name', $teamNames)
            ->distinct()
            ->pluck('role_name')
            ->toArray();
    }

    public static function getTeamMembersAndRolesByProject($proj_name)
    {
        return [
            'userTeams' => self::getTeamMembersByProject($proj_name),
            'roles' => self::getRolesByProject($proj_name),
        ];
    }

    public static function getAllTeamMembers($username)
    {
        // Get all team names the user belongs to
        $teamNames = self::where('username', $username)->pluck('team_name')->toArray();

        // Get all distinct usernames from those teams
        $usernames = self::whereIn('team_name', $teamNames)->pluck('username')->toArray();

        // Get full User records
        return \App\User::whereIn('username', $usernames)->get();
    }

    protected static function boot()
    {
        parent::boot();
    }

    /**
     * Check if this is a project assignment
     */
    public function isProjectAssignment()
    {
        return !is_null($this->project_id);
    }

    /**
     * Get team members for a specific team (team-only memberships)
     */
    public static function getTeamMembers($teamName)
    {
        return static::teamOnly()
            ->where('team_name', $teamName)
            ->where('invitation_status', 'accepted')
            ->get();
    }

    /**
     * Get project assignments for a specific project
     */
    public static function getProjectAssignments($projectId)
    {
        return static::projectAssignments()
            ->where('project_id', $projectId)
            ->get();
    }    /**
     * Get the role context (team vs project specific)
     */
    public function getRoleContext()
    {
        if ($this->isTeamOnlyMembership()) {
            return 'Team Role';
        }
        
        return 'Project Role';
    }

    /**
     * Get available roles for assignment based on context
     */
    public function getAvailableRoles()
    {
        if ($this->isTeamOnlyMembership()) {
            // For team-only memberships, get team roles (global roles)
            return Role::whereNull('project_id')->get();
        } else {
            // For project assignments, get only project-specific roles
            return Role::where('project_id', $this->project_id)->get();
        }
    }

    /**
     * Get all project roles for a specific user
     */
    public static function getUserProjectRoles($username)
    {
        return static::projectAssignments()
            ->where('username', $username)
            ->with(['project:id,proj_name', 'role'])
            ->get()
            ->groupBy('project_id');
    }

    /**
     * Check if user has a specific role in a specific project
     */
    public static function userHasRoleInProject($username, $projectId, $roleName)
    {
        return static::projectAssignments()
            ->where('username', $username)
            ->where('project_id', $projectId)
            ->where('role_name', $roleName)
            ->where('invitation_status', 'accepted')
            ->exists();
    }

    /**
     * Check if user is a team manager for a specific team
     */
    public static function isTeamManager($username, $teamName)
    {
        return static::teamOnly()
            ->where('username', $username)
            ->where('team_name', $teamName)
            ->where('role_name', 'Team Manager')
            ->where('invitation_status', 'accepted')
            ->exists();
    }

    /**
     * Check if user is a team member (any role) for a specific team
     */
    public static function isTeamMember($username, $teamName)
    {
        return static::teamOnly()
            ->where('username', $username)
            ->where('team_name', $teamName)
            ->whereIn('role_name', ['Team Manager', 'Team Member'])
            ->where('invitation_status', 'accepted')
            ->exists();
    }

    /**
     * Get team-specific roles (hardcoded)
     */
    public static function getTeamRoles()
    {
        return [
            'Team Manager' => [
                'description' => 'Can manage team members, roles, and settings',
                'permissions' => ['add_members', 'remove_members', 'change_roles', 'view_team']
            ],
            'Team Member' => [
                'description' => 'Can view team and accept invitations',
                'permissions' => ['view_team', 'accept_invitations']
            ]
        ];
    }

    /**
     * Check if user has permission for a specific action in a team
     */
    public static function userCanPerformAction($username, $teamName, $action)
    {
        $userRole = static::teamOnly()
            ->where('username', $username)
            ->where('team_name', $teamName)
            ->where('invitation_status', 'accepted')
            ->value('role_name');

        if (!$userRole) {
            return false;
        }

        $teamRoles = static::getTeamRoles();
        
        return isset($teamRoles[$userRole]['permissions']) && 
               in_array($action, $teamRoles[$userRole]['permissions']);
    }

    public function showAccessControlList()
    {
        $teams = TeamMapping::select('team_id', 'team_name')->distinct()->get();
        return view('content/pages/access-control-list', compact('teams'));
    }

}
