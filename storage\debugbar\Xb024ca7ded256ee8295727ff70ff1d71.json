{"__meta": {"id": "Xb024ca7ded256ee8295727ff70ff1d71", "datetime": "2025-08-18 23:07:00", "utime": 1755529620.245568, "method": "GET", "uri": "/bugtrack/42", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:06:59] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755529619.505199, "xdebug_link": null, "collector": "log"}, {"message": "[23:06:59] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/bugtrack/42", "message_html": null, "is_string": false, "label": "debug", "time": 1755529619.590399, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755529619.076421, "end": 1755529620.245591, "duration": 1.1691699028015137, "duration_str": "1.17s", "measures": [{"label": "Booting", "start": 1755529619.076421, "relative_start": 0, "end": 1755529619.473546, "relative_end": 1755529619.473546, "duration": 0.3971250057220459, "duration_str": "397ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755529619.473559, "relative_start": 0.3971378803253174, "end": 1755529620.245593, "relative_end": 2.1457672119140625e-06, "duration": 0.7720341682434082, "duration_str": "772ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23997400, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "bugtrack.index (\\resources\\views\\bugtrack\\index.blade.php)", "param_count": 4, "params": ["bugtracks", "statuses", "projectId", "results"], "type": "blade"}, {"name": "layouts.detailsPartialLoad (\\resources\\views\\layouts\\detailsPartialLoad.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "bugtracks", "statuses", "projectId", "results", "__empty_1", "__currentLoopData", "loop"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "bugtracks", "statuses", "projectId", "results", "__empty_1", "__currentLoopData", "loop", "isMenu", "contentNavbar", "containerNav", "isNavbar", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "bugtracks", "statuses", "projectId", "results", "__empty_1", "__currentLoopData", "loop", "isMenu", "contentNavbar", "containerNav", "isNavbar", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "bugtracks", "statuses", "projectId", "results", "__empty_1", "__currentLoopData", "loop", "isMenu", "contentNavbar", "containerNav", "isNavbar", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "bugtracks", "statuses", "projectId", "results", "__empty_1", "__currentLoopData", "loop", "isMenu", "contentNavbar", "containerNav", "isNavbar", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET bugtrack/{projectId}", "middleware": "web", "controller": "App\\Http\\Controllers\\BugtrackingController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "bugtrack.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\BugtrackingController.php&line=22\">\\app\\Http\\Controllers\\BugtrackingController.php:22-113</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00378, "accumulated_duration_str": "3.78ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 15.608}, {"sql": "select * from `bugtrack` where `project_id` = '42' order by `created_at` desc", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BugtrackingController.php", "line": 26}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\app\\Http\\Controllers\\BugtrackingController.php:26", "connection": "sagile", "start_percent": 15.608, "width_percent": 15.079}, {"sql": "select * from `bugtrack` where `project_id` = '42' and `status` != 'Closed' order by `created_at` desc", "type": "query", "params": [], "bindings": ["42", "Closed"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BugtrackingController.php", "line": 32}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\app\\Http\\Controllers\\BugtrackingController.php:32", "connection": "sagile", "start_percent": 30.688, "width_percent": 15.344}, {"sql": "select * from `bugscore` order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BugtrackingController.php", "line": 48}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00148, "duration_str": "1.48ms", "stmt_id": "\\app\\Http\\Controllers\\BugtrackingController.php:48", "connection": "sagile", "start_percent": 46.032, "width_percent": 39.153}, {"sql": "select * from `bugscore` order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BugtrackingController.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "\\app\\Http\\Controllers\\BugtrackingController.php:51", "connection": "sagile", "start_percent": 85.185, "width_percent": 14.815}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/bugtrack/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/bugtrack/42", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1839015358 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1839015358\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-267171099 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-267171099\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1826779959 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InlSdFJMQjM5MlRpbDZNZmJKTjFQZGc9PSIsInZhbHVlIjoiQXVHUlY5NEViRnNhdlg2VXhvUkZiM2pTQzQ2NXpmeW1GZXJRcHY3M0tPaEswUWk1S0lsZFd4WVAyS0RTTUtXUTV4RkFobkhHdEptNDd5eTl2MHVoTXB4N2s1UzdFVmxkM1hqM1VDL0dSOWRwbTQwV3BueWZNK0M1OEZpb0FPUmciLCJtYWMiOiI3NWUwZWY1YjYyOWFjNGFmZTRjY2MxNTc5NGU0OTVlZTg1ZTRlOTk3ZTQ3NGRhODJhMTE0OTBjM2IyOWFjN2QxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjFGaTkxOWFGM2t6akhucGNYV2U2T1E9PSIsInZhbHVlIjoiWlNlOTFOdUd3ZUNYYjdOSnh6ZmIwbjZLNy9XdUg5Y3g4WXI1KzFsR0xpdm13MGg1RndHa0V0RGFBTnJiNG9wQVhzc2VlL3BOam1sSTRnN1FGcTgrd0NqUS9PbnpTcEhFWjc0SlIzWkR3eE1uenNOdk9IV0IzMHhvZllra2N6Y3AiLCJtYWMiOiI5ZDEyZDFhMDFiNmRjNmU4ZWM4ZjNiNTIwZWU1NDkwMGUzZDY5NWI1MjNlYThiZGVmZWU3M2VhYTAwOGVkNGQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1826779959\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1819719432 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59197</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/bugtrack/42</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/bugtrack/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/index.php/bugtrack/42</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InlSdFJMQjM5MlRpbDZNZmJKTjFQZGc9PSIsInZhbHVlIjoiQXVHUlY5NEViRnNhdlg2VXhvUkZiM2pTQzQ2NXpmeW1GZXJRcHY3M0tPaEswUWk1S0lsZFd4WVAyS0RTTUtXUTV4RkFobkhHdEptNDd5eTl2MHVoTXB4N2s1UzdFVmxkM1hqM1VDL0dSOWRwbTQwV3BueWZNK0M1OEZpb0FPUmciLCJtYWMiOiI3NWUwZWY1YjYyOWFjNGFmZTRjY2MxNTc5NGU0OTVlZTg1ZTRlOTk3ZTQ3NGRhODJhMTE0OTBjM2IyOWFjN2QxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjFGaTkxOWFGM2t6akhucGNYV2U2T1E9PSIsInZhbHVlIjoiWlNlOTFOdUd3ZUNYYjdOSnh6ZmIwbjZLNy9XdUg5Y3g4WXI1KzFsR0xpdm13MGg1RndHa0V0RGFBTnJiNG9wQVhzc2VlL3BOam1sSTRnN1FGcTgrd0NqUS9PbnpTcEhFWjc0SlIzWkR3eE1uenNOdk9IV0IzMHhvZllra2N6Y3AiLCJtYWMiOiI5ZDEyZDFhMDFiNmRjNmU4ZWM4ZjNiNTIwZWU1NDkwMGUzZDY5NWI1MjNlYThiZGVmZWU3M2VhYTAwOGVkNGQ4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755529619.0764</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755529619</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1819719432\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1295234454 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1295234454\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-24961353 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:06:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImhROUtSYWYyTTdsQzJYQ0xMSlR6REE9PSIsInZhbHVlIjoiSkJZY1F4bm41Vm8rK3pweFBENjNFNWIyZHQ2T2JBK3d0MFhyNmdpOHFvVzg4eWRkblpBR25PNzZSZUlMMlNVTXFJTVVKQjRmV1hEcDlzOFFGb3ovcDlpZW9tTW9iMklqRTVMbUFOQ0JWaUZHMkttK0M2WEF3SUdINkJoc3N4bUQiLCJtYWMiOiIzODQ3MTc2Yzc0ZDIwM2I4OGM5ZmQyMzFjN2I0NTFkNzk2NjAzMGJjZjU5MDdhMjAwMWQ2MjVlMjA4YTAzOGY0IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:07:00 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkhmeGVkNU9DdmNyV0lQNG16Vk5scGc9PSIsInZhbHVlIjoiNVhUYW0vVjZPZnNtNVpOam5oMVpRTlQ5VUM5Wnk0cnl4eEEzYkFpQ3EzSk1zZ25kWTY1eGI2N094eURza0RaeG93TnFtMk1WWmNnTmV0QWxHeW5INjh6SDVMOUZ5emVRcFdGOUlqZDFXcnBMQ3lqMHFFWUNkaG5RdlR1M0VZL1kiLCJtYWMiOiJkYTMwZDYzNmU4ODQwOWEyMGY4MGQ2MzI3MGEyMzBiZWFhY2I5Yzk4NjdjMTdkZDA4MmU5Nzk5YzMxNWU2ZWY4IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:07:00 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImhROUtSYWYyTTdsQzJYQ0xMSlR6REE9PSIsInZhbHVlIjoiSkJZY1F4bm41Vm8rK3pweFBENjNFNWIyZHQ2T2JBK3d0MFhyNmdpOHFvVzg4eWRkblpBR25PNzZSZUlMMlNVTXFJTVVKQjRmV1hEcDlzOFFGb3ovcDlpZW9tTW9iMklqRTVMbUFOQ0JWaUZHMkttK0M2WEF3SUdINkJoc3N4bUQiLCJtYWMiOiIzODQ3MTc2Yzc0ZDIwM2I4OGM5ZmQyMzFjN2I0NTFkNzk2NjAzMGJjZjU5MDdhMjAwMWQ2MjVlMjA4YTAzOGY0IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:07:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkhmeGVkNU9DdmNyV0lQNG16Vk5scGc9PSIsInZhbHVlIjoiNVhUYW0vVjZPZnNtNVpOam5oMVpRTlQ5VUM5Wnk0cnl4eEEzYkFpQ3EzSk1zZ25kWTY1eGI2N094eURza0RaeG93TnFtMk1WWmNnTmV0QWxHeW5INjh6SDVMOUZ5emVRcFdGOUlqZDFXcnBMQ3lqMHFFWUNkaG5RdlR1M0VZL1kiLCJtYWMiOiJkYTMwZDYzNmU4ODQwOWEyMGY4MGQ2MzI3MGEyMzBiZWFhY2I5Yzk4NjdjMTdkZDA4MmU5Nzk5YzMxNWU2ZWY4IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:07:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-24961353\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1716370040 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/bugtrack/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1716370040\", {\"maxDepth\":0})</script>\n"}}