<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Roles</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
            margin: 0;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .table {
            background-color: white;
            border-radius: 0.25rem;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .button-container {
            display: flex;
            gap: 0.5rem;
        }
        .alert {
            margin-bottom: 1rem;
        }
        /* Notification styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            font-weight: 500;
            z-index: 9999;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            animation: fadeIn 0.3s ease-out;
            max-width: 350px;
        }
        
        .notification-success {
            background-color: #28a745;
            border-left: 5px solid #1e7e34;
        }
        
        .notification-error {
            background-color: #dc3545;
            border-left: 5px solid #bd2130;
        }
        
        @keyframes  fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo e(session('success')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        <?php
        // Get project ID from route parameter
            $projectId = request()->route('project_id');
            $currentProject = null;
            
            if ($projectId && isset($pro)) {
                $currentProject = $pro->firstWhere('id', $projectId);
            } elseif (isset($pro) && count($pro) > 0) {
                $currentProject = $pro->first();
                $projectId = $currentProject->id;
            }
        ?>

        <?php if($currentProject && auth()->user()->can('add_roles', $currentProject)): ?>
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Project Roles</h4>
                    <a href="<?php echo e(route('roles.create', ['project_id' => $projectId])); ?>" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i> Add Role
                    </a>
                </div>
        <?php else: ?>
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Project Roles</h4>
                </div>
        <?php endif; ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Role</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($role->role_name); ?></td>
                                    <td>
                                        <div class="button-container">
                                            <?php if($currentProject && auth()->user()->can('edit_roles', $currentProject)): ?>
                                            <a href="<?php echo e(route('roles.edit', [$role,'project_id' => $projectId])); ?>" class="btn btn-primary btn-sm">
                                                <i class="fas fa-edit me-1"></i> Edit
                                            </a>
                                            <?php endif; ?>

                                            <?php if($currentProject && auth()->user()->can('delete_roles', $currentProject)): ?>
                                            <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo e($role->id); ?>">
                                                <i class="fas fa-trash me-1"></i> Delete
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Team Member Roles Section -->
        <div class="card mt-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Team Member Roles</h4>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Username</th>
                                <th>Current Role</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $teamMembers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $teammember): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <?php echo e($teammember->username); ?>

                                    </td>
                                    <td class="role-cell">
                                        <?php if($currentProject && auth()->user()->can('updateUserRole_roles', $currentProject)): ?>
                                            <select name="role_name" class="form-select form-select-sm role-select" data-teammapping-id="<?php echo e($teammember->teammapping_id); ?>">
                                                <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($role->role_name); ?>" <?php echo e($teammember->role_name == $role->role_name ? 'selected' : ''); ?>>
                                                        <?php echo e($role->role_name); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo e($teammember->role_name ?? 'No role assigned'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="3">No active team members found</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true" data-bs-backdrop="false">
            <div class="modal-dialog shadow-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        Are you sure you want to delete this role? This action cannot be undone.
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <form action="<?php echo e(route('roles.destroy', $role)); ?>" method="post">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-danger">Delete Role</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css"></script>
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Select all role dropdowns
        const roleSelects = document.querySelectorAll('.role-select');
        
        // Function to show notifications
        function showNotification(message, type) {
            // Remove any existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => {
                notification.remove();
            });
            
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            
            // Add to body
            document.body.appendChild(notification);
            
            // Auto remove after 3 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateY(-10px)';
                notification.style.transition = 'all 0.3s ease-out';
                
                // Complete removal after animation
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }
        
        // Add change event listener to each dropdown
        roleSelects.forEach(select => {
            select.addEventListener('change', function() {
                const teammappingId = this.getAttribute('data-teammapping-id');
                const roleName = this.value;
                const originalValue = this.getAttribute('data-original-value') || '';
                
                // Skip AJAX call if the role hasn't changed
                if (originalValue === roleName) {
                    return;
                }
                
                // Store current selection as original for next comparison
                this.setAttribute('data-original-value', roleName);
                
                // Create form data
                const formData = new FormData();
                formData.append('role_name', roleName);
                formData.append('_token', '<?php echo e(csrf_token()); ?>');
                
                // Visual feedback - disable select during AJAX
                this.disabled = true;
                
                // Send AJAX request
                fetch(`<?php echo e(url('roles')); ?>/${teammappingId}/updateTeamMemberRole`, {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    // Re-enable select
                    this.disabled = false;
                    
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.message || 'Failed to update role');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        showNotification(data.message, 'success');
                    } else {
                        showNotification(data.message || 'An error occurred', 'error');
                    }
                })
                .catch(error => {
                    // Re-enable select if still disabled
                    this.disabled = false;
                    console.error('Error updating role:', error);
                    showNotification(error.message || 'Failed to update role. Please try again.', 'error');
                });
            });
            
            // Set initial data-original-value attribute
            select.setAttribute('data-original-value', select.value);
        });
    });
    </script>

</body>
</html>


<?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/role/index.blade.php ENDPATH**/ ?>