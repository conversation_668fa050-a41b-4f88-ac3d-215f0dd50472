{"__meta": {"id": "X4600ae7f49161644690d16b3d3414dc6", "datetime": "2025-08-19 13:31:42", "utime": 1755581502.938183, "method": "POST", "uri": "/projects", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[13:31:41] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755581501.217512, "xdebug_link": null, "collector": "log"}, {"message": "[13:31:41] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/projects", "message_html": null, "is_string": false, "label": "debug", "time": 1755581501.277931, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755581500.904689, "end": 1755581502.938219, "duration": 2.0335299968719482, "duration_str": "2.03s", "measures": [{"label": "Booting", "start": 1755581500.904689, "relative_start": 0, "end": 1755581501.197632, "relative_end": 1755581501.197632, "duration": 0.29294300079345703, "duration_str": "293ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755581501.197642, "relative_start": 0.2929530143737793, "end": 1755581502.938222, "relative_end": 2.86102294921875e-06, "duration": 1.7405798435211182, "duration_str": "1.74s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25517024, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 9, "templates": [{"name": "project.newIndex (\\resources\\views\\project\\newIndex.blade.php)", "param_count": 1, "params": ["projects"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 9, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "POST projects", "middleware": "web", "controller": "App\\Http\\Controllers\\ProjectController@store", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "projects.store", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProjectController.php&line=75\">\\app\\Http\\Controllers\\ProjectController.php:75-220</a>"}, "queries": {"nb_statements": 98, "nb_failed_statements": 0, "accumulated_duration": 0.09033000000000002, "accumulated_duration_str": "90.33ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 0.52}, {"sql": "select count(*) as aggregate from `projects` where `proj_name` = 'Food Ordering System'", "type": "query", "params": [], "bindings": ["Food Ordering System"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 447}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "sagile", "start_percent": 0.52, "width_percent": 0.443}, {"sql": "insert into `projects` (`proj_name`, `proj_desc`, `start_date`, `end_date`, `team_name`, `updated_at`, `created_at`) values ('Food Ordering System', 'this is a web-based food oerdering system', '2025-08-19', '2025-12-31', 'Team 888', '2025-08-19 13:31:41', '2025-08-19 13:31:41')", "type": "query", "params": [], "bindings": ["Food Ordering System", "this is a web-based food oerdering system", "2025-08-19", "2025-12-31", "Team 888", "2025-08-19 13:31:41", "2025-08-19 13:31:41"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 98}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00123, "duration_str": "1.23ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:98", "connection": "sagile", "start_percent": 0.963, "width_percent": 1.362}, {"sql": "insert into `statuses` (`title`, `slug`, `order`, `project_id`) values ('Backlog', 'backlog', 1, 43)", "type": "query", "params": [], "bindings": ["Backlog", "backlog", "1", "43"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Project.php", "line": 22}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 98}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Project.php:22", "connection": "sagile", "start_percent": 2.325, "width_percent": 1.096}, {"sql": "insert into `statuses` (`title`, `slug`, `order`, `project_id`) values ('Up Next', 'up-next', 2, 43)", "type": "query", "params": [], "bindings": ["Up Next", "up-next", "2", "43"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Project.php", "line": 22}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 98}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "\\app\\Project.php:22", "connection": "sagile", "start_percent": 3.421, "width_percent": 1.074}, {"sql": "insert into `statuses` (`title`, `slug`, `order`, `project_id`) values ('In Progress', 'in-progress', 3, 43)", "type": "query", "params": [], "bindings": ["In Progress", "in-progress", "3", "43"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Project.php", "line": 22}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 98}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "\\app\\Project.php:22", "connection": "sagile", "start_percent": 4.495, "width_percent": 1.074}, {"sql": "insert into `statuses` (`title`, `slug`, `order`, `project_id`) values ('Done', 'done', 4, 43)", "type": "query", "params": [], "bindings": ["Done", "done", "4", "43"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Project.php", "line": 22}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 98}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Project.php:22", "connection": "sagile", "start_percent": 5.568, "width_percent": 1.041}, {"sql": "insert into `roles` (`role_name`, `project_id`, `updated_at`, `created_at`) values ('Project Manager', 43, '2025-08-19 13:31:41', '2025-08-19 13:31:41')", "type": "query", "params": [], "bindings": ["Project Manager", "43", "2025-08-19 13:31:41", "2025-08-19 13:31:41"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 104}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:104", "connection": "sagile", "start_percent": 6.609, "width_percent": 1.074}, {"sql": "insert into `roles` (`role_name`, `project_id`, `updated_at`, `created_at`) values ('Developer', 43, '2025-08-19 13:31:41', '2025-08-19 13:31:41')", "type": "query", "params": [], "bindings": ["Developer", "43", "2025-08-19 13:31:41", "2025-08-19 13:31:41"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 110}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:110", "connection": "sagile", "start_percent": 7.683, "width_percent": 1.007}, {"sql": "select `id` from `permission` where `key` in ('view_roles', 'addLane_kanban', 'addTask_kanban', 'editLane_kanban', 'deleteLane_kanban', 'deleteTask_kanban', 'addComment_kanban', 'updateTaskStatus_kanban', 'editTask_kanban', 'addUserStory_backlog', 'beginSprint_backlog', 'addToSprint_backlog', 'endSprint_backlog', 'add_userstory', 'edit_userstory', 'delete_userstory', 'editStatus_userstory', 'view_task', 'add_task', 'edit_task', 'delete_task', 'viewCalendar_task', 'viewComments_task', 'add_roles', 'edit_roles', 'delete_roles', 'updateUserRole_roles', 'add_status', 'edit_status', 'delete_status', 'edit_details', 'delete_details', 'share_details', 'view_sprintArchive', 'viewKanbanArchive_sprintArchive', 'viewBurndownArchive_sprintArchive', 'view_kanban', 'view_burndown', 'view_backlog', 'view_userstory', 'view_forum', 'view_bugtracking', 'view_status', 'view_details')", "type": "query", "params": [], "bindings": ["view_roles", "addLane_kanban", "addTask_kanban", "editLane_kanban", "deleteLane_kanban", "deleteTask_kanban", "addComment_kanban", "updateTaskStatus_kanban", "editTask_kanban", "addUserStory_backlog", "beginSprint_backlog", "addToSprint_backlog", "endSprint_backlog", "add_userstory", "edit_userstory", "delete_userstory", "editStatus_userstory", "view_task", "add_task", "edit_task", "delete_task", "viewCalendar_task", "viewComments_task", "add_roles", "edit_roles", "delete_roles", "updateUserRole_roles", "add_status", "edit_status", "delete_status", "edit_details", "delete_details", "share_details", "view_sprintArchive", "viewKanbanArchive_sprintArchive", "viewBurndownArchive_sprintArchive", "view_kanban", "view_burndown", "view_backlog", "view_userstory", "view_forum", "view_bugtracking", "view_status", "view_details"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 148}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:148", "connection": "sagile", "start_percent": 8.69, "width_percent": 0.576}, {"sql": "select `id` from `permission` where `key` in ('view_roles', 'addLane_kanban', 'addTask_kanban', 'editLane_kanban', 'deleteLane_kanban', 'deleteTask_kanban', 'addComment_kanban', 'updateTaskStatus_kanban', 'editTask_kanban', 'addUserStory_backlog', 'beginSprint_backlog', 'addToSprint_backlog', 'endSprint_backlog', 'add_userstory', 'edit_userstory', 'delete_userstory', 'editStatus_userstory', 'view_task', 'add_task', 'edit_task', 'delete_task', 'viewCalendar_task', 'viewComments_task', 'view_sprintArchive', 'viewKanbanArchive_sprintArchive', 'viewBurndownArchive_sprintArchive', 'view_kanban', 'view_burndown', 'view_backlog', 'view_userstory', 'view_forum', 'view_bugtracking', 'view_status', 'view_details')", "type": "query", "params": [], "bindings": ["view_roles", "addLane_kanban", "addTask_kanban", "editLane_kanban", "deleteLane_kanban", "deleteTask_kanban", "addComment_kanban", "updateTaskStatus_kanban", "editTask_kanban", "addUserStory_backlog", "beginSprint_backlog", "addToSprint_backlog", "endSprint_backlog", "add_userstory", "edit_userstory", "delete_userstory", "editStatus_userstory", "view_task", "add_task", "edit_task", "delete_task", "viewCalendar_task", "viewComments_task", "view_sprintArchive", "viewKanbanArchive_sprintArchive", "viewBurndownArchive_sprintArchive", "view_kanban", "view_burndown", "view_backlog", "view_userstory", "view_forum", "view_bugtracking", "view_status", "view_details"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 151}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:151", "connection": "sagile", "start_percent": 9.266, "width_percent": 0.498}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (1, 33)", "type": "query", "params": [], "bindings": ["1", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 9.764, "width_percent": 1.107}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (2, 33)", "type": "query", "params": [], "bindings": ["2", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011200000000000001, "duration_str": "1.12ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 10.871, "width_percent": 1.24}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (3, 33)", "type": "query", "params": [], "bindings": ["3", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011200000000000001, "duration_str": "1.12ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 12.111, "width_percent": 1.24}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (4, 33)", "type": "query", "params": [], "bindings": ["4", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 13.351, "width_percent": 1.14}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (5, 33)", "type": "query", "params": [], "bindings": ["5", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011200000000000001, "duration_str": "1.12ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 14.491, "width_percent": 1.24}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (6, 33)", "type": "query", "params": [], "bindings": ["6", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 15.731, "width_percent": 1.118}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (7, 33)", "type": "query", "params": [], "bindings": ["7", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 16.849, "width_percent": 1.096}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (8, 33)", "type": "query", "params": [], "bindings": ["8", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 17.945, "width_percent": 1.229}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (9, 33)", "type": "query", "params": [], "bindings": ["9", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 19.174, "width_percent": 1.118}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (10, 33)", "type": "query", "params": [], "bindings": ["10", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 20.292, "width_percent": 1.041}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (11, 33)", "type": "query", "params": [], "bindings": ["11", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 21.333, "width_percent": 1.107}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (12, 33)", "type": "query", "params": [], "bindings": ["12", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 22.44, "width_percent": 0.886}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (13, 33)", "type": "query", "params": [], "bindings": ["13", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0015400000000000001, "duration_str": "1.54ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 23.326, "width_percent": 1.705}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (14, 33)", "type": "query", "params": [], "bindings": ["14", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 25.03, "width_percent": 0.919}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (15, 33)", "type": "query", "params": [], "bindings": ["15", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 25.949, "width_percent": 0.841}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (16, 33)", "type": "query", "params": [], "bindings": ["16", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 26.791, "width_percent": 0.908}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (17, 33)", "type": "query", "params": [], "bindings": ["17", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00227, "duration_str": "2.27ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 27.698, "width_percent": 2.513}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (18, 33)", "type": "query", "params": [], "bindings": ["18", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 30.211, "width_percent": 0.875}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (19, 33)", "type": "query", "params": [], "bindings": ["19", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 31.086, "width_percent": 1.096}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (20, 33)", "type": "query", "params": [], "bindings": ["20", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 32.182, "width_percent": 1.207}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (21, 33)", "type": "query", "params": [], "bindings": ["21", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 33.389, "width_percent": 1.096}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (22, 33)", "type": "query", "params": [], "bindings": ["22", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 34.485, "width_percent": 1.007}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (23, 33)", "type": "query", "params": [], "bindings": ["23", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 35.492, "width_percent": 1.074}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (24, 33)", "type": "query", "params": [], "bindings": ["24", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 36.566, "width_percent": 1.14}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (25, 33)", "type": "query", "params": [], "bindings": ["25", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 37.706, "width_percent": 0.974}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (26, 33)", "type": "query", "params": [], "bindings": ["26", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 38.68, "width_percent": 1.096}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (27, 33)", "type": "query", "params": [], "bindings": ["27", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 39.776, "width_percent": 1.007}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (28, 33)", "type": "query", "params": [], "bindings": ["28", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 40.784, "width_percent": 1.118}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (29, 33)", "type": "query", "params": [], "bindings": ["29", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 41.902, "width_percent": 1.007}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (30, 33)", "type": "query", "params": [], "bindings": ["30", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 42.909, "width_percent": 1.107}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (31, 33)", "type": "query", "params": [], "bindings": ["31", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 44.016, "width_percent": 0.908}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (32, 33)", "type": "query", "params": [], "bindings": ["32", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 44.924, "width_percent": 0.852}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (33, 33)", "type": "query", "params": [], "bindings": ["33", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0012900000000000001, "duration_str": "1.29ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 45.777, "width_percent": 1.428}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (34, 33)", "type": "query", "params": [], "bindings": ["34", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 47.205, "width_percent": 0.897}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (35, 33)", "type": "query", "params": [], "bindings": ["35", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 48.101, "width_percent": 0.808}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (36, 33)", "type": "query", "params": [], "bindings": ["36", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 48.91, "width_percent": 1.018}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (37, 33)", "type": "query", "params": [], "bindings": ["37", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 49.928, "width_percent": 0.952}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (38, 33)", "type": "query", "params": [], "bindings": ["38", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 50.88, "width_percent": 0.886}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (39, 33)", "type": "query", "params": [], "bindings": ["39", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00095, "duration_str": "950μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 51.766, "width_percent": 1.052}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (40, 33)", "type": "query", "params": [], "bindings": ["40", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 52.817, "width_percent": 0.864}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (41, 33)", "type": "query", "params": [], "bindings": ["41", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0009599999999999999, "duration_str": "960μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 53.681, "width_percent": 1.063}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (42, 33)", "type": "query", "params": [], "bindings": ["42", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 54.744, "width_percent": 0.974}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (43, 33)", "type": "query", "params": [], "bindings": ["43", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 55.718, "width_percent": 1.007}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (44, 33)", "type": "query", "params": [], "bindings": ["44", "33"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 56.725, "width_percent": 0.963}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (1, 34)", "type": "query", "params": [], "bindings": ["1", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 57.688, "width_percent": 0.908}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (2, 34)", "type": "query", "params": [], "bindings": ["2", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 58.596, "width_percent": 0.93}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (3, 34)", "type": "query", "params": [], "bindings": ["3", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 59.526, "width_percent": 1.041}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (4, 34)", "type": "query", "params": [], "bindings": ["4", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 60.567, "width_percent": 0.897}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (5, 34)", "type": "query", "params": [], "bindings": ["5", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 61.464, "width_percent": 1.096}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (6, 34)", "type": "query", "params": [], "bindings": ["6", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 62.56, "width_percent": 0.963}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (7, 34)", "type": "query", "params": [], "bindings": ["7", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 63.523, "width_percent": 0.875}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (8, 34)", "type": "query", "params": [], "bindings": ["8", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 64.397, "width_percent": 0.952}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (9, 34)", "type": "query", "params": [], "bindings": ["9", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00085, "duration_str": "850μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 65.349, "width_percent": 0.941}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (10, 34)", "type": "query", "params": [], "bindings": ["10", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00102, "duration_str": "1.02ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 66.29, "width_percent": 1.129}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (11, 34)", "type": "query", "params": [], "bindings": ["11", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 67.419, "width_percent": 1.506}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (12, 34)", "type": "query", "params": [], "bindings": ["12", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00113, "duration_str": "1.13ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 68.925, "width_percent": 1.251}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (13, 34)", "type": "query", "params": [], "bindings": ["13", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008900000000000001, "duration_str": "890μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 70.176, "width_percent": 0.985}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (14, 34)", "type": "query", "params": [], "bindings": ["14", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 71.161, "width_percent": 1.173}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (15, 34)", "type": "query", "params": [], "bindings": ["15", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00107, "duration_str": "1.07ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 72.335, "width_percent": 1.185}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (16, 34)", "type": "query", "params": [], "bindings": ["16", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0018700000000000001, "duration_str": "1.87ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 73.519, "width_percent": 2.07}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (17, 34)", "type": "query", "params": [], "bindings": ["17", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 75.59, "width_percent": 0.908}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (18, 34)", "type": "query", "params": [], "bindings": ["18", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 76.497, "width_percent": 0.908}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (19, 34)", "type": "query", "params": [], "bindings": ["19", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 77.405, "width_percent": 0.996}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (20, 34)", "type": "query", "params": [], "bindings": ["20", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008900000000000001, "duration_str": "890μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 78.401, "width_percent": 0.985}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (21, 34)", "type": "query", "params": [], "bindings": ["21", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 79.387, "width_percent": 1.03}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (22, 34)", "type": "query", "params": [], "bindings": ["22", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 80.416, "width_percent": 1.074}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (23, 34)", "type": "query", "params": [], "bindings": ["23", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 81.49, "width_percent": 0.83}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (24, 34)", "type": "query", "params": [], "bindings": ["24", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 82.32, "width_percent": 0.963}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (25, 34)", "type": "query", "params": [], "bindings": ["25", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 83.284, "width_percent": 0.974}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (26, 34)", "type": "query", "params": [], "bindings": ["26", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 84.258, "width_percent": 0.93}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (27, 34)", "type": "query", "params": [], "bindings": ["27", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 85.188, "width_percent": 0.974}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (28, 34)", "type": "query", "params": [], "bindings": ["28", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 86.162, "width_percent": 0.797}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (37, 34)", "type": "query", "params": [], "bindings": ["37", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 86.959, "width_percent": 0.974}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (38, 34)", "type": "query", "params": [], "bindings": ["38", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00211, "duration_str": "2.11ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 87.933, "width_percent": 2.336}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (39, 34)", "type": "query", "params": [], "bindings": ["39", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 90.269, "width_percent": 0.875}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (40, 34)", "type": "query", "params": [], "bindings": ["40", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 91.144, "width_percent": 0.952}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (41, 34)", "type": "query", "params": [], "bindings": ["41", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 92.096, "width_percent": 0.83}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (43, 34)", "type": "query", "params": [], "bindings": ["43", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 92.926, "width_percent": 1.041}, {"sql": "select * from `teammappings` where `team_name` = 'Team 888'", "type": "query", "params": [], "bindings": ["Team 888"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 171}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:171", "connection": "sagile", "start_percent": 93.967, "width_percent": 0.476}, {"sql": "insert into `teammappings` (`username`, `team_name`, `project_id`, `invitation_status`, `role_name`, `updated_at`, `created_at`) values ('ivlyn', 'Team 888', 43, 'accepted', 'Project Manager', '2025-08-19 13:31:42', '2025-08-19 13:31:42')", "type": "query", "params": [], "bindings": ["ivlyn", "Team 888", "43", "accepted", "Project Manager", "2025-08-19 13:31:42", "2025-08-19 13:31:42"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 190}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00074, "duration_str": "740μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:190", "connection": "sagile", "start_percent": 94.443, "width_percent": 0.819}, {"sql": "insert into `teammappings` (`username`, `team_name`, `project_id`, `invitation_status`, `role_name`, `updated_at`, `created_at`) values ('tay', 'Team 888', 43, 'accepted', 'Developer', '2025-08-19 13:31:42', '2025-08-19 13:31:42')", "type": "query", "params": [], "bindings": ["tay", "Team 888", "43", "accepted", "Developer", "2025-08-19 13:31:42", "2025-08-19 13:31:42"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 190}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:190", "connection": "sagile", "start_percent": 95.262, "width_percent": 0.952}, {"sql": "select * from `teammappings` where `project_id` = 43", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 341}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 194}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:341", "connection": "sagile", "start_percent": 96.214, "width_percent": 0.365}, {"sql": "select * from `users` where `username` = 'ivlyn' limit 1", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 344}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 194}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:344", "connection": "sagile", "start_percent": 96.579, "width_percent": 0.443}, {"sql": "select * from `users` where `username` = 'tay' limit 1", "type": "query", "params": [], "bindings": ["tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 344}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 194}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:344", "connection": "sagile", "start_percent": 97.022, "width_percent": 0.509}, {"sql": "insert into `project_user` (`project_id`, `user_id`, `project_access`, `sprint_access`, `forum_access`, `userstory_access`, `secfeature_access`, `updated_at`, `created_at`) values (43, 30, 1, 1, 1, 1, 1, '2025-08-19 13:31:42', '2025-08-19 13:31:42')", "type": "query", "params": [], "bindings": ["43", "30", "1", "1", "1", "1", "1", "2025-08-19 13:31:42", "2025-08-19 13:31:42"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 205}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:205", "connection": "sagile", "start_percent": 97.531, "width_percent": 0.952}, {"sql": "select `project_id` from `teammappings` where `username` = 'iv<PERSON>' and `project_id` is not null", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 212}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:212", "connection": "sagile", "start_percent": 98.483, "width_percent": 0.598}, {"sql": "select * from `projects` where `id` in (42, 43)", "type": "query", "params": [], "bindings": ["42", "43"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 216}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:216", "connection": "sagile", "start_percent": 99.081, "width_percent": 0.919}]}, "models": {"data": {"App\\Project": 1, "App\\TeamMapping": 4, "App\\Permission": 78, "App\\User": 3}, "count": 86}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/projects/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/projects", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-373775813 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-373775813\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1392025 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n  \"<span class=sf-dump-key>proj_desc</span>\" => \"<span class=sf-dump-str title=\"41 characters\">this is a web-based food oerdering system</span>\"\n  \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-19</span>\"\n  \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-12-31</span>\"\n  \"<span class=sf-dump-key>team</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Team 888</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1392025\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">741</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryAfHDHUCP2GACjUF4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/projects/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ilc0TGZWR2lDY2FDY29pdTZzeVZaWGc9PSIsInZhbHVlIjoibWNZN3I5TkZIMWwwWHdpVW00eU5LeUIwcWZ1SFNVRG1TZ0N2QkwySnVNYVVKWWxtU0prbVV4KzRHdmdQaUg1RzhYeTFnTmlYY05OSFJoK2NtdjNzTHVCMm9qN2M2VE9sUm1FZWRTdk50Ym9KcldOdHdISFpRcm9wblpzb0VjYzUiLCJtYWMiOiJlN2RkNzJjOTI0Njc0NzI3M2QxYjVjNmZmYjAyMjYyNGZkMjFjNGZhZWMwNGM0MzAwMzE2NGM5Mjg3YmQwYTc3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im5jM1l3d2pmVElRMldldVFvb25aMWc9PSIsInZhbHVlIjoibEhET2FFZlhCOTExWTJzaHI2ekMydnlBSFhVNnBwc2ZBdzZEY1QvSG8wZlR1Z0NXUzJoN1JOc3ZjWnc5NDZEUkNLVTVQSHVmLzR4NEZncEtkazBqTzltZEpXYmhhdk1MVDB1dCswS3BkQ0dDRHdUbXJodzBzMnVjbVIwTGljVHMiLCJtYWMiOiJjN2U4ZjUxMDAwZjdlMGEzZTlkMmU3NmEzMTZmZDk4ZmNkMjdmMDE2Yzg1MWM5ZDU5ZTc4YjQwM2Y2ZTNhMDcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1915226421 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60727</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/projects</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/projects</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/index.php/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">741</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">741</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryAfHDHUCP2GACjUF4</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryAfHDHUCP2GACjUF4</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/projects/create</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ilc0TGZWR2lDY2FDY29pdTZzeVZaWGc9PSIsInZhbHVlIjoibWNZN3I5TkZIMWwwWHdpVW00eU5LeUIwcWZ1SFNVRG1TZ0N2QkwySnVNYVVKWWxtU0prbVV4KzRHdmdQaUg1RzhYeTFnTmlYY05OSFJoK2NtdjNzTHVCMm9qN2M2VE9sUm1FZWRTdk50Ym9KcldOdHdISFpRcm9wblpzb0VjYzUiLCJtYWMiOiJlN2RkNzJjOTI0Njc0NzI3M2QxYjVjNmZmYjAyMjYyNGZkMjFjNGZhZWMwNGM0MzAwMzE2NGM5Mjg3YmQwYTc3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im5jM1l3d2pmVElRMldldVFvb25aMWc9PSIsInZhbHVlIjoibEhET2FFZlhCOTExWTJzaHI2ekMydnlBSFhVNnBwc2ZBdzZEY1QvSG8wZlR1Z0NXUzJoN1JOc3ZjWnc5NDZEUkNLVTVQSHVmLzR4NEZncEtkazBqTzltZEpXYmhhdk1MVDB1dCswS3BkQ0dDRHdUbXJodzBzMnVjbVIwTGljVHMiLCJtYWMiOiJjN2U4ZjUxMDAwZjdlMGEzZTlkMmU3NmEzMTZmZDk4ZmNkMjdmMDE2Yzg1MWM5ZDU5ZTc4YjQwM2Y2ZTNhMDcyIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755581500.9047</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755581500</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1915226421\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GKxRuD042xMQ4k3v5EHCZLsF1sfAXXnsz4rkTCog</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1231141775 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:31:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkFFOFpmVXY1Y3RSZWR0b21hN0w0c3c9PSIsInZhbHVlIjoiMG8rcjJwWlV5dnBtUzM3V2RpTWMzY2w5MnZXUERQbnhjMkpNV2pDanM4cFFMYW9uRS81WXZoajJlc2VybzJ0NVdscU96MDVETFZZQTVnckR6N0JENkorc3lyOXNKUUFyaGIzalJ6UDJ0bjEvaVgrT2ErZHMwQnhMUmd6dmpjc0EiLCJtYWMiOiJjN2JkOGQ4ZmM4MWUzMzRjNWRhZmI1NDZkZjZhZGJlMzdiMTc1NTNlZmZlYTJiMTliNTk5MTU3Y2UyMDI2NjE5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:31:42 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImVFQ3RLbFpSSFB6Vi84UVF3Ymg2dUE9PSIsInZhbHVlIjoiQTFVQmpnekRPSlZPQVQwcmxoWnZuUTBJNyt6K29jb0ZpU0NmRTNFVUZoZ1RCUGZGTGUxREVBU2d2U0gweWZBTmNpWG9KTnQwWGw3eGU0L2RkUEMrU2d4WFYrWjlHRU5nd1Uzd2pMdWxKQm5ZZmRqTVFYSzZyYnlqdUtkSnNETGoiLCJtYWMiOiI4M2Y3NmNkYWEyNWZjMThiMzY3Y2ZmYmFjY2UxYjFmMmJlMWE5MGZjMzJjOGI1MjcxYjY5MzM0OTI3MTA2ZjY5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:31:42 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkFFOFpmVXY1Y3RSZWR0b21hN0w0c3c9PSIsInZhbHVlIjoiMG8rcjJwWlV5dnBtUzM3V2RpTWMzY2w5MnZXUERQbnhjMkpNV2pDanM4cFFMYW9uRS81WXZoajJlc2VybzJ0NVdscU96MDVETFZZQTVnckR6N0JENkorc3lyOXNKUUFyaGIzalJ6UDJ0bjEvaVgrT2ErZHMwQnhMUmd6dmpjc0EiLCJtYWMiOiJjN2JkOGQ4ZmM4MWUzMzRjNWRhZmI1NDZkZjZhZGJlMzdiMTc1NTNlZmZlYTJiMTliNTk5MTU3Y2UyMDI2NjE5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:31:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImVFQ3RLbFpSSFB6Vi84UVF3Ymg2dUE9PSIsInZhbHVlIjoiQTFVQmpnekRPSlZPQVQwcmxoWnZuUTBJNyt6K29jb0ZpU0NmRTNFVUZoZ1RCUGZGTGUxREVBU2d2U0gweWZBTmNpWG9KTnQwWGw3eGU0L2RkUEMrU2d4WFYrWjlHRU5nd1Uzd2pMdWxKQm5ZZmRqTVFYSzZyYnlqdUtkSnNETGoiLCJtYWMiOiI4M2Y3NmNkYWEyNWZjMThiMzY3Y2ZmYmFjY2UxYjFmMmJlMWE5MGZjMzJjOGI1MjcxYjY5MzM0OTI3MTA2ZjY5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:31:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1231141775\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-981280997 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/projects/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-981280997\", {\"maxDepth\":0})</script>\n"}}