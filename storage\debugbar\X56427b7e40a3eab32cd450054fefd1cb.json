{"__meta": {"id": "X56427b7e40a3eab32cd450054fefd1cb", "datetime": "2025-08-19 14:21:09", "utime": 1755584469.38323, "method": "GET", "uri": "/home", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[14:21:09] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584469.117154, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584468.590532, "end": 1755584469.383251, "duration": 0.7927188873291016, "duration_str": "793ms", "measures": [{"label": "Booting", "start": 1755584468.590532, "relative_start": 0, "end": 1755584469.084256, "relative_end": 1755584469.084256, "duration": 0.49372386932373047, "duration_str": "494ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584469.084269, "relative_start": 0.49373698234558105, "end": 1755584469.383253, "relative_end": 2.1457672119140625e-06, "duration": 0.2989840507507324, "duration_str": "299ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25413776, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 16, "templates": [{"name": "home (\\resources\\views\\home.blade.php)", "param_count": 3, "params": ["pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "inc.dashboard (\\resources\\views\\inc\\dashboard.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "layouts.app3 (\\resources\\views\\layouts\\app3.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData", "__currentLoopData", "project", "loop"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "pros", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bugChartData", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET home", "middleware": "web", "controller": "App\\Http\\Controllers\\HomeController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "home", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\HomeController.php&line=27\">\\app\\Http\\Controllers\\HomeController.php:27-139</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.00332, "accumulated_duration_str": "3.32ms", "statements": [{"sql": "select * from `users` where `id` = 31 limit 1", "type": "query", "params": [], "bindings": ["31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 13.554}, {"sql": "select `team_name` from `teammappings` where `username` = 'tay'", "type": "query", "params": [], "bindings": ["tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\HomeController.php", "line": 38}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Http\\Controllers\\HomeController.php:38", "connection": "sagile", "start_percent": 13.554, "width_percent": 12.048}, {"sql": "select * from `projects` where `team_name` in ('Team 888', 'Team 888', 'Team AD', 'Team AD')", "type": "query", "params": [], "bindings": ["Team 888", "Team 888", "Team AD", "Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\HomeController.php", "line": 41}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Http\\Controllers\\HomeController.php:41", "connection": "sagile", "start_percent": 25.602, "width_percent": 11.747}, {"sql": "select * from `sprint` where `proj_name` = 'SAgile'", "type": "query", "params": [], "bindings": ["SAgile"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\HomeController.php", "line": 61}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Http\\Controllers\\HomeController.php:61", "connection": "sagile", "start_percent": 37.349, "width_percent": 9.337}, {"sql": "select * from `calendar`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\HomeController.php", "line": 83}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Http\\Controllers\\HomeController.php:83", "connection": "sagile", "start_percent": 46.687, "width_percent": 13.554}, {"sql": "select `status`, COUNT(*) as count from `bugtrack` group by `status`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\HomeController.php", "line": 113}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Http\\Controllers\\HomeController.php:113", "connection": "sagile", "start_percent": 60.241, "width_percent": 12.651}, {"sql": "select `severity`, COUNT(*) as count from `bugtrack` group by `severity`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\HomeController.php", "line": 119}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Http\\Controllers\\HomeController.php:119", "connection": "sagile", "start_percent": 72.892, "width_percent": 10.843}, {"sql": "select * from `user_stories` where `proj_id` = 45", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 743}, {"index": 15, "namespace": "view", "name": "3bc33b71296da0ad751132d1ed592a4779650d83", "line": 186}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:743", "connection": "sagile", "start_percent": 83.735, "width_percent": 16.265}]}, "models": {"data": {"App\\Project": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Gf8WwtCrGWaxQlU79zc0pNXq8iCcBZ5LNC6rM5bb", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/home\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "31", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755584468\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/home", "status_code": "<pre class=sf-dump id=sf-dump-625786568 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-625786568\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1545437138 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1545437138\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-884892197 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-884892197\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-291576925 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IloxakZtZFgrQkE3QS8yeklnU0tkd1E9PSIsInZhbHVlIjoick5LV1NUdmJiTmROazB5NGNQa2xJQXdkUUhDbzVabjJlMWIxSGt3S2IzTkFzQlBOV2Y4MENZcUZlaWdZbG04T2syZ2l6Z3VnVmNmY09FUHo5NjVKcWNMeWxKQ0RnNzNITWl3cXFmTk1LbHNHRzVhQWtTVVV6MEYvdEtxR3ArbGYiLCJtYWMiOiJmNWE3MDI2NzU1NzMwYjFmMjQwN2NkNjdkYzdhZjgyMWI4ODM1MGJhNzdkZjNkZjM2OTQ4ODIxMTc0YTlhNDJjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InZKNWNlTGVCOC9uTmZCTnBLNU56bkE9PSIsInZhbHVlIjoiZUZCWDdTcU5XazQ1Ym95SlJ6am5qK0I1cmdGTi8zQ2FDeHRzalVBZTJqWXpQVWhHd0I4UWVkRTZEZnVSNVFuNm5zc05INk4xbjFnRWNuZGY4S0s3cmdvS0dINkhjTEZ5bXpDQU1ObkFwd2hQTzhkSU9mbGIvMkFyS1A2Y3J6dUEiLCJtYWMiOiI2ZGE2NDYwZDJmZmJiNzc0ZDgxYzhmN2NhN2NmNGUzM2Q1ZWM1OTZkZWRiNWFiZjM4ODVhMmNjYWNhOWEzODYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-291576925\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-15864872 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">49273</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"5 characters\">/home</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"5 characters\">/home</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/index.php/home</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IloxakZtZFgrQkE3QS8yeklnU0tkd1E9PSIsInZhbHVlIjoick5LV1NUdmJiTmROazB5NGNQa2xJQXdkUUhDbzVabjJlMWIxSGt3S2IzTkFzQlBOV2Y4MENZcUZlaWdZbG04T2syZ2l6Z3VnVmNmY09FUHo5NjVKcWNMeWxKQ0RnNzNITWl3cXFmTk1LbHNHRzVhQWtTVVV6MEYvdEtxR3ArbGYiLCJtYWMiOiJmNWE3MDI2NzU1NzMwYjFmMjQwN2NkNjdkYzdhZjgyMWI4ODM1MGJhNzdkZjNkZjM2OTQ4ODIxMTc0YTlhNDJjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InZKNWNlTGVCOC9uTmZCTnBLNU56bkE9PSIsInZhbHVlIjoiZUZCWDdTcU5XazQ1Ym95SlJ6am5qK0I1cmdGTi8zQ2FDeHRzalVBZTJqWXpQVWhHd0I4UWVkRTZEZnVSNVFuNm5zc05INk4xbjFnRWNuZGY4S0s3cmdvS0dINkhjTEZ5bXpDQU1ObkFwd2hQTzhkSU9mbGIvMkFyS1A2Y3J6dUEiLCJtYWMiOiI2ZGE2NDYwZDJmZmJiNzc0ZDgxYzhmN2NhN2NmNGUzM2Q1ZWM1OTZkZWRiNWFiZjM4ODVhMmNjYWNhOWEzODYwIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584468.5905</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584468</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15864872\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1762162194 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gf8WwtCrGWaxQlU79zc0pNXq8iCcBZ5LNC6rM5bb</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Y5RUSXSwBgBrFq8oNyWObubNEtQ5lYLQfTmmXryF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1762162194\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1061634535 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:21:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlBVNUlNU0V4VTJlL09CZkJuM01SQ0E9PSIsInZhbHVlIjoiRHNtM2JpLzBGZ1R4azAwUzJ1eHNkMDh4TE9nV1oxUHNGdXo0ekVwcHBNM2ZsNTBzNlpGOWVzS2tNRUhHY3F0VjBPMDI1MS8rdC9qMnd2ZzVvUVk3NzZSdlUrUWFoRjZBSHh6UjNWQnlKRTk1MEhSNm10KzR5UGJPLzF3QjRsREkiLCJtYWMiOiI1Y2M5ODBiMjc3NzE2M2ZlMTYyZTg0MDdlNTZkYThhNjgzYjAzMDMxMGJiMTFlODIyODhmNTkwYWViZmYzMmFmIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:09 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjZIQmcvVHJhZFFPTjdkOHpCRU1KVmc9PSIsInZhbHVlIjoiSzdYZGJwSlJqQjBNS2xQQjRnU3VKbWYvRm5OVHFsS1J3Z0h5aTBVeXdLSHgwYTRvRmZlNWJWTmdBSzRmOVUrNkhkL0xpalNkK1VWMElESEtLOW5NOWdraHhuSk5LelFxeW5BSnhxNFdxaU9NL0xMWlJObi8xT2U0ajY2S3BhOFAiLCJtYWMiOiJjYzkxY2M2YTcxNTllMzc2MTc0YWM5OGVmZTM5Yjk1MmZkNjZjOTcwYzNkZGIzNjRhZGI5NjNhMDA3MmY4OTlhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:09 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlBVNUlNU0V4VTJlL09CZkJuM01SQ0E9PSIsInZhbHVlIjoiRHNtM2JpLzBGZ1R4azAwUzJ1eHNkMDh4TE9nV1oxUHNGdXo0ekVwcHBNM2ZsNTBzNlpGOWVzS2tNRUhHY3F0VjBPMDI1MS8rdC9qMnd2ZzVvUVk3NzZSdlUrUWFoRjZBSHh6UjNWQnlKRTk1MEhSNm10KzR5UGJPLzF3QjRsREkiLCJtYWMiOiI1Y2M5ODBiMjc3NzE2M2ZlMTYyZTg0MDdlNTZkYThhNjgzYjAzMDMxMGJiMTFlODIyODhmNTkwYWViZmYzMmFmIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjZIQmcvVHJhZFFPTjdkOHpCRU1KVmc9PSIsInZhbHVlIjoiSzdYZGJwSlJqQjBNS2xQQjRnU3VKbWYvRm5OVHFsS1J3Z0h5aTBVeXdLSHgwYTRvRmZlNWJWTmdBSzRmOVUrNkhkL0xpalNkK1VWMElESEtLOW5NOWdraHhuSk5LelFxeW5BSnhxNFdxaU9NL0xMWlJObi8xT2U0ajY2S3BhOFAiLCJtYWMiOiJjYzkxY2M2YTcxNTllMzc2MTc0YWM5OGVmZTM5Yjk1MmZkNjZjOTcwYzNkZGIzNjRhZGI5NjNhMDA3MmY4OTlhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1061634535\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-949077612 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gf8WwtCrGWaxQlU79zc0pNXq8iCcBZ5LNC6rM5bb</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755584468</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-949077612\", {\"maxDepth\":0})</script>\n"}}