{"__meta": {"id": "X1b2e9ff346353e4541a7dd193ebdccdd", "datetime": "2025-08-19 11:08:37", "utime": 1755572917.497786, "method": "POST", "uri": "/upload-radar-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[11:08:37] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572917.365988, "xdebug_link": null, "collector": "log"}, {"message": "[11:08:37] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/upload-radar-chart", "message_html": null, "is_string": false, "label": "debug", "time": 1755572917.483154, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572916.837692, "end": 1755572917.497821, "duration": 0.6601290702819824, "duration_str": "660ms", "measures": [{"label": "Booting", "start": 1755572916.837692, "relative_start": 0, "end": 1755572917.3336, "relative_end": 1755572917.3336, "duration": 0.4959080219268799, "duration_str": "496ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572917.333623, "relative_start": 0.49593091011047363, "end": 1755572917.497823, "relative_end": 1.9073486328125e-06, "duration": 0.1642000675201416, "duration_str": "164ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23393976, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST upload-radar-chart", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@uploadRadarChart", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=141\">\\app\\Http\\Controllers\\CIGController.php:141-151</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00076, "accumulated_duration_str": "760μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/cig/create/42?filter_sprint_id=37\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"path_info": "/upload-radar-chart", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1578094536 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"23966 characters\">data:image/png;base64,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</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578094536\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1084350651 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">23978</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkJiZjhkdDRoOXU4Y2JaalcxbnJLOVE9PSIsInZhbHVlIjoiSDlUYnBKclRoN3lXTS9aWVExdEZLOEZiU3JNZmpKeFV6bVZRcnRGM3MvdlFFZnFrYlBiWFV6UEdDQ3V2U01hNGJMMDRFL3kxZVBTVUhSWW8wUDA2TlZKOC9vSXc0VDBKQllrYzRSaUpoQ01yd0VjdzI2d3FTc1A3RjBMRURvZ3giLCJtYWMiOiI3ZmY0MTE1YjM2NGJjNDkxMjExYjYxMDYyYzJmOGUzM2UyY2FmMjlkZDA1YTgxZDVmMzJkNDFlMDE1ZWU3YTc0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlZ4UnplQncvb0NVRnM4ZXlPY1UwVEE9PSIsInZhbHVlIjoidStMdTFxVGVTNlpFTUxxYkdXTUo4RXZUQmxFakdKUFRWRmtIVjNwRTNLT0xKSXZNa3N6bW1LbzlibjFJMDliVUM3bHNVdno1N3FERXdZWGZoT3BZeWVmTysvWmx3S3NZblJ5OVdVSVNxQUxxRGZ5S2hPYitOc3UwV09TS1pseTIiLCJtYWMiOiI5ZTU5ZmVmNjYwNGQ4MTlhYmRkYzA1MTkzMmY4ZDlmY2I1ZDY1M2M3ZGQ0OTZiM2FlMjgzNjJkMjhhNDczYzA2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1084350651\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-384459149 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56032</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/index.php/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">23978</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">23978</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkJiZjhkdDRoOXU4Y2JaalcxbnJLOVE9PSIsInZhbHVlIjoiSDlUYnBKclRoN3lXTS9aWVExdEZLOEZiU3JNZmpKeFV6bVZRcnRGM3MvdlFFZnFrYlBiWFV6UEdDQ3V2U01hNGJMMDRFL3kxZVBTVUhSWW8wUDA2TlZKOC9vSXc0VDBKQllrYzRSaUpoQ01yd0VjdzI2d3FTc1A3RjBMRURvZ3giLCJtYWMiOiI3ZmY0MTE1YjM2NGJjNDkxMjExYjYxMDYyYzJmOGUzM2UyY2FmMjlkZDA1YTgxZDVmMzJkNDFlMDE1ZWU3YTc0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlZ4UnplQncvb0NVRnM4ZXlPY1UwVEE9PSIsInZhbHVlIjoidStMdTFxVGVTNlpFTUxxYkdXTUo4RXZUQmxFakdKUFRWRmtIVjNwRTNLT0xKSXZNa3N6bW1LbzlibjFJMDliVUM3bHNVdno1N3FERXdZWGZoT3BZeWVmTysvWmx3S3NZblJ5OVdVSVNxQUxxRGZ5S2hPYitOc3UwV09TS1pseTIiLCJtYWMiOiI5ZTU5ZmVmNjYwNGQ4MTlhYmRkYzA1MTkzMmY4ZDlmY2I1ZDY1M2M3ZGQ0OTZiM2FlMjgzNjJkMjhhNDczYzA2IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572916.8377</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572916</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384459149\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1183122814 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183122814\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1569782232 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 03:08:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InRaZi9hdnAvbnU5aERYVC9yYlduNXc9PSIsInZhbHVlIjoiOEVybmh0dHJCY2Rlc1l6ckdlS25BN1BySldNRFFqazB4aG1KTTZZMEZ4dzMxTGRuSGtwY0dDczJVdW1kNzdSc21oSmJjRE9Wc3lDbWtXSjcxOUl5SjVUeHdSbmt6aFZuVFdaNk1JZFBDT0dub1czRkMraU5TWFhyWnl3aktmL2giLCJtYWMiOiI0ZmE3ZDhmMjYwZjFiN2Q5NDI0NTg1NWJmY2RhMDY2YWQzNzExNmQzYWU0YjYzZTk5ZGI2YmRhZTgxNGNlOGM0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:37 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InVENFcreEJtRmloQ0hGZWNkRVlyS2c9PSIsInZhbHVlIjoiTjB2SHZNVVhsT1dUeVlLTDB0b1M2ODR3eDg2Qm5LMW9qRXNCeHlPVUNsaktYRWdtcnBxRHI0VWkzTXVWNnVBeVlrOGZuYm9CdHdFNXVFdWVxUEFYWUlEL0V4bFlCL0Z5blFzNDQ1RkpBYndkM3BJRUh0Q3F1MjJ0UmxRVkFMVysiLCJtYWMiOiIyMGQxNDQ5Mzc3NWI5NTQ4Y2U3ODc1YTdkNjRmNzg4MWE0ZTQ3ZGY2NzdhYmQ3Y2IwZjkzMDcyZDJjMDkwYzkzIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:37 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InRaZi9hdnAvbnU5aERYVC9yYlduNXc9PSIsInZhbHVlIjoiOEVybmh0dHJCY2Rlc1l6ckdlS25BN1BySldNRFFqazB4aG1KTTZZMEZ4dzMxTGRuSGtwY0dDczJVdW1kNzdSc21oSmJjRE9Wc3lDbWtXSjcxOUl5SjVUeHdSbmt6aFZuVFdaNk1JZFBDT0dub1czRkMraU5TWFhyWnl3aktmL2giLCJtYWMiOiI0ZmE3ZDhmMjYwZjFiN2Q5NDI0NTg1NWJmY2RhMDY2YWQzNzExNmQzYWU0YjYzZTk5ZGI2YmRhZTgxNGNlOGM0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InVENFcreEJtRmloQ0hGZWNkRVlyS2c9PSIsInZhbHVlIjoiTjB2SHZNVVhsT1dUeVlLTDB0b1M2ODR3eDg2Qm5LMW9qRXNCeHlPVUNsaktYRWdtcnBxRHI0VWkzTXVWNnVBeVlrOGZuYm9CdHdFNXVFdWVxUEFYWUlEL0V4bFlCL0Z5blFzNDQ1RkpBYndkM3BJRUh0Q3F1MjJ0UmxRVkFMVysiLCJtYWMiOiIyMGQxNDQ5Mzc3NWI5NTQ4Y2U3ODc1YTdkNjRmNzg4MWE0ZTQ3ZGY2NzdhYmQ3Y2IwZjkzMDcyZDJjMDkwYzkzIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1569782232\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1010289261 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=37</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010289261\", {\"maxDepth\":0})</script>\n"}}