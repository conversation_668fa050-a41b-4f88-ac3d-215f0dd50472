<?php
    // Find the project once to use in permission checks
    $projectForCheck = $project ?? App\Project::find($userstory->proj_id ?? $tasks->first()->proj_id ?? null);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Tasks</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
            margin: 0;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .table {
            background-color: white;
            border-radius: 0.25rem;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .button-container {
            display: flex;
            gap: 0.5rem;
        }
        .action-buttons {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="d-flex align-items-center">
                        <a href="<?php echo e(route('userstory.index', ['proj_id' => $project->id])); ?>" class="btn btn-outline-secondary me-3">
                            <i class="bx bx-arrow-back"></i> Back
                        </a>
                        <h4 class="mb-0"><?php echo e($userstory->user_story ?? 'Project Tasks'); ?></h4>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Task</th>
                                <th>Description</th>
                                <th>Assigned To</th>
                                <th>Status</th>   
                                <?php if(Auth::user()->can('viewComments_task', $projectForCheck) || 
                                    Auth::user()->can('edit_task', $projectForCheck) || 
                                    Auth::user()->can('delete_task', $projectForCheck)): ?>
                                <th>Actions</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($task->title); ?></td>
                                    <td><?php echo e($task->description); ?></td>
                                    <td>
                                        <?php
                                            // Ensure user_names is sanitized by trimming extra quotes and then decoding
                                            $userNames = json_decode(trim($task->user_names, '"'), true);
                                        ?>

                                        <?php if(is_array($userNames)): ?>
                                            <?php $__currentLoopData = $userNames; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user_show): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div>- <?php echo e($user_show); ?></div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php else: ?>
                                            <p>No valid users found.</p>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                            $status = $statuses->firstWhere('id', $task->status_id);
                                        ?>
                                        <?php echo e($status->title); ?>

                                    </td>
                                    <td>
                                        <div class="button-container">
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('viewComments_task', $projectForCheck)): ?>
                                            <a href="<?php echo e(route('tasks.viewCommentList', [$task->id, $task->sprint_id])); ?>" class="btn btn-info btn-sm">
                                                <i class="bx bx-comment me-1"></i> Comment
                                            </a>
                                            <?php endif; ?>
                                            
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit_task', $projectForCheck)): ?>
                                            <a href="<?php echo e(route('tasks.edit', [$task->id])); ?>" class="btn btn-primary btn-sm">
                                                <i class="bx bx-edit me-1"></i> Edit
                                            </a>
                                            <?php endif; ?>
                                            
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete_task', $projectForCheck)): ?>
                                            <a href="<?php echo e(route('tasks.destroy', $task)); ?>" class="btn btn-danger btn-sm" 
                                               onclick="return confirm('Are you sure you want to delete this task?');">
                                                <i class="bx bx-trash me-1"></i> Delete
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="<?php echo e((Auth::user()->can('viewComments_task', $projectForCheck) || 
                                                   Auth::user()->can('edit_task', $projectForCheck) || 
                                                   Auth::user()->can('delete_task', $projectForCheck)) ? 5 : 4); ?>" 
                                        class="text-center">No tasks added yet</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                  <div class="action-buttons">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('add_task', $projectForCheck)): ?>
                    <a href="<?php echo e(route('tasks.create', $userstory_id)); ?>" class="btn btn-success">
                        <i class="bx bx-plus me-1"></i> Add Task
                    </a>
                    <?php endif; ?>
                    
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('viewCalendar_task', $projectForCheck)): ?>
                    <a href="<?php echo e(route('tasks.calendarTask', $userstory_id)); ?>" class="btn btn-primary">
                        <i class="bx bx-calendar me-1"></i> Task Calendar
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css"></script>
    <script>
        // Inform parent window to resize iframe if in an iframe
        window.onload = function() {
            if (window.parent && window.parent.document) {
                const height = document.body.scrollHeight;
                window.parent.postMessage({
                    type: 'resize',
                    iframeId: 'tasks-iframe',
                    height: height
                }, '*');
            }
        };
    </script>
</body>
</html><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/tasks/index.blade.php ENDPATH**/ ?>