{"__meta": {"id": "Xf84ae78430507541de510647149cd55f", "datetime": "2025-08-19 13:22:18", "utime": 1755580938.79785, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[13:22:18] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755580938.711151, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755580938.183989, "end": 1755580938.797876, "duration": 0.613886833190918, "duration_str": "614ms", "measures": [{"label": "Booting", "start": 1755580938.183989, "relative_start": 0, "end": 1755580938.671858, "relative_end": 1755580938.671858, "duration": 0.4878690242767334, "duration_str": "488ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755580938.671872, "relative_start": 0.4878828525543213, "end": 1755580938.797879, "relative_end": 3.0994415283203125e-06, "duration": 0.***************, "duration_str": "126ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "welcome (\\resources\\views\\welcome.blade.php)", "param_count": 0, "params": [], "type": "blade"}]}, "route": {"uri": "GET /", "middleware": "web", "uses": "Closure() {#1346\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#345 …}\n  file: \"C:\\xampp\\htdocs\\SAgilePMT_UTM\\routes\\web.php\"\n  line: \"97 to 99\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\routes\\web.php&line=97\">\\routes\\web.php:97-99</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "uw8hg5J9o9V6gQtquibmriJoJy2qzmsrQ8Ttp38j", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-697725691 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-697725691\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-411829668 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-411829668\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1684622799 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Im9jT0N0YjZOcGhJRzJvb1hqRDdxK2c9PSIsInZhbHVlIjoiVVdDQThYZFBLajdlZXNTeUJ5T1hRN3hmZ0xkRVV6Y21jUk12NDFpK2I1RlZORHo2UGlzdXBtTkZxNng2eGtEWDBRWUpiSTRZZE1naHBFMVZ4MlNMREJLWitkbXBnVXM3WDRjdW5oRExycDFpMkRkMEY1a1dVS05MaEhpeXFkR3YiLCJtYWMiOiJmM2FiMTM4OTkwZTMwNDZlMzk3MjZiNTUxZGQyNmY4NTFmZWEyYjE1NTczMTA4N2M0M2RmYzZiNjdmZmRlNzEyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im5mdnJnTXdwejNQZlNvUXZ0Z3hWM0E9PSIsInZhbHVlIjoiN2o5SlFiZkVOMjVLUlcwMGVkWHBLanJuTFBKVFRlNEZiMnZ5U28vMGFCWE5UYzBJUVVYZldoVnIxWFVmZEVGQkpMMmdPYm4rem9RcUJRVkFMRnhVMmNDanRkamZmOVhyRzhtejNQWjhpN1g1MHAzSllLb0YxN2pFZXZhS0FGUHgiLCJtYWMiOiI2N2E4OWY2MWJiMDQ0NDQ1YjI3NDIwZTM1MzA5NDRhM2Q4MTY1N2UyMzRkYjFmMDk5MjcwNWI1OTViZGQxZTgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1684622799\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-198749725 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">53937</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str>/</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Im9jT0N0YjZOcGhJRzJvb1hqRDdxK2c9PSIsInZhbHVlIjoiVVdDQThYZFBLajdlZXNTeUJ5T1hRN3hmZ0xkRVV6Y21jUk12NDFpK2I1RlZORHo2UGlzdXBtTkZxNng2eGtEWDBRWUpiSTRZZE1naHBFMVZ4MlNMREJLWitkbXBnVXM3WDRjdW5oRExycDFpMkRkMEY1a1dVS05MaEhpeXFkR3YiLCJtYWMiOiJmM2FiMTM4OTkwZTMwNDZlMzk3MjZiNTUxZGQyNmY4NTFmZWEyYjE1NTczMTA4N2M0M2RmYzZiNjdmZmRlNzEyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im5mdnJnTXdwejNQZlNvUXZ0Z3hWM0E9PSIsInZhbHVlIjoiN2o5SlFiZkVOMjVLUlcwMGVkWHBLanJuTFBKVFRlNEZiMnZ5U28vMGFCWE5UYzBJUVVYZldoVnIxWFVmZEVGQkpMMmdPYm4rem9RcUJRVkFMRnhVMmNDanRkamZmOVhyRzhtejNQWjhpN1g1MHAzSllLb0YxN2pFZXZhS0FGUHgiLCJtYWMiOiI2N2E4OWY2MWJiMDQ0NDQ1YjI3NDIwZTM1MzA5NDRhM2Q4MTY1N2UyMzRkYjFmMDk5MjcwNWI1OTViZGQxZTgzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755580938.184</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755580938</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-198749725\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1141624351 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uw8hg5J9o9V6gQtquibmriJoJy2qzmsrQ8Ttp38j</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NNYboL75mYg50g8LI2tDVVJ6vMLdAutLqDhoSsf8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1141624351\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1811252343 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:22:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlRCcm8wVlBGWUdSNXdFamw0bTZHUWc9PSIsInZhbHVlIjoibzhWM0JCa1lFS0t1SkJEYnFxVW5kbTNtbXhFQVIzVE0ySnVQbnZlbXVWN3dQVWlmQk1YNEIxK1VwVFFTSjBWQzB3RWVteHR2b0VSODJyL1ZBNXBlbGVXWTNUeGM5b3BaajBwbjdaWjRSMzNGK0NRMzBURTlPS1VmbUVMYVNjanIiLCJtYWMiOiJjNjQ5ZTEzNzBkMzg5M2VmNjRjMWY2ZDc5ZDY2MmQyYjEzNDk4NGYyMmVkZmUxNGY5ZGU1MWFhZGY5NmIzODBiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:22:18 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlJvbWpLN0VCdmZFLzJqNm9RMDUzMUE9PSIsInZhbHVlIjoicERmT0pUakxGZEFxd24xME92dm9KcFJUUEZWeHVjZjFNaklWT1VkVnFyeHdXNUEwSUFsNHRoQy90NDlZMkUxd3lwMmJvSVpuS0dhMC9ZMFJQVTY5emZRSlJpdFovUmQ5aEJuUnVYbmJlSTF5ZUF3U2ZRZC9FNnFmanpOeG5sQUsiLCJtYWMiOiJlOGNiNzVmMTZjZTEwMGMzYWU2NTE1ODU1ODgyYjkxODliOGEyNzNjYWFiMDg1ZTExM2Q4NjQzYjUxYjM1ZjljIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:22:18 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlRCcm8wVlBGWUdSNXdFamw0bTZHUWc9PSIsInZhbHVlIjoibzhWM0JCa1lFS0t1SkJEYnFxVW5kbTNtbXhFQVIzVE0ySnVQbnZlbXVWN3dQVWlmQk1YNEIxK1VwVFFTSjBWQzB3RWVteHR2b0VSODJyL1ZBNXBlbGVXWTNUeGM5b3BaajBwbjdaWjRSMzNGK0NRMzBURTlPS1VmbUVMYVNjanIiLCJtYWMiOiJjNjQ5ZTEzNzBkMzg5M2VmNjRjMWY2ZDc5ZDY2MmQyYjEzNDk4NGYyMmVkZmUxNGY5ZGU1MWFhZGY5NmIzODBiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:22:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlJvbWpLN0VCdmZFLzJqNm9RMDUzMUE9PSIsInZhbHVlIjoicERmT0pUakxGZEFxd24xME92dm9KcFJUUEZWeHVjZjFNaklWT1VkVnFyeHdXNUEwSUFsNHRoQy90NDlZMkUxd3lwMmJvSVpuS0dhMC9ZMFJQVTY5emZRSlJpdFovUmQ5aEJuUnVYbmJlSTF5ZUF3U2ZRZC9FNnFmanpOeG5sQUsiLCJtYWMiOiJlOGNiNzVmMTZjZTEwMGMzYWU2NTE1ODU1ODgyYjkxODliOGEyNzNjYWFiMDg1ZTExM2Q4NjQzYjUxYjM1ZjljIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:22:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1811252343\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2039328011 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uw8hg5J9o9V6gQtquibmriJoJy2qzmsrQ8Ttp38j</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2039328011\", {\"maxDepth\":0})</script>\n"}}