{"__meta": {"id": "X80e4dc51f26d03e4a8a2d99f8878c228", "datetime": "2025-08-19 13:17:31", "utime": 1755580651.634091, "method": "GET", "uri": "/logout", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[13:17:31] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755580651.495018, "xdebug_link": null, "collector": "log"}, {"message": "[13:17:31] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/logout", "message_html": null, "is_string": false, "label": "debug", "time": 1755580651.551544, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755580651.202205, "end": 1755580651.63412, "duration": 0.4319150447845459, "duration_str": "432ms", "measures": [{"label": "Booting", "start": 1755580651.202205, "relative_start": 0, "end": 1755580651.474603, "relative_end": 1755580651.474603, "duration": 0.2723979949951172, "duration_str": "272ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755580651.474616, "relative_start": 0.2724111080169678, "end": 1755580651.634123, "relative_end": 3.0994415283203125e-06, "duration": 0.15950703620910645, "duration_str": "160ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23295752, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET logout", "middleware": "web", "controller": "\\App\\Http\\Controllers\\Auth\\LoginController@logout", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php&line=166\">\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php:166-181</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00047, "accumulated_duration_str": "470μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "load.permissions", "line": 22}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "BkrRPIr4Izbs1esgnOnLniu1ROlR55WioisLwo42", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/logout\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/logout", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-77987337 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-77987337\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1616160096 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/team</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InRzb2EyVXdyT0FuZEJKaFVuRk5yc2c9PSIsInZhbHVlIjoib0wvQi92NzYvMlZ6Y1lyeWM3M3ROekcwUHQyQXU2UjJRdFhReTdOVG5TeG9GNEVieitSY0VFYmZpVlVxN1NiVUM3d1UyenZoZGRvSzZ4c1VZQlZZVDhJWjhsTlFUMVJVUXJBamgrb0ZCYk00NkVrbndjMTljc2c5cm9MQXVsT1MiLCJtYWMiOiIzNjBkMGZiYjYyZDRhZGM1OGRlNzhjYjYxNDEwNWI0ZWM5ZmFkZTM3NzAyZGJlYjJkMmIyN2ZiYzBlNWYxY2Q5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImhGM0dNWFhEU3J4NWpGQTI3WG96UHc9PSIsInZhbHVlIjoibjdqQmJ1R2h3b1Y1bm4zMkt6RFVtYWlUdkJmU2ZqK1VBdDJJY1VFNnN1dEtOSXdvY2ViUGJlVzJYOFNnZnFLekxSSFVVK1lab1VvVVU1WG41Zllld3lNOWRpeVkzK2l1TGFoa1QwM1YwVFQ5UVVMSjd2SVZiRjZ0RXcyak4vazMiLCJtYWMiOiI3MzM2MDdiZDY4MWFlMGI3NGJkOTFmMGRmMjE4ZmVlYmZhZTEwM2U1ZTMzMWI2NmJiM2Y0NTBiODJhM2QwNzcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1616160096\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-870746731 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54914</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"7 characters\">/logout</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"7 characters\">/logout</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/index.php/logout</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/team</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InRzb2EyVXdyT0FuZEJKaFVuRk5yc2c9PSIsInZhbHVlIjoib0wvQi92NzYvMlZ6Y1lyeWM3M3ROekcwUHQyQXU2UjJRdFhReTdOVG5TeG9GNEVieitSY0VFYmZpVlVxN1NiVUM3d1UyenZoZGRvSzZ4c1VZQlZZVDhJWjhsTlFUMVJVUXJBamgrb0ZCYk00NkVrbndjMTljc2c5cm9MQXVsT1MiLCJtYWMiOiIzNjBkMGZiYjYyZDRhZGM1OGRlNzhjYjYxNDEwNWI0ZWM5ZmFkZTM3NzAyZGJlYjJkMmIyN2ZiYzBlNWYxY2Q5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImhGM0dNWFhEU3J4NWpGQTI3WG96UHc9PSIsInZhbHVlIjoibjdqQmJ1R2h3b1Y1bm4zMkt6RFVtYWlUdkJmU2ZqK1VBdDJJY1VFNnN1dEtOSXdvY2ViUGJlVzJYOFNnZnFLekxSSFVVK1lab1VvVVU1WG41Zllld3lNOWRpeVkzK2l1TGFoa1QwM1YwVFQ5UVVMSjd2SVZiRjZ0RXcyak4vazMiLCJtYWMiOiI3MzM2MDdiZDY4MWFlMGI3NGJkOTFmMGRmMjE4ZmVlYmZhZTEwM2U1ZTMzMWI2NmJiM2Y0NTBiODJhM2QwNzcwIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755580651.2022</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755580651</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-870746731\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-998203501 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-998203501\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1353802205 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:17:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InpGSk55dTN2MEhrVXZxbHZ1VWk5T1E9PSIsInZhbHVlIjoiSWZUelhjQkhtcDRwdnFRSXVmODYxWGtrZUM2Qmk0VXI4YmpSL1FwNk40bURDeHIzNEZTQmlPWDNjcXNpQnVYNlJ0ZThCeXQ2dHg1aHkzS3A0aEV2Ujk2a0tDMUFWemJLaTlTVGpOUnUzTUJlR0xraXNVSUlLVWpodjY2T0hDOEsiLCJtYWMiOiI0M2YxNjRhY2NhZDhmMjY2Yjc3NjM4YjdjNjE2MzZmMzA4OTJmYmIyYTQ5OWQ0ODIxNDYzM2IzNjhiYjc3ZGRiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:17:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im1SNUZlVUNuTm1jOEhKSEIvRHRpL1E9PSIsInZhbHVlIjoiQ3ZwV3p5VTVTZGRONUJaTm4vdEZmU0Q0R2dZSVpsSHE4NEhCVHFGSm5CeElzWkgzWE9zeUJEVEw5VEZNdnVUL1ZOZHQ4NkxpUitpUzBSOXIwU2gwTFB6QW1OZ2lURGx2YnYvTFZ6bUYrZWpWWWxucXVDTnBBZ3RRVXpYdUpKd2YiLCJtYWMiOiIxMjdkZjM2MzcwNzEyYzk5OWRiYjBiYTIwNzJjNjgxYWQwY2YzMmZlM2NjN2FhMjYxZjlkNjYxMjE2MWY4NzNhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:17:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InpGSk55dTN2MEhrVXZxbHZ1VWk5T1E9PSIsInZhbHVlIjoiSWZUelhjQkhtcDRwdnFRSXVmODYxWGtrZUM2Qmk0VXI4YmpSL1FwNk40bURDeHIzNEZTQmlPWDNjcXNpQnVYNlJ0ZThCeXQ2dHg1aHkzS3A0aEV2Ujk2a0tDMUFWemJLaTlTVGpOUnUzTUJlR0xraXNVSUlLVWpodjY2T0hDOEsiLCJtYWMiOiI0M2YxNjRhY2NhZDhmMjY2Yjc3NjM4YjdjNjE2MzZmMzA4OTJmYmIyYTQ5OWQ0ODIxNDYzM2IzNjhiYjc3ZGRiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:17:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im1SNUZlVUNuTm1jOEhKSEIvRHRpL1E9PSIsInZhbHVlIjoiQ3ZwV3p5VTVTZGRONUJaTm4vdEZmU0Q0R2dZSVpsSHE4NEhCVHFGSm5CeElzWkgzWE9zeUJEVEw5VEZNdnVUL1ZOZHQ4NkxpUitpUzBSOXIwU2gwTFB6QW1OZ2lURGx2YnYvTFZ6bUYrZWpWWWxucXVDTnBBZ3RRVXpYdUpKd2YiLCJtYWMiOiIxMjdkZjM2MzcwNzEyYzk5OWRiYjBiYTIwNzJjNjgxYWQwY2YzMmZlM2NjN2FhMjYxZjlkNjYxMjE2MWY4NzNhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:17:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353802205\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-868009838 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BkrRPIr4Izbs1esgnOnLniu1ROlR55WioisLwo42</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://127.0.0.1:8000/logout</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868009838\", {\"maxDepth\":0})</script>\n"}}