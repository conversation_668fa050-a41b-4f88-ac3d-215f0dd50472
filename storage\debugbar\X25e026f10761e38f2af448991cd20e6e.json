{"__meta": {"id": "X25e026f10761e38f2af448991cd20e6e", "datetime": "2025-08-18 23:43:08", "utime": 1755531788.209607, "method": "POST", "uri": "/upload-radar-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:43:08] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755531788.1147, "xdebug_link": null, "collector": "log"}, {"message": "[23:43:08] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/upload-radar-chart", "message_html": null, "is_string": false, "label": "debug", "time": 1755531788.199025, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755531787.77551, "end": 1755531788.20963, "duration": 0.43411993980407715, "duration_str": "434ms", "measures": [{"label": "Booting", "start": 1755531787.77551, "relative_start": 0, "end": 1755531788.087493, "relative_end": 1755531788.087493, "duration": 0.3119828701019287, "duration_str": "312ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755531788.08751, "relative_start": 0.312000036239624, "end": 1755531788.209632, "relative_end": 1.9073486328125e-06, "duration": 0.12212181091308594, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23492632, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST upload-radar-chart", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@uploadRadarChart", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=141\">\\app\\Http\\Controllers\\CIGController.php:141-151</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00082, "accumulated_duration_str": "820μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/radar-data/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]"}, "request": {"path_info": "/upload-radar-chart", "status_code": "<pre class=sf-dump id=sf-dump-857349483 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-857349483\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-976193971 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-976193971\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1435147256 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"73158 characters\">data:image/png;base64,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</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1435147256\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1673145950 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">73170</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InZJVzgwSUpMSkF1M2ZocmxnNGM4aUE9PSIsInZhbHVlIjoiWlJlT2JGV2pJU3VCUTNyS041V3AxRXVxcnd3eFNMbCtwZDlPb1prRDF2M2w0cEVoUFVXQXNWc2xFVXY0N2xza3MybTJObmFUbzhiRmZZcjlMV2hCTXJZSU1OZW1UV05wajNuMGpoTnVveUJBWTJVbCtnYVVrMVBHZ3VXZm8wMkQiLCJtYWMiOiI4YjZlNmY3YWUxMmVlY2ZlYmExYWEyOTk5OTE5MjM5ZjA4NWJkMzI4ZGUyYTJhNjUxNTRiNWRkYzk2NmVlYTZmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik55dmRqSnBlT3UxanpHOUV5akNwckE9PSIsInZhbHVlIjoiQnVyUk0ydFdubUx2L1VlOEhkVGVRSUhuUTFZRnNoRDR5RXo5M1NRcDk1Q0Vic2hxcHZmQVZDYmpDV3haQVNzbkVmeStYYmFoSjArd3FiUytYMkNMTDdCR2FZZHgwNEtnK2V5YjlIdThpVkluSTZOREo1UGNTdEJNazJrMFFyWE8iLCJtYWMiOiIxZDQ4YThhMDdjMmU0YjM4ZmUwYjI1NmIyMzg3ZTU3ZGI1YjZmMTkzY2M2OTliODM3MmM5MjlhZDQ5M2FhNTg0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1673145950\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-592839848 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">62204</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/index.php/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">73170</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">73170</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InZJVzgwSUpMSkF1M2ZocmxnNGM4aUE9PSIsInZhbHVlIjoiWlJlT2JGV2pJU3VCUTNyS041V3AxRXVxcnd3eFNMbCtwZDlPb1prRDF2M2w0cEVoUFVXQXNWc2xFVXY0N2xza3MybTJObmFUbzhiRmZZcjlMV2hCTXJZSU1OZW1UV05wajNuMGpoTnVveUJBWTJVbCtnYVVrMVBHZ3VXZm8wMkQiLCJtYWMiOiI4YjZlNmY3YWUxMmVlY2ZlYmExYWEyOTk5OTE5MjM5ZjA4NWJkMzI4ZGUyYTJhNjUxNTRiNWRkYzk2NmVlYTZmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik55dmRqSnBlT3UxanpHOUV5akNwckE9PSIsInZhbHVlIjoiQnVyUk0ydFdubUx2L1VlOEhkVGVRSUhuUTFZRnNoRDR5RXo5M1NRcDk1Q0Vic2hxcHZmQVZDYmpDV3haQVNzbkVmeStYYmFoSjArd3FiUytYMkNMTDdCR2FZZHgwNEtnK2V5YjlIdThpVkluSTZOREo1UGNTdEJNazJrMFFyWE8iLCJtYWMiOiIxZDQ4YThhMDdjMmU0YjM4ZmUwYjI1NmIyMzg3ZTU3ZGI1YjZmMTkzY2M2OTliODM3MmM5MjlhZDQ5M2FhNTg0IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755531787.7755</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755531787</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-592839848\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-95401113 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-95401113\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2126576657 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:43:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InpaMEc0dzRQeGhKVHVIYW5QSE5qVlE9PSIsInZhbHVlIjoic1gwUE1RNTk1bGFiYXg5b3ExUTlQMXlKUVBpTEgvR1RHS2Vqb3d3SUhUbDlGeVN0bGQ4ZElLbE5BdHJjRUFsOEdvZE9QcWtwSmhsa1llWklIcVcrL20zRzcxVGJhTEhIRjlueEZ6UU1uS3YvUG5SYmxYc1ljNk5uemx0MDlUOXUiLCJtYWMiOiIzNjY2YTY5YTE4NDcxZTc0OTdiOTliZTFiMzA0NmQ2MjMzODFkZWY3YzQzZThiMTA0OWExN2YwYTFiOTQzYzA1IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:08 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InZrd0hVdWkvTmRZU0xHMk1tNk11TWc9PSIsInZhbHVlIjoiOWpJRDllU3VNTWdkOFJSamlUenlSTEV1RlJTeXhXWGs5S3IwS2d2WDdUQ3FncFRza1pmTngzSkJORFlESnRVUHJCems0Si9tQnlOdERhTVZsUGdCbUl2ckh0WGJaL0JQMG1uTTRPQXk5Tm4zbnRRSUpja3pyZ3JsUDY0b3lPL0QiLCJtYWMiOiI4Zjk2NzdlYWQ3MzI2ZmQ2M2FhZjgxM2ExMzJmYjVjZDlkN2Q0YTVjYjUyM2E1OWM4MDYxZWZmZDdjYzJjMGVmIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:08 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InpaMEc0dzRQeGhKVHVIYW5QSE5qVlE9PSIsInZhbHVlIjoic1gwUE1RNTk1bGFiYXg5b3ExUTlQMXlKUVBpTEgvR1RHS2Vqb3d3SUhUbDlGeVN0bGQ4ZElLbE5BdHJjRUFsOEdvZE9QcWtwSmhsa1llWklIcVcrL20zRzcxVGJhTEhIRjlueEZ6UU1uS3YvUG5SYmxYc1ljNk5uemx0MDlUOXUiLCJtYWMiOiIzNjY2YTY5YTE4NDcxZTc0OTdiOTliZTFiMzA0NmQ2MjMzODFkZWY3YzQzZThiMTA0OWExN2YwYTFiOTQzYzA1IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InZrd0hVdWkvTmRZU0xHMk1tNk11TWc9PSIsInZhbHVlIjoiOWpJRDllU3VNTWdkOFJSamlUenlSTEV1RlJTeXhXWGs5S3IwS2d2WDdUQ3FncFRza1pmTngzSkJORFlESnRVUHJCems0Si9tQnlOdERhTVZsUGdCbUl2ckh0WGJaL0JQMG1uTTRPQXk5Tm4zbnRRSUpja3pyZ3JsUDY0b3lPL0QiLCJtYWMiOiI4Zjk2NzdlYWQ3MzI2ZmQ2M2FhZjgxM2ExMzJmYjVjZDlkN2Q0YTVjYjUyM2E1OWM4MDYxZWZmZDdjYzJjMGVmIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2126576657\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-551904363 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/radar-data/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-551904363\", {\"maxDepth\":0})</script>\n"}}