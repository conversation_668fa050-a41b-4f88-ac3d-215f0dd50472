{"__meta": {"id": "Xad9e544b94c6ce9770d5230394ac8853", "datetime": "2025-08-18 23:56:25", "utime": 1755532585.003226, "method": "GET", "uri": "/nfr/general/2/details?project_filter=&sprint_filter=", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:56:24] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755532584.766355, "xdebug_link": null, "collector": "log"}, {"message": "[23:56:24] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/nfr/general/2/details", "message_html": null, "is_string": false, "label": "debug", "time": 1755532584.831511, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755532584.424253, "end": 1755532585.003252, "duration": 0.5789990425109863, "duration_str": "579ms", "measures": [{"label": "Booting", "start": 1755532584.424253, "relative_start": 0, "end": 1755532584.743531, "relative_end": 1755532584.743531, "duration": 0.3192780017852783, "duration_str": "319ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755532584.743544, "relative_start": 0.3192911148071289, "end": 1755532585.003255, "relative_end": 2.86102294921875e-06, "duration": 0.25971078872680664, "duration_str": "260ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25363872, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 19, "templates": [{"name": "nfr.viewGeneral (\\resources\\views\\nfr\\viewGeneral.blade.php)", "param_count": 7, "params": ["generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.dashboard (\\resources\\views\\inc\\dashboard.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\resources\\views\\vendor\\pagination\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET nfr/general/{general_nfr_id}/details", "middleware": "web", "controller": "App\\Http\\Controllers\\GeneralNFRController@viewGeneral", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "nfr.viewGeneral", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\GeneralNFRController.php&line=47\">\\app\\Http\\Controllers\\GeneralNFRController.php:47-93</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00362, "accumulated_duration_str": "3.62ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 13.536}, {"sql": "select * from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\GeneralNFRController.php", "line": 52}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Http\\Controllers\\GeneralNFRController.php:52", "connection": "sagile", "start_percent": 13.536, "width_percent": 13.812}, {"sql": "select * from `projects`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\GeneralNFRController.php", "line": 62}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\GeneralNFRController.php:62", "connection": "sagile", "start_percent": 27.348, "width_percent": 11.326}, {"sql": "select * from `generalnfr` where `general_nfr_id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\GeneralNFR.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\GeneralNFRController.php", "line": 69}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\GeneralNFR.php:36", "connection": "sagile", "start_percent": 38.674, "width_percent": 11.602}, {"sql": "select count(distinct `user_story_general_nfr`.`user_story_id`) as aggregate from `user_story_general_nfr` inner join `user_stories` on `user_story_general_nfr`.`user_story_id` = `user_stories`.`u_id` inner join `projects` on `user_stories`.`proj_id` = CAST(projects.id AS CHAR) left join `sprint` on `user_stories`.`sprint_id` = CAST(sprint.sprint_id AS CHAR) and `user_stories`.`sprint_id` != '0' where `user_story_general_nfr`.`general_nfr_id` = '2'", "type": "query", "params": [], "bindings": ["0", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\UserStoryGeneralNfr.php", "line": 81}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\GeneralNFRController.php", "line": 80}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\app\\UserStoryGeneralNfr.php:81", "connection": "sagile", "start_percent": 50.276, "width_percent": 17.403}, {"sql": "select distinct `user_stories`.`user_story`, `user_stories`.`u_id`, `projects`.`proj_name`, COALESCE(sprint.sprint_name, \"Backlog\") as sprint_name from `user_story_general_nfr` inner join `user_stories` on `user_story_general_nfr`.`user_story_id` = `user_stories`.`u_id` inner join `projects` on `user_stories`.`proj_id` = CAST(projects.id AS CHAR) left join `sprint` on `user_stories`.`sprint_id` = CAST(sprint.sprint_id AS CHAR) and `user_stories`.`sprint_id` != '0' where `user_story_general_nfr`.`general_nfr_id` = '2' limit 8 offset 0", "type": "query", "params": [], "bindings": ["0", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\UserStoryGeneralNfr.php", "line": 81}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\GeneralNFRController.php", "line": 80}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\UserStoryGeneralNfr.php:81", "connection": "sagile", "start_percent": 67.68, "width_percent": 19.89}, {"sql": "select * from `sprint`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Sprint.php", "line": 76}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\GeneralNFRController.php", "line": 88}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Sprint.php:76", "connection": "sagile", "start_percent": 87.569, "width_percent": 12.431}]}, "models": {"data": {"App\\Sprint": 1, "App\\GeneralNFR": 1, "App\\Project": 1, "App\\TeamMapping": 2, "App\\User": 1}, "count": 6}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/nfr/general/2/details?project_filter=&sprint_filter=\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/nfr/general/2/details", "status_code": "<pre class=sf-dump id=sf-dump-*********4 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********4\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-636594650 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>project_filter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sprint_filter</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-636594650\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1755869361 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>project_filter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sprint_filter</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1755869361\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-776042954 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"76 characters\">http://127.0.0.1:8000/nfr/general/2/details?project_filter=&amp;sprint_filter=34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InY3aDVpSDBieFFibDR3NUpITjFrU2c9PSIsInZhbHVlIjoiVDZvS0hDczY0WEhjN1JJanE1TGlROGNDdFo4dG5PV3VZSWJPL3I4RWtHY0JuUEtNc01mZnRqdHhtQUZ4eEVNSEN1WnNMM1NQNW5TdHZPTlg4dzg3R2xQWm44UXJHL2VzQm5sWFJIUllOTXdkUS82dVREOXM0K2hjZ3d6bzA0aysiLCJtYWMiOiI2MjgwZGE0MmNkY2I4MjRlZTNlNWQwYjdlYzEyNjBjMDNjOGYzMzI4MzMwYjlkNTk2ZmFkMmU3NmVkYmFiYzk2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ikl5K3d3dUhQZ0FVODNxYUlPRU9SWmc9PSIsInZhbHVlIjoiTmRERTFIemlDMU5tb2JTMDlhTTFORkNoalhTRkFkZml4RUpkcm5EWEJvUjIvaEdFZXRUYkRhRXB0Qzc0ZUxQYUFQc2hheG9od0NGa051RnRsU3B2VmwwM1EyRTM1blllQW4rZnBYNmk2a1JpcnNnd0wxTlh4YTVpVE42TW83WGMiLCJtYWMiOiJmMGUxMjQ4MGMyNWJlZjkwMmU0YmJjMWU2MTY0YjExYjA4YzI2NzExZTY0YTcxMmE0YzQxMDljNDc5NmIyZjJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-776042954\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-756763997 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51427</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"53 characters\">/nfr/general/2/details?project_filter=&amp;sprint_filter=</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/nfr/general/2/details</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/index.php/nfr/general/2/details</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"30 characters\">project_filter=&amp;sprint_filter=</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://127.0.0.1:8000/nfr/general/2/details?project_filter=&amp;sprint_filter=34</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InY3aDVpSDBieFFibDR3NUpITjFrU2c9PSIsInZhbHVlIjoiVDZvS0hDczY0WEhjN1JJanE1TGlROGNDdFo4dG5PV3VZSWJPL3I4RWtHY0JuUEtNc01mZnRqdHhtQUZ4eEVNSEN1WnNMM1NQNW5TdHZPTlg4dzg3R2xQWm44UXJHL2VzQm5sWFJIUllOTXdkUS82dVREOXM0K2hjZ3d6bzA0aysiLCJtYWMiOiI2MjgwZGE0MmNkY2I4MjRlZTNlNWQwYjdlYzEyNjBjMDNjOGYzMzI4MzMwYjlkNTk2ZmFkMmU3NmVkYmFiYzk2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ikl5K3d3dUhQZ0FVODNxYUlPRU9SWmc9PSIsInZhbHVlIjoiTmRERTFIemlDMU5tb2JTMDlhTTFORkNoalhTRkFkZml4RUpkcm5EWEJvUjIvaEdFZXRUYkRhRXB0Qzc0ZUxQYUFQc2hheG9od0NGa051RnRsU3B2VmwwM1EyRTM1blllQW4rZnBYNmk2a1JpcnNnd0wxTlh4YTVpVE42TW83WGMiLCJtYWMiOiJmMGUxMjQ4MGMyNWJlZjkwMmU0YmJjMWU2MTY0YjExYjA4YzI2NzExZTY0YTcxMmE0YzQxMDljNDc5NmIyZjJmIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755532584.4243</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755532584</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-756763997\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1757047888 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1757047888\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1349679808 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:56:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InFLWDRUOFhYQkhweFAwVEJNWFRtc2c9PSIsInZhbHVlIjoiblNPY2MrU0pDdUhocWQxdkRYOVptR3ZvV3BUdnRhYVZNQ25XVTQ2bjhkZFJ5cS9DLzBjc056TVY0ZFV4eDVTa1BvWmFBSENvY1l1dDNJZUU5ZTVrNW5Zdnk5Z3VaeStXSTRvL2RtTithY0RucGRiWlZzeE4ycVQ4YW56ZVkxRGwiLCJtYWMiOiI3YjgwMjFhMmFiZjhhODUwYzc5YWE0MGY2MjI1NTZiZGFmZmJhOTMxODkwZmQzNzZjMTc1MTM2MTBmMGY4MjUxIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:56:24 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImEzRUR3SlBWdlVWdHZ6QndLMFNBMHc9PSIsInZhbHVlIjoieUQ2bGoyQWJuWU1WSE1PMEhxVW1zaFdxajY2Vlk1MDZEanhXalczWXE1TDNwUzUrTHJIUWxrbFcwN2MvQVQxSU1oQUZvbGhlVWhwdTdoQVFZMUl3aUtETCtzMmsvVmxaS2JiQ3NDUDB2enIyeEJ5dzRLNklVa2ppRHdpK0k0WWoiLCJtYWMiOiJjMjQwNDZmYWI1ZjVjNjQ1NjhhMDM3ZjliYWZmMTY4ZmRiNDIyMGI1Nzk4MTFkZWU1OGY4YzQ1ODQzZjFmMzQ5IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:56:24 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InFLWDRUOFhYQkhweFAwVEJNWFRtc2c9PSIsInZhbHVlIjoiblNPY2MrU0pDdUhocWQxdkRYOVptR3ZvV3BUdnRhYVZNQ25XVTQ2bjhkZFJ5cS9DLzBjc056TVY0ZFV4eDVTa1BvWmFBSENvY1l1dDNJZUU5ZTVrNW5Zdnk5Z3VaeStXSTRvL2RtTithY0RucGRiWlZzeE4ycVQ4YW56ZVkxRGwiLCJtYWMiOiI3YjgwMjFhMmFiZjhhODUwYzc5YWE0MGY2MjI1NTZiZGFmZmJhOTMxODkwZmQzNzZjMTc1MTM2MTBmMGY4MjUxIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:56:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImEzRUR3SlBWdlVWdHZ6QndLMFNBMHc9PSIsInZhbHVlIjoieUQ2bGoyQWJuWU1WSE1PMEhxVW1zaFdxajY2Vlk1MDZEanhXalczWXE1TDNwUzUrTHJIUWxrbFcwN2MvQVQxSU1oQUZvbGhlVWhwdTdoQVFZMUl3aUtETCtzMmsvVmxaS2JiQ3NDUDB2enIyeEJ5dzRLNklVa2ppRHdpK0k0WWoiLCJtYWMiOiJjMjQwNDZmYWI1ZjVjNjQ1NjhhMDM3ZjliYWZmMTY4ZmRiNDIyMGI1Nzk4MTFkZWU1OGY4YzQ1ODQzZjFmMzQ5IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:56:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349679808\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-825440714 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"74 characters\">http://127.0.0.1:8000/nfr/general/2/details?project_filter=&amp;sprint_filter=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-825440714\", {\"maxDepth\":0})</script>\n"}}