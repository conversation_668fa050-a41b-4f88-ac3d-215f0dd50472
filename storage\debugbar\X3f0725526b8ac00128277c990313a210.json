{"__meta": {"id": "X3f0725526b8ac00128277c990313a210", "datetime": "2025-08-19 10:54:40", "utime": 1755572080.441087, "method": "GET", "uri": "/backlogTest/42", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 33, "messages": [{"message": "[10:54:40] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572080.06923, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/backlogTest/42", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.177876, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.info: BacklogController@index started {\"project_id\":\"42\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755572080.178037, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.info: Project found {\"project\":{\"id\":42,\"team_name\":\"<PERSON><PERSON><PERSON>'s team\",\"proj_name\":\"Food Ordering System\",\"proj_desc\":\"A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.\",\"start_date\":\"2025-08-18\",\"end_date\":\"2026-01-25\",\"shareable_slug\":null,\"created_at\":\"2025-08-18T15:02:05.000000Z\",\"updated_at\":\"2025-08-18T15:02:05.000000Z\"}}", "message_html": null, "is_string": false, "label": "info", "time": 1755572080.201701, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.info: Explicit active sprint check completed {\"proj_name\":\"Food Ordering System\",\"active_sprint_found\":false,\"active_sprint_data\":null}", "message_html": null, "is_string": false, "label": "info", "time": 1755572080.22308, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.info: No explicit active sprint found, checking NULL active_sprint sprints", "message_html": null, "is_string": false, "label": "info", "time": 1755572080.223213, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.info: Active sprint ID determined {\"active_sprint_id\":null}", "message_html": null, "is_string": false, "label": "info", "time": 1755572080.243685, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.info: No active sprint - getting backlog user stories", "message_html": null, "is_string": false, "label": "info", "time": 1755572080.243846, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.info: Backlog non-done user stories retrieved {\"backlog_user_stories_count\":3,\"backlog_user_story_ids\":[46,47,49],\"done_status_id\":208}", "message_html": null, "is_string": false, "label": "info", "time": 1755572080.288546, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.info: Starting task retrieval for user stories", "message_html": null, "is_string": false, "label": "info", "time": 1755572080.288654, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: Processing tasks for user story {\"user_story_id\":46}", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.310144, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: All tasks retrieved (no active sprint) excluding done tasks {\"user_story_id\":46,\"done_task_status_id\":208,\"tasks_count\":0,\"task_ids\":[]}", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.332394, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: Processing tasks for user story {\"user_story_id\":47}", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.332497, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: All tasks retrieved (no active sprint) excluding done tasks {\"user_story_id\":47,\"done_task_status_id\":208,\"tasks_count\":0,\"task_ids\":[]}", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.357509, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: Processing tasks for user story {\"user_story_id\":49}", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.357614, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: All tasks retrieved (no active sprint) excluding done tasks {\"user_story_id\":49,\"done_task_status_id\":208,\"tasks_count\":0,\"task_ids\":[]}", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.382347, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.info: Task retrieval completed {\"total_user_stories_with_tasks\":3,\"total_tasks_retrieved\":0}", "message_html": null, "is_string": false, "label": "info", "time": 1755572080.382448, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.info: BacklogController@index completed successfully {\"project_id\":\"42\",\"active_sprint_id\":null,\"user_stories_count\":3,\"tasks_by_user_story_count\":3,\"note\":\"Filtered out user stories with Done status and tasks with done status\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755572080.382527, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: Gate check for permission: addToSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.395784, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.397671, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.397754, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: Gate check for permission: addToSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.405468, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.407088, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.407185, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: Gate check for permission: addToSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.407804, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.409324, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.409417, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: Gate check for permission: addUserStory_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.409969, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.411825, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.411955, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: Gate check for permission: beginSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.412626, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.414336, "xdebug_link": null, "collector": "log"}, {"message": "[10:54:40] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572080.414567, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572079.548496, "end": 1755572080.441151, "duration": 0.8926548957824707, "duration_str": "893ms", "measures": [{"label": "Booting", "start": 1755572079.548496, "relative_start": 0, "end": 1755572080.037359, "relative_end": 1755572080.037359, "duration": 0.4888629913330078, "duration_str": "489ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572080.037373, "relative_start": 0.4888770580291748, "end": 1755572080.441154, "relative_end": 3.0994415283203125e-06, "duration": 0.4037809371948242, "duration_str": "404ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24167664, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "backlogTest.index (\\resources\\views\\backlogTest\\index.blade.php)", "param_count": 4, "params": ["project", "userStories", "tasksByUserStory", "activeSprint"], "type": "blade"}, {"name": "inc.character-counter (\\resources\\views\\inc\\character-counter.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "userStories", "tasksByUserStory", "activeSprint", "__currentLoopData", "userStory", "loop", "inputId", "counterId", "max<PERSON><PERSON><PERSON>"], "type": "blade"}, {"name": "inc.character-counter (\\resources\\views\\inc\\character-counter.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "userStories", "tasksByUserStory", "activeSprint", "__currentLoopData", "userStory", "loop", "inputId", "counterId", "max<PERSON><PERSON><PERSON>"], "type": "blade"}]}, "route": {"uri": "GET backlogTest/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\BacklogController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "backlogTest.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\BacklogController.php&line=19\">\\app\\Http\\Controllers\\BacklogController.php:19-239</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.00747, "accumulated_duration_str": "7.47ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 9.772}, {"sql": "select * from `projects` where `projects`.`id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 24}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:24", "connection": "sagile", "start_percent": 9.772, "width_percent": 8.969}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` = 1 limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 30}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:30", "connection": "sagile", "start_percent": 18.742, "width_percent": 9.639}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` is null and `start_sprint` <= '2025-08-19 10:54:40' and `end_sprint` >= '2025-08-19 10:54:40' limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System", "2025-08-19 10:54:40", "2025-08-19 10:54:40"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 48}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:48", "connection": "sagile", "start_percent": 28.38, "width_percent": 9.505}, {"sql": "select * from `statuses` where `project_id` = '42' and `title` = 'Done' limit 1", "type": "query", "params": [], "bindings": ["42", "Done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 144}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:144", "connection": "sagile", "start_percent": 37.885, "width_percent": 11.111}, {"sql": "select * from `user_stories` where `proj_id` = '42' and (`sprint_id` = 0 or `sprint_id` is null) and `status_id` != 208", "type": "query", "params": [], "bindings": ["42", "0", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 157}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:157", "connection": "sagile", "start_percent": 48.996, "width_percent": 10.71}, {"sql": "select * from `statuses` where `project_id` = '42' and `slug` = 'done' limit 1", "type": "query", "params": [], "bindings": ["42", "done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 173}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:173", "connection": "sagile", "start_percent": 59.705, "width_percent": 11.245}, {"sql": "select * from `tasks` where `userstory_id` = 46 and `status_id` != 208", "type": "query", "params": [], "bindings": ["46", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 209}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:209", "connection": "sagile", "start_percent": 70.95, "width_percent": 8.568}, {"sql": "select * from `tasks` where `userstory_id` = 47 and `status_id` != 208", "type": "query", "params": [], "bindings": ["47", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 209}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:209", "connection": "sagile", "start_percent": 79.518, "width_percent": 10.442}, {"sql": "select * from `tasks` where `userstory_id` = 49 and `status_id` != 208", "type": "query", "params": [], "bindings": ["49", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 209}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:209", "connection": "sagile", "start_percent": 89.96, "width_percent": 10.04}]}, "models": {"data": {"App\\UserStory": 3, "App\\Status": 2, "App\\Project": 1, "App\\User": 1}, "count": 7}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 5, "messages": [{"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755572080.40353, "xdebug_link": null}, {"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-261445779 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-261445779\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755572080.407455, "xdebug_link": null}, {"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1577160000 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1577160000\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755572080.409645, "xdebug_link": null}, {"message": "[\n  ability => addUserStory_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-777385917 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">addUserStory_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-777385917\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755572080.412234, "xdebug_link": null}, {"message": "[\n  ability => beginSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-784987477 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">beginSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-784987477\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755572080.414753, "xdebug_link": null}]}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backlogTest/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755570218\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/backlogTest/42", "status_code": "<pre class=sf-dump id=sf-dump-1482452133 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1482452133\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1056111231 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1056111231\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-3636604 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-3636604\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2119112175 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlY5eGlPV1ZpSkRRV202aDkzNXBhaWc9PSIsInZhbHVlIjoiMS9pOHdCbnBRYys3dTRNVjRrSUhqRnh5dzdVZWp0bktYODRycEpzdndqNXp1UForem5sTWhBRDVkTUtTWmdiejd2aDUrem5CRDdPRk9MMjFsNS84Q1JTVXdtOWlER1NFRisramRPNmg5cjJ4d0FnbEVtYlpRaFFybUtJRG1QVmEiLCJtYWMiOiI0OGEzMmY4YWY4ZGJhMzE1MGVkMGVkYWNlMjE2OWE0MWU5ZTljYTBiYzIxNTEwNmI0ZjI1ZTM4YWYxOGVlZDk4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImtRZTFTcmlkdVpBK21SYko0emxkUVE9PSIsInZhbHVlIjoieEw5WCtENmdrV1BGNHpsQWxXR0RMdzhFamZ6S1dRQnhKNmZ3WlJpeGw5cjhSMis0WkJJckExUndKVjRESHN2anNCcDVxUTVZYkZFNC82S0VtcFRzYU1VM2hwb2E1QkMyTUpFMGh6cmY2NFBzZTZjWVd1SEdsT2hWbWUzbThidngiLCJtYWMiOiIwYWE1ZjQ3NWVhM2M3OTNmZTZhMGJiMTQwOGI0YmU0NDE5MjRiMDdhMDQ4ZTQzNTcxYzRmNGIxYTU1ZWM1NGVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2119112175\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1385714735 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51431</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/index.php/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlY5eGlPV1ZpSkRRV202aDkzNXBhaWc9PSIsInZhbHVlIjoiMS9pOHdCbnBRYys3dTRNVjRrSUhqRnh5dzdVZWp0bktYODRycEpzdndqNXp1UForem5sTWhBRDVkTUtTWmdiejd2aDUrem5CRDdPRk9MMjFsNS84Q1JTVXdtOWlER1NFRisramRPNmg5cjJ4d0FnbEVtYlpRaFFybUtJRG1QVmEiLCJtYWMiOiI0OGEzMmY4YWY4ZGJhMzE1MGVkMGVkYWNlMjE2OWE0MWU5ZTljYTBiYzIxNTEwNmI0ZjI1ZTM4YWYxOGVlZDk4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImtRZTFTcmlkdVpBK21SYko0emxkUVE9PSIsInZhbHVlIjoieEw5WCtENmdrV1BGNHpsQWxXR0RMdzhFamZ6S1dRQnhKNmZ3WlJpeGw5cjhSMis0WkJJckExUndKVjRESHN2anNCcDVxUTVZYkZFNC82S0VtcFRzYU1VM2hwb2E1QkMyTUpFMGh6cmY2NFBzZTZjWVd1SEdsT2hWbWUzbThidngiLCJtYWMiOiIwYWE1ZjQ3NWVhM2M3OTNmZTZhMGJiMTQwOGI0YmU0NDE5MjRiMDdhMDQ4ZTQzNTcxYzRmNGIxYTU1ZWM1NGVmIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572079.5485</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572079</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1385714735\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-802368475 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-802368475\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-238703313 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:54:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InA5cjQzeUxpaGpScmF3SzJSYUlmalE9PSIsInZhbHVlIjoiR1pYRnVVMC9hcFFZeDU2NjZTSk0xL055ZkhVY2paZXFhcEx1YUZ6SEsyRHY1MUd0V3NqMk82enB6amkvY2ovWUh0aGFha1VEbGRhb1Q3OTQySXk0S1NmL2dkSkJ4YVJlL0ROY04xSkZ1eWdTQkwzMFdhWFM3YnF0Tnc5eHNZdjUiLCJtYWMiOiI1ZGI4MTU0NjE0NTQ3YjQzZjNlNDNiMWM1MzA2MjllNDViZjI0MzU5YTJmZDgxNjY2ODczMjA2MTNlYzIwZDc5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:54:40 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImtSTmhEZm1RK3dFa21aR1lVRWJ4U2c9PSIsInZhbHVlIjoiTE5oeGd1RytuZjZZNnFjRjllcWlvcGYvdXpyRVZXN2JPeHFmV1VUdDdXbXIzU2M5b2p3UHFpK2JNeW80cnBHb1kwYkFNUDVDUHYyZU56dWM4Z01kQ283RDJ0WUM1U2ErdXNoOW9hemJxdmJ4anU3eXAvNXIxcEQyYU9kMkZqTloiLCJtYWMiOiI5ZDczYjEyNmE1NjAxNmM1YmY0NzM1MDBkOWQwNDk1ZDE2MDliY2JkM2U5MzJlYjU4NzYyZTE1ZTZmYzQ3ZjZiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:54:40 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InA5cjQzeUxpaGpScmF3SzJSYUlmalE9PSIsInZhbHVlIjoiR1pYRnVVMC9hcFFZeDU2NjZTSk0xL055ZkhVY2paZXFhcEx1YUZ6SEsyRHY1MUd0V3NqMk82enB6amkvY2ovWUh0aGFha1VEbGRhb1Q3OTQySXk0S1NmL2dkSkJ4YVJlL0ROY04xSkZ1eWdTQkwzMFdhWFM3YnF0Tnc5eHNZdjUiLCJtYWMiOiI1ZGI4MTU0NjE0NTQ3YjQzZjNlNDNiMWM1MzA2MjllNDViZjI0MzU5YTJmZDgxNjY2ODczMjA2MTNlYzIwZDc5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:54:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImtSTmhEZm1RK3dFa21aR1lVRWJ4U2c9PSIsInZhbHVlIjoiTE5oeGd1RytuZjZZNnFjRjllcWlvcGYvdXpyRVZXN2JPeHFmV1VUdDdXbXIzU2M5b2p3UHFpK2JNeW80cnBHb1kwYkFNUDVDUHYyZU56dWM4Z01kQ283RDJ0WUM1U2ErdXNoOW9hemJxdmJ4anU3eXAvNXIxcEQyYU9kMkZqTloiLCJtYWMiOiI5ZDczYjEyNmE1NjAxNmM1YmY0NzM1MDBkOWQwNDk1ZDE2MDliY2JkM2U5MzJlYjU4NzYyZTE1ZTZmYzQ3ZjZiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:54:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-238703313\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1445938944 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755570218</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1445938944\", {\"maxDepth\":0})</script>\n"}}