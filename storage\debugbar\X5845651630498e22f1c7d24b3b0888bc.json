{"__meta": {"id": "X5845651630498e22f1c7d24b3b0888bc", "datetime": "2025-08-19 13:49:12", "utime": 1755582552.802627, "method": "GET", "uri": "/sprint-archive/45/burndown", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[13:49:12] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755582552.632123, "xdebug_link": null, "collector": "log"}, {"message": "[13:49:12] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/sprint-archive/45/burndown", "message_html": null, "is_string": false, "label": "debug", "time": 1755582552.69226, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755582552.34824, "end": 1755582552.802649, "duration": 0.454409122467041, "duration_str": "454ms", "measures": [{"label": "Booting", "start": 1755582552.34824, "relative_start": 0, "end": 1755582552.613212, "relative_end": 1755582552.613212, "duration": 0.2649722099304199, "duration_str": "265ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755582552.613223, "relative_start": 0.2649831771850586, "end": 1755582552.802651, "relative_end": 1.9073486328125e-06, "duration": 0.18942785263061523, "duration_str": "189ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24187712, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "sprint.archives.burndown (\\resources\\views\\sprint\\archives\\burndown.blade.php)", "param_count": 3, "params": ["sprint", "project", "archive"], "type": "blade"}]}, "route": {"uri": "GET sprint-archive/{sprint_id}/burndown", "middleware": "web", "controller": "App\\Http\\Controllers\\SprintArchiveController@viewArchive", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "sprint.viewArchiveBurndown", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\SprintArchiveController.php&line=29\">\\app\\Http\\Controllers\\SprintArchiveController.php:29-58</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0017900000000000001, "accumulated_duration_str": "1.79ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 25.14}, {"sql": "select * from `sprint` where `sprint`.`sprint_id` = '45' limit 1", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintArchiveController.php", "line": 31}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\SprintArchiveController.php:31", "connection": "sagile", "start_percent": 25.14, "width_percent": 25.698}, {"sql": "select * from `sprint_archives` where `sprint_archives`.`sprint_id` in (45)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintArchiveController.php", "line": 31}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\SprintArchiveController.php:31", "connection": "sagile", "start_percent": 50.838, "width_percent": 25.698}, {"sql": "select * from `projects` where `proj_name` = 'Food Ordering System' limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintArchiveController.php", "line": 32}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Http\\Controllers\\SprintArchiveController.php:32", "connection": "sagile", "start_percent": 76.536, "width_percent": 23.464}]}, "models": {"data": {"App\\Project": 1, "App\\SprintArchive": 1, "App\\Sprint": 1, "App\\User": 1}, "count": 4}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/sprint-archive/45/burndown\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/sprint-archive/45/burndown", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2033878469 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2033878469\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-839452266 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-839452266\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1189980330 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/project/43/sprint-archives</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjBiVmpXMldFYzJPb2VpRW5XT1JWNEE9PSIsInZhbHVlIjoiYlJuQzN2U1h5U05sL2NqVG5MWmJDWGdkd1Urb1RNSForeVlXcVFBSGczbUI1RzBmVWt4a09VcEtKdWZnU3NBMkw2eTF5bmJteTlWb1Y0dWd0cmIzNHlKa0tyNUtGU1k5ZkpZcHJsNjlHaDcydW5lSzB5aWJVaTRocEtacElIb1giLCJtYWMiOiI2NjU3ZmQ0NTNiZDQ5NDZkZTZlNGEyYmE2Mzg4ZDYzYjA1MDUwMjM0ZjkxYzUzNWE3ZTZmMTU0YWRlOGU2ODYyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkM3N0luTENIY0ZPTituM1ExM25FWkE9PSIsInZhbHVlIjoiUWRsTGswYXlENG95NDlHV0lZTU9wd0hxRlFkblcyTG9XN3BmQUxYZzNINENMdDVwOE0zUWV1RUZQbW11WkVSczE0TU5VUUpOb0hOd3VUN0JDOVRrOU4yTlZuN2EzQkt2QkVHakV2akc2b293Ui9sRklIdkhsQWF2ZStaMGRObWQiLCJtYWMiOiJjNTQ5NzcwZDhhYzQ0MmM5YmQ1NTgyMDg1NWQ3Y2Y3MjY2ZTg4ZjFhNWI4ZjM2YjVjNTM0NWYyYWRjYTU1OWYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1189980330\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-96179531 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60407</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/sprint-archive/45/burndown</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/sprint-archive/45/burndown</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/index.php/sprint-archive/45/burndown</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/project/43/sprint-archives</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjBiVmpXMldFYzJPb2VpRW5XT1JWNEE9PSIsInZhbHVlIjoiYlJuQzN2U1h5U05sL2NqVG5MWmJDWGdkd1Urb1RNSForeVlXcVFBSGczbUI1RzBmVWt4a09VcEtKdWZnU3NBMkw2eTF5bmJteTlWb1Y0dWd0cmIzNHlKa0tyNUtGU1k5ZkpZcHJsNjlHaDcydW5lSzB5aWJVaTRocEtacElIb1giLCJtYWMiOiI2NjU3ZmQ0NTNiZDQ5NDZkZTZlNGEyYmE2Mzg4ZDYzYjA1MDUwMjM0ZjkxYzUzNWE3ZTZmMTU0YWRlOGU2ODYyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkM3N0luTENIY0ZPTituM1ExM25FWkE9PSIsInZhbHVlIjoiUWRsTGswYXlENG95NDlHV0lZTU9wd0hxRlFkblcyTG9XN3BmQUxYZzNINENMdDVwOE0zUWV1RUZQbW11WkVSczE0TU5VUUpOb0hOd3VUN0JDOVRrOU4yTlZuN2EzQkt2QkVHakV2akc2b293Ui9sRklIdkhsQWF2ZStaMGRObWQiLCJtYWMiOiJjNTQ5NzcwZDhhYzQ0MmM5YmQ1NTgyMDg1NWQ3Y2Y3MjY2ZTg4ZjFhNWI4ZjM2YjVjNTM0NWYyYWRjYTU1OWYzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755582552.3482</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755582552</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-96179531\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-840447918 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-840447918\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1184469653 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:49:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IklWUUtIMVlTTXRFQW53RGJKUlIyN2c9PSIsInZhbHVlIjoidFlNK3dPZ0JtTVlwaEJJY2d1ajlCenc0UzU2R0pvaXVqV1IrdFFucVJxZm1iZklCS25pYUhYWHhtTy84OUtGRk1wb0tOYWlmRnBSNWxjYUxhemFka0pUTE5na01HZThtR05DUjMrVDVxOG15ZkNmaEFlNk1zcFIrMzFOdVdvZjMiLCJtYWMiOiJhM2IxNzEwYjY2N2M1OGNkODlmZmY2MTc3YWE2Y2I5MGI5MjNhNzZlNWM1NmY4N2E2MDY0OGFhNzY4ZTYzNWUyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:49:12 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InRiVGNuZy96dm9uT3dvKytlVDhqalE9PSIsInZhbHVlIjoicXdac1FBMWptYUZhWTJ3VTVuOW1Ed0VkU1pNWnRQQWpDTFlreTZlbnZLQWRoQ2JSUnJNMDM2SEV1VUp1Mmc2N1ZaOVJyTG53Ukt4cysxODZHbVE3eUNmQWk1REdHTFBraGhjSWJidndQaE1lVVR5ekU1UGdVYk41bldDeXNESFQiLCJtYWMiOiI0Nzc2OGQyM2U0ODg0MWZiYmQyOTE3ZmZmOTdhY2RmMzk3NGUyZTA2MTQ4ZDVkYmY1ZmJlYTAxZmNkNTM1YzhjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:49:12 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IklWUUtIMVlTTXRFQW53RGJKUlIyN2c9PSIsInZhbHVlIjoidFlNK3dPZ0JtTVlwaEJJY2d1ajlCenc0UzU2R0pvaXVqV1IrdFFucVJxZm1iZklCS25pYUhYWHhtTy84OUtGRk1wb0tOYWlmRnBSNWxjYUxhemFka0pUTE5na01HZThtR05DUjMrVDVxOG15ZkNmaEFlNk1zcFIrMzFOdVdvZjMiLCJtYWMiOiJhM2IxNzEwYjY2N2M1OGNkODlmZmY2MTc3YWE2Y2I5MGI5MjNhNzZlNWM1NmY4N2E2MDY0OGFhNzY4ZTYzNWUyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:49:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InRiVGNuZy96dm9uT3dvKytlVDhqalE9PSIsInZhbHVlIjoicXdac1FBMWptYUZhWTJ3VTVuOW1Ed0VkU1pNWnRQQWpDTFlreTZlbnZLQWRoQ2JSUnJNMDM2SEV1VUp1Mmc2N1ZaOVJyTG53Ukt4cysxODZHbVE3eUNmQWk1REdHTFBraGhjSWJidndQaE1lVVR5ekU1UGdVYk41bldDeXNESFQiLCJtYWMiOiI0Nzc2OGQyM2U0ODg0MWZiYmQyOTE3ZmZmOTdhY2RmMzk3NGUyZTA2MTQ4ZDVkYmY1ZmJlYTAxZmNkNTM1YzhjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:49:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1184469653\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-73240418 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/sprint-archive/45/burndown</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-73240418\", {\"maxDepth\":0})</script>\n"}}