

<?php echo $__env->make('inc.breadcrumbStyle', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->startSection('content'); ?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header Section with Breadcrumb -->
            <div class="mb-4">
                <div class="d-flex align-items-center">
                    <h1 class="mb-0"><a href="<?php echo e(route('project-assignments.index')); ?>" class="breadcrumb-link">Project Assignments</a></h1>
                    <h1 class="mb-0"><span class="breadcrumb-separator">/</span></h1>
                    <h1 class="mb-0 breadcrumb-current"><?php echo e($project->proj_name); ?></h1>
                </div>
                <hr class="my-3" style="border-top: 2px solid #e3e6f0;">
                <div class="d-flex justify-content-between align-items-center mt-2">
                    <div>
                        <h2 class="h3 mb-0"><?php echo e($project->proj_name); ?> - Assignments</h2>
                        <p class="text-muted mb-0">Team: <strong><?php echo e($project->team_name); ?></strong></p>
                    </div>
                    <?php if($canManageAssignments): ?>
                        <a href="<?php echo e(route('project-assignments.create', ['project_id' => $project->id])); ?>" 
                           class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>Assign Member
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo e(session('error')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-users me-2"></i>
                                Project Assignments (<?php echo e($projectAssignments->count()); ?>)
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if($projectAssignments->count() > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Member</th>
                                                <th>Project Role</th>
                                                <th>Assigned Date</th>
                                                <?php if($canManageAssignments): ?>
                                                    <th>Actions</th>
                                                <?php endif; ?>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $projectAssignments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $assignment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="avatar-sm bg-primary rounded-circle me-2 d-flex align-items-center justify-content-center">
                                                                <i class="fas fa-user text-white"></i>
                                                            </div>
                                                            <div>
                                                                <div class="fw-bold"><?php echo e($assignment->username); ?></div>
                                                                <?php if($assignment->user && $assignment->user->email): ?>
                                                                    <small class="text-muted"><?php echo e($assignment->user->email); ?></small>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <?php if($canManageAssignments): ?>
                                                            <select class="form-select form-select-sm role-select" 
                                                                    data-assignment-id="<?php echo e($assignment->teammapping_id); ?>"
                                                                    style="width: auto;">
                                                                <?php $__currentLoopData = $project->getAvailableRoles(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <option value="<?php echo e($role->role_name); ?>" 
                                                                            <?php echo e($assignment->role_name == $role->role_name ? 'selected' : ''); ?>>
                                                                        <?php echo e($role->role_name); ?>

                                                                    </option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </select>
                                                        <?php else: ?>
                                                            <span class="badge bg-primary"><?php echo e($assignment->role_name); ?></span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="text-muted">
                                                            <?php echo e($assignment->created_at ? $assignment->created_at->format('M d, Y') : 'N/A'); ?>

                                                        </span>
                                                    </td>
                                                    <?php if($canManageAssignments): ?>
                                                        <td>
                                                            <button type="button" 
                                                                    class="btn btn-danger btn-sm" 
                                                                    data-bs-toggle="modal" 
                                                                    data-bs-target="#removeModal<?php echo e($assignment->teammapping_id); ?>">
                                                                <i class="fas fa-times me-1"></i>Remove
                                                            </button>
                                                        </td>
                                                    <?php endif; ?>
                                                </tr>

                                                <?php if($canManageAssignments): ?>
                                                    <!-- Remove Confirmation Modal -->
                                                    <div class="modal fade" id="removeModal<?php echo e($assignment->teammapping_id); ?>" 
                                                         tabindex="-1" aria-hidden="true">
                                                        <div class="modal-dialog">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title">Remove Assignment</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    Are you sure you want to remove <strong><?php echo e($assignment->username); ?></strong> 
                                                                    from this project?
                                                                    <br><br>
                                                                    <small class="text-muted">
                                                                        Note: This will only remove them from the project assignment. 
                                                                        They will still remain a member of the team.
                                                                    </small>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                                                        Cancel
                                                                    </button>
                                                                    <form action="<?php echo e(route('project-assignments.destroy', $assignment->teammapping_id)); ?>" 
                                                                          method="POST" class="d-inline">
                                                                        <?php echo csrf_field(); ?>
                                                                        <?php echo method_field('DELETE'); ?>
                                                                        <button type="submit" class="btn btn-danger">
                                                                            Remove from Project
                                                                        </button>
                                                                    </form>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No Assignments Yet</h5>
                                    <p class="text-muted">No team members have been assigned to this project.</p>
                                    <?php if($canManageAssignments): ?>
                                        <a href="<?php echo e(route('project-assignments.create', ['project_id' => $project->id])); ?>" 
                                           class="btn btn-success">
                                            <i class="fas fa-plus me-1"></i>Assign First Member
                                        </a>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-1"></i>
                                Project Details
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <small class="text-muted">Project Name</small>
                                <div class="fw-bold"><?php echo e($project->proj_name); ?></div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Team</small>
                                <div class="fw-bold"><?php echo e($project->team_name); ?></div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Description</small>
                                <div><?php echo e($project->proj_desc ?: 'No description available'); ?></div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Duration</small>
                                <div>
                                    <?php echo e($project->start_date ? \Carbon\Carbon::parse($project->start_date)->format('M d, Y') : 'TBD'); ?> 
                                    - 
                                    <?php echo e($project->end_date ? \Carbon\Carbon::parse($project->end_date)->format('M d, Y') : 'TBD'); ?>

                                </div>
                            </div>
                            <div class="mb-0">
                                <small class="text-muted">Total Assignments</small>
                                <div class="fw-bold"><?php echo e($projectAssignments->count()); ?> members</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.avatar-sm {
    width: 32px;
    height: 32px;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
}

/* Notification styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 4px;
    color: white;
    font-weight: 500;
    z-index: 9999;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    animation: fadeIn 0.3s ease-out;
    max-width: 350px;
}

.notification-success {
    background-color: #28a745;
    border-left: 5px solid #1e7e34;
}

.notification-error {
    background-color: #dc3545;
    border-left: 5px solid #bd2130;
}

@keyframes  fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to show notifications
    function showNotification(message, type) {
        // Remove any existing notifications
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => {
            notification.remove();
        });
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Add to body
        document.body.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-10px)';
            notification.style.transition = 'all 0.3s ease-out';
            
            // Complete removal after animation
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    // Handle role updates
    document.querySelectorAll('.role-select').forEach(function(select) {
        select.addEventListener('change', function() {
            const assignmentId = this.dataset.assignmentId;
            const newRole = this.value;
            const originalValue = this.dataset.originalValue;
            
            // Skip if no change
            if (originalValue === newRole) {
                return;
            }
            
            // Visual feedback - disable select during AJAX
            this.disabled = true;
            
            fetch(`<?php echo e(route('project-assignments.index')); ?>/update-role/${assignmentId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    role_name: newRole
                })
            })
            .then(response => {
                // Re-enable select
                this.disabled = false;
                
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.message || 'Failed to update role');
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Update the stored original value to prevent reversion
                    this.dataset.originalValue = newRole;
                    
                    // Show success notification
                    showNotification(data.message || 'Role updated successfully', 'success');
                    
                    // Remove any existing alerts
                    const existingAlerts = document.querySelectorAll('.alert');
                    existingAlerts.forEach(alert => alert.remove());
                } else {
                    throw new Error(data.message || 'Failed to update role');
                }
            })
            .catch(error => {
                // Re-enable select if still disabled
                this.disabled = false;
                console.error('Error:', error);
                
                // Revert the selection to original value
                this.value = originalValue;
                
                // Show error notification
                showNotification(error.message || 'Failed to update role. Please try again.', 'error');
            });
        });
        
        // Store original value
        select.dataset.originalValue = select.value;
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/project-assignments/show.blade.php ENDPATH**/ ?>