{"__meta": {"id": "Xbb741eaf30d5b20b186a6b8833ba55b8", "datetime": "2025-08-19 14:01:19", "utime": 1755583279.146358, "method": "PUT", "uri": "/updateTaskStatus", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[14:01:18] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755583278.996959, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:19] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/updateTaskStatus", "message_html": null, "is_string": false, "label": "debug", "time": 1755583279.053758, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755583278.72126, "end": 1755583279.146381, "duration": 0.42512083053588867, "duration_str": "425ms", "measures": [{"label": "Booting", "start": 1755583278.72126, "relative_start": 0, "end": 1755583278.978027, "relative_end": 1755583278.978027, "duration": 0.25676703453063965, "duration_str": "257ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755583278.978038, "relative_start": 0.2567780017852783, "end": 1755583279.146384, "relative_end": 3.0994415283203125e-06, "duration": 0.16834592819213867, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23480304, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT updateTaskStatus", "middleware": "web", "controller": "App\\Http\\Controllers\\StatusController@updateTaskStatus", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "kanban.updateTaskStatus", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\StatusController.php&line=228\">\\app\\Http\\Controllers\\StatusController.php:228-257</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.00447, "accumulated_duration_str": "4.47ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 10.962}, {"sql": "select * from `tasks` where `tasks`.`id` = '122' limit 1", "type": "query", "params": [], "bindings": ["122"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 234}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00061, "duration_str": "610μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:234", "connection": "sagile", "start_percent": 10.962, "width_percent": 13.647}, {"sql": "select `slug` from `statuses` where `id` = '211'", "type": "query", "params": [], "bindings": ["211"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 239}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:239", "connection": "sagile", "start_percent": 24.609, "width_percent": 8.725}, {"sql": "select * from `tasks` where `tasks`.`id` = '120' limit 1", "type": "query", "params": [], "bindings": ["120"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 234}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:234", "connection": "sagile", "start_percent": 33.333, "width_percent": 10.515}, {"sql": "select `slug` from `statuses` where `id` = '212'", "type": "query", "params": [], "bindings": ["212"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 239}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:239", "connection": "sagile", "start_percent": 43.848, "width_percent": 12.528}, {"sql": "select * from `tasks` where `tasks`.`id` = '121' limit 1", "type": "query", "params": [], "bindings": ["121"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 234}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:234", "connection": "sagile", "start_percent": 56.376, "width_percent": 10.067}, {"sql": "select `slug` from `statuses` where `id` = '212'", "type": "query", "params": [], "bindings": ["212"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 239}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:239", "connection": "sagile", "start_percent": 66.443, "width_percent": 8.949}, {"sql": "update `tasks` set `status_id` = '212', `completion_date` = '2025-08-19 14:01:19', `tasks`.`updated_at` = '2025-08-19 14:01:19' where `id` = 121", "type": "query", "params": [], "bindings": ["212", "2025-08-19 14:01:19", "2025-08-19 14:01:19", "121"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 250}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:250", "connection": "sagile", "start_percent": 75.391, "width_percent": 24.609}]}, "models": {"data": {"App\\Task": 3, "App\\User": 1}, "count": 4}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/43/kanbanBoard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"path_info": "/updateTaskStatus", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1602667467 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1602667467\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-446919820 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>positions</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>taskId</span>\" => \"<span class=sf-dump-str title=\"3 characters\">122</span>\"\n      \"<span class=sf-dump-key>statusId</span>\" => \"<span class=sf-dump-str title=\"3 characters\">211</span>\"\n      \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>taskId</span>\" => \"<span class=sf-dump-str title=\"3 characters\">120</span>\"\n      \"<span class=sf-dump-key>statusId</span>\" => \"<span class=sf-dump-str title=\"3 characters\">212</span>\"\n      \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>taskId</span>\" => \"<span class=sf-dump-str title=\"3 characters\">121</span>\"\n      \"<span class=sf-dump-key>statusId</span>\" => \"<span class=sf-dump-str title=\"3 characters\">212</span>\"\n      \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>2</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-446919820\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-738593178 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">156</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/43/kanbanBoard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImRHSjU0dlJSQjcvcFc2QXdGNWZ4TGc9PSIsInZhbHVlIjoiZWFTTEZ0WlBFUlZFY2Q0MkJBSlE4TkpUbWJEQVNwMmxSNEJwWjVnVllIS05Dcm9yOHdqaWtNRzFPSDlnMU5VdWZ6cmh5SUlydXY3NEFKOXF1UDhqZ2FDeDh0KzNWd3d6cnJXZmpUdlR5WUFqU0ROMk1aeDM2ZjczU3ZmQVZTc1UiLCJtYWMiOiI3NDgxYzg2MTY1MmE0ZTkwOTkxODMxMmVjYzA1NWM4Yzk3Mzg3ZTQwNWQzMzk0N2E0ZWIzNmVkNjRlMTFmNzhjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Inh6UzRacTV2dmhBUm5SK2RTd0tHWlE9PSIsInZhbHVlIjoicTVGNmFJNkR0bVorRGs2NTJTMVpyWFl5aFlNekNNQlp0bXVwdmlxWUVZU3UvTUx3U09QN2F4aDJwanpuSXRBc0xVbytvMFd6SWJNUHcrYksvUEdUaExSQlpSc09hcFFOdGd5UzAxZFgzNXdmSzBCMEIzMm1PRjA0ZWthSUZXYngiLCJtYWMiOiJhMWMzZjkxNWNkNWIxNzgxYTJmM2JkZmJlMGQwNDE2Y2FhMWE2ZjUxNDkxN2VhZTQ3N2U0NjE5ODRmN2MyNWQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-738593178\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1241168359 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">62411</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/updateTaskStatus</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/updateTaskStatus</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/index.php/updateTaskStatus</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">156</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">156</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/43/kanbanBoard</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImRHSjU0dlJSQjcvcFc2QXdGNWZ4TGc9PSIsInZhbHVlIjoiZWFTTEZ0WlBFUlZFY2Q0MkJBSlE4TkpUbWJEQVNwMmxSNEJwWjVnVllIS05Dcm9yOHdqaWtNRzFPSDlnMU5VdWZ6cmh5SUlydXY3NEFKOXF1UDhqZ2FDeDh0KzNWd3d6cnJXZmpUdlR5WUFqU0ROMk1aeDM2ZjczU3ZmQVZTc1UiLCJtYWMiOiI3NDgxYzg2MTY1MmE0ZTkwOTkxODMxMmVjYzA1NWM4Yzk3Mzg3ZTQwNWQzMzk0N2E0ZWIzNmVkNjRlMTFmNzhjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Inh6UzRacTV2dmhBUm5SK2RTd0tHWlE9PSIsInZhbHVlIjoicTVGNmFJNkR0bVorRGs2NTJTMVpyWFl5aFlNekNNQlp0bXVwdmlxWUVZU3UvTUx3U09QN2F4aDJwanpuSXRBc0xVbytvMFd6SWJNUHcrYksvUEdUaExSQlpSc09hcFFOdGd5UzAxZFgzNXdmSzBCMEIzMm1PRjA0ZWthSUZXYngiLCJtYWMiOiJhMWMzZjkxNWNkNWIxNzgxYTJmM2JkZmJlMGQwNDE2Y2FhMWE2ZjUxNDkxN2VhZTQ3N2U0NjE5ODRmN2MyNWQxIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755583278.7213</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755583278</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1241168359\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2142323326 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2142323326\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1548419146 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:01:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImNCV1E4NXlFVEV5K212b3hQV1owYXc9PSIsInZhbHVlIjoialFZMkpNNHFhdUVHNW1EQ21uVUZUWWVxbGxGQnFHQ2I3UmxsUFBFSDJQQnBQaEtEaldFUzl0UU5nZHU3UTk3Q0NJTmtlYkJhVDRMR0hpRTRlT0Z0aW53TnZIVjVJeUh6RUhZU3U5VTlRMnFpRXJrU1NtWlRia2ZJL2RHajhRM0wiLCJtYWMiOiI4OWFhM2M2YmQzMTcyOGY0Y2RlMGU4NWQ5Y2ExM2FlNjA0ZDkxYjc0ZjNiM2YyOWZjYjkyYzkzNzhkMmMzZGZkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:01:19 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImxnN3lnL0pVZjAxcXN4SWtOMnp4QXc9PSIsInZhbHVlIjoiT0JpOU5hQjhISzljeTluOXpBanhKSEFRcUx4djNUUGpiMTJLSEdqWXZtcXN6bDVYQ0IvblFIQ0dlbDI3NWh3MnZBQjNKN3BWbHQ2V0hSc3VENVpvT2RwYkd3ZFRIUTdITFY5UlNoTnp5dGsrTVdGT3JoUVA1V2hkTUl5V2xlc24iLCJtYWMiOiI4MjVkMDM1MzlmZTI1Y2NlMThkYTE1YzcxYzBlYzZlYzI2OTU3OWE1NjdhZDRiYzFjOWQzMDIzNTY1Y2EyMjYyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:01:19 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImNCV1E4NXlFVEV5K212b3hQV1owYXc9PSIsInZhbHVlIjoialFZMkpNNHFhdUVHNW1EQ21uVUZUWWVxbGxGQnFHQ2I3UmxsUFBFSDJQQnBQaEtEaldFUzl0UU5nZHU3UTk3Q0NJTmtlYkJhVDRMR0hpRTRlT0Z0aW53TnZIVjVJeUh6RUhZU3U5VTlRMnFpRXJrU1NtWlRia2ZJL2RHajhRM0wiLCJtYWMiOiI4OWFhM2M2YmQzMTcyOGY0Y2RlMGU4NWQ5Y2ExM2FlNjA0ZDkxYjc0ZjNiM2YyOWZjYjkyYzkzNzhkMmMzZGZkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:01:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImxnN3lnL0pVZjAxcXN4SWtOMnp4QXc9PSIsInZhbHVlIjoiT0JpOU5hQjhISzljeTluOXpBanhKSEFRcUx4djNUUGpiMTJLSEdqWXZtcXN6bDVYQ0IvblFIQ0dlbDI3NWh3MnZBQjNKN3BWbHQ2V0hSc3VENVpvT2RwYkd3ZFRIUTdITFY5UlNoTnp5dGsrTVdGT3JoUVA1V2hkTUl5V2xlc24iLCJtYWMiOiI4MjVkMDM1MzlmZTI1Y2NlMThkYTE1YzcxYzBlYzZlYzI2OTU3OWE1NjdhZDRiYzFjOWQzMDIzNTY1Y2EyMjYyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:01:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1548419146\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-83966562 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/43/kanbanBoard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-83966562\", {\"maxDepth\":0})</script>\n"}}