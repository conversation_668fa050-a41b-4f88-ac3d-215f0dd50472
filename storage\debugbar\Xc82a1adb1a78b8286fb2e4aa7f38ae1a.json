{"__meta": {"id": "Xc82a1adb1a78b8286fb2e4aa7f38ae1a", "datetime": "2025-08-19 10:40:00", "utime": 1755571200.07401, "method": "GET", "uri": "/userstory/47/edit", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[10:39:59] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755571199.548038, "xdebug_link": null, "collector": "log"}, {"message": "[10:39:59] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/userstory/47/edit", "message_html": null, "is_string": false, "label": "debug", "time": 1755571199.641817, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755571199.199145, "end": 1755571200.074049, "duration": 0.8749039173126221, "duration_str": "875ms", "measures": [{"label": "Booting", "start": 1755571199.199145, "relative_start": 0, "end": 1755571199.523552, "relative_end": 1755571199.523552, "duration": 0.32440686225891113, "duration_str": "324ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755571199.523566, "relative_start": 0.3244209289550781, "end": 1755571200.074052, "relative_end": 3.0994415283203125e-06, "duration": 0.5504860877990723, "duration_str": "550ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24219992, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "userstory.edit (\\resources\\views\\userstory\\edit.blade.php)", "param_count": 10, "params": ["title", "userstory", "statuses", "teamlist", "generalNFRNames", "generalNFRIds", "selectedGeneralNFRIds", "selectedSpecificNFRIds", "specificNFRs", "specificNFRIds"], "type": "blade"}]}, "route": {"uri": "GET userstory/{userstory}/edit", "middleware": "web", "controller": "App\\Http\\Controllers\\UserStoryController@edit", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "userstory.edit", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\UserStoryController.php&line=246\">\\app\\Http\\Controllers\\UserStoryController.php:246-298</a>"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.00762, "accumulated_duration_str": "7.62ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 8.793}, {"sql": "select * from `user_stories` where `u_id` = '47' limit 1", "type": "query", "params": [], "bindings": ["47"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 55}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 859}, {"index": 18, "namespace": "middleware", "name": "bindings", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 20, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php:55", "connection": "sagile", "start_percent": 8.793, "width_percent": 6.299}, {"sql": "select * from `projects` where `projects`.`id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 248}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:248", "connection": "sagile", "start_percent": 15.092, "width_percent": 6.693}, {"sql": "select * from `statuses` where `project_id` = 42", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 251}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:251", "connection": "sagile", "start_percent": 21.785, "width_percent": 7.48}, {"sql": "select `username` from `teammappings` where `project_id` = '42'", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 254}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:254", "connection": "sagile", "start_percent": 29.265, "width_percent": 8.661}, {"sql": "select `secfeature_name` from `security_features`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 259}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:259", "connection": "sagile", "start_percent": 37.927, "width_percent": 6.43}, {"sql": "select `perfeature_name` from `performance_features`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 260}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:260", "connection": "sagile", "start_percent": 44.357, "width_percent": 6.693}, {"sql": "select `general_nfr_id` from `generalnfr`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\SpecificNFR.php", "line": 33}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 282}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\SpecificNFR.php:33", "connection": "sagile", "start_percent": 51.05, "width_percent": 6.037}, {"sql": "select `specific_nfr` from `nfr` where `general_nfr_id` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\SpecificNFR.php", "line": 37}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 282}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\app\\SpecificNFR.php:37", "connection": "sagile", "start_percent": 57.087, "width_percent": 7.48}, {"sql": "select `nfr_id` from `nfr` where `general_nfr_id` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\SpecificNFR.php", "line": 41}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 282}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\SpecificNFR.php:41", "connection": "sagile", "start_percent": 64.567, "width_percent": 10.761}, {"sql": "select `specific_nfr` from `nfr` where `general_nfr_id` = 2", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\SpecificNFR.php", "line": 37}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 282}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\SpecificNFR.php:37", "connection": "sagile", "start_percent": 75.328, "width_percent": 5.906}, {"sql": "select `nfr_id` from `nfr` where `general_nfr_id` = 2", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\SpecificNFR.php", "line": 41}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 282}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\SpecificNFR.php:41", "connection": "sagile", "start_percent": 81.234, "width_percent": 5.906}, {"sql": "select * from `generalnfr`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\GeneralNFR.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 283}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\GeneralNFR.php:41", "connection": "sagile", "start_percent": 87.139, "width_percent": 6.693}, {"sql": "select * from `user_story_general_nfr` where `user_story_id` = 47", "type": "query", "params": [], "bindings": ["47"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\UserStoryGeneralNfr.php", "line": 117}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 286}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\UserStoryGeneralNfr.php:117", "connection": "sagile", "start_percent": 93.832, "width_percent": 6.168}]}, "models": {"data": {"App\\UserStoryGeneralNfr": 2, "App\\SpecificNFR": 3, "App\\GeneralNFR": 4, "App\\PerformanceFeature": 1, "App\\SecurityFeature": 1, "App\\TeamMapping": 1, "App\\Status": 4, "App\\Project": 1, "App\\UserStory": 1, "App\\User": 1}, "count": 19}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/userstory/47/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/userstory/47/edit", "status_code": "<pre class=sf-dump id=sf-dump-1919162780 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1919162780\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-273198836 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-273198836\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-148685551 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-148685551\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1461592308 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/userstory/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlRmcTIvb1gxZ1BiQnlqM2FaZW9VREE9PSIsInZhbHVlIjoibDU4ODdycmViTmpPRzIzbFBiZURFcmx3Z0lDQWc4VTlCMGdjQ2Z1TDc2aTRqVFhVRHlNalpoOXFxVlo2UjR5WU5ZWGJxM3NvRERTRklhK3ZNeFUwdUd1SVJVYXJWZmFlOC9iaFR1Tkc4VHN2RW0wK3pIMWVXelhua1NLaWY4RXQiLCJtYWMiOiIyMzIzMWU4ZGVmYmNkYzI5ZTJhMzFjNDQ2MGRmMDg1ODM3ZTdhZWYyMzVlMTdhMjg4ZTM3MTg0OWRiNjgyYjRhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImV4c2JIOGVWeTRsV1Rpc2N6ZGxLeFE9PSIsInZhbHVlIjoiSXU3N1hKdHRENG1HWGthaVhVSlEwSTdhSDZrYXlaaTV5VkYxT2NvSDhXQ1Q2TkVvNlByaFljdzBqVDEvK2xSeFdpTldBUUkvd2dTRllhL1VodHRaOVRlaENwL3E0aDFzTXM5bG0zUkxRTFAyclRtUHpsZGR6QThPbUUySWZZVWEiLCJtYWMiOiI5MDg1Y2UyYmQzMmE2MzEzMTdkNjc2OTVkNzcwYTZjZmZmY2I3ZmYwMGY1YTI0NDgyMjZhMDFmODM4OGJhZDQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1461592308\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-701741281 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">65275</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"18 characters\">/userstory/47/edit</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"18 characters\">/userstory/47/edit</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"28 characters\">/index.php/userstory/47/edit</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/userstory/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlRmcTIvb1gxZ1BiQnlqM2FaZW9VREE9PSIsInZhbHVlIjoibDU4ODdycmViTmpPRzIzbFBiZURFcmx3Z0lDQWc4VTlCMGdjQ2Z1TDc2aTRqVFhVRHlNalpoOXFxVlo2UjR5WU5ZWGJxM3NvRERTRklhK3ZNeFUwdUd1SVJVYXJWZmFlOC9iaFR1Tkc4VHN2RW0wK3pIMWVXelhua1NLaWY4RXQiLCJtYWMiOiIyMzIzMWU4ZGVmYmNkYzI5ZTJhMzFjNDQ2MGRmMDg1ODM3ZTdhZWYyMzVlMTdhMjg4ZTM3MTg0OWRiNjgyYjRhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImV4c2JIOGVWeTRsV1Rpc2N6ZGxLeFE9PSIsInZhbHVlIjoiSXU3N1hKdHRENG1HWGthaVhVSlEwSTdhSDZrYXlaaTV5VkYxT2NvSDhXQ1Q2TkVvNlByaFljdzBqVDEvK2xSeFdpTldBUUkvd2dTRllhL1VodHRaOVRlaENwL3E0aDFzTXM5bG0zUkxRTFAyclRtUHpsZGR6QThPbUUySWZZVWEiLCJtYWMiOiI5MDg1Y2UyYmQzMmE2MzEzMTdkNjc2OTVkNzcwYTZjZmZmY2I3ZmYwMGY1YTI0NDgyMjZhMDFmODM4OGJhZDQwIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755571199.1991</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755571199</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701741281\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-705101543 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-705101543\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-572339926 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:39:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImlwRzNnN0kyQStHRzJuWlg3WXZPdnc9PSIsInZhbHVlIjoiR1IrQ3l2VGkyK0dBZjl2SEZrem5PYVBsU1dibWxveHVCUDBxRlZ1K3dLdlBieU96eGtPaTRjUFRFcmJIR0NoK3FCR2gxT3VxVGtQSkVtczhTcWc4ZzhEbGMxdTlmUFUwb2hiZGlscGtFd1p0ZVNsYW1HMTJFRXVSZGVqY1plVmEiLCJtYWMiOiIxZTBjOTdiMmE5MDI0NzAyM2VlNTZhYjNiYjAyYTM2ODkyOGU5NzAzNzJkNjY3NGNlMWY3OTUwYmEzMDNjM2MxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:40:00 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IngzSFhqMC9MUEVlZklRVndsRE1UQ3c9PSIsInZhbHVlIjoieE5xZGpPZG1YYkpvc2JWclYzQTkvWGE2YW5IRW1jL0lFaDlXZXVxVCt5Vkx2TzJVTmJKTXpZK1BnanRyUVpCTVNkUDBHV3VsOXBZRlU5SE1LZXVGamdLSXpkSFlGSHRjdnM5SzltL3BLdENDV2Nwa0dIK3hMVWI1Z3ZlSXF0VmkiLCJtYWMiOiI3YzYyOTEwMzdmZjk0NTdkZDE4MTY1Mjg4N2IzMmZiMDg1Y2YyMDExNzk0ZWEzNjlmYzc4MDgzYTM2YjQxOTM5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:40:00 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImlwRzNnN0kyQStHRzJuWlg3WXZPdnc9PSIsInZhbHVlIjoiR1IrQ3l2VGkyK0dBZjl2SEZrem5PYVBsU1dibWxveHVCUDBxRlZ1K3dLdlBieU96eGtPaTRjUFRFcmJIR0NoK3FCR2gxT3VxVGtQSkVtczhTcWc4ZzhEbGMxdTlmUFUwb2hiZGlscGtFd1p0ZVNsYW1HMTJFRXVSZGVqY1plVmEiLCJtYWMiOiIxZTBjOTdiMmE5MDI0NzAyM2VlNTZhYjNiYjAyYTM2ODkyOGU5NzAzNzJkNjY3NGNlMWY3OTUwYmEzMDNjM2MxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:40:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IngzSFhqMC9MUEVlZklRVndsRE1UQ3c9PSIsInZhbHVlIjoieE5xZGpPZG1YYkpvc2JWclYzQTkvWGE2YW5IRW1jL0lFaDlXZXVxVCt5Vkx2TzJVTmJKTXpZK1BnanRyUVpCTVNkUDBHV3VsOXBZRlU5SE1LZXVGamdLSXpkSFlGSHRjdnM5SzltL3BLdENDV2Nwa0dIK3hMVWI1Z3ZlSXF0VmkiLCJtYWMiOiI3YzYyOTEwMzdmZjk0NTdkZDE4MTY1Mjg4N2IzMmZiMDg1Y2YyMDExNzk0ZWEzNjlmYzc4MDgzYTM2YjQxOTM5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:40:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-572339926\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1426121256 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/userstory/47/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426121256\", {\"maxDepth\":0})</script>\n"}}