


<?php
    $inputId = $inputId ?? 'input_field';
    $counterId = $counterId ?? 'char_count';
    $maxLength = $maxLength ?? 100;
?>

<small class="text-muted">
    <span id="<?php echo e($counterId); ?>">0</span>/<?php echo e($maxLength); ?> characters
</small>

<?php if (! $__env->hasRenderedOnce('061f9a8a-9545-40b0-b2da-bf9934c7f1f8')): $__env->markAsRenderedOnce('061f9a8a-9545-40b0-b2da-bf9934c7f1f8'); ?>
<script>
    // Character Counter Utility
    window.CharCounter = window.CharCounter || {
        updateCount: function(inputId, counterId, maxLength) {
            const input = document.getElementById(inputId);
            const counter = document.getElementById(counterId);
            
            if (!input || !counter) return;
            
            const currentLength = input.value.length;
            counter.textContent = currentLength;
            
            if (currentLength >= maxLength) {
                counter.style.color = 'red';
            } else {
                counter.style.color = '';
            }
        },
        
        init: function(inputId, counterId, maxLength) {
            const input = document.getElementById(inputId);
            
            if (input) {
                // Initialize count
                this.updateCount(inputId, counterId, maxLength);
                
                // Add event listeners
                input.addEventListener('input', () => {
                    this.updateCount(inputId, counterId, maxLength);
                });
                
                input.addEventListener('keyup', () => {
                    this.updateCount(inputId, counterId, maxLength);
                });
                
                input.addEventListener('paste', () => {
                    setTimeout(() => {
                        this.updateCount(inputId, counterId, maxLength);
                    }, 10);
                });
            }
        }
    };
    
    // Legacy function for backward compatibility
    function updateCharCount(inputId, counterId, maxLength) {
        CharCounter.updateCount(inputId, counterId, maxLength);
    }
</script>
<?php endif; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        CharCounter.init('<?php echo e($inputId); ?>', '<?php echo e($counterId); ?>', <?php echo e($maxLength); ?>);
    });
</script>
<?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/inc/character-counter.blade.php ENDPATH**/ ?>