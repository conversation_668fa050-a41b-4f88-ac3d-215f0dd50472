{"__meta": {"id": "Xb71146d281b410720e3249080a2e41ec", "datetime": "2025-08-18 23:41:45", "utime": 1755531705.324613, "method": "PUT", "uri": "/nfr/general/2/specific/2", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:41:45] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755531705.168703, "xdebug_link": null, "collector": "log"}, {"message": "[23:41:45] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/nfr/general/2/specific/2", "message_html": null, "is_string": false, "label": "debug", "time": 1755531705.241216, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755531704.771355, "end": 1755531705.324643, "duration": 0.5532879829406738, "duration_str": "553ms", "measures": [{"label": "Booting", "start": 1755531704.771355, "relative_start": 0, "end": 1755531705.143862, "relative_end": 1755531705.143862, "duration": 0.37250709533691406, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755531705.143878, "relative_start": 0.37252306938171387, "end": 1755531705.324645, "relative_end": 2.1457672119140625e-06, "duration": 0.18076705932617188, "duration_str": "181ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24232520, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT nfr/general/{general_nfr_id}/specific/{nfr_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\SpecificNFRController@updateSpecific", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "nfr.updateSpecific", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\SpecificNFRController.php&line=159\">\\app\\Http\\Controllers\\SpecificNFRController.php:159-189</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00266, "accumulated_duration_str": "2.66ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 17.669}, {"sql": "select count(*) as aggregate from `nfr` where `specific_nfr` = 'Usability' and (`general_nfr_id` = '2' and `nfr_id` != '2')", "type": "query", "params": [], "bindings": ["Usability", "2", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 447}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "sagile", "start_percent": 17.669, "width_percent": 18.421}, {"sql": "select * from `nfr` where `nfr_id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\SpecificNFR.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 179}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\SpecificNFR.php:55", "connection": "sagile", "start_percent": 36.09, "width_percent": 20.301}, {"sql": "update `nfr` set `specific_nfr_desc` = 'The system should be easy to learn, efficient to use, and provide a smooth user experience.', `created_at` = '2025-08-18 23:41:45', `nfr`.`updated_at` = '2025-08-18 23:41:45' where `nfr_id` = 2", "type": "query", "params": [], "bindings": ["The system should be easy to learn, efficient to use, and provide a smooth user experience.", "2025-08-18 23:41:45", "2025-08-18 23:41:45", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 185}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00116, "duration_str": "1.16ms", "stmt_id": "\\app\\Http\\Controllers\\SpecificNFRController.php:185", "connection": "sagile", "start_percent": 56.391, "width_percent": 43.609}]}, "models": {"data": {"App\\SpecificNFR": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/nfr/general/2/specific/2\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "success": "Specific NFR updated successfully.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/nfr/general/2/specific/2", "status_code": "<pre class=sf-dump id=sf-dump-58420721 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-58420721\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1657009678 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1657009678\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-970131815 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>specific_nfr</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Usability</span>\"\n  \"<span class=sf-dump-key>specific_nfr_desc</span>\" => \"<span class=sf-dump-str title=\"91 characters\">The system should be easy to learn, efficient to use, and provide a smooth user experience.</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-970131815\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1986220375 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">196</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">http://127.0.0.1:8000/nfr/general/2/specific/2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlllN3cydVFJM1lEdHhpeVVVYkViRFE9PSIsInZhbHVlIjoiL2lPNnVDaG0rOE1kdGVBeE40Rkg2MDNnQ0NETVFreXNOVUxJZ2JZVXFMeSsvdGRUOS9NWDJvbTE4Tk9zb1UxVS9NL1pKaFM0TXBFckYyYVdVb0RKb2crSUFuT3ZORkdJa2drcTVQWjhUcVZ6QlFpa1RseUtXSE9mTmw0Zm43bHMiLCJtYWMiOiI4ZTkzZjU0MDhhMTgyZDk0ZjZiYjdmNDNiMThjZjViN2FmMWUxOWExMmNmNzRlYzlkMjNkNDg2NTZmY2ExODkyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImpJWFF2QkFwcldsVk82VHA2STZwcUE9PSIsInZhbHVlIjoiM2c2QXRXUElLYjl4cGxOS2JBTGFScTFJMmdESDd5MTNPRTQvVmlkUEprVDZGMXFBWDZad2MzQUNmNDVEWGJvOFRQM0xJTzhTVFJDbHZJeXpoQ0kxRGEzNDlNck1sK0toa1NmbTlOdng5amVHU2tCaitUV09MdTF3cG9WbysycXAiLCJtYWMiOiJkNDc3NjZjY2VmZjQ5NjM4ODI5ZjI4MTg3ZWUwODg0M2JhMTczNGVmYWMxOTUwYjEwZWY1NzcxNzdhZjk4ZjIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1986220375\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1470904056 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">53450</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/nfr/general/2/specific/2</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/nfr/general/2/specific/2</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"35 characters\">/index.php/nfr/general/2/specific/2</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">196</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">196</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://127.0.0.1:8000/nfr/general/2/specific/2</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlllN3cydVFJM1lEdHhpeVVVYkViRFE9PSIsInZhbHVlIjoiL2lPNnVDaG0rOE1kdGVBeE40Rkg2MDNnQ0NETVFreXNOVUxJZ2JZVXFMeSsvdGRUOS9NWDJvbTE4Tk9zb1UxVS9NL1pKaFM0TXBFckYyYVdVb0RKb2crSUFuT3ZORkdJa2drcTVQWjhUcVZ6QlFpa1RseUtXSE9mTmw0Zm43bHMiLCJtYWMiOiI4ZTkzZjU0MDhhMTgyZDk0ZjZiYjdmNDNiMThjZjViN2FmMWUxOWExMmNmNzRlYzlkMjNkNDg2NTZmY2ExODkyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImpJWFF2QkFwcldsVk82VHA2STZwcUE9PSIsInZhbHVlIjoiM2c2QXRXUElLYjl4cGxOS2JBTGFScTFJMmdESDd5MTNPRTQvVmlkUEprVDZGMXFBWDZad2MzQUNmNDVEWGJvOFRQM0xJTzhTVFJDbHZJeXpoQ0kxRGEzNDlNck1sK0toa1NmbTlOdng5amVHU2tCaitUV09MdTF3cG9WbysycXAiLCJtYWMiOiJkNDc3NjZjY2VmZjQ5NjM4ODI5ZjI4MTg3ZWUwODg0M2JhMTczNGVmYWMxOTUwYjEwZWY1NzcxNzdhZjk4ZjIyIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755531704.7714</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755531704</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1470904056\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1442632811 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1442632811\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-121129845 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:41:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/nfr/general/2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImhySTdVNFNzVkUvZ0dNNzdEUFU4ZVE9PSIsInZhbHVlIjoiV2JhZUptSzlwc1RtZGVaZDZ3bXh1aUhuV3RDRXhadFNNWDVrN2daY1dhSkVrNlVmSCs1YjNPenlDTStlaDhCSnZVRmhNS2ZMYlFVQmNDMjdhUHRnZmRvSzlZWXdyYWVORTRJVm0waVI2RXgvMmsyV3U0SVdsWmw0S2V2SFE5SmMiLCJtYWMiOiJlOTU3YzljMWY3Mzg1ZTQ2OWU2MDRlMzBmY2VjYmI5Yzk4YjhiZjc5OTVmODhiZmVhZjJkOTY5YjY0OThmNmNiIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:41:45 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjhuUFJpMnlUS0M3NFg0ZThJK2x4Unc9PSIsInZhbHVlIjoidnBuMy9zV0duaDBXeE9obEtiUFMrTjNzNXJYKzZjeFlTNjBRRVhoaGFoeTBWSmRIT0w2eUpweE9sM2txWHE1bmYxd2R4aWtvUGF6Uk5TZlRKdS9mTDVScXN1ZXJnQk9WRWlXT0JPSzEvem90aHFJdjZhNFhqNzY1ZG01Z0JIbUMiLCJtYWMiOiIwODNmOGE3YjMzMGM0YWRjMjc1ODM2OTk4M2YyY2IzNTk3Mzk0Njg4YmM0YzlkMDNiNGNjNTQ4NTliMGM4ZDM1IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:41:45 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImhySTdVNFNzVkUvZ0dNNzdEUFU4ZVE9PSIsInZhbHVlIjoiV2JhZUptSzlwc1RtZGVaZDZ3bXh1aUhuV3RDRXhadFNNWDVrN2daY1dhSkVrNlVmSCs1YjNPenlDTStlaDhCSnZVRmhNS2ZMYlFVQmNDMjdhUHRnZmRvSzlZWXdyYWVORTRJVm0waVI2RXgvMmsyV3U0SVdsWmw0S2V2SFE5SmMiLCJtYWMiOiJlOTU3YzljMWY3Mzg1ZTQ2OWU2MDRlMzBmY2VjYmI5Yzk4YjhiZjc5OTVmODhiZmVhZjJkOTY5YjY0OThmNmNiIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:41:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjhuUFJpMnlUS0M3NFg0ZThJK2x4Unc9PSIsInZhbHVlIjoidnBuMy9zV0duaDBXeE9obEtiUFMrTjNzNXJYKzZjeFlTNjBRRVhoaGFoeTBWSmRIT0w2eUpweE9sM2txWHE1bmYxd2R4aWtvUGF6Uk5TZlRKdS9mTDVScXN1ZXJnQk9WRWlXT0JPSzEvem90aHFJdjZhNFhqNzY1ZG01Z0JIbUMiLCJtYWMiOiIwODNmOGE3YjMzMGM0YWRjMjc1ODM2OTk4M2YyY2IzNTk3Mzk0Njg4YmM0YzlkMDNiNGNjNTQ4NTliMGM4ZDM1IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:41:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-121129845\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-560345518 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://127.0.0.1:8000/nfr/general/2/specific/2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Specific NFR updated successfully.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-560345518\", {\"maxDepth\":0})</script>\n"}}