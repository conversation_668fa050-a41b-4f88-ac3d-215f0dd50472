{"__meta": {"id": "Xf3560fc08176152dfebe52d6bad314e7", "datetime": "2025-08-19 14:17:37", "utime": 1755584257.89374, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[14:17:37] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584257.806357, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584257.290884, "end": 1755584257.893766, "duration": 0.602881908416748, "duration_str": "603ms", "measures": [{"label": "Booting", "start": 1755584257.290884, "relative_start": 0, "end": 1755584257.77187, "relative_end": 1755584257.77187, "duration": 0.4809858798980713, "duration_str": "481ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584257.77189, "relative_start": 0.4810059070587158, "end": 1755584257.893769, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "welcome (\\resources\\views\\welcome.blade.php)", "param_count": 0, "params": [], "type": "blade"}]}, "route": {"uri": "GET /", "middleware": "web", "uses": "Closure() {#1346\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#345 …}\n  file: \"C:\\xampp\\htdocs\\SAgilePMT_UTM\\routes\\web.php\"\n  line: \"97 to 99\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\routes\\web.php&line=97\">\\routes\\web.php:97-99</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c08f14W1YhAnmRzrfRfZnDJzinCpvqLv8e0tmSAK", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-327216271 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-327216271\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2114981961 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2114981961\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1345359323 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/project-assignments/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InZhc2E4MVBVSVExVWN4S3JsZ25zVkE9PSIsInZhbHVlIjoiV3FmaU9LOFFFSXU5Z2VWNFd2MytDeU8vVVlJWUwrSEJaTzhZbSt1bFVhVFBsSWVyN1JXa3NJaVJ5aC9ZMXhXd1R1aUxxU0hWMDl6RFdRd1Z6SEtvRHAyRFY0M1NLOXhoU2ZMTVhPZlA1N3p0alFzTUpjb21ZN1VRUVBwSkRpdUwiLCJtYWMiOiI2M2ExNjE2NmE0NWYwYjVjZGVlNmM0NWE1NTRkN2Y5MWM4NWExMWVmNGE5MmRiMTQ5OGVkNzIzNTk3ZTE0ZmVlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImM5YWNhbE81cHJuTHJxRFIxNi9yMkE9PSIsInZhbHVlIjoiM05GTFg0WlNvSk1IRE5uSW1SbVU0anlwMmczVWpXOFRMU1Q2SFFpWFFkeSt5WWRMMUNqL09TQ3hsL3oyWHVEVVRLTTBtOE9PRUJIMGF1M1lDQ01TU0JXSUxGM0hSNmVPV1RKbVBZRmVybktHc3ZnWm9KYXVpeTlRaXVUNXpEQ0oiLCJtYWMiOiI4ZmEyYTNkNWZlMGI1MjFhM2ZiY2MyMzJhOTQwMDhlYTFmMTJiNjk0ZTQwOTQ2MjRhMzBhZmU0YTdkYzM5NmQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1345359323\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-797960202 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">65195</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str>/</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/project-assignments/45</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InZhc2E4MVBVSVExVWN4S3JsZ25zVkE9PSIsInZhbHVlIjoiV3FmaU9LOFFFSXU5Z2VWNFd2MytDeU8vVVlJWUwrSEJaTzhZbSt1bFVhVFBsSWVyN1JXa3NJaVJ5aC9ZMXhXd1R1aUxxU0hWMDl6RFdRd1Z6SEtvRHAyRFY0M1NLOXhoU2ZMTVhPZlA1N3p0alFzTUpjb21ZN1VRUVBwSkRpdUwiLCJtYWMiOiI2M2ExNjE2NmE0NWYwYjVjZGVlNmM0NWE1NTRkN2Y5MWM4NWExMWVmNGE5MmRiMTQ5OGVkNzIzNTk3ZTE0ZmVlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImM5YWNhbE81cHJuTHJxRFIxNi9yMkE9PSIsInZhbHVlIjoiM05GTFg0WlNvSk1IRE5uSW1SbVU0anlwMmczVWpXOFRMU1Q2SFFpWFFkeSt5WWRMMUNqL09TQ3hsL3oyWHVEVVRLTTBtOE9PRUJIMGF1M1lDQ01TU0JXSUxGM0hSNmVPV1RKbVBZRmVybktHc3ZnWm9KYXVpeTlRaXVUNXpEQ0oiLCJtYWMiOiI4ZmEyYTNkNWZlMGI1MjFhM2ZiY2MyMzJhOTQwMDhlYTFmMTJiNjk0ZTQwOTQ2MjRhMzBhZmU0YTdkYzM5NmQ5IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584257.2909</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584257</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-797960202\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-126214103 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c08f14W1YhAnmRzrfRfZnDJzinCpvqLv8e0tmSAK</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7L0VzPhg1k9zRWhdQZhC6efgZf3Rxt3y4zaxOeJ2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-126214103\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2139204140 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:17:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImlUUStrbVJYTWY5c3NPZWQ5V3diRFE9PSIsInZhbHVlIjoiWitCdmkvVDMxTG5wOUxveUoydVJWSGh3SFZSbnVWcmtWeVlCV0NWSFEvZFFDeHlvb25wWjJpRUU2WFVPOTBjTFJUVUxPOFZiaHEvc3pkS2Y0bFhBeTIxQkpGSzE0d2lRSjc0YzM0RVVZUnkyUEFOelIvaUhMczRZdW04NDRMc28iLCJtYWMiOiIxYmM4NmZmNzQwNmI1MThkZGE3MDRlM2JiOTIzNTI3YzZhNzhlODY3ZDEzZjI5NGYyZDhlNzI4YTQxOGU3MGFjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:17:37 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlpUanZoRnFKQ3lLMkk0eUFYQU9mUFE9PSIsInZhbHVlIjoiT3FvemlzZFhVZmVDcWFuWmJVbmJCV3RqemNvSy9NQkxpUEpvK3JrU2hrVTEySEszWmliaVl1aU9HN3k1ajBSWWhTeTdUakVERmhHWUxWR05tWkFiRkhDcEFDeS9VaGhVM3NBUTh3UENBeC81b3ZJOHhIQUR1M0xYYW5ORm50N0giLCJtYWMiOiJlZThhYzQ4MWNhOGZkMDhjM2I5OWI1YzcwNmE5OGQyNTU2ZjAyNGRkM2E5NmI0YTExY2JjNWI1MzUwMjM2OWJlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:17:37 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImlUUStrbVJYTWY5c3NPZWQ5V3diRFE9PSIsInZhbHVlIjoiWitCdmkvVDMxTG5wOUxveUoydVJWSGh3SFZSbnVWcmtWeVlCV0NWSFEvZFFDeHlvb25wWjJpRUU2WFVPOTBjTFJUVUxPOFZiaHEvc3pkS2Y0bFhBeTIxQkpGSzE0d2lRSjc0YzM0RVVZUnkyUEFOelIvaUhMczRZdW04NDRMc28iLCJtYWMiOiIxYmM4NmZmNzQwNmI1MThkZGE3MDRlM2JiOTIzNTI3YzZhNzhlODY3ZDEzZjI5NGYyZDhlNzI4YTQxOGU3MGFjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:17:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlpUanZoRnFKQ3lLMkk0eUFYQU9mUFE9PSIsInZhbHVlIjoiT3FvemlzZFhVZmVDcWFuWmJVbmJCV3RqemNvSy9NQkxpUEpvK3JrU2hrVTEySEszWmliaVl1aU9HN3k1ajBSWWhTeTdUakVERmhHWUxWR05tWkFiRkhDcEFDeS9VaGhVM3NBUTh3UENBeC81b3ZJOHhIQUR1M0xYYW5ORm50N0giLCJtYWMiOiJlZThhYzQ4MWNhOGZkMDhjM2I5OWI1YzcwNmE5OGQyNTU2ZjAyNGRkM2E5NmI0YTExY2JjNWI1MzUwMjM2OWJlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:17:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2139204140\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1348306114 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c08f14W1YhAnmRzrfRfZnDJzinCpvqLv8e0tmSAK</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1348306114\", {\"maxDepth\":0})</script>\n"}}