
<?php if($errorMsg): ?>
    <h2>Error</h2>
    <div class="alert alert-danger">
        <?php echo e($errorMsg); ?>

    </div>
<?php else: ?>
    <h2 class="mb-4">Use Case Diagram</h2>
    <div class="text-center mb-4">
        <img src="<?php echo e($dataUri); ?>" alt="Use Case Diagram" class="img-fluid">
    </div>

    <?php if(isset($plantumlText)): ?>
        <div class="button-container mt-4">
            <a href="javascript:void(0);" class="btn btn-primary" id="edit-diagram-btn">
                Edit Diagram
            </a>
            
            <div class="form-check mt-2">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="editorModeToggle" checked>
                            <label class="form-check-label" for="editorModeToggle">Embedded Editor</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="createNewToggle">
                            <label class="form-check-label" for="createNewToggle">Create New Diagram</label>
                        </div>
                    </div>
                </div>
            </div>

            
            
            
        </div>
    <?php endif; ?>
<?php endif; ?>

<script>
// Store the data as variables instead of passing them directly to avoid syntax errors
const projectId = <?php echo e($project->id ?? 'null'); ?>;
const systemName = "<?php echo e(addslashes($project->proj_name ?? '')); ?>";
const plantumlText = `<?php echo $plantumlText ?? ''; ?>`;

// Function to decode HTML entities
function decodeHtmlEntities(text) {
    const textArea = document.createElement('textarea');
    textArea.innerHTML = text;
    return textArea.value;
}

// Display the decoded PlantUML text
document.addEventListener('DOMContentLoaded', function() {
    const plantumlDisplay = document.getElementById('plantuml-display');
    if (plantumlDisplay) {
        plantumlDisplay.textContent = decodeHtmlEntities(plantumlText);
    }
});

// Display debug messages both in console and on page
function logToDebug(message) {
    //disabled for production later
    // console.log("DEBUG: " + message);
    // const debugDiv = document.getElementById('debug-output');
    // if (debugDiv) {
    //     const timestamp = new Date().toLocaleTimeString();
    //     debugDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
    //     debugDiv.scrollTop = debugDiv.scrollHeight;
    // }
}

// Test if sagile-diagram-editor is reachable
function testConnection() {
    logToDebug("Testing connection to sagile-diagram-editor...");
    
    fetch('https://sagile-diagram-editor-wxyf9.ondigitalocean.app/api/healthcheck', {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => {
        logToDebug("Connection test response status: " + response.status);
        return response.text();
    })
    .then(text => {
        logToDebug("Connection test response: " + text);
    })
    .catch(error => {
        logToDebug("Connection test error: " + error.message + ". This suggests the API server might not be running or is not accessible.");
    });
}

// Main function to send data to the editor
function sendToEditor() {
    logToDebug("Function called with project ID: " + projectId);
    logToDebug("PlantUML text length: " + plantumlText.length);
    logToDebug("System name: " + systemName);
    
    // Check the toggle states
    const useEmbeddedMode = document.getElementById('editorModeToggle').checked;
    const createNew = document.getElementById('createNewToggle').checked;
    
    logToDebug("Using embedded editor mode: " + useEmbeddedMode);
    logToDebug("Create new diagram: " + createNew);
    
    // Test connection first
    testConnection();
    
    try {
        logToDebug("Preparing to send request to external API...");
        
        // Log the data we're sending
        const requestData = {
            project_id: projectId,
            plantuml: decodeHtmlEntities(plantumlText),  // Use decoded text
            system_name: systemName,
            create_new: createNew ? true : false  // Changed from null to false
        };
        logToDebug("Request data: " + JSON.stringify(requestData));
        
        // Send the actual request
        fetch('https://sagile-diagram-editor-wxyf9.ondigitalocean.app/api/diagrams/process-plantuml', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            logToDebug("Response received. Status: " + response.status);
            
            // Try to get response text even if it's an error
            return response.text().then(text => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}, body: ${text}`);
                }
                
                try {
                    return JSON.parse(text);
                } catch (e) {
                    logToDebug("Warning: Received non-JSON response: " + text);
                    throw new Error("Invalid JSON in response");
                }
            });
        })
        .then(data => {
            logToDebug("Response parsed successfully: " + JSON.stringify(data));
            if (data.success) {
                logToDebug("Redirecting to editor...");
                
                if (useEmbeddedMode) {
                    // Use the embedded editor - current implementation
                    window.location.href = `https://sagile-diagram-editor-wxyf9.ondigitalocean.app/${projectId}`;
                } else {
                    // Open in a new window/tabT
                    window.open(`https://sagile-diagram-editor-wxyf9.ondigitalocean.app/${projectId}`, '_blank');
                    logToDebug("Opened editor in new tab");
                }
            } else {
                throw new Error(data.message || 'Failed to process PlantUML');
            }
        })
        .catch(error => {
            logToDebug("Error in API call: " + error.message);
            console.error('Error:', error);
            alert('Error: ' + error.message);
        });
    } catch (error) {
        logToDebug("Exception caught: " + error.message);
        console.error('Exception:', error);
        alert('Exception: ' + error.message);
    }
}

// Save the toggle states to localStorage
function saveToggleStates() {
    const useEmbeddedMode = document.getElementById('editorModeToggle').checked;
    const createNew = document.getElementById('createNewToggle').checked;
    
    localStorage.setItem('ucdEditorEmbeddedMode', useEmbeddedMode);
    localStorage.setItem('ucdEditorCreateNew', createNew);
    
    logToDebug("Saved editor preferences: Embedded=" + useEmbeddedMode + ", CreateNew=" + createNew);
}

// Load the toggle states from localStorage
function loadToggleStates() {
    const savedEmbeddedMode = localStorage.getItem('ucdEditorEmbeddedMode');
    const savedCreateNew = localStorage.getItem('ucdEditorCreateNew');
    
    if (savedEmbeddedMode !== null) {
        document.getElementById('editorModeToggle').checked = savedEmbeddedMode === 'true';
    }
    
    if (savedCreateNew !== null) {
        document.getElementById('createNewToggle').checked = savedCreateNew === 'true';
    }
    
    logToDebug("Loaded editor preferences: Embedded=" + 
        (savedEmbeddedMode === 'true') + ", CreateNew=" + (savedCreateNew === 'true'));
}

// Log that the script has loaded
logToDebug("Script loaded and ready");

// Add event listeners
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('edit-diagram-btn').addEventListener('click', () => sendToEditor());
    document.getElementById('editorModeToggle').addEventListener('change', saveToggleStates);
    document.getElementById('createNewToggle').addEventListener('change', saveToggleStates);
    
    // Load saved preferences
    loadToggleStates();
});
</script>

<?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/ucd/index.blade.php ENDPATH**/ ?>