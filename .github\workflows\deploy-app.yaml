name: Deploy to DigitalOcean App Platform

on:
  push:
    branches:
      - Ivlyn_SAgile

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Deploy to DigitalOcean
        run: |
          curl -X PUT "https://api.digitalocean.com/v2/apps/5b9807b7-7caf-48e7-b1b1-0fee021436bf?i=4986cd" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer ${{ secrets.DO_TOKEN }}" \
            --data-binary @.do/app.yaml
