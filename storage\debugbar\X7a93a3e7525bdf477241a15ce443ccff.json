{"__meta": {"id": "X7a93a3e7525bdf477241a15ce443ccff", "datetime": "2025-08-18 23:43:13", "utime": 1755531793.239253, "method": "POST", "uri": "/upload-radar-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:43:13] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755531793.153026, "xdebug_link": null, "collector": "log"}, {"message": "[23:43:13] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/upload-radar-chart", "message_html": null, "is_string": false, "label": "debug", "time": 1755531793.228184, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755531792.804019, "end": 1755531793.239276, "duration": 0.4352569580078125, "duration_str": "435ms", "measures": [{"label": "Booting", "start": 1755531792.804019, "relative_start": 0, "end": 1755531793.126984, "relative_end": 1755531793.126984, "duration": 0.3229649066925049, "duration_str": "323ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755531793.126998, "relative_start": 0.3229789733886719, "end": 1755531793.239279, "relative_end": 3.0994415283203125e-06, "duration": 0.11228108406066895, "duration_str": "112ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23484440, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST upload-radar-chart", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@uploadRadarChart", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=141\">\\app\\Http\\Controllers\\CIGController.php:141-151</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.0005200000000000001, "accumulated_duration_str": "520μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/radar-data/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]"}, "request": {"path_info": "/upload-radar-chart", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1205563677 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1205563677\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1554068670 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"69426 characters\">data:image/png;base64,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</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554068670\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1834045353 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">69438</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkY2eUpaekh0ZHU5TTJHZ05EZFRJcGc9PSIsInZhbHVlIjoiTlRuMjNEK1lCTWp6UDBSay9CdkpPSU4xa29Jb0xqVEREVldhalJYWjRKRDQzVis1NVRINnlxSzBJZGdHeXV3RWlhUW4yTWNvaGpPT3RzRldtamY4NUxPTHhuSTh5cGE5Q2NtM1U4U3k0eitBZEhtMUJNZkR3bm1BYzZzSWF2NkciLCJtYWMiOiIzZmUxNzA5MjViZGFhNmE5YWZkODVkNDNlZDhlMWU3ZTM3YjNlZGUwNDlmOGNiOTc2NzhiNTVkMWY1ZmFiNTA1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik13cHhGODc2Qms0OFM4Y29qandsVVE9PSIsInZhbHVlIjoiSHh3R1RuamVOUDA4TTIrTE1YcEJERW9kT2loUkVTOFd5b3pRWlVVL2kxYnhJSXZpMy9xRU9MaGN3cUF1UE1tdXZydk53YzUvWmNXZi9PZ3FnSTd1UGdZLzVkQkRiTHJ1QnM0KzFITjZLL0dCWEFrUkttNnFobzJJTDBuNU1zdmgiLCJtYWMiOiI5MmRkNTE2YjI2NjY2YTE2OWYzNDk5NWRmZDA0YTNkNjg3ZmJjOTFlMmEwNWQ4OTBkNTAwMDdkNGI1NGZhMzE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1834045353\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-634662196 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54411</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/index.php/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">69438</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">69438</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkY2eUpaekh0ZHU5TTJHZ05EZFRJcGc9PSIsInZhbHVlIjoiTlRuMjNEK1lCTWp6UDBSay9CdkpPSU4xa29Jb0xqVEREVldhalJYWjRKRDQzVis1NVRINnlxSzBJZGdHeXV3RWlhUW4yTWNvaGpPT3RzRldtamY4NUxPTHhuSTh5cGE5Q2NtM1U4U3k0eitBZEhtMUJNZkR3bm1BYzZzSWF2NkciLCJtYWMiOiIzZmUxNzA5MjViZGFhNmE5YWZkODVkNDNlZDhlMWU3ZTM3YjNlZGUwNDlmOGNiOTc2NzhiNTVkMWY1ZmFiNTA1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik13cHhGODc2Qms0OFM4Y29qandsVVE9PSIsInZhbHVlIjoiSHh3R1RuamVOUDA4TTIrTE1YcEJERW9kT2loUkVTOFd5b3pRWlVVL2kxYnhJSXZpMy9xRU9MaGN3cUF1UE1tdXZydk53YzUvWmNXZi9PZ3FnSTd1UGdZLzVkQkRiTHJ1QnM0KzFITjZLL0dCWEFrUkttNnFobzJJTDBuNU1zdmgiLCJtYWMiOiI5MmRkNTE2YjI2NjY2YTE2OWYzNDk5NWRmZDA0YTNkNjg3ZmJjOTFlMmEwNWQ4OTBkNTAwMDdkNGI1NGZhMzE4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755531792.804</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755531792</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-634662196\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1165341589 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1165341589\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1608517360 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:43:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlgwbWw3Q2xhMnV2UGcrOGFrQUVsUXc9PSIsInZhbHVlIjoibEhDSHdTU01aZjNVcTg2Sm5nQ2JnZ0N6R0JKOGxNQ3VlOVQxNVF6bWFoMDBQU1JDdU5zRHVVWmdERXR2SU1rakJPV01Ba2tVblBxQ0tlbHpHcmJ4Vi9ST3JiZ2RNYmFjVDhhRjFpSGxtSG9BT1RFWE1sSVQzSno3OVk0c2xvZUUiLCJtYWMiOiIyYjNhNjBjMTk1ZGUwZjY4ZjhmMWMwY2Y4ZDVmMTRjNjFmOGRmNGZjMTJhOTg4ZGU4MGQwNWFmMWNiNWMwNjE0IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:13 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InpEMXY1QUtQSEFud3FGVDdKeVZiTmc9PSIsInZhbHVlIjoieHNHaU5xTlAySnlSRjlWV251SWt4VHdxRVhxL0JTV2p1dHFJTlpVNTR3V0JNNXpZclFVL2ZyRmNweDBuMy9EZXgxRG9oK1M1dEF5OTZNTndPWWJyMkYzSk5qSXBKb1IvWW5VcHkxdm9CNGU2SjQyV3gyL0lsdU9CbkNZZi9Xay8iLCJtYWMiOiI5MmY2MjljNGI1MjVjMGM0YWUzNmMyZWRjMGU1NWQ3YWE0ZWE2Nzc1NjFiZGVjZDI1ZjM5OTcwODZlY2Y4MWIwIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:13 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlgwbWw3Q2xhMnV2UGcrOGFrQUVsUXc9PSIsInZhbHVlIjoibEhDSHdTU01aZjNVcTg2Sm5nQ2JnZ0N6R0JKOGxNQ3VlOVQxNVF6bWFoMDBQU1JDdU5zRHVVWmdERXR2SU1rakJPV01Ba2tVblBxQ0tlbHpHcmJ4Vi9ST3JiZ2RNYmFjVDhhRjFpSGxtSG9BT1RFWE1sSVQzSno3OVk0c2xvZUUiLCJtYWMiOiIyYjNhNjBjMTk1ZGUwZjY4ZjhmMWMwY2Y4ZDVmMTRjNjFmOGRmNGZjMTJhOTg4ZGU4MGQwNWFmMWNiNWMwNjE0IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InpEMXY1QUtQSEFud3FGVDdKeVZiTmc9PSIsInZhbHVlIjoieHNHaU5xTlAySnlSRjlWV251SWt4VHdxRVhxL0JTV2p1dHFJTlpVNTR3V0JNNXpZclFVL2ZyRmNweDBuMy9EZXgxRG9oK1M1dEF5OTZNTndPWWJyMkYzSk5qSXBKb1IvWW5VcHkxdm9CNGU2SjQyV3gyL0lsdU9CbkNZZi9Xay8iLCJtYWMiOiI5MmY2MjljNGI1MjVjMGM0YWUzNmMyZWRjMGU1NWQ3YWE0ZWE2Nzc1NjFiZGVjZDI1ZjM5OTcwODZlY2Y4MWIwIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1608517360\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1786121398 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/radar-data/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1786121398\", {\"maxDepth\":0})</script>\n"}}