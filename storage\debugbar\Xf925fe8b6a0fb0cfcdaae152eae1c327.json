{"__meta": {"id": "Xf925fe8b6a0fb0cfcdaae152eae1c327", "datetime": "2025-08-18 23:30:08", "utime": 1755531008.125872, "method": "GET", "uri": "/backlogTest/42", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 34, "messages": [{"message": "[23:30:07] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755531007.764816, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:07] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/backlogTest/42", "message_html": null, "is_string": false, "label": "debug", "time": 1755531007.883248, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:07] LOG.info: BacklogController@index started {\"project_id\":\"42\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755531007.88345, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:07] LOG.info: Project found {\"project\":{\"id\":42,\"team_name\":\"<PERSON><PERSON><PERSON>'s team\",\"proj_name\":\"Food Ordering System\",\"proj_desc\":\"A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.\",\"start_date\":\"2025-08-18\",\"end_date\":\"2026-01-25\",\"shareable_slug\":null,\"created_at\":\"2025-08-18T15:02:05.000000Z\",\"updated_at\":\"2025-08-18T15:02:05.000000Z\"}}", "message_html": null, "is_string": false, "label": "info", "time": 1755531007.914289, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:07] LOG.info: Explicit active sprint check completed {\"proj_name\":\"Food Ordering System\",\"active_sprint_found\":true,\"active_sprint_data\":{\"sprint_id\":34,\"sprint_name\":\"Sprint 1\",\"sprint_desc\":\"this is sprint 1\",\"start_sprint\":\"2025-08-18\",\"end_sprint\":\"2025-09-01\",\"active_sprint\":1,\"proj_name\":\"Food Ordering System\",\"users_name\":\"ivlyn\",\"created_at\":\"2025-08-18T15:30:07.000000Z\",\"updated_at\":\"2025-08-18T15:30:07.000000Z\"}}", "message_html": null, "is_string": false, "label": "info", "time": 1755531007.94638, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:07] LOG.info: Active sprint ID determined {\"active_sprint_id\":34}", "message_html": null, "is_string": false, "label": "info", "time": 1755531007.946569, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:07] LOG.info: Processing user stories with active sprint {\"active_sprint_id\":34}", "message_html": null, "is_string": false, "label": "info", "time": 1755531007.946689, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.info: All non-done user stories retrieved {\"total_user_stories\":1,\"user_story_ids\":[45],\"done_status_id\":208}", "message_html": null, "is_string": false, "label": "info", "time": 1755531008.004624, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: Processing user story {\"user_story_id\":45,\"user_story_sprint_id\":\"0\",\"active_sprint_id\":34}", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.00492, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: User story not in active sprint - adding to collection {\"user_story_id\":45}", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.005084, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.info: User stories filtering completed with active sprint {\"filtered_user_stories_count\":1,\"filtered_user_story_ids\":[45]}", "message_html": null, "is_string": false, "label": "info", "time": 1755531008.005498, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.info: Starting task retrieval for user stories", "message_html": null, "is_string": false, "label": "info", "time": 1755531008.005577, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: Processing tasks for user story {\"user_story_id\":45}", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.03388, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: Tasks filtered for active sprint and done status {\"user_story_id\":45,\"active_sprint_id\":34,\"done_task_status_id\":208,\"tasks_count\":1,\"task_ids\":[103]}", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.065492, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.info: Task retrieval completed {\"total_user_stories_with_tasks\":1,\"total_tasks_retrieved\":1}", "message_html": null, "is_string": false, "label": "info", "time": 1755531008.065645, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.info: BacklogController@index completed successfully {\"project_id\":\"42\",\"active_sprint_id\":34,\"user_stories_count\":1,\"tasks_by_user_story_count\":1,\"note\":\"Filtered out user stories with Done status and tasks with done status\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755531008.065845, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: Gate check for permission: addToSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.079741, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.082992, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.083132, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: Gate check for permission: addToSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.091083, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.092922, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.093032, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: Gate check for permission: addUserStory_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.093744, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.095292, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.095392, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: Gate check for permission: beginSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.095992, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.097576, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.097691, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: Gate check for permission: addToSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.098439, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.099909, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.099989, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: Gate check for permission: endSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.100504, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.10225, "xdebug_link": null, "collector": "log"}, {"message": "[23:30:08] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755531008.10236, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755531007.237091, "end": 1755531008.125982, "duration": 0.8888909816741943, "duration_str": "889ms", "measures": [{"label": "Booting", "start": 1755531007.237091, "relative_start": 0, "end": 1755531007.732353, "relative_end": 1755531007.732353, "duration": 0.49526190757751465, "duration_str": "495ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755531007.732366, "relative_start": 0.49527502059936523, "end": 1755531008.125986, "relative_end": 4.0531158447265625e-06, "duration": 0.39362001419067383, "duration_str": "394ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24104800, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "backlogTest.index (\\resources\\views\\backlogTest\\index.blade.php)", "param_count": 4, "params": ["project", "userStories", "tasksByUserStory", "activeSprint"], "type": "blade"}]}, "route": {"uri": "GET backlogTest/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\BacklogController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "backlogTest.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\BacklogController.php&line=19\">\\app\\Http\\Controllers\\BacklogController.php:19-239</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00631, "accumulated_duration_str": "6.31ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 10.935}, {"sql": "select * from `projects` where `projects`.`id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 24}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:24", "connection": "sagile", "start_percent": 10.935, "width_percent": 12.678}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` = 1 limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 30}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00138, "duration_str": "1.38ms", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:30", "connection": "sagile", "start_percent": 23.613, "width_percent": 21.87}, {"sql": "select * from `statuses` where `project_id` = '42' and `title` = 'Done' limit 1", "type": "query", "params": [], "bindings": ["42", "Done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 75}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:75", "connection": "sagile", "start_percent": 45.483, "width_percent": 13.629}, {"sql": "select * from `user_stories` where `proj_id` = '42' and `status_id` != 208", "type": "query", "params": [], "bindings": ["42", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 82}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:82", "connection": "sagile", "start_percent": 59.113, "width_percent": 14.422}, {"sql": "select * from `statuses` where `project_id` = '42' and `slug` = 'done' limit 1", "type": "query", "params": [], "bindings": ["42", "done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 173}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:173", "connection": "sagile", "start_percent": 73.534, "width_percent": 12.837}, {"sql": "select * from `tasks` where `userstory_id` = 45 and (`sprint_id` != 34 or `sprint_id` is null or `sprint_id` = 0) and `status_id` != 208", "type": "query", "params": [], "bindings": ["45", "34", "0", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 191}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:191", "connection": "sagile", "start_percent": 86.371, "width_percent": 13.629}]}, "models": {"data": {"App\\Task": 1, "App\\UserStory": 1, "App\\Status": 2, "App\\Sprint": 1, "App\\Project": 1, "App\\User": 1}, "count": 7}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 6, "messages": [{"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755531008.088917, "xdebug_link": null}, {"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-21835184 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21835184\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755531008.093325, "xdebug_link": null}, {"message": "[\n  ability => addUserStory_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-559145001 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">addUserStory_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-559145001\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755531008.095655, "xdebug_link": null}, {"message": "[\n  ability => beginSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1785005786 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">beginSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1785005786\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755531008.098002, "xdebug_link": null}, {"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1349572632 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349572632\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755531008.100224, "xdebug_link": null}, {"message": "[\n  ability => endSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1076600939 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">endSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076600939\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755531008.102621, "xdebug_link": null}]}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backlogTest/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755527110\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/backlogTest/42", "status_code": "<pre class=sf-dump id=sf-dump-837284575 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-837284575\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1202237046 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1202237046\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-359965768 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-359965768\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1590464638 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Im1teGQ3WkhNb2RuYys3Q2lhVXcxN0E9PSIsInZhbHVlIjoiZ1N3Q2VPUVlqdTRmUnFUR0lvK1dXK3QyY3R5UkovSnhwYmJMMVNHais3RTZNOGY0V1pFQkVWWGlJbjhHMzFMclVoNm9EWjZKZ3p2RDlmY2h2U1JQcUlYekVnYmtPU25zZUhOS0ovY3R3dVRncXIzQ3NJc0wwcHp0aDJCWnZsS0EiLCJtYWMiOiJjNDkxMDQ2NWMxNGNhNDYwZWY0Y2U1NWM4ODY4Zjg4NWU3ODk5OTlkYmM4NTdkNGZmZDY0OTAzNGU0YWQ1ZTg3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkxGQmg5ZW1zd2dEQTArdVpDY21XaVE9PSIsInZhbHVlIjoibnk5Ri9VOGVkcVBmcDhFcHVFT0dLbTlrQm1sK0h5REhwWDcvenZVOStWc0o0SUQrQnNrU1pvbGZlUXBNbjIyNnFvYUhITVdvVU56VW1xQmpReUNwWFQxME5uNHE1T3dhKzBRb0tuNUVlU1ZMQjRHTU82TUZyL2R1NEoyVHd1S1AiLCJtYWMiOiIyODRkY2VkNmExNzdkOTdlODc2ZGY3MDdhYWEyZjNjODJiNzkzNzA2Njg5YzUyZjdlYTI0NjYyYTA2ZTc1NDg3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1590464638\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1634591361 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">62246</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/index.php/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Im1teGQ3WkhNb2RuYys3Q2lhVXcxN0E9PSIsInZhbHVlIjoiZ1N3Q2VPUVlqdTRmUnFUR0lvK1dXK3QyY3R5UkovSnhwYmJMMVNHais3RTZNOGY0V1pFQkVWWGlJbjhHMzFMclVoNm9EWjZKZ3p2RDlmY2h2U1JQcUlYekVnYmtPU25zZUhOS0ovY3R3dVRncXIzQ3NJc0wwcHp0aDJCWnZsS0EiLCJtYWMiOiJjNDkxMDQ2NWMxNGNhNDYwZWY0Y2U1NWM4ODY4Zjg4NWU3ODk5OTlkYmM4NTdkNGZmZDY0OTAzNGU0YWQ1ZTg3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkxGQmg5ZW1zd2dEQTArdVpDY21XaVE9PSIsInZhbHVlIjoibnk5Ri9VOGVkcVBmcDhFcHVFT0dLbTlrQm1sK0h5REhwWDcvenZVOStWc0o0SUQrQnNrU1pvbGZlUXBNbjIyNnFvYUhITVdvVU56VW1xQmpReUNwWFQxME5uNHE1T3dhKzBRb0tuNUVlU1ZMQjRHTU82TUZyL2R1NEoyVHd1S1AiLCJtYWMiOiIyODRkY2VkNmExNzdkOTdlODc2ZGY3MDdhYWEyZjNjODJiNzkzNzA2Njg5YzUyZjdlYTI0NjYyYTA2ZTc1NDg3IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755531007.2371</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755531007</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1634591361\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1434673788 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1434673788\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1219410128 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:30:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImpLZWQ3YWZQUStvbEVVVTV1N3BvTVE9PSIsInZhbHVlIjoia0tZTlc4RzE3Uld0RGNCSEFCK3NaaFdPSWMvQlNHMUZ5UzVlbHVyNXMrVXk3SzJhbm9hdjN5QU9zcGFwem5acll4Z2ZKbW9pR25zc2w0aFN5S3RNL3k4NFpzeFpFRmJ0M29MMEg1NDlFL08zNFZZK2YvSmYwbWVPeGJ2M2xuMnoiLCJtYWMiOiJjNmY0ZWQzYjdlNmRjMWQ0NGRiODBlMGFhZDYyMzcwNzhhYjE5NmZkMDNjYjM1ZjIyYjRmNTczMDA1ZWE0NzRiIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:30:08 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImVtZ0xDOXl3SUcyVnlqRFc4b1JkZ2c9PSIsInZhbHVlIjoiVDFPeXh2eFFWZ2pZWnhmZFdIaWJ4cVBZaFJBcUJGQTUzQnJibWR3M2R1NGhEMEpWL3NzcG9QbXg2ZVVEOHNNekdwU2dwV1EvL3JvV1gwZGZrKy9sZFQ2NUU2b0pFU2s2d3ZMYVpaZ0VVQXI3cTVVcDNHQUNXYjdvek1TdEhkbGYiLCJtYWMiOiJiYjBmMTE4YWQ4OWJjN2EzM2U0Y2QwZTBhNDIxZTFlY2ViZTZkMmExNmJmYWUxMzNhNDA4NTUwOGEzNTYzNGFmIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:30:08 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImpLZWQ3YWZQUStvbEVVVTV1N3BvTVE9PSIsInZhbHVlIjoia0tZTlc4RzE3Uld0RGNCSEFCK3NaaFdPSWMvQlNHMUZ5UzVlbHVyNXMrVXk3SzJhbm9hdjN5QU9zcGFwem5acll4Z2ZKbW9pR25zc2w0aFN5S3RNL3k4NFpzeFpFRmJ0M29MMEg1NDlFL08zNFZZK2YvSmYwbWVPeGJ2M2xuMnoiLCJtYWMiOiJjNmY0ZWQzYjdlNmRjMWQ0NGRiODBlMGFhZDYyMzcwNzhhYjE5NmZkMDNjYjM1ZjIyYjRmNTczMDA1ZWE0NzRiIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:30:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImVtZ0xDOXl3SUcyVnlqRFc4b1JkZ2c9PSIsInZhbHVlIjoiVDFPeXh2eFFWZ2pZWnhmZFdIaWJ4cVBZaFJBcUJGQTUzQnJibWR3M2R1NGhEMEpWL3NzcG9QbXg2ZVVEOHNNekdwU2dwV1EvL3JvV1gwZGZrKy9sZFQ2NUU2b0pFU2s2d3ZMYVpaZ0VVQXI3cTVVcDNHQUNXYjdvek1TdEhkbGYiLCJtYWMiOiJiYjBmMTE4YWQ4OWJjN2EzM2U0Y2QwZTBhNDIxZTFlY2ViZTZkMmExNmJmYWUxMzNhNDA4NTUwOGEzNTYzNGFmIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:30:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1219410128\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1784062331 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755527110</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1784062331\", {\"maxDepth\":0})</script>\n"}}