{"__meta": {"id": "Xa3f1347fa0bc2cd1f7664e61518d9125", "datetime": "2025-08-18 23:28:36", "utime": 1755530916.853787, "method": "GET", "uri": "/backlogTest/42", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 18, "messages": [{"message": "[23:28:36] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755530916.248797, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/backlogTest/42", "message_html": null, "is_string": false, "label": "debug", "time": 1755530916.604491, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.info: BacklogController@index started {\"project_id\":\"42\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755530916.604771, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.info: Project found {\"project\":{\"id\":42,\"team_name\":\"<PERSON><PERSON><PERSON>'s team\",\"proj_name\":\"Food Ordering System\",\"proj_desc\":\"A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.\",\"start_date\":\"2025-08-18\",\"end_date\":\"2026-01-25\",\"shareable_slug\":null,\"created_at\":\"2025-08-18T15:02:05.000000Z\",\"updated_at\":\"2025-08-18T15:02:05.000000Z\"}}", "message_html": null, "is_string": false, "label": "info", "time": 1755530916.631058, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.info: Explicit active sprint check completed {\"proj_name\":\"Food Ordering System\",\"active_sprint_found\":false,\"active_sprint_data\":null}", "message_html": null, "is_string": false, "label": "info", "time": 1755530916.65213, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.info: No explicit active sprint found, checking NULL active_sprint sprints", "message_html": null, "is_string": false, "label": "info", "time": 1755530916.652213, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.info: Active sprint ID determined {\"active_sprint_id\":null}", "message_html": null, "is_string": false, "label": "info", "time": 1755530916.67356, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.info: No active sprint - getting backlog user stories", "message_html": null, "is_string": false, "label": "info", "time": 1755530916.673642, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.info: Backlog non-done user stories retrieved {\"backlog_user_stories_count\":0,\"backlog_user_story_ids\":[],\"done_status_id\":208}", "message_html": null, "is_string": false, "label": "info", "time": 1755530916.721073, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.info: Starting task retrieval for user stories", "message_html": null, "is_string": false, "label": "info", "time": 1755530916.721155, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.info: Task retrieval completed {\"total_user_stories_with_tasks\":0,\"total_tasks_retrieved\":0}", "message_html": null, "is_string": false, "label": "info", "time": 1755530916.747294, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.info: BacklogController@index completed successfully {\"project_id\":\"42\",\"active_sprint_id\":null,\"user_stories_count\":0,\"tasks_by_user_story_count\":0,\"note\":\"Filtered out user stories with Done status and tasks with done status\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755530916.747409, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.debug: Gate check for permission: addUserStory_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755530916.772393, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team i<PERSON><PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755530916.778138, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755530916.778244, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.debug: Gate check for permission: beginSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755530916.798396, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team i<PERSON><PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755530916.799912, "xdebug_link": null, "collector": "log"}, {"message": "[23:28:36] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755530916.800014, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755530915.318423, "end": 1755530916.853836, "duration": 1.5354130268096924, "duration_str": "1.54s", "measures": [{"label": "Booting", "start": 1755530915.318423, "relative_start": 0, "end": 1755530916.183744, "relative_end": 1755530916.183744, "duration": 0.8653209209442139, "duration_str": "865ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755530916.183767, "relative_start": 0.8653440475463867, "end": 1755530916.853839, "relative_end": 2.86102294921875e-06, "duration": 0.6700718402862549, "duration_str": "670ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24137424, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "backlogTest.index (\\resources\\views\\backlogTest\\index.blade.php)", "param_count": 4, "params": ["project", "userStories", "tasksByUserStory", "activeSprint"], "type": "blade"}, {"name": "inc.character-counter (\\resources\\views\\inc\\character-counter.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "project", "userStories", "tasksByUserStory", "activeSprint", "inputId", "counterId", "max<PERSON><PERSON><PERSON>"], "type": "blade"}, {"name": "inc.character-counter (\\resources\\views\\inc\\character-counter.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "project", "userStories", "tasksByUserStory", "activeSprint", "inputId", "counterId", "max<PERSON><PERSON><PERSON>"], "type": "blade"}]}, "route": {"uri": "GET backlogTest/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\BacklogController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "backlogTest.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\BacklogController.php&line=19\">\\app\\Http\\Controllers\\BacklogController.php:19-239</a>"}, "queries": {"nb_statements": 13, "nb_failed_statements": 0, "accumulated_duration": 0.01013, "accumulated_duration_str": "10.13ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0008900000000000001, "duration_str": "890μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 8.786}, {"sql": "select exists(select * from `user_role` where `user_id` = 30 and `role_id` = 0) as `exists`", "type": "query", "params": [], "bindings": ["30", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "\\app\\Traits\\HasCachedPermissions.php", "line": 279}, {"index": 10, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 13, "namespace": null, "name": "\\app\\Traits\\HasCachedPermissions.php", "line": 275}, {"index": 14, "namespace": null, "name": "\\app\\User.php", "line": 202}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Traits\\HasCachedPermissions.php:279", "connection": "sagile", "start_percent": 8.786, "width_percent": 8.983}, {"sql": "select `team_name`, `role_name`, `project_id` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": "middleware", "name": "load.permissions", "line": 81}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 16, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "middleware::load.permissions:81", "connection": "sagile", "start_percent": 17.769, "width_percent": 9.181}, {"sql": "select `role_id` from `user_role` where `user_id` = 30", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": "middleware", "name": "load.permissions", "line": 87}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 16, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "middleware::load.permissions:87", "connection": "sagile", "start_percent": 26.95, "width_percent": 8.588}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Team Manager' and `project_id` is null limit 1", "type": "query", "params": [], "bindings": ["Team Manager"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 35.538, "width_percent": 7.009}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Project Manager' and `project_id` = 42 limit 1", "type": "query", "params": [], "bindings": ["Project Manager", "42"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 42.547, "width_percent": 7.404}, {"sql": "select `permission`.`key` from `permission_role` inner join `permission` on `permission`.`id` = `permission_role`.`permission_id` where `permission_role`.`role_id` = 31", "type": "query", "params": [], "bindings": ["31"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": "middleware", "name": "load.permissions", "line": 121}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 16, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "middleware::load.permissions:121", "connection": "sagile", "start_percent": 49.951, "width_percent": 7.799}, {"sql": "select * from `projects` where `projects`.`id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 24}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:24", "connection": "sagile", "start_percent": 57.749, "width_percent": 6.12}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` = 1 limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 30}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:30", "connection": "sagile", "start_percent": 63.87, "width_percent": 7.7}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` is null and `start_sprint` <= '2025-08-18 23:28:36' and `end_sprint` >= '2025-08-18 23:28:36' limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System", "2025-08-18 23:28:36", "2025-08-18 23:28:36"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 48}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:48", "connection": "sagile", "start_percent": 71.57, "width_percent": 6.417}, {"sql": "select * from `statuses` where `project_id` = '42' and `title` = 'Done' limit 1", "type": "query", "params": [], "bindings": ["42", "Done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 144}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:144", "connection": "sagile", "start_percent": 77.986, "width_percent": 8.49}, {"sql": "select * from `user_stories` where `proj_id` = '42' and (`sprint_id` = 0 or `sprint_id` is null) and `status_id` != 208", "type": "query", "params": [], "bindings": ["42", "0", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 157}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:157", "connection": "sagile", "start_percent": 86.476, "width_percent": 7.009}, {"sql": "select * from `statuses` where `project_id` = '42' and `slug` = 'done' limit 1", "type": "query", "params": [], "bindings": ["42", "done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 173}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:173", "connection": "sagile", "start_percent": 93.485, "width_percent": 6.515}]}, "models": {"data": {"App\\Status": 2, "App\\Project": 1, "App\\User": 1}, "count": 4}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[\n  ability => addUserStory_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">addUserStory_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755530916.79212, "xdebug_link": null}, {"message": "[\n  ability => beginSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1383338247 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">beginSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1383338247\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755530916.800292, "xdebug_link": null}]}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backlogTest/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755527110\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/backlogTest/42", "status_code": "<pre class=sf-dump id=sf-dump-1703709687 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1703709687\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1353353232 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1353353232\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1970269367 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1970269367\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1757315623 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InhCdGU2bVkzVDhvcFYzMkR2U3d3Rmc9PSIsInZhbHVlIjoidlo2QzMvdU5nRW53QXMzd0FLSERoV0huZmsxako5ajlaN2xBTUF1OXh0ZnVSRlRYN2xPZzR6bWY0aG03cnlnNW13cE00ZHhLYzRjK1NCbjlXNkdlZW1Kcnkxci9ueTJyZTFYZmUvcU5WM0x3R0xHaEQ4a2x3a1kvNHVRcTNUZ0UiLCJtYWMiOiJiN2VlYjkwY2JiZjZlN2EyNzE2ODgwMmYxNmQ3ZmQ5MDUyZTQ4ODRlZTBlYmJkYzc2OTI0ZTQyNjY0ZGQxZDNmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ii9IYytjYUhLTzVidk5PSEtsQWNlQWc9PSIsInZhbHVlIjoiWDErN0w4ZUVGM3J2aWpSZWsrT1FqL2FNc3JMNldGNTBMZUxoSTVDRld2VG9Xb2kxQlg4QVNZVWl0N2IxejhRa3Q4VzVJWU1KTmlrKzVBN2RHZkhEY2hZc2RySlV5ZTVxNHZXb05LRnpHT3IrWmgzbUpxS2tSTy9CS2VhUCtQOW8iLCJtYWMiOiJmOTM0N2Y2NDM4M2EyNzZkYTkwNDVkMWVhZjNkZWRjNGI5YTljOTlmYWI1NWZiZTFmNjlhZWJhZTMxMjc3ZDlkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1757315623\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-301496316 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59228</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/index.php/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InhCdGU2bVkzVDhvcFYzMkR2U3d3Rmc9PSIsInZhbHVlIjoidlo2QzMvdU5nRW53QXMzd0FLSERoV0huZmsxako5ajlaN2xBTUF1OXh0ZnVSRlRYN2xPZzR6bWY0aG03cnlnNW13cE00ZHhLYzRjK1NCbjlXNkdlZW1Kcnkxci9ueTJyZTFYZmUvcU5WM0x3R0xHaEQ4a2x3a1kvNHVRcTNUZ0UiLCJtYWMiOiJiN2VlYjkwY2JiZjZlN2EyNzE2ODgwMmYxNmQ3ZmQ5MDUyZTQ4ODRlZTBlYmJkYzc2OTI0ZTQyNjY0ZGQxZDNmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ii9IYytjYUhLTzVidk5PSEtsQWNlQWc9PSIsInZhbHVlIjoiWDErN0w4ZUVGM3J2aWpSZWsrT1FqL2FNc3JMNldGNTBMZUxoSTVDRld2VG9Xb2kxQlg4QVNZVWl0N2IxejhRa3Q4VzVJWU1KTmlrKzVBN2RHZkhEY2hZc2RySlV5ZTVxNHZXb05LRnpHT3IrWmgzbUpxS2tSTy9CS2VhUCtQOW8iLCJtYWMiOiJmOTM0N2Y2NDM4M2EyNzZkYTkwNDVkMWVhZjNkZWRjNGI5YTljOTlmYWI1NWZiZTFmNjlhZWJhZTMxMjc3ZDlkIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755530915.3184</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755530915</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-301496316\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1916493707 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1916493707\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-861630963 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:28:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlU3OWRxOE1VaUV4cmlHWkZZdFhSemc9PSIsInZhbHVlIjoiYkZxRVpYMTBvZkFHWGdReXBrL2drZHZnUVo3T2MyeWZPdGRWSFc1dllCL2dPSENRUFlZREozRmVwekhJaTJucWFFS0ZhU2k0Q0ZGeFhmUWtQVUdzcmlIb0JpZEhOclNMT1pFVDczNkE5TUdjRmZaTzhuWGtoWU53dHJiaVZNYmEiLCJtYWMiOiJhMGNkYjk3MDU0ZmRiZGJiZGE4NGJhYjNlOTRjNTYxZDFhZTA4NzM2NDY2MmJmMGNhZTkzODhlZDdkMmE5NzMwIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:28:36 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InRWRHk3aUZwMlJqRVZrNnJtTEZ3YVE9PSIsInZhbHVlIjoibmE0MDhCNkRXSCtxQStYNktRK2wwbXZEbVl4Wk5Eckt3RWpwQ1pvUDEzSHZaRy93Mm5wR1pqc3JacW8rcDcrQVEvWDBFZjdhVjhXSm5wSTYydHc3TDJRM1dWWFgxWWp0Vkc4OGhuQlN5a3JrOEU3VHk1bGR3OWdMTFlya1dFZTEiLCJtYWMiOiJiZGJiODUzMGIxNDdjY2Y1NmYyMWIxYTExNjQxMmUzNmQ1NWJhNGZmZGE2ZmU4ODNjYTU2ZTU3ZDBjMDY2MWQ1IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:28:36 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlU3OWRxOE1VaUV4cmlHWkZZdFhSemc9PSIsInZhbHVlIjoiYkZxRVpYMTBvZkFHWGdReXBrL2drZHZnUVo3T2MyeWZPdGRWSFc1dllCL2dPSENRUFlZREozRmVwekhJaTJucWFFS0ZhU2k0Q0ZGeFhmUWtQVUdzcmlIb0JpZEhOclNMT1pFVDczNkE5TUdjRmZaTzhuWGtoWU53dHJiaVZNYmEiLCJtYWMiOiJhMGNkYjk3MDU0ZmRiZGJiZGE4NGJhYjNlOTRjNTYxZDFhZTA4NzM2NDY2MmJmMGNhZTkzODhlZDdkMmE5NzMwIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:28:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InRWRHk3aUZwMlJqRVZrNnJtTEZ3YVE9PSIsInZhbHVlIjoibmE0MDhCNkRXSCtxQStYNktRK2wwbXZEbVl4Wk5Eckt3RWpwQ1pvUDEzSHZaRy93Mm5wR1pqc3JacW8rcDcrQVEvWDBFZjdhVjhXSm5wSTYydHc3TDJRM1dWWFgxWWp0Vkc4OGhuQlN5a3JrOEU3VHk1bGR3OWdMTFlya1dFZTEiLCJtYWMiOiJiZGJiODUzMGIxNDdjY2Y1NmYyMWIxYTExNjQxMmUzNmQ1NWJhNGZmZGE2ZmU4ODNjYTU2ZTU3ZDBjMDY2MWQ1IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:28:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-861630963\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755527110</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}