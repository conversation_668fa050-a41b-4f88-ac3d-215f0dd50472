{"__meta": {"id": "X8369171dfade681d5941fe7f9a94ac25", "datetime": "2025-08-18 23:41:45", "utime": 1755531705.900707, "method": "GET", "uri": "/nfr/general/2", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:41:45] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755531705.688088, "xdebug_link": null, "collector": "log"}, {"message": "[23:41:45] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/nfr/general/2", "message_html": null, "is_string": false, "label": "debug", "time": 1755531705.760645, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755531705.33911, "end": 1755531705.900731, "duration": 0.5616211891174316, "duration_str": "562ms", "measures": [{"label": "Booting", "start": 1755531705.33911, "relative_start": 0, "end": 1755531705.664391, "relative_end": 1755531705.664391, "duration": 0.32528114318847656, "duration_str": "325ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755531705.664405, "relative_start": 0.32529520988464355, "end": 1755531705.900733, "relative_end": 1.9073486328125e-06, "duration": 0.2363278865814209, "duration_str": "236ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25232600, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 18, "templates": [{"name": "nfr.show (\\resources\\views\\nfr\\show.blade.php)", "param_count": 5, "params": ["nfrs", "user", "roles", "title", "general_nfr_id"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 9, "params": ["__env", "app", "menuData", "errors", "nfrs", "user", "roles", "title", "general_nfr_id"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 9, "params": ["__env", "app", "menuData", "errors", "nfrs", "user", "roles", "title", "general_nfr_id"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 9, "params": ["__env", "app", "menuData", "errors", "nfrs", "user", "roles", "title", "general_nfr_id"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 9, "params": ["__env", "app", "menuData", "errors", "nfrs", "user", "roles", "title", "general_nfr_id"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\resources\\views\\vendor\\pagination\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "nfrs", "user", "roles", "title", "general_nfr_id", "__currentLoopData", "nfr", "loop"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "nfrs", "user", "roles", "title", "general_nfr_id", "__currentLoopData", "nfr", "loop"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "nfrs", "user", "roles", "title", "general_nfr_id", "__currentLoopData", "nfr", "loop"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "nfrs", "user", "roles", "title", "general_nfr_id", "__currentLoopData", "nfr", "loop"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "nfrs", "user", "roles", "title", "general_nfr_id", "__currentLoopData", "nfr", "loop"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "nfrs", "user", "roles", "title", "general_nfr_id", "__currentLoopData", "nfr", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "nfrs", "user", "roles", "title", "general_nfr_id", "__currentLoopData", "nfr", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "nfrs", "user", "roles", "title", "general_nfr_id", "__currentLoopData", "nfr", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "nfrs", "user", "roles", "title", "general_nfr_id", "__currentLoopData", "nfr", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 21, "params": ["__env", "app", "menuData", "errors", "nfrs", "user", "roles", "title", "general_nfr_id", "__currentLoopData", "nfr", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 21, "params": ["__env", "app", "menuData", "errors", "nfrs", "user", "roles", "title", "general_nfr_id", "__currentLoopData", "nfr", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 21, "params": ["__env", "app", "menuData", "errors", "nfrs", "user", "roles", "title", "general_nfr_id", "__currentLoopData", "nfr", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET nfr/general/{general_nfr_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\SpecificNFRController@show", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "nfr.show", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\SpecificNFRController.php&line=116\">\\app\\Http\\Controllers\\SpecificNFRController.php:116-139</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00299, "accumulated_duration_str": "2.99ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 17.391}, {"sql": "select `role_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 121}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Http\\Controllers\\SpecificNFRController.php:121", "connection": "sagile", "start_percent": 17.391, "width_percent": 15.05}, {"sql": "select * from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 124}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Http\\Controllers\\SpecificNFRController.php:124", "connection": "sagile", "start_percent": 32.441, "width_percent": 16.388}, {"sql": "select * from `generalnfr` where `general_nfr_id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\GeneralNFR.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 133}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\app\\GeneralNFR.php:36", "connection": "sagile", "start_percent": 48.829, "width_percent": 19.398}, {"sql": "select count(*) as aggregate from `nfr` where `general_nfr_id` = '2'", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\SpecificNFR.php", "line": 50}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 134}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\SpecificNFR.php:50", "connection": "sagile", "start_percent": 68.227, "width_percent": 16.054}, {"sql": "select * from `nfr` where `general_nfr_id` = '2' limit 8 offset 0", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\SpecificNFR.php", "line": 50}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 134}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\SpecificNFR.php:50", "connection": "sagile", "start_percent": 84.281, "width_percent": 15.719}]}, "models": {"data": {"App\\SpecificNFR": 2, "App\\GeneralNFR": 1, "App\\TeamMapping": 2, "App\\User": 1}, "count": 6}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/nfr/general/2\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/nfr/general/2", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-753155956 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-753155956\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2116898848 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2116898848\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-468636577 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">http://127.0.0.1:8000/nfr/general/2/specific/2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImhySTdVNFNzVkUvZ0dNNzdEUFU4ZVE9PSIsInZhbHVlIjoiV2JhZUptSzlwc1RtZGVaZDZ3bXh1aUhuV3RDRXhadFNNWDVrN2daY1dhSkVrNlVmSCs1YjNPenlDTStlaDhCSnZVRmhNS2ZMYlFVQmNDMjdhUHRnZmRvSzlZWXdyYWVORTRJVm0waVI2RXgvMmsyV3U0SVdsWmw0S2V2SFE5SmMiLCJtYWMiOiJlOTU3YzljMWY3Mzg1ZTQ2OWU2MDRlMzBmY2VjYmI5Yzk4YjhiZjc5OTVmODhiZmVhZjJkOTY5YjY0OThmNmNiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjhuUFJpMnlUS0M3NFg0ZThJK2x4Unc9PSIsInZhbHVlIjoidnBuMy9zV0duaDBXeE9obEtiUFMrTjNzNXJYKzZjeFlTNjBRRVhoaGFoeTBWSmRIT0w2eUpweE9sM2txWHE1bmYxd2R4aWtvUGF6Uk5TZlRKdS9mTDVScXN1ZXJnQk9WRWlXT0JPSzEvem90aHFJdjZhNFhqNzY1ZG01Z0JIbUMiLCJtYWMiOiIwODNmOGE3YjMzMGM0YWRjMjc1ODM2OTk4M2YyY2IzNTk3Mzk0Njg4YmM0YzlkMDNiNGNjNTQ4NTliMGM4ZDM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-468636577\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1998777831 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54955</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"14 characters\">/nfr/general/2</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"14 characters\">/nfr/general/2</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/index.php/nfr/general/2</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://127.0.0.1:8000/nfr/general/2/specific/2</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImhySTdVNFNzVkUvZ0dNNzdEUFU4ZVE9PSIsInZhbHVlIjoiV2JhZUptSzlwc1RtZGVaZDZ3bXh1aUhuV3RDRXhadFNNWDVrN2daY1dhSkVrNlVmSCs1YjNPenlDTStlaDhCSnZVRmhNS2ZMYlFVQmNDMjdhUHRnZmRvSzlZWXdyYWVORTRJVm0waVI2RXgvMmsyV3U0SVdsWmw0S2V2SFE5SmMiLCJtYWMiOiJlOTU3YzljMWY3Mzg1ZTQ2OWU2MDRlMzBmY2VjYmI5Yzk4YjhiZjc5OTVmODhiZmVhZjJkOTY5YjY0OThmNmNiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjhuUFJpMnlUS0M3NFg0ZThJK2x4Unc9PSIsInZhbHVlIjoidnBuMy9zV0duaDBXeE9obEtiUFMrTjNzNXJYKzZjeFlTNjBRRVhoaGFoeTBWSmRIT0w2eUpweE9sM2txWHE1bmYxd2R4aWtvUGF6Uk5TZlRKdS9mTDVScXN1ZXJnQk9WRWlXT0JPSzEvem90aHFJdjZhNFhqNzY1ZG01Z0JIbUMiLCJtYWMiOiIwODNmOGE3YjMzMGM0YWRjMjc1ODM2OTk4M2YyY2IzNTk3Mzk0Njg4YmM0YzlkMDNiNGNjNTQ4NTliMGM4ZDM1IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755531705.3391</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755531705</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1998777831\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-573640322 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-573640322\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1967121076 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:41:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImlMY1FmRTdHNlIwTWF6UW1JTEFqSEE9PSIsInZhbHVlIjoiSjUvZVYrckd2b1p4ejBJcUtoSFJCL0N2R1pmSjU2MXMweS9oeHQ0QjRXaVo5TjJLRzdPVklZUGFJWUs2YUx2ZE9HaUZ6WG1qVFZsdUpCY3EyVzJXQzliS3dtbXB2Qjd6bzVURVNQdFRXQ2pRS242bmFkQmtRcGFsZVRxQm1kK1IiLCJtYWMiOiI2MzIxZWYwYjFlNmYwZGU0Mjk4ZjNkNjc1NjRmNWRmNjdiNTA2Yzk3NTdkZjM0M2M4YjNkNGQxNTA3ZThiMTZhIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:41:45 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkFYN0dGc1JTMTVxRStzcWhWSjdabWc9PSIsInZhbHVlIjoiUU5HQXRIWGttZE9MZmNpQ2IyQXhVV29FOXd3SGFoVzV1aE1XNWxnU1NwTjQwQ3o5UjY1YUNtbUdNekF4VFEzQTdBV01LMXhPM0Q4VzZyTVA3U3FiRDZaSjJkc2xPM2lLUGZ0OTl4VjNQRTFEbktacWsrR3htRVBLaTl3dFUwR1IiLCJtYWMiOiIwZWQ0Y2U2MGFlODVlZWNmZmE4YWRiMjY0YmMyM2Q2ZDQyZjBmZjFlMGE5OTk1N2MwMjEwNDJhNmIyMWViODZhIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:41:45 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImlMY1FmRTdHNlIwTWF6UW1JTEFqSEE9PSIsInZhbHVlIjoiSjUvZVYrckd2b1p4ejBJcUtoSFJCL0N2R1pmSjU2MXMweS9oeHQ0QjRXaVo5TjJLRzdPVklZUGFJWUs2YUx2ZE9HaUZ6WG1qVFZsdUpCY3EyVzJXQzliS3dtbXB2Qjd6bzVURVNQdFRXQ2pRS242bmFkQmtRcGFsZVRxQm1kK1IiLCJtYWMiOiI2MzIxZWYwYjFlNmYwZGU0Mjk4ZjNkNjc1NjRmNWRmNjdiNTA2Yzk3NTdkZjM0M2M4YjNkNGQxNTA3ZThiMTZhIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:41:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkFYN0dGc1JTMTVxRStzcWhWSjdabWc9PSIsInZhbHVlIjoiUU5HQXRIWGttZE9MZmNpQ2IyQXhVV29FOXd3SGFoVzV1aE1XNWxnU1NwTjQwQ3o5UjY1YUNtbUdNekF4VFEzQTdBV01LMXhPM0Q4VzZyTVA3U3FiRDZaSjJkc2xPM2lLUGZ0OTl4VjNQRTFEbktacWsrR3htRVBLaTl3dFUwR1IiLCJtYWMiOiIwZWQ0Y2U2MGFlODVlZWNmZmE4YWRiMjY0YmMyM2Q2ZDQyZjBmZjFlMGE5OTk1N2MwMjEwNDJhNmIyMWViODZhIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:41:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1967121076\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1305992893 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/nfr/general/2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305992893\", {\"maxDepth\":0})</script>\n"}}