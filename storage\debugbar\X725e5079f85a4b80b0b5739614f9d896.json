{"__meta": {"id": "X725e5079f85a4b80b0b5739614f9d896", "datetime": "2025-08-19 14:18:38", "utime": 1755584318.65121, "method": "GET", "uri": "/project-assignments", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[14:18:38] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584318.466479, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584318.147249, "end": 1755584318.651234, "duration": 0.5039849281311035, "duration_str": "504ms", "measures": [{"label": "Booting", "start": 1755584318.147249, "relative_start": 0, "end": 1755584318.446872, "relative_end": 1755584318.446872, "duration": 0.2996230125427246, "duration_str": "300ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584318.446883, "relative_start": 0.2996339797973633, "end": 1755584318.651236, "relative_end": 2.1457672119140625e-06, "duration": 0.20435309410095215, "duration_str": "204ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25397384, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 14, "templates": [{"name": "project-assignments.index (\\resources\\views\\project-assignments\\index.blade.php)", "param_count": 3, "params": ["projects", "projectAssignments", "managedTeams"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET project-assignments", "middleware": "web", "controller": "App\\Http\\Controllers\\ProjectAssignmentController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "project-assignments.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProjectAssignmentController.php&line=18\">\\app\\Http\\Controllers\\ProjectAssignmentController.php:18-52</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00317, "accumulated_duration_str": "3.17ms", "statements": [{"sql": "select * from `users` where `id` = 31 limit 1", "type": "query", "params": [], "bindings": ["31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 13.565}, {"sql": "select `team_name` from `teammappings` where `project_id` is null and `username` = 'tay' and `invitation_status` = 'accepted'", "type": "query", "params": [], "bindings": ["tay", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 26}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:26", "connection": "sagile", "start_percent": 13.565, "width_percent": 15.457}, {"sql": "select * from `projects` where `team_name` in ('Team 888', 'Team AD')", "type": "query", "params": [], "bindings": ["Team 888", "Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 29}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:29", "connection": "sagile", "start_percent": 29.022, "width_percent": 12.934}, {"sql": "select * from `teammappings` where `project_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 34}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:34", "connection": "sagile", "start_percent": 41.956, "width_percent": 13.88}, {"sql": "select * from `users` where `users`.`username` in ('UAT_1', 'ammarjmldnout', 'ivlyn', 'tay')", "type": "query", "params": [], "bindings": ["UAT_1", "ammarjmldnout", "ivlyn", "tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 34}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:34", "connection": "sagile", "start_percent": 55.836, "width_percent": 16.404}, {"sql": "select * from `projects` where `projects`.`id` in (41, 42, 43, 44, 45)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 34}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:34", "connection": "sagile", "start_percent": 72.24, "width_percent": 13.249}, {"sql": "select * from `teams` where `teams`.`team_name` in ('Team 888', 'Team AD', 'ivlyn\\'s team', 'uatTestTeam1')", "type": "query", "params": [], "bindings": ["Team 888", "Team AD", "ivlyn&#039;s team", "uatTestTeam1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 34}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:34", "connection": "sagile", "start_percent": 85.489, "width_percent": 14.511}]}, "models": {"data": {"App\\Team": 2, "App\\TeamMapping": 8, "App\\Project": 2, "App\\User": 5}, "count": 17}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/project-assignments\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "31", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/project-assignments", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2119947976 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2119947976\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1496703385 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1496703385\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-779311561 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/teammapping/Team%20AD</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Iis0YWR5S2FSN2NLeGUxam9WRjU5UEE9PSIsInZhbHVlIjoiaHdWQUN3VXN4ZVovNWExdllIR0lnNk1iUktDcnFKMWw0TDc0UGdaMXBYeC92dFRTNFNVUU4zbGkwdUdrY0ZwTFlaSmMvR0VtYWhhR1Y4SkZQVFY5dVpkcXpnaXB6bUtoenA4V2pLMVpqZzRDSXordE5oNjVvMlZoeFltTXlFa0YiLCJtYWMiOiJlNTVhZDJjMzE3MzZjYzUzOWFkYzM4NmIzZjE5NzA2MWJiZWIzNzNkODc3MmI1NGMyYWZkNzBiMjU0ODY3NDA3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjlJNEZxbGp3UzI0NUp0Z3NycXJ1YlE9PSIsInZhbHVlIjoiTUtlRDdYWlNlRTZRbDE2d042aU5FNm9jbGVWeXF5VEVLY2dTc1BLdE9uTDBVUWp3NGpJeVZWUHNtUERzUjdtMCt2NHlQL1J2ZTJBRTNjSXlPM0VZNGRES0xSUGFKdUVSSjNvVEUxZ1E3bFpmRFdjaVVUUUFjaGhONytOakhQUmsiLCJtYWMiOiIxNTFhOTY5NzU4MDI0ZTdkYTNhNmU0NDY1ZWM3ZjE4ZDU5YzEyZDFjMTc0YTczNDg3ZjM5NGVkOGM4MmFhM2I4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-779311561\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1126035054 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55704</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/project-assignments</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/project-assignments</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/index.php/project-assignments</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/teammapping/Team%20AD</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Iis0YWR5S2FSN2NLeGUxam9WRjU5UEE9PSIsInZhbHVlIjoiaHdWQUN3VXN4ZVovNWExdllIR0lnNk1iUktDcnFKMWw0TDc0UGdaMXBYeC92dFRTNFNVUU4zbGkwdUdrY0ZwTFlaSmMvR0VtYWhhR1Y4SkZQVFY5dVpkcXpnaXB6bUtoenA4V2pLMVpqZzRDSXordE5oNjVvMlZoeFltTXlFa0YiLCJtYWMiOiJlNTVhZDJjMzE3MzZjYzUzOWFkYzM4NmIzZjE5NzA2MWJiZWIzNzNkODc3MmI1NGMyYWZkNzBiMjU0ODY3NDA3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjlJNEZxbGp3UzI0NUp0Z3NycXJ1YlE9PSIsInZhbHVlIjoiTUtlRDdYWlNlRTZRbDE2d042aU5FNm9jbGVWeXF5VEVLY2dTc1BLdE9uTDBVUWp3NGpJeVZWUHNtUERzUjdtMCt2NHlQL1J2ZTJBRTNjSXlPM0VZNGRES0xSUGFKdUVSSjNvVEUxZ1E3bFpmRFdjaVVUUUFjaGhONytOakhQUmsiLCJtYWMiOiIxNTFhOTY5NzU4MDI0ZTdkYTNhNmU0NDY1ZWM3ZjE4ZDU5YzEyZDFjMTc0YTczNDg3ZjM5NGVkOGM4MmFhM2I4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584318.1472</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584318</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1126035054\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2128093075 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CfapZDSBofQwHxJsJhdmHULLiISKd6PUgVWjQqhr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2128093075\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:18:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im5MZUM5Y3QySTRIcFkvK3l0elQ3SkE9PSIsInZhbHVlIjoiRDBUV2JTU3BNeG5odzRqWnVuS3lnWXRuMmFHR25lSnpXOXYwMThWNFVNTkhENHBBNWNoVy95UXFpL0NwZk9MRGZFK2dXYjhMMkRFd0xUVkhlVXIzalB1bXJvNmJKVjJKZkRGdXJxYVIwM2d5TFNuUXBDYUNqNThPS2h2aERaS3kiLCJtYWMiOiIzNmQ2ZTk1ZmIwZTU0ZjY4YTg2ZWU5ZTdjMDIwYTNiOTViZDczNDgzYThjYjgxN2UyZDRiYTAxZWMzYzRiZjIyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:18:38 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImFsWlJnV3VxclJBUFlCeDdoVUVSVGc9PSIsInZhbHVlIjoiVElBWE9WRHZVaENvUUFnNExGd1ZnZU9scFZZNnFLRzMzUEJiUDJrWkdRUitTeU9qOGhlVXJNV1dSRk9XRWRLUjh5dVEzMjZTZUZYNmxRTTc2Wi9pVGNjR3kyTFI1M1lWTU1Fd2tSbnhST0lJQjQ4ODJoMmxQY1UwME94VU4wdnkiLCJtYWMiOiJkNjNiMDI2ZmQ1MDI4MGM0ZWNiYTNjMDJhYWNhMjRjNDAwMzA1NGQxM2I0ZjQxNzAxMzg1MmI1YTM4M2ZmYmU3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:18:38 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im5MZUM5Y3QySTRIcFkvK3l0elQ3SkE9PSIsInZhbHVlIjoiRDBUV2JTU3BNeG5odzRqWnVuS3lnWXRuMmFHR25lSnpXOXYwMThWNFVNTkhENHBBNWNoVy95UXFpL0NwZk9MRGZFK2dXYjhMMkRFd0xUVkhlVXIzalB1bXJvNmJKVjJKZkRGdXJxYVIwM2d5TFNuUXBDYUNqNThPS2h2aERaS3kiLCJtYWMiOiIzNmQ2ZTk1ZmIwZTU0ZjY4YTg2ZWU5ZTdjMDIwYTNiOTViZDczNDgzYThjYjgxN2UyZDRiYTAxZWMzYzRiZjIyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:18:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImFsWlJnV3VxclJBUFlCeDdoVUVSVGc9PSIsInZhbHVlIjoiVElBWE9WRHZVaENvUUFnNExGd1ZnZU9scFZZNnFLRzMzUEJiUDJrWkdRUitTeU9qOGhlVXJNV1dSRk9XRWRLUjh5dVEzMjZTZUZYNmxRTTc2Wi9pVGNjR3kyTFI1M1lWTU1Fd2tSbnhST0lJQjQ4ODJoMmxQY1UwME94VU4wdnkiLCJtYWMiOiJkNjNiMDI2ZmQ1MDI4MGM0ZWNiYTNjMDJhYWNhMjRjNDAwMzA1NGQxM2I0ZjQxNzAxMzg1MmI1YTM4M2ZmYmU3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:18:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/project-assignments</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}