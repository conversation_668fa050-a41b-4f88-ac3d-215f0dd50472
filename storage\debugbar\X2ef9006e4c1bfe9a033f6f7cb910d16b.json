{"__meta": {"id": "X2ef9006e4c1bfe9a033f6f7cb910d16b", "datetime": "2025-08-19 13:33:25", "utime": 1755581605.084682, "method": "GET", "uri": "/project-assignments/43", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 7, "messages": [{"message": "[13:33:24] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755581604.875347, "xdebug_link": null, "collector": "log"}, {"message": "[13:33:24] LOG.debug: LoadUserPermissions: Extracted project ID: 43 from URL: http://127.0.0.1:8000/project-assignments/43", "message_html": null, "is_string": false, "label": "debug", "time": 1755581604.940021, "xdebug_link": null, "collector": "log"}, {"message": "[13:33:24] LOG.debug: LoadUserPermissions: Found project 43 with team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581604.954099, "xdebug_link": null, "collector": "log"}, {"message": "[13:33:24] LOG.debug: LoadUserPermissions: Looking for team role map for team: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581604.954169, "xdebug_link": null, "collector": "log"}, {"message": "[13:33:24] LOG.debug: LoadUserPermissions: Available teams in role map: [\"i<PERSON><PERSON>'s team\",\"Team 888\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755581604.954222, "xdebug_link": null, "collector": "log"}, {"message": "[13:33:24] LOG.debug: LoadUserPermissions: Found team data structure: multiple_projects", "message_html": null, "is_string": false, "label": "debug", "time": 1755581604.954264, "xdebug_link": null, "collector": "log"}, {"message": "[13:33:24] LOG.debug: LoadUserPermissions: Set permissions for user <PERSON><PERSON><PERSON> in team Team 888 for project 43: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755581604.954345, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755581604.581297, "end": 1755581605.084705, "duration": 0.5034081935882568, "duration_str": "503ms", "measures": [{"label": "Booting", "start": 1755581604.581297, "relative_start": 0, "end": 1755581604.854918, "relative_end": 1755581604.854918, "duration": 0.2736210823059082, "duration_str": "274ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755581604.85493, "relative_start": 0.2736330032348633, "end": 1755581605.084708, "relative_end": 2.86102294921875e-06, "duration": 0.22977805137634277, "duration_str": "230ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25516232, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 14, "templates": [{"name": "project-assignments.show (\\resources\\views\\project-assignments\\show.blade.php)", "param_count": 3, "params": ["project", "projectAssignments", "canManageAssignments"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET project-assignments/{project}", "middleware": "web", "controller": "App\\Http\\Controllers\\ProjectAssignmentController@show", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "project-assignments.show", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProjectAssignmentController.php&line=146\">\\app\\Http\\Controllers\\ProjectAssignmentController.php:146-159</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.0037800000000000004, "accumulated_duration_str": "3.78ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 11.64}, {"sql": "select `id`, `team_name` from `projects` where `id` = '43' limit 1", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 167}, {"index": 14, "namespace": "middleware", "name": "load.permissions", "line": 33}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 16, "namespace": "middleware", "name": "bindings", "line": 50}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "middleware::load.permissions:167", "connection": "sagile", "start_percent": 11.64, "width_percent": 10.847}, {"sql": "select * from `projects` where `projects`.`id` = '43' limit 1", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 148}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:148", "connection": "sagile", "start_percent": 22.487, "width_percent": 11.905}, {"sql": "select * from `teammappings` where `project_id` is not null and `project_id` = '43'", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 153}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:153", "connection": "sagile", "start_percent": 34.392, "width_percent": 14.286}, {"sql": "select * from `users` where `users`.`username` in ('ivlyn', 'tay')", "type": "query", "params": [], "bindings": ["ivlyn", "tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 153}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:153", "connection": "sagile", "start_percent": 48.677, "width_percent": 13.228}, {"sql": "select exists(select * from `teammappings` where `project_id` is null and `username` = 'ivlyn' and `team_name` = 'Team 888' and `role_name` = 'Team Manager' and `invitation_status` = 'accepted') as `exists`", "type": "query", "params": [], "bindings": ["ivlyn", "Team 888", "Team Manager", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 294}, {"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 156}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:294", "connection": "sagile", "start_percent": 61.905, "width_percent": 16.931}, {"sql": "select * from `roles` where `project_id` = 43", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Project.php", "line": 81}, {"index": 15, "namespace": "view", "name": "c87405fae7fd5db3ca9ba3400477180b915713eb", "line": 94}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Project.php:81", "connection": "sagile", "start_percent": 78.836, "width_percent": 10.582}, {"sql": "select * from `roles` where `project_id` = 43", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Project.php", "line": 81}, {"index": 15, "namespace": "view", "name": "c87405fae7fd5db3ca9ba3400477180b915713eb", "line": 94}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Project.php:81", "connection": "sagile", "start_percent": 89.418, "width_percent": 10.582}]}, "models": {"data": {"App\\Role": 4, "App\\TeamMapping": 2, "App\\Project": 1, "App\\User": 3}, "count": 10}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/project-assignments/43\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755580949\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/project-assignments/43", "status_code": "<pre class=sf-dump id=sf-dump-1963166587 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1963166587\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1873898135 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1873898135\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1598321054 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1598321054\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1142415832 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/project-assignments</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImJLZ0N2THNmL2ZuN2JCMWlDZTRIZWc9PSIsInZhbHVlIjoiQ2hqNFZLK28xa3dMRnVjaVFjTUhEaUlLaHdGd2dMZlNlbWE4RHFsNWxtcFVVQjBaZFJTaHA3T2xudjdCaEErU3ZuVGo3azl0VW9WZVpOdC9UbjBhY2ZjZkYzZHk1SCtyR0U1aDI4amd6Z1NBemFoN2txc0FWSVMvdy9zS09JRmEiLCJtYWMiOiI3NzkwZGY5MmE4MjQyNzExODBkOWU0YWNmMTZhOWQxZjFiNWRmNTE1ZmJkNWIwODRmMzUwMGJkOTkxZWVhNDZhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ikkxb2dMVkVvL3hvTlVEektNdWxBbHc9PSIsInZhbHVlIjoiMExnSUNvN2tLZDcvczRudnNQUWlJTHVtN0tIcGZvYlkvTnZod1pQUUtCd2NPTUhDcCtxTUdWeVAxNWt3eElHaDBpbEx4Y0lsNzdKMVVGRFVSeFYreVJTakpPczN0eDBNZm5UbU5YYzdjRFJoR2NHNCtjM0tsdWFNRDRiMU1lU28iLCJtYWMiOiJkYjkwMDZkZDc2YWE5YzI3OGVmZjViMTlmYzU5MTRmOGE2MDM1YjhjYjgyNzYxNzkzN2QwNmFhODM0MTBhMjY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1142415832\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-433015302 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59657</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/project-assignments/43</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/project-assignments/43</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/index.php/project-assignments/43</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/project-assignments</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImJLZ0N2THNmL2ZuN2JCMWlDZTRIZWc9PSIsInZhbHVlIjoiQ2hqNFZLK28xa3dMRnVjaVFjTUhEaUlLaHdGd2dMZlNlbWE4RHFsNWxtcFVVQjBaZFJTaHA3T2xudjdCaEErU3ZuVGo3azl0VW9WZVpOdC9UbjBhY2ZjZkYzZHk1SCtyR0U1aDI4amd6Z1NBemFoN2txc0FWSVMvdy9zS09JRmEiLCJtYWMiOiI3NzkwZGY5MmE4MjQyNzExODBkOWU0YWNmMTZhOWQxZjFiNWRmNTE1ZmJkNWIwODRmMzUwMGJkOTkxZWVhNDZhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ikkxb2dMVkVvL3hvTlVEektNdWxBbHc9PSIsInZhbHVlIjoiMExnSUNvN2tLZDcvczRudnNQUWlJTHVtN0tIcGZvYlkvTnZod1pQUUtCd2NPTUhDcCtxTUdWeVAxNWt3eElHaDBpbEx4Y0lsNzdKMVVGRFVSeFYreVJTakpPczN0eDBNZm5UbU5YYzdjRFJoR2NHNCtjM0tsdWFNRDRiMU1lU28iLCJtYWMiOiJkYjkwMDZkZDc2YWE5YzI3OGVmZjViMTlmYzU5MTRmOGE2MDM1YjhjYjgyNzYxNzkzN2QwNmFhODM0MTBhMjY4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755581604.5813</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755581604</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-433015302\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1277438867 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GKxRuD042xMQ4k3v5EHCZLsF1sfAXXnsz4rkTCog</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1277438867\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1894228226 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:33:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkFrTTBUSmFubUtmMFFYcUo4QTI5dGc9PSIsInZhbHVlIjoiQkZ2dk00N0Y0Ym8zNng0d21QaWtLbUxOVnhjNVRxMzRtREtHV3pydkZIaW1EUnFqTEtnWmNiQUgvbzg0c0VhSitvZk9raFJXTUZ0K2ExTTJOSFVUazdZbE04ekVWbWNOL1VENlFiYllWZUpKZlhrWjU5ZlRKanhkVU5GK0hqb28iLCJtYWMiOiI2MzA1ZTVkZGJmN2JhNDIyYzc2NDdjMDM5ZjZjZDdlYzFlMGQ5MzdjODlkZmRiZDlmYzlmNmI4M2YwZDUxZTVkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:33:25 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im9oNkNGeXhGYmx1Ymt5b2tMdEtXNWc9PSIsInZhbHVlIjoiQU5oelZ2SzNsRjRyTy9aM2xjMituWjFMYTBRU3JUZEtOa01WU1krTmdoZHNHNkZ1djBLbkhNWk5QeTR0aXdLbFBWV2F6NXpFY3BaR3R4aVh4ZVFZOGU5YzFrTnk5Z3RqdlhjeXFXMFpzbzdSY1cveDJrd24zbTlhZEJUK3FsUmQiLCJtYWMiOiIzYmQ0MWI5Mzg4YzU2Y2MwZDcwNmFlYWI5ZjYxODUxOTZjMGRlZjAxZTViYTcyNWY3MjA1NDBjMWFhNWJhZjRiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:33:25 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkFrTTBUSmFubUtmMFFYcUo4QTI5dGc9PSIsInZhbHVlIjoiQkZ2dk00N0Y0Ym8zNng0d21QaWtLbUxOVnhjNVRxMzRtREtHV3pydkZIaW1EUnFqTEtnWmNiQUgvbzg0c0VhSitvZk9raFJXTUZ0K2ExTTJOSFVUazdZbE04ekVWbWNOL1VENlFiYllWZUpKZlhrWjU5ZlRKanhkVU5GK0hqb28iLCJtYWMiOiI2MzA1ZTVkZGJmN2JhNDIyYzc2NDdjMDM5ZjZjZDdlYzFlMGQ5MzdjODlkZmRiZDlmYzlmNmI4M2YwZDUxZTVkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:33:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im9oNkNGeXhGYmx1Ymt5b2tMdEtXNWc9PSIsInZhbHVlIjoiQU5oelZ2SzNsRjRyTy9aM2xjMituWjFMYTBRU3JUZEtOa01WU1krTmdoZHNHNkZ1djBLbkhNWk5QeTR0aXdLbFBWV2F6NXpFY3BaR3R4aVh4ZVFZOGU5YzFrTnk5Z3RqdlhjeXFXMFpzbzdSY1cveDJrd24zbTlhZEJUK3FsUmQiLCJtYWMiOiIzYmQ0MWI5Mzg4YzU2Y2MwZDcwNmFlYWI5ZjYxODUxOTZjMGRlZjAxZTViYTcyNWY3MjA1NDBjMWFhNWJhZjRiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:33:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1894228226\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-524239965 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/project-assignments/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755580949</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-524239965\", {\"maxDepth\":0})</script>\n"}}