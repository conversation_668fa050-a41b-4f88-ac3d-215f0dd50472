{"__meta": {"id": "X7088c93ec6e293403841d6f5138e4ab3", "datetime": "2025-08-18 23:02:08", "utime": 1755529328.241112, "method": "POST", "uri": "/projects", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:02:05] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755529325.596266, "xdebug_link": null, "collector": "log"}, {"message": "[23:02:05] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/projects", "message_html": null, "is_string": false, "label": "debug", "time": 1755529325.725412, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755529324.988654, "end": 1755529328.241143, "duration": 3.2524890899658203, "duration_str": "3.25s", "measures": [{"label": "Booting", "start": 1755529324.988654, "relative_start": 0, "end": 1755529325.554168, "relative_end": 1755529325.554168, "duration": 0.565514087677002, "duration_str": "566ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755529325.554184, "relative_start": 0.5655300617218018, "end": 1755529328.241146, "relative_end": 3.0994415283203125e-06, "duration": 2.686962127685547, "duration_str": "2.69s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25501328, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 9, "templates": [{"name": "project.newIndex (\\resources\\views\\project\\newIndex.blade.php)", "param_count": 1, "params": ["projects"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 9, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "POST projects", "middleware": "web", "controller": "App\\Http\\Controllers\\ProjectController@store", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "projects.store", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProjectController.php&line=75\">\\app\\Http\\Controllers\\ProjectController.php:75-220</a>"}, "queries": {"nb_statements": 96, "nb_failed_statements": 0, "accumulated_duration": 0.12214999999999993, "accumulated_duration_str": "122ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 0.63}, {"sql": "select count(*) as aggregate from `projects` where `proj_name` = 'Food Ordering System'", "type": "query", "params": [], "bindings": ["Food Ordering System"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 447}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "sagile", "start_percent": 0.63, "width_percent": 0.573}, {"sql": "insert into `projects` (`proj_name`, `proj_desc`, `start_date`, `end_date`, `team_name`, `updated_at`, `created_at`) values ('Food Ordering System', 'A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.', '2025-08-18', '2026-01-25', 'ivlyn\\'s team', '2025-08-18 23:02:05', '2025-08-18 23:02:05')", "type": "query", "params": [], "bindings": ["Food Ordering System", "A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.", "2025-08-18", "2026-01-25", "ivlyn&#039;s team", "2025-08-18 23:02:05", "2025-08-18 23:02:05"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 98}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00152, "duration_str": "1.52ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:98", "connection": "sagile", "start_percent": 1.203, "width_percent": 1.244}, {"sql": "insert into `statuses` (`title`, `slug`, `order`, `project_id`) values ('Backlog', 'backlog', 1, 42)", "type": "query", "params": [], "bindings": ["Backlog", "backlog", "1", "42"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Project.php", "line": 22}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 98}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0022299999999999998, "duration_str": "2.23ms", "stmt_id": "\\app\\Project.php:22", "connection": "sagile", "start_percent": 2.448, "width_percent": 1.826}, {"sql": "insert into `statuses` (`title`, `slug`, `order`, `project_id`) values ('Up Next', 'up-next', 2, 42)", "type": "query", "params": [], "bindings": ["Up Next", "up-next", "2", "42"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Project.php", "line": 22}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 98}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "\\app\\Project.php:22", "connection": "sagile", "start_percent": 4.273, "width_percent": 1.113}, {"sql": "insert into `statuses` (`title`, `slug`, `order`, `project_id`) values ('In Progress', 'in-progress', 3, 42)", "type": "query", "params": [], "bindings": ["In Progress", "in-progress", "3", "42"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Project.php", "line": 22}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 98}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0014, "duration_str": "1.4ms", "stmt_id": "\\app\\Project.php:22", "connection": "sagile", "start_percent": 5.387, "width_percent": 1.146}, {"sql": "insert into `statuses` (`title`, `slug`, `order`, `project_id`) values ('Done', 'done', 4, 42)", "type": "query", "params": [], "bindings": ["Done", "done", "4", "42"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Project.php", "line": 22}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 98}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0014199999999999998, "duration_str": "1.42ms", "stmt_id": "\\app\\Project.php:22", "connection": "sagile", "start_percent": 6.533, "width_percent": 1.163}, {"sql": "insert into `roles` (`role_name`, `project_id`, `updated_at`, `created_at`) values ('Project Manager', 42, '2025-08-18 23:02:06', '2025-08-18 23:02:06')", "type": "query", "params": [], "bindings": ["Project Manager", "42", "2025-08-18 23:02:06", "2025-08-18 23:02:06"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 104}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00149, "duration_str": "1.49ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:104", "connection": "sagile", "start_percent": 7.695, "width_percent": 1.22}, {"sql": "insert into `roles` (`role_name`, `project_id`, `updated_at`, `created_at`) values ('Developer', 42, '2025-08-18 23:02:06', '2025-08-18 23:02:06')", "type": "query", "params": [], "bindings": ["Developer", "42", "2025-08-18 23:02:06", "2025-08-18 23:02:06"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 110}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0016200000000000001, "duration_str": "1.62ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:110", "connection": "sagile", "start_percent": 8.915, "width_percent": 1.326}, {"sql": "select `id` from `permission` where `key` in ('view_roles', 'addLane_kanban', 'addTask_kanban', 'editLane_kanban', 'deleteLane_kanban', 'deleteTask_kanban', 'addComment_kanban', 'updateTaskStatus_kanban', 'editTask_kanban', 'addUserStory_backlog', 'beginSprint_backlog', 'addToSprint_backlog', 'endSprint_backlog', 'add_userstory', 'edit_userstory', 'delete_userstory', 'editStatus_userstory', 'view_task', 'add_task', 'edit_task', 'delete_task', 'viewCalendar_task', 'viewComments_task', 'add_roles', 'edit_roles', 'delete_roles', 'updateUserRole_roles', 'add_status', 'edit_status', 'delete_status', 'edit_details', 'delete_details', 'share_details', 'view_sprintArchive', 'viewKanbanArchive_sprintArchive', 'viewBurndownArchive_sprintArchive', 'view_kanban', 'view_burndown', 'view_backlog', 'view_userstory', 'view_forum', 'view_bugtracking', 'view_status', 'view_details')", "type": "query", "params": [], "bindings": ["view_roles", "addLane_kanban", "addTask_kanban", "editLane_kanban", "deleteLane_kanban", "deleteTask_kanban", "addComment_kanban", "updateTaskStatus_kanban", "editTask_kanban", "addUserStory_backlog", "beginSprint_backlog", "addToSprint_backlog", "endSprint_backlog", "add_userstory", "edit_userstory", "delete_userstory", "editStatus_userstory", "view_task", "add_task", "edit_task", "delete_task", "viewCalendar_task", "viewComments_task", "add_roles", "edit_roles", "delete_roles", "updateUserRole_roles", "add_status", "edit_status", "delete_status", "edit_details", "delete_details", "share_details", "view_sprintArchive", "viewKanbanArchive_sprintArchive", "viewBurndownArchive_sprintArchive", "view_kanban", "view_burndown", "view_backlog", "view_userstory", "view_forum", "view_bugtracking", "view_status", "view_details"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 148}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:148", "connection": "sagile", "start_percent": 10.242, "width_percent": 0.679}, {"sql": "select `id` from `permission` where `key` in ('view_roles', 'addLane_kanban', 'addTask_kanban', 'editLane_kanban', 'deleteLane_kanban', 'deleteTask_kanban', 'addComment_kanban', 'updateTaskStatus_kanban', 'editTask_kanban', 'addUserStory_backlog', 'beginSprint_backlog', 'addToSprint_backlog', 'endSprint_backlog', 'add_userstory', 'edit_userstory', 'delete_userstory', 'editStatus_userstory', 'view_task', 'add_task', 'edit_task', 'delete_task', 'viewCalendar_task', 'viewComments_task', 'view_sprintArchive', 'viewKanbanArchive_sprintArchive', 'viewBurndownArchive_sprintArchive', 'view_kanban', 'view_burndown', 'view_backlog', 'view_userstory', 'view_forum', 'view_bugtracking', 'view_status', 'view_details')", "type": "query", "params": [], "bindings": ["view_roles", "addLane_kanban", "addTask_kanban", "editLane_kanban", "deleteLane_kanban", "deleteTask_kanban", "addComment_kanban", "updateTaskStatus_kanban", "editTask_kanban", "addUserStory_backlog", "beginSprint_backlog", "addToSprint_backlog", "endSprint_backlog", "add_userstory", "edit_userstory", "delete_userstory", "editStatus_userstory", "view_task", "add_task", "edit_task", "delete_task", "viewCalendar_task", "viewComments_task", "view_sprintArchive", "viewKanbanArchive_sprintArchive", "viewBurndownArchive_sprintArchive", "view_kanban", "view_burndown", "view_backlog", "view_userstory", "view_forum", "view_bugtracking", "view_status", "view_details"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 151}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:151", "connection": "sagile", "start_percent": 10.921, "width_percent": 0.622}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (1, 31)", "type": "query", "params": [], "bindings": ["1", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00263, "duration_str": "2.63ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 11.543, "width_percent": 2.153}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (2, 31)", "type": "query", "params": [], "bindings": ["2", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00131, "duration_str": "1.31ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 13.696, "width_percent": 1.072}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (3, 31)", "type": "query", "params": [], "bindings": ["3", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0013700000000000001, "duration_str": "1.37ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 14.769, "width_percent": 1.122}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (4, 31)", "type": "query", "params": [], "bindings": ["4", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00126, "duration_str": "1.26ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 15.89, "width_percent": 1.032}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (5, 31)", "type": "query", "params": [], "bindings": ["5", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00151, "duration_str": "1.51ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 16.922, "width_percent": 1.236}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (6, 31)", "type": "query", "params": [], "bindings": ["6", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00141, "duration_str": "1.41ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 18.158, "width_percent": 1.154}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (7, 31)", "type": "query", "params": [], "bindings": ["7", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00126, "duration_str": "1.26ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 19.312, "width_percent": 1.032}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (8, 31)", "type": "query", "params": [], "bindings": ["8", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00132, "duration_str": "1.32ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 20.344, "width_percent": 1.081}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (9, 31)", "type": "query", "params": [], "bindings": ["9", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0017800000000000001, "duration_str": "1.78ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 21.424, "width_percent": 1.457}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (10, 31)", "type": "query", "params": [], "bindings": ["10", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00123, "duration_str": "1.23ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 22.882, "width_percent": 1.007}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (11, 31)", "type": "query", "params": [], "bindings": ["11", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0014199999999999998, "duration_str": "1.42ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 23.889, "width_percent": 1.163}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (12, 31)", "type": "query", "params": [], "bindings": ["12", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00157, "duration_str": "1.57ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 25.051, "width_percent": 1.285}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (13, 31)", "type": "query", "params": [], "bindings": ["13", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00127, "duration_str": "1.27ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 26.336, "width_percent": 1.04}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (14, 31)", "type": "query", "params": [], "bindings": ["14", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00122, "duration_str": "1.22ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 27.376, "width_percent": 0.999}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (15, 31)", "type": "query", "params": [], "bindings": ["15", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011799999999999998, "duration_str": "1.18ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 28.375, "width_percent": 0.966}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (16, 31)", "type": "query", "params": [], "bindings": ["16", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00125, "duration_str": "1.25ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 29.341, "width_percent": 1.023}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (17, 31)", "type": "query", "params": [], "bindings": ["17", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0012, "duration_str": "1.2ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 30.364, "width_percent": 0.982}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (18, 31)", "type": "query", "params": [], "bindings": ["18", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 31.347, "width_percent": 0.974}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (19, 31)", "type": "query", "params": [], "bindings": ["19", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00116, "duration_str": "1.16ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 32.321, "width_percent": 0.95}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (20, 31)", "type": "query", "params": [], "bindings": ["20", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00114, "duration_str": "1.14ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 33.271, "width_percent": 0.933}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (21, 31)", "type": "query", "params": [], "bindings": ["21", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0016899999999999999, "duration_str": "1.69ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 34.204, "width_percent": 1.384}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (22, 31)", "type": "query", "params": [], "bindings": ["22", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 35.587, "width_percent": 0.868}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (23, 31)", "type": "query", "params": [], "bindings": ["23", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 36.455, "width_percent": 0.868}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (24, 31)", "type": "query", "params": [], "bindings": ["24", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00102, "duration_str": "1.02ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 37.323, "width_percent": 0.835}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (25, 31)", "type": "query", "params": [], "bindings": ["25", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00122, "duration_str": "1.22ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 38.158, "width_percent": 0.999}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (26, 31)", "type": "query", "params": [], "bindings": ["26", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00122, "duration_str": "1.22ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 39.157, "width_percent": 0.999}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (27, 31)", "type": "query", "params": [], "bindings": ["27", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00133, "duration_str": "1.33ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 40.156, "width_percent": 1.089}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (28, 31)", "type": "query", "params": [], "bindings": ["28", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 41.244, "width_percent": 0.843}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (29, 31)", "type": "query", "params": [], "bindings": ["29", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00114, "duration_str": "1.14ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 42.088, "width_percent": 0.933}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (30, 31)", "type": "query", "params": [], "bindings": ["30", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011799999999999998, "duration_str": "1.18ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 43.021, "width_percent": 0.966}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (31, 31)", "type": "query", "params": [], "bindings": ["31", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00123, "duration_str": "1.23ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 43.987, "width_percent": 1.007}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (32, 31)", "type": "query", "params": [], "bindings": ["32", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00114, "duration_str": "1.14ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 44.994, "width_percent": 0.933}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (33, 31)", "type": "query", "params": [], "bindings": ["33", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0012, "duration_str": "1.2ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 45.927, "width_percent": 0.982}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (34, 31)", "type": "query", "params": [], "bindings": ["34", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0014199999999999998, "duration_str": "1.42ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 46.91, "width_percent": 1.163}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (35, 31)", "type": "query", "params": [], "bindings": ["35", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 48.072, "width_percent": 0.901}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (36, 31)", "type": "query", "params": [], "bindings": ["36", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 48.973, "width_percent": 0.974}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (37, 31)", "type": "query", "params": [], "bindings": ["37", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 49.947, "width_percent": 0.974}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (38, 31)", "type": "query", "params": [], "bindings": ["38", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00121, "duration_str": "1.21ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 50.921, "width_percent": 0.991}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (39, 31)", "type": "query", "params": [], "bindings": ["39", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0013700000000000001, "duration_str": "1.37ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 51.912, "width_percent": 1.122}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (40, 31)", "type": "query", "params": [], "bindings": ["40", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011799999999999998, "duration_str": "1.18ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 53.033, "width_percent": 0.966}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (41, 31)", "type": "query", "params": [], "bindings": ["41", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0013, "duration_str": "1.3ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 53.999, "width_percent": 1.064}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (42, 31)", "type": "query", "params": [], "bindings": ["42", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0014299999999999998, "duration_str": "1.43ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 55.063, "width_percent": 1.171}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (43, 31)", "type": "query", "params": [], "bindings": ["43", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 56.234, "width_percent": 0.974}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (44, 31)", "type": "query", "params": [], "bindings": ["44", "31"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00138, "duration_str": "1.38ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 57.208, "width_percent": 1.13}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (1, 32)", "type": "query", "params": [], "bindings": ["1", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0014399999999999999, "duration_str": "1.44ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 58.338, "width_percent": 1.179}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (2, 32)", "type": "query", "params": [], "bindings": ["2", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00148, "duration_str": "1.48ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 59.517, "width_percent": 1.212}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (3, 32)", "type": "query", "params": [], "bindings": ["3", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00134, "duration_str": "1.34ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 60.729, "width_percent": 1.097}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (4, 32)", "type": "query", "params": [], "bindings": ["4", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 61.826, "width_percent": 0.892}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (5, 32)", "type": "query", "params": [], "bindings": ["5", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00107, "duration_str": "1.07ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 62.718, "width_percent": 0.876}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (6, 32)", "type": "query", "params": [], "bindings": ["6", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00226, "duration_str": "2.26ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 63.594, "width_percent": 1.85}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (7, 32)", "type": "query", "params": [], "bindings": ["7", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0037, "duration_str": "3.7ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 65.444, "width_percent": 3.029}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (8, 32)", "type": "query", "params": [], "bindings": ["8", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 68.473, "width_percent": 0.974}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (9, 32)", "type": "query", "params": [], "bindings": ["9", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00122, "duration_str": "1.22ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 69.447, "width_percent": 0.999}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (10, 32)", "type": "query", "params": [], "bindings": ["10", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0013, "duration_str": "1.3ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 70.446, "width_percent": 1.064}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (11, 32)", "type": "query", "params": [], "bindings": ["11", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00125, "duration_str": "1.25ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 71.51, "width_percent": 1.023}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (12, 32)", "type": "query", "params": [], "bindings": ["12", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00128, "duration_str": "1.28ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 72.534, "width_percent": 1.048}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (13, 32)", "type": "query", "params": [], "bindings": ["13", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011200000000000001, "duration_str": "1.12ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 73.582, "width_percent": 0.917}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (14, 32)", "type": "query", "params": [], "bindings": ["14", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00116, "duration_str": "1.16ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 74.499, "width_percent": 0.95}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (15, 32)", "type": "query", "params": [], "bindings": ["15", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0012, "duration_str": "1.2ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 75.448, "width_percent": 0.982}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (16, 32)", "type": "query", "params": [], "bindings": ["16", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 76.431, "width_percent": 0.868}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (17, 32)", "type": "query", "params": [], "bindings": ["17", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00123, "duration_str": "1.23ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 77.298, "width_percent": 1.007}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (18, 32)", "type": "query", "params": [], "bindings": ["18", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 78.305, "width_percent": 0.974}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (19, 32)", "type": "query", "params": [], "bindings": ["19", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00124, "duration_str": "1.24ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 79.28, "width_percent": 1.015}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (20, 32)", "type": "query", "params": [], "bindings": ["20", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 80.295, "width_percent": 0.819}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (21, 32)", "type": "query", "params": [], "bindings": ["21", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00108, "duration_str": "1.08ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 81.113, "width_percent": 0.884}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (22, 32)", "type": "query", "params": [], "bindings": ["22", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 81.998, "width_percent": 0.737}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (23, 32)", "type": "query", "params": [], "bindings": ["23", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0014299999999999998, "duration_str": "1.43ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 82.734, "width_percent": 1.171}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (24, 32)", "type": "query", "params": [], "bindings": ["24", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 83.905, "width_percent": 0.909}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (25, 32)", "type": "query", "params": [], "bindings": ["25", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00102, "duration_str": "1.02ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 84.814, "width_percent": 0.835}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (26, 32)", "type": "query", "params": [], "bindings": ["26", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 85.649, "width_percent": 0.819}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (27, 32)", "type": "query", "params": [], "bindings": ["27", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 86.467, "width_percent": 0.892}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (28, 32)", "type": "query", "params": [], "bindings": ["28", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0010400000000000001, "duration_str": "1.04ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 87.36, "width_percent": 0.851}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (37, 32)", "type": "query", "params": [], "bindings": ["37", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00122, "duration_str": "1.22ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 88.211, "width_percent": 0.999}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (38, 32)", "type": "query", "params": [], "bindings": ["38", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0014399999999999999, "duration_str": "1.44ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 89.21, "width_percent": 1.179}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (39, 32)", "type": "query", "params": [], "bindings": ["39", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 90.389, "width_percent": 1.113}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (40, 32)", "type": "query", "params": [], "bindings": ["40", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00108, "duration_str": "1.08ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 91.502, "width_percent": 0.884}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (41, 32)", "type": "query", "params": [], "bindings": ["41", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00127, "duration_str": "1.27ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 92.386, "width_percent": 1.04}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (43, 32)", "type": "query", "params": [], "bindings": ["43", "32"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00146, "duration_str": "1.46ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 93.426, "width_percent": 1.195}, {"sql": "select * from `teammappings` where `team_name` = 'ivlyn\\'s team'", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 171}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:171", "connection": "sagile", "start_percent": 94.621, "width_percent": 0.647}, {"sql": "insert into `teammappings` (`username`, `team_name`, `project_id`, `invitation_status`, `role_name`, `updated_at`, `created_at`) values ('ivlyn', 'ivlyn\\'s team', 42, 'accepted', 'Project Manager', '2025-08-18 23:02:08', '2025-08-18 23:02:08')", "type": "query", "params": [], "bindings": ["ivlyn", "ivlyn&#039;s team", "42", "accepted", "Project Manager", "2025-08-18 23:02:08", "2025-08-18 23:02:08"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 190}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00121, "duration_str": "1.21ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:190", "connection": "sagile", "start_percent": 95.268, "width_percent": 0.991}, {"sql": "select * from `teammappings` where `project_id` = 42", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 341}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 194}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:341", "connection": "sagile", "start_percent": 96.259, "width_percent": 0.352}, {"sql": "select * from `users` where `username` = 'ivlyn' limit 1", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 344}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 194}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00085, "duration_str": "850μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:344", "connection": "sagile", "start_percent": 96.611, "width_percent": 0.696}, {"sql": "insert into `project_user` (`project_id`, `user_id`, `project_access`, `sprint_access`, `forum_access`, `userstory_access`, `secfeature_access`, `updated_at`, `created_at`) values (42, 30, 1, 1, 1, 1, 1, '2025-08-18 23:02:08', '2025-08-18 23:02:08')", "type": "query", "params": [], "bindings": ["42", "30", "1", "1", "1", "1", "1", "2025-08-18 23:02:08", "2025-08-18 23:02:08"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 205}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00232, "duration_str": "2.32ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:205", "connection": "sagile", "start_percent": 97.307, "width_percent": 1.899}, {"sql": "select `project_id` from `teammappings` where `username` = 'iv<PERSON>' and `project_id` is not null", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 212}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:212", "connection": "sagile", "start_percent": 99.206, "width_percent": 0.377}, {"sql": "select * from `projects` where `id` in (42)", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 216}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:216", "connection": "sagile", "start_percent": 99.582, "width_percent": 0.418}]}, "models": {"data": {"App\\Project": 1, "App\\TeamMapping": 2, "App\\Permission": 78, "App\\User": 2}, "count": 83}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/projects/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/projects", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1972742834 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1972742834\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1412785412 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n  \"<span class=sf-dump-key>proj_desc</span>\" => \"<span class=sf-dump-str title=\"213 characters\">A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.</span>\"\n  \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n  \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2026-01-25</span>\"\n  \"<span class=sf-dump-key>team</span>\" => \"<span class=sf-dump-str title=\"12 characters\">ivlyn&#039;s team</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1412785412\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-552114605 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">917</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryVJylNtGUVZeKWaTK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/projects/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik5CNy9OdnRoSDh0UGx5dGJoL3N2Q2c9PSIsInZhbHVlIjoiSjFqRENhV3E5OERQWkFhNFByVEp2akVVOTYvUkczWDk0V1hYYXp1TjhCQy9jUW9zVGp6N2Y4KzdwSW5ZOXlLZkR0QkxuTk1URjl5Q2YxMXh4ekNrMmhpdE5aSkJ1aU1idnE0SHJlaHV0dHRzZHUyTStSZjF0N1NnbWxEZTc3WkgiLCJtYWMiOiJmZDM4YjdjNTI4MTIyNDg4NzBhMmZmYzIxNGMwOTFmYjY0Mjc2ODg4NmFhZTk0N2I0ZGIzZjI4YWM3OWI0ZDEzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkZVUytKdndXaUNPZ0NIcEFmV3RGMFE9PSIsInZhbHVlIjoiMk1rQnpNS1FlSEw5bnluK3dYdTk2MWNaWUZwZkpITHNqM1VuOTdaQmtBejJuQVJZdk5qcFNWYTBKNW0rSXFicDJYTXZJMFJKR0ZJZjJGWmxKQzFSV3RoOU9VelR6dnpPa2FrSUZkd2liajhWZmtTY0xPMGRWSFBlSmpVVDBybGIiLCJtYWMiOiI2NmNkMDQzOGI1MTJkMGQzYjY2NzIzNjY0NWM4ZGNkNGQ5N2QwOWNlZTE1NjFkMWQ0YzgxNWY0NjFiOTA0ODEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-552114605\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1200678253 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58762</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/projects</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/projects</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/index.php/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">917</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">917</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryVJylNtGUVZeKWaTK</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryVJylNtGUVZeKWaTK</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/projects/create</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik5CNy9OdnRoSDh0UGx5dGJoL3N2Q2c9PSIsInZhbHVlIjoiSjFqRENhV3E5OERQWkFhNFByVEp2akVVOTYvUkczWDk0V1hYYXp1TjhCQy9jUW9zVGp6N2Y4KzdwSW5ZOXlLZkR0QkxuTk1URjl5Q2YxMXh4ekNrMmhpdE5aSkJ1aU1idnE0SHJlaHV0dHRzZHUyTStSZjF0N1NnbWxEZTc3WkgiLCJtYWMiOiJmZDM4YjdjNTI4MTIyNDg4NzBhMmZmYzIxNGMwOTFmYjY0Mjc2ODg4NmFhZTk0N2I0ZGIzZjI4YWM3OWI0ZDEzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkZVUytKdndXaUNPZ0NIcEFmV3RGMFE9PSIsInZhbHVlIjoiMk1rQnpNS1FlSEw5bnluK3dYdTk2MWNaWUZwZkpITHNqM1VuOTdaQmtBejJuQVJZdk5qcFNWYTBKNW0rSXFicDJYTXZJMFJKR0ZJZjJGWmxKQzFSV3RoOU9VelR6dnpPa2FrSUZkd2liajhWZmtTY0xPMGRWSFBlSmpVVDBybGIiLCJtYWMiOiI2NmNkMDQzOGI1MTJkMGQzYjY2NzIzNjY0NWM4ZGNkNGQ5N2QwOWNlZTE1NjFkMWQ0YzgxNWY0NjFiOTA0ODEzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755529324.9887</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755529324</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1200678253\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1148445916 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1148445916\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1446385694 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:02:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjU3ZmlLTTVnend1VS9yVlpqT0R6TGc9PSIsInZhbHVlIjoiZzVqQzdtVlVCNEl2VnNOTlpIbk9lemRCYlJHWXZmTHpvUzg1RWRTQlRlUnU3QzlRZ3FvWDh0M3lKQnRGSVRuOE9kcGFXL1NRTUtET2xkNlFmRGxUUURlZWgxcXY4ejhSR3VhdEFwaU9TaWZuczNVOFBqQTZDc1U2Wm9GbnNkdkUiLCJtYWMiOiI5ZGY1OGE2MzI3ZTA2ZDhlOTMxY2MzZWI2MzY4MTEwM2IwY2Q3ZWIwY2NjNjQwZjI5Nzg3ODA0MzI0MzJjZThlIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:02:08 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im1laGRXVUJjRnhWNFJxU0d1VVBkQlE9PSIsInZhbHVlIjoiR2JZUzF3Ymh2TTZoKy82QXo2ak5KTXlkWGNUa1phd0xyVk50RGpvWUZBWURBLy9LaGdQSWhyUlpUU0tSU1BTbDErR1hMWFJyMzRTdHg1blRyQWVZRFRudklTYXp1RGZOR2N4TDBoci9sZXhZZXBrRnU4Q1h1eVM3Nk5hM0FidE0iLCJtYWMiOiI2NzkzMTE4ZDE4YjY1ODg0ZDI4MjE5OTFmY2Y1ZDg4ZGI1Mzk2NmQ0ZmMwOGE5ZjAxOTU5OTI5NWZiOGE5ZDFjIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:02:08 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjU3ZmlLTTVnend1VS9yVlpqT0R6TGc9PSIsInZhbHVlIjoiZzVqQzdtVlVCNEl2VnNOTlpIbk9lemRCYlJHWXZmTHpvUzg1RWRTQlRlUnU3QzlRZ3FvWDh0M3lKQnRGSVRuOE9kcGFXL1NRTUtET2xkNlFmRGxUUURlZWgxcXY4ejhSR3VhdEFwaU9TaWZuczNVOFBqQTZDc1U2Wm9GbnNkdkUiLCJtYWMiOiI5ZGY1OGE2MzI3ZTA2ZDhlOTMxY2MzZWI2MzY4MTEwM2IwY2Q3ZWIwY2NjNjQwZjI5Nzg3ODA0MzI0MzJjZThlIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:02:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im1laGRXVUJjRnhWNFJxU0d1VVBkQlE9PSIsInZhbHVlIjoiR2JZUzF3Ymh2TTZoKy82QXo2ak5KTXlkWGNUa1phd0xyVk50RGpvWUZBWURBLy9LaGdQSWhyUlpUU0tSU1BTbDErR1hMWFJyMzRTdHg1blRyQWVZRFRudklTYXp1RGZOR2N4TDBoci9sZXhZZXBrRnU4Q1h1eVM3Nk5hM0FidE0iLCJtYWMiOiI2NzkzMTE4ZDE4YjY1ODg0ZDI4MjE5OTFmY2Y1ZDg4ZGI1Mzk2NmQ0ZmMwOGE5ZjAxOTU5OTI5NWZiOGE5ZDFjIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:02:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1446385694\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1181070276 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/projects/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1181070276\", {\"maxDepth\":0})</script>\n"}}