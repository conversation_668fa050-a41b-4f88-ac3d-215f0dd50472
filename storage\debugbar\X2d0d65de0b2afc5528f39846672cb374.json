{"__meta": {"id": "X2d0d65de0b2afc5528f39846672cb374", "datetime": "2025-08-19 10:56:57", "utime": 1755572217.573809, "method": "POST", "uri": "/upload-radar-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[10:56:57] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572217.44382, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:57] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/upload-radar-chart", "message_html": null, "is_string": false, "label": "debug", "time": 1755572217.558844, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.974575, "end": 1755572217.573843, "duration": 0.5992679595947266, "duration_str": "599ms", "measures": [{"label": "Booting", "start": **********.974575, "relative_start": 0, "end": 1755572217.404056, "relative_end": 1755572217.404056, "duration": 0.42948102951049805, "duration_str": "429ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572217.404076, "relative_start": 0.4295010566711426, "end": 1755572217.573847, "relative_end": 4.0531158447265625e-06, "duration": 0.1697709560394287, "duration_str": "170ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23402120, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST upload-radar-chart", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@uploadRadarChart", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=141\">\\app\\Http\\Controllers\\CIGController.php:141-151</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.0008399999999999999, "accumulated_duration_str": "840μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/radar-data/42/36\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"path_info": "/upload-radar-chart", "status_code": "<pre class=sf-dump id=sf-dump-228806080 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-228806080\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-501285877 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"28282 characters\">data:image/png;base64,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</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-501285877\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">28294</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImVVNGhiL01IUVI2aHl5bGo3cVcvWFE9PSIsInZhbHVlIjoiY0V2QWVaNThzVEN2UUpYMnhsOWMrZmdFeG4zbE1LSGlsMllYamJQYTN6cEVNa0Y4L0Q1UDR4RWwrSmlJdDQyTGRjSk4yYTJQdUpVNTRxeWxVUWhqTjF6RmZGbGYrTFdCR2s4S24yUktJNEF1QlhyVzZHNVZHa0ZDTTFsbk41elAiLCJtYWMiOiJmNjY1NTM0Mjk0YTZhZTNkMzkwMmZiNWNjMjUwYjJlOTE1M2Q1MmRjZGYwNGMzMGNkN2FmMTY3NTQzYzBkZjRkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjlKUmd6RjJYb2ROWUsybDBwQkFiT3c9PSIsInZhbHVlIjoiNXU1OHRpVGk4YTE2eEJBQ3ZHZlAzeVBMTCtmSXltbHFlNUZ6UEdHV1liZWtMWjN4ekt5WVRGd3NjOStTV1pnekhLVzhia2cyM3VBdy9YaHBHbUREU1dPdnhKakVaN2ttdEQxbXQxMGoxbUpxczNwS2pRS096MGZPNk5XQllCMWUiLCJtYWMiOiJiY2ExNjBkMDUzNWIwYTFkNGE2MDJkMDRlOWM0MjFjZDExZmVhNDZmY2E3MDE3NzAyY2JlOWY5NzczYjA4ODAzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-317651147 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58232</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/index.php/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">28294</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">28294</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImVVNGhiL01IUVI2aHl5bGo3cVcvWFE9PSIsInZhbHVlIjoiY0V2QWVaNThzVEN2UUpYMnhsOWMrZmdFeG4zbE1LSGlsMllYamJQYTN6cEVNa0Y4L0Q1UDR4RWwrSmlJdDQyTGRjSk4yYTJQdUpVNTRxeWxVUWhqTjF6RmZGbGYrTFdCR2s4S24yUktJNEF1QlhyVzZHNVZHa0ZDTTFsbk41elAiLCJtYWMiOiJmNjY1NTM0Mjk0YTZhZTNkMzkwMmZiNWNjMjUwYjJlOTE1M2Q1MmRjZGYwNGMzMGNkN2FmMTY3NTQzYzBkZjRkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjlKUmd6RjJYb2ROWUsybDBwQkFiT3c9PSIsInZhbHVlIjoiNXU1OHRpVGk4YTE2eEJBQ3ZHZlAzeVBMTCtmSXltbHFlNUZ6UEdHV1liZWtMWjN4ekt5WVRGd3NjOStTV1pnekhLVzhia2cyM3VBdy9YaHBHbUREU1dPdnhKakVaN2ttdEQxbXQxMGoxbUpxczNwS2pRS096MGZPNk5XQllCMWUiLCJtYWMiOiJiY2ExNjBkMDUzNWIwYTFkNGE2MDJkMDRlOWM0MjFjZDExZmVhNDZmY2E3MDE3NzAyY2JlOWY5NzczYjA4ODAzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.9746</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-317651147\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1116465947 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:56:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjlyZWx0VldFVmgxV2FTa3NwMjFiMGc9PSIsInZhbHVlIjoiQno4SVBCeE9Ta2RnYUxFbnBzejhZSTZNWWFxd0dRVjBVbUFmeHgvTVBOSS9mWnRGY2E0YXJRZUpOaitlcDlqdWY1NEwrQmRGSmVYcEovelpDdnpnT0Rpcy94am1oMTZ0bEtzUmpmWXhjRGFmWThtaGRlY0N2QjladFBkSVBhZmoiLCJtYWMiOiI1ZWNiNjE0MDc1ZTY1ZDM2NjRlMWNlYjNmNGJiYTQwYWY3YzE1NjVhY2ZjNTU1NmQxMTg3OGIzMzk0NzU0ZTg2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:57 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IktnQ3N6ZzFYRHl3bWI5MXN2RkhWZFE9PSIsInZhbHVlIjoiak1rcHQwT3BhOFB6NE1EdlhUK3c1UDA0TWoxVjdIbFU2bFI1d1MvbmVva1lhdVVyaEV0S0VHRzRpU0tKdjNrc09scEhwSXZ0Rk9CN0NkekNMM1FoVUNvSmc2TkJZV1p2Nlh6NCtnbTMyYWpSbUdvTnZpWms5VVMxc1c4RmpvTkYiLCJtYWMiOiJmYjc4MWM2ZDMyNDMxMGM0MGFiMDJlNTA1YjhkYTkwMDBiMmI0MjljZTM1MzI5MTBhMzE3Y2U1N2JiNDgzNGQ1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:57 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjlyZWx0VldFVmgxV2FTa3NwMjFiMGc9PSIsInZhbHVlIjoiQno4SVBCeE9Ta2RnYUxFbnBzejhZSTZNWWFxd0dRVjBVbUFmeHgvTVBOSS9mWnRGY2E0YXJRZUpOaitlcDlqdWY1NEwrQmRGSmVYcEovelpDdnpnT0Rpcy94am1oMTZ0bEtzUmpmWXhjRGFmWThtaGRlY0N2QjladFBkSVBhZmoiLCJtYWMiOiI1ZWNiNjE0MDc1ZTY1ZDM2NjRlMWNlYjNmNGJiYTQwYWY3YzE1NjVhY2ZjNTU1NmQxMTg3OGIzMzk0NzU0ZTg2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IktnQ3N6ZzFYRHl3bWI5MXN2RkhWZFE9PSIsInZhbHVlIjoiak1rcHQwT3BhOFB6NE1EdlhUK3c1UDA0TWoxVjdIbFU2bFI1d1MvbmVva1lhdVVyaEV0S0VHRzRpU0tKdjNrc09scEhwSXZ0Rk9CN0NkekNMM1FoVUNvSmc2TkJZV1p2Nlh6NCtnbTMyYWpSbUdvTnZpWms5VVMxc1c4RmpvTkYiLCJtYWMiOiJmYjc4MWM2ZDMyNDMxMGM0MGFiMDJlNTA1YjhkYTkwMDBiMmI0MjljZTM1MzI5MTBhMzE3Y2U1N2JiNDgzNGQ1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1116465947\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-767263461 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/radar-data/42/36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-767263461\", {\"maxDepth\":0})</script>\n"}}