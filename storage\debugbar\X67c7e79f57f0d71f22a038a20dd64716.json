{"__meta": {"id": "X67c7e79f57f0d71f22a038a20dd64716", "datetime": "2025-08-19 13:39:23", "utime": 1755581963.378209, "method": "GET", "uri": "/backlogTest/43", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 37, "messages": [{"message": "[13:39:23] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755581963.221252, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/backlogTest/43", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.277391, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.info: BacklogController@index started {\"project_id\":\"43\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755581963.277496, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.info: Project found {\"project\":{\"id\":43,\"team_name\":\"Team 888\",\"proj_name\":\"Food Ordering System\",\"proj_desc\":\"this is a web-based food oerdering system\",\"start_date\":\"2025-08-19\",\"end_date\":\"2025-12-31\",\"shareable_slug\":null,\"created_at\":\"2025-08-19T05:31:41.000000Z\",\"updated_at\":\"2025-08-19T05:31:41.000000Z\"}}", "message_html": null, "is_string": false, "label": "info", "time": 1755581963.288394, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.info: Explicit active sprint check completed {\"proj_name\":\"Food Ordering System\",\"active_sprint_found\":true,\"active_sprint_data\":{\"sprint_id\":45,\"sprint_name\":\"Sprint 1\",\"sprint_desc\":\"this is sprint 1\",\"start_sprint\":\"2025-08-19\",\"end_sprint\":\"2025-09-02\",\"active_sprint\":1,\"proj_name\":\"Food Ordering System\",\"users_name\":\"ivlyn\",\"created_at\":\"2025-08-19T05:39:22.000000Z\",\"updated_at\":\"2025-08-19T05:39:22.000000Z\"}}", "message_html": null, "is_string": false, "label": "info", "time": 1755581963.301244, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.info: Active sprint ID determined {\"active_sprint_id\":45}", "message_html": null, "is_string": false, "label": "info", "time": 1755581963.301337, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.info: Processing user stories with active sprint {\"active_sprint_id\":45}", "message_html": null, "is_string": false, "label": "info", "time": 1755581963.301385, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.info: All non-done user stories retrieved {\"total_user_stories\":1,\"user_story_ids\":[57],\"done_status_id\":212}", "message_html": null, "is_string": false, "label": "info", "time": 1755581963.325305, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Processing user story {\"user_story_id\":57,\"user_story_sprint_id\":\"0\",\"active_sprint_id\":45}", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.325387, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: User story not in active sprint - adding to collection {\"user_story_id\":57}", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.325444, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.info: User stories filtering completed with active sprint {\"filtered_user_stories_count\":1,\"filtered_user_story_ids\":[57]}", "message_html": null, "is_string": false, "label": "info", "time": 1755581963.32551, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.info: Starting task retrieval for user stories", "message_html": null, "is_string": false, "label": "info", "time": 1755581963.32555, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Processing tasks for user story {\"user_story_id\":57}", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.337793, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Tasks filtered for active sprint and done status {\"user_story_id\":57,\"active_sprint_id\":45,\"done_task_status_id\":212,\"tasks_count\":2,\"task_ids\":[118,119]}", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.350423, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.info: Task retrieval completed {\"total_user_stories_with_tasks\":1,\"total_tasks_retrieved\":2}", "message_html": null, "is_string": false, "label": "info", "time": 1755581963.350491, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.info: BacklogController@index completed successfully {\"project_id\":\"43\",\"active_sprint_id\":45,\"user_stories_count\":1,\"tasks_by_user_story_count\":1,\"note\":\"Filtered out user stories with Done status and tasks with done status\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755581963.350542, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Gate check for permission: addToSprint_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.357365, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.358309, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.358438, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Gate check for permission: addToSprint_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.362066, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.362845, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.362891, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Gate check for permission: addToSprint_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.36318, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.36393, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.363978, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Gate check for permission: addUserStory_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.364229, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.364933, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.364977, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Gate check for permission: beginSprint_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.365239, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.366084, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.366129, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Gate check for permission: addToSprint_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.366416, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.367206, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.36725, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Gate check for permission: endSprint_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.367464, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.368164, "xdebug_link": null, "collector": "log"}, {"message": "[13:39:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581963.368208, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755581962.946956, "end": 1755581963.37826, "duration": 0.4313039779663086, "duration_str": "431ms", "measures": [{"label": "Booting", "start": 1755581962.946956, "relative_start": 0, "end": 1755581963.203122, "relative_end": 1755581963.203122, "duration": 0.2561659812927246, "duration_str": "256ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755581963.203133, "relative_start": 0.2561771869659424, "end": 1755581963.378262, "relative_end": 2.1457672119140625e-06, "duration": 0.17512893676757812, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24125144, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "backlogTest.index (\\resources\\views\\backlogTest\\index.blade.php)", "param_count": 4, "params": ["project", "userStories", "tasksByUserStory", "activeSprint"], "type": "blade"}]}, "route": {"uri": "GET backlogTest/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\BacklogController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "backlogTest.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\BacklogController.php&line=19\">\\app\\Http\\Controllers\\BacklogController.php:19-239</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.003, "accumulated_duration_str": "3ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 15.667}, {"sql": "select * from `projects` where `projects`.`id` = '43' limit 1", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 24}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:24", "connection": "sagile", "start_percent": 15.667, "width_percent": 14}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` = 1 limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 30}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:30", "connection": "sagile", "start_percent": 29.667, "width_percent": 15}, {"sql": "select * from `statuses` where `project_id` = '43' and `title` = 'Done' limit 1", "type": "query", "params": [], "bindings": ["43", "Done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 75}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:75", "connection": "sagile", "start_percent": 44.667, "width_percent": 16.667}, {"sql": "select * from `user_stories` where `proj_id` = '43' and `status_id` != 212", "type": "query", "params": [], "bindings": ["43", "212"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 82}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:82", "connection": "sagile", "start_percent": 61.333, "width_percent": 10.667}, {"sql": "select * from `statuses` where `project_id` = '43' and `slug` = 'done' limit 1", "type": "query", "params": [], "bindings": ["43", "done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 173}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:173", "connection": "sagile", "start_percent": 72, "width_percent": 14.333}, {"sql": "select * from `tasks` where `userstory_id` = 57 and (`sprint_id` != 45 or `sprint_id` is null or `sprint_id` = 0) and `status_id` != 212", "type": "query", "params": [], "bindings": ["57", "45", "0", "212"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 191}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:191", "connection": "sagile", "start_percent": 86.333, "width_percent": 13.667}]}, "models": {"data": {"App\\Task": 2, "App\\UserStory": 1, "App\\Status": 2, "App\\Sprint": 1, "App\\Project": 1, "App\\User": 1}, "count": 8}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 7, "messages": [{"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755581963.361221, "xdebug_link": null}, {"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-369360276 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-369360276\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755581963.363009, "xdebug_link": null}, {"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-387737616 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387737616\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755581963.364084, "xdebug_link": null}, {"message": "[\n  ability => addUserStory_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-544714868 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">addUserStory_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-544714868\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755581963.365082, "xdebug_link": null}, {"message": "[\n  ability => beginSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-66089856 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">beginSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-66089856\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755581963.366244, "xdebug_link": null}, {"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-632804690 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-632804690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755581963.367355, "xdebug_link": null}, {"message": "[\n  ability => endSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-129518042 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">endSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-129518042\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755581963.36831, "xdebug_link": null}]}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backlogTest/43\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755581799\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/backlogTest/43", "status_code": "<pre class=sf-dump id=sf-dump-1529253233 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1529253233\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1243972985 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1243972985\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-713835994 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-713835994\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1556284625 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkxORGdiZjhSSnFUclk3OUk4a283Smc9PSIsInZhbHVlIjoiMUVNemw3SVRjQmRjN0RYcTlXdjhpelNZWVpJNFpNS0pza0RrVjVTbUI3ZU51OVpUWkJYZElxS3o4RU90ellxT203ZjEwM2RBN0czWU9STEkwbSs0TlJMWTFjT1BXazdEVlYydEFReGRYeFBGanFNRzNqZXZOQmFCSXlrbm0va04iLCJtYWMiOiJmNGJjNzBjM2UzZWM1N2FiZmFkZjYyNzg0YWI0NmZmYzFhYTA0NjA2YjFkMWMwOWQzN2YzMGE2ZjZjZjcxZmI2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImZ0aENTUDJIS1owc1U3SXZuYmJrd3c9PSIsInZhbHVlIjoiSGdpNUVmbzg0Z3VNSEJzQ2Jhb3ZueStGSXQzV2E1dVJsWmxzTGlKRWhSZTc2OUVzMHBmTjZEbkx6MGh4aUc4UFVsRmU0bkt5VDgzaC9NaC91ZUVLYTBzYTZxTmpYc05XRnkzbTVsNFVVcURIN0o0SWJLakpJeXNUL0JuaGd1YnoiLCJtYWMiOiIwZmYwM2YzZjc4NmFkM2VjMWJlM2I3YWYwZmVhYjcxNTBlOGIxYmI3NzE1YzBmNTBkMDFiMmYyZjIyMTJlNTYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1556284625\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-321582454 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54121</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/43</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/43</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/index.php/backlogTest/43</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/43</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkxORGdiZjhSSnFUclk3OUk4a283Smc9PSIsInZhbHVlIjoiMUVNemw3SVRjQmRjN0RYcTlXdjhpelNZWVpJNFpNS0pza0RrVjVTbUI3ZU51OVpUWkJYZElxS3o4RU90ellxT203ZjEwM2RBN0czWU9STEkwbSs0TlJMWTFjT1BXazdEVlYydEFReGRYeFBGanFNRzNqZXZOQmFCSXlrbm0va04iLCJtYWMiOiJmNGJjNzBjM2UzZWM1N2FiZmFkZjYyNzg0YWI0NmZmYzFhYTA0NjA2YjFkMWMwOWQzN2YzMGE2ZjZjZjcxZmI2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImZ0aENTUDJIS1owc1U3SXZuYmJrd3c9PSIsInZhbHVlIjoiSGdpNUVmbzg0Z3VNSEJzQ2Jhb3ZueStGSXQzV2E1dVJsWmxzTGlKRWhSZTc2OUVzMHBmTjZEbkx6MGh4aUc4UFVsRmU0bkt5VDgzaC9NaC91ZUVLYTBzYTZxTmpYc05XRnkzbTVsNFVVcURIN0o0SWJLakpJeXNUL0JuaGd1YnoiLCJtYWMiOiIwZmYwM2YzZjc4NmFkM2VjMWJlM2I3YWYwZmVhYjcxNTBlOGIxYmI3NzE1YzBmNTBkMDFiMmYyZjIyMTJlNTYzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755581962.947</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755581962</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-321582454\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-689330172 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-689330172\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-420768418 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:39:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ii82ZTdPSUJZM2lNejZrYzl4UG1GZEE9PSIsInZhbHVlIjoiVzc2UXB6UUVkdnl4SGp0ZkIvTmZzb3VoRXJMWVF5V0JlZ3BaelBwbXJtSDlwNlptd3cxK1kvM2lZL3dOMDBUMmZ2dExFRjhRWGZlaGUxZGNJWmU0TGMyNWFra1JkZVU1RlV4YkltWkduVU04YndvbFlQdnpzbVhFSXF6ZUVFOUoiLCJtYWMiOiI4Y2Y0ZjM2MDlhN2M2MmE4NTIzNzcxOTI5NmRlNWRhMDcwNjg1MTU4MGJjMTRjYTI5M2VmOTRhNjIzMGE2ZTBlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:39:23 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InFwREt5MTE3T1BnZ2ZSM2pKOWFCOVE9PSIsInZhbHVlIjoiV1c3b0ZVNWNhYk9TUDRyWFFiek5jVHNGWmhwbytOMW9sT1JRUXI3N0c1b3lMTXNNNmNCbVNES2ExOWpvc0c1R3IwQjQ4R0czM0dWL1FCd2d4Z2FXYkhCcVJweGpTdzZrY29nZEJlRmkwTkdmd295QlRqc25mOE8zWFM5Q3RSbTIiLCJtYWMiOiI0NGJhZjUxYTMxZDYxOTQ1M2FhYTU0N2JiZDBiZWQ0MTY1ZTJhNGNjZDMwZTZlNGQxNGEzNDM0ZTIyYmY0Yzk5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:39:23 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ii82ZTdPSUJZM2lNejZrYzl4UG1GZEE9PSIsInZhbHVlIjoiVzc2UXB6UUVkdnl4SGp0ZkIvTmZzb3VoRXJMWVF5V0JlZ3BaelBwbXJtSDlwNlptd3cxK1kvM2lZL3dOMDBUMmZ2dExFRjhRWGZlaGUxZGNJWmU0TGMyNWFra1JkZVU1RlV4YkltWkduVU04YndvbFlQdnpzbVhFSXF6ZUVFOUoiLCJtYWMiOiI4Y2Y0ZjM2MDlhN2M2MmE4NTIzNzcxOTI5NmRlNWRhMDcwNjg1MTU4MGJjMTRjYTI5M2VmOTRhNjIzMGE2ZTBlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:39:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InFwREt5MTE3T1BnZ2ZSM2pKOWFCOVE9PSIsInZhbHVlIjoiV1c3b0ZVNWNhYk9TUDRyWFFiek5jVHNGWmhwbytOMW9sT1JRUXI3N0c1b3lMTXNNNmNCbVNES2ExOWpvc0c1R3IwQjQ4R0czM0dWL1FCd2d4Z2FXYkhCcVJweGpTdzZrY29nZEJlRmkwTkdmd295QlRqc25mOE8zWFM5Q3RSbTIiLCJtYWMiOiI0NGJhZjUxYTMxZDYxOTQ1M2FhYTU0N2JiZDBiZWQ0MTY1ZTJhNGNjZDMwZTZlNGQxNGEzNDM0ZTIyYmY0Yzk5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:39:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-420768418\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2125786620 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755581799</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2125786620\", {\"maxDepth\":0})</script>\n"}}