{"__meta": {"id": "Xa7d95148c02e86239f4463715491cfb5", "datetime": "2025-08-19 14:18:18", "utime": 1755584298.985043, "method": "GET", "uri": "/backlogTest/45", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 15, "messages": [{"message": "[14:18:18] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584298.811185, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:18] LOG.info: BacklogController@index started {\"project_id\":\"45\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755584298.871032, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:18] LOG.info: Project found {\"project\":{\"id\":45,\"team_name\":\"Team AD\",\"proj_name\":\"<PERSON><PERSON><PERSON>\",\"proj_desc\":\"aa\",\"start_date\":\"2025-08-19\",\"end_date\":\"2025-12-31\",\"shareable_slug\":null,\"created_at\":\"2025-08-19T06:06:36.000000Z\",\"updated_at\":\"2025-08-19T06:06:36.000000Z\"}}", "message_html": null, "is_string": false, "label": "info", "time": 1755584298.883648, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:18] LOG.info: Explicit active sprint check completed {\"proj_name\":\"<PERSON><PERSON>le\",\"active_sprint_found\":false,\"active_sprint_data\":null}", "message_html": null, "is_string": false, "label": "info", "time": 1755584298.896099, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:18] LOG.info: No explicit active sprint found, checking NULL active_sprint sprints", "message_html": null, "is_string": false, "label": "info", "time": 1755584298.896158, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:18] LOG.info: Active sprint ID determined {\"active_sprint_id\":null}", "message_html": null, "is_string": false, "label": "info", "time": 1755584298.908172, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:18] LOG.info: No active sprint - getting backlog user stories", "message_html": null, "is_string": false, "label": "info", "time": 1755584298.90823, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:18] LOG.info: Backlog non-done user stories retrieved {\"backlog_user_stories_count\":0,\"backlog_user_story_ids\":[],\"done_status_id\":220}", "message_html": null, "is_string": false, "label": "info", "time": 1755584298.940339, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:18] LOG.info: Starting task retrieval for user stories", "message_html": null, "is_string": false, "label": "info", "time": 1755584298.940398, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:18] LOG.info: Task retrieval completed {\"total_user_stories_with_tasks\":0,\"total_tasks_retrieved\":0}", "message_html": null, "is_string": false, "label": "info", "time": 1755584298.952701, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:18] LOG.info: BacklogController@index completed successfully {\"project_id\":\"45\",\"active_sprint_id\":null,\"user_stories_count\":0,\"tasks_by_user_story_count\":0,\"note\":\"Filtered out user stories with Done status and tasks with done status\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755584298.952765, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:18] LOG.debug: Gate check for permission: addUserStory_backlog on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584298.960776, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:18] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584298.961828, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:18] LOG.debug: Gate check for permission: beginSprint_backlog on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584298.966369, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:18] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584298.96721, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584298.508114, "end": 1755584298.985081, "duration": 0.47696685791015625, "duration_str": "477ms", "measures": [{"label": "Booting", "start": 1755584298.508114, "relative_start": 0, "end": 1755584298.791333, "relative_end": 1755584298.791333, "duration": 0.2832188606262207, "duration_str": "283ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584298.791343, "relative_start": 0.28322887420654297, "end": 1755584298.985084, "relative_end": 3.0994415283203125e-06, "duration": 0.1937410831451416, "duration_str": "194ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24076792, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "backlogTest.index (\\resources\\views\\backlogTest\\index.blade.php)", "param_count": 4, "params": ["project", "userStories", "tasksByUserStory", "activeSprint"], "type": "blade"}, {"name": "inc.character-counter (\\resources\\views\\inc\\character-counter.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "project", "userStories", "tasksByUserStory", "activeSprint", "inputId", "counterId", "max<PERSON><PERSON><PERSON>"], "type": "blade"}, {"name": "inc.character-counter (\\resources\\views\\inc\\character-counter.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "project", "userStories", "tasksByUserStory", "activeSprint", "inputId", "counterId", "max<PERSON><PERSON><PERSON>"], "type": "blade"}]}, "route": {"uri": "GET backlogTest/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\BacklogController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "backlogTest.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\BacklogController.php&line=19\">\\app\\Http\\Controllers\\BacklogController.php:19-239</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.009600000000000001, "accumulated_duration_str": "9.6ms", "statements": [{"sql": "select * from `users` where `id` = 31 limit 1", "type": "query", "params": [], "bindings": ["31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 4.896}, {"sql": "select * from `projects` where `projects`.`id` = '45' limit 1", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 24}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:24", "connection": "sagile", "start_percent": 4.896, "width_percent": 4.479}, {"sql": "select * from `sprint` where `proj_name` = 'SAgile' and `active_sprint` = 1 limit 1", "type": "query", "params": [], "bindings": ["SAgile", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 30}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:30", "connection": "sagile", "start_percent": 9.375, "width_percent": 4.479}, {"sql": "select * from `sprint` where `proj_name` = 'SAgile' and `active_sprint` is null and `start_sprint` <= '2025-08-19 14:18:18' and `end_sprint` >= '2025-08-19 14:18:18' limit 1", "type": "query", "params": [], "bindings": ["SAgile", "2025-08-19 14:18:18", "2025-08-19 14:18:18"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 48}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:48", "connection": "sagile", "start_percent": 13.854, "width_percent": 4.479}, {"sql": "select * from `statuses` where `project_id` = '45' and `title` = 'Done' limit 1", "type": "query", "params": [], "bindings": ["45", "Done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 144}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00683, "duration_str": "6.83ms", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:144", "connection": "sagile", "start_percent": 18.333, "width_percent": 71.146}, {"sql": "select * from `user_stories` where `proj_id` = '45' and (`sprint_id` = 0 or `sprint_id` is null) and `status_id` != 220", "type": "query", "params": [], "bindings": ["45", "0", "220"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 157}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:157", "connection": "sagile", "start_percent": 89.479, "width_percent": 4.479}, {"sql": "select * from `statuses` where `project_id` = '45' and `slug` = 'done' limit 1", "type": "query", "params": [], "bindings": ["45", "done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 173}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:173", "connection": "sagile", "start_percent": 93.958, "width_percent": 6.042}]}, "models": {"data": {"App\\Status": 2, "App\\Project": 1, "App\\User": 1}, "count": 4}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[\n  ability => addUserStory_backlog,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">addUserStory_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584298.964988, "xdebug_link": null}, {"message": "[\n  ability => beginSprint_backlog,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1356467467 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">beginSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1356467467\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584298.967314, "xdebug_link": null}]}, "session": {"_token": "p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backlogTest/45\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "31", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755584266\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/backlogTest/45", "status_code": "<pre class=sf-dump id=sf-dump-1791108877 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1791108877\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-694130046 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-694130046\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1419111775 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1419111775\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-860724631 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlB2NXNnOEh1SFBOL0JEQWJaa3YvVlE9PSIsInZhbHVlIjoiLzQwMlMrTm5BWGlFczZUYVI3NXErNXdWckJyMm14V05PUWZPTDBSNVpvMmM2dWFYdzh6TGVNK3IyZ3FzZUdDczcvb3Jwc003Ujl4cXdNTjJjM1lieCtwbU5MT2lMdlJBZXFDMnF2TGp6VUxmaVJ4dk1SRzBmSnZVUUJYUFYzY1giLCJtYWMiOiJkZjIyYzhmYmRmMDE2ZDUwN2IzNjcwMGM2NDA0Njk0NDgxZDdiZDAyYTEzMTBkYmJjMDkwY2U0ZjQzMGY1YWViIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlcxdXVQeHhGNGlGeTduQUhPL2J1dmc9PSIsInZhbHVlIjoiWGVFUXUvVzg0dGxSRUNlOG9iZTFpSkV3NmZpb3N6emdQQWh1bmZIS1VsTkthb0QxVmszaHI5RUFEY2hYNTBja3kycDY0MjBvcHNMaWZ4SjJhWkcrQkk4QTZ5NDdOV29DUXdBOUxrNGpud2doNFRub1hNdnpoVTlsZks4WGRIZGEiLCJtYWMiOiI1MGQ1ZTcxZjJjMjI4MDAxZWRmOTM3MzEzZDgzZDEwNWVmMmNiNzE0YjE2YWNkMzUyZjU5MzE5ZDUwZGNlOTI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860724631\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-2030214533 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59776</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/45</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/45</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/index.php/backlogTest/45</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/45</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlB2NXNnOEh1SFBOL0JEQWJaa3YvVlE9PSIsInZhbHVlIjoiLzQwMlMrTm5BWGlFczZUYVI3NXErNXdWckJyMm14V05PUWZPTDBSNVpvMmM2dWFYdzh6TGVNK3IyZ3FzZUdDczcvb3Jwc003Ujl4cXdNTjJjM1lieCtwbU5MT2lMdlJBZXFDMnF2TGp6VUxmaVJ4dk1SRzBmSnZVUUJYUFYzY1giLCJtYWMiOiJkZjIyYzhmYmRmMDE2ZDUwN2IzNjcwMGM2NDA0Njk0NDgxZDdiZDAyYTEzMTBkYmJjMDkwY2U0ZjQzMGY1YWViIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlcxdXVQeHhGNGlGeTduQUhPL2J1dmc9PSIsInZhbHVlIjoiWGVFUXUvVzg0dGxSRUNlOG9iZTFpSkV3NmZpb3N6emdQQWh1bmZIS1VsTkthb0QxVmszaHI5RUFEY2hYNTBja3kycDY0MjBvcHNMaWZ4SjJhWkcrQkk4QTZ5NDdOV29DUXdBOUxrNGpud2doNFRub1hNdnpoVTlsZks4WGRIZGEiLCJtYWMiOiI1MGQ1ZTcxZjJjMjI4MDAxZWRmOTM3MzEzZDgzZDEwNWVmMmNiNzE0YjE2YWNkMzUyZjU5MzE5ZDUwZGNlOTI0IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584298.5081</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584298</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2030214533\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1063647714 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CfapZDSBofQwHxJsJhdmHULLiISKd6PUgVWjQqhr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1063647714\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2106917968 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:18:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkMxeW1mZ1EwRDkrQXFCYnNhajJmTEE9PSIsInZhbHVlIjoicFpibWQ3OHlNOG5lUC9hcmNsS243VCtDTGFyaU9iNmpuaXFoQUxmQ2poMGxINStwRFlMZjdEamJEelhzemo3UlJVQjFKQURwTDhoRWZ2QmxSSkZreHd2OWl5ZzU3TnhaRlhQZTVkM2srQkZUdTRjY2NoRUk2cXpsS050RnVDNWQiLCJtYWMiOiJmMDkxNDk2NDI0ZDg1NWQ4NzkxNTk3ODgyMzZkYmIxNjA2YThjMmZjYThjMjAyZDMwMGMwZjcxMWYyZjBlMmVkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:18:18 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkFXNnhTemIxVzllUDFaUmNaZEVXZFE9PSIsInZhbHVlIjoieUQ5ZVlBTnlNZGhqQW5UQkE3bEtvMExhYWxEU25ZekhFNklqOHluVTFqa1k4cWxGcmh4cUhkV2R2bWkxL1RYSlJSVEpzK2dlajdXeWZhd0ZaOGRsY0pZc1lMK0drdjFqL2dzdlp6ZkEvekdWYWRQVCtiMTd6ZUdEQUpxcE9JZE0iLCJtYWMiOiIwOWZlNTFlZmZlY2Y2YmE1NmQ1MzNmMTg0ZGY3NjM2OWJmMzg5MjVmNmVkNTFkMDhkMjFhYzIwYTQ4MDU4YjBlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:18:18 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkMxeW1mZ1EwRDkrQXFCYnNhajJmTEE9PSIsInZhbHVlIjoicFpibWQ3OHlNOG5lUC9hcmNsS243VCtDTGFyaU9iNmpuaXFoQUxmQ2poMGxINStwRFlMZjdEamJEelhzemo3UlJVQjFKQURwTDhoRWZ2QmxSSkZreHd2OWl5ZzU3TnhaRlhQZTVkM2srQkZUdTRjY2NoRUk2cXpsS050RnVDNWQiLCJtYWMiOiJmMDkxNDk2NDI0ZDg1NWQ4NzkxNTk3ODgyMzZkYmIxNjA2YThjMmZjYThjMjAyZDMwMGMwZjcxMWYyZjBlMmVkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:18:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkFXNnhTemIxVzllUDFaUmNaZEVXZFE9PSIsInZhbHVlIjoieUQ5ZVlBTnlNZGhqQW5UQkE3bEtvMExhYWxEU25ZekhFNklqOHluVTFqa1k4cWxGcmh4cUhkV2R2bWkxL1RYSlJSVEpzK2dlajdXeWZhd0ZaOGRsY0pZc1lMK0drdjFqL2dzdlp6ZkEvekdWYWRQVCtiMTd6ZUdEQUpxcE9JZE0iLCJtYWMiOiIwOWZlNTFlZmZlY2Y2YmE1NmQ1MzNmMTg0ZGY3NjM2OWJmMzg5MjVmNmVkNTFkMDhkMjFhYzIwYTQ4MDU4YjBlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:18:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2106917968\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1161534588 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755584266</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1161534588\", {\"maxDepth\":0})</script>\n"}}