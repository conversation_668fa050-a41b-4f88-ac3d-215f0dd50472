{"__meta": {"id": "Xd3c0a5db4860317b5ba3f133fe360bff", "datetime": "2025-08-19 14:27:16", "utime": 1755584836.916434, "method": "POST", "uri": "/projects", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[14:27:15] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584835.30457, "xdebug_link": null, "collector": "log"}, {"message": "[14:27:15] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/projects", "message_html": null, "is_string": false, "label": "debug", "time": 1755584835.385034, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584834.956168, "end": 1755584836.916454, "duration": 1.9602861404418945, "duration_str": "1.96s", "measures": [{"label": "Booting", "start": 1755584834.956168, "relative_start": 0, "end": 1755584835.281887, "relative_end": 1755584835.281887, "duration": 0.32571911811828613, "duration_str": "326ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584835.2819, "relative_start": 0.3257319927215576, "end": 1755584836.916456, "relative_end": 1.9073486328125e-06, "duration": 1.6345560550689697, "duration_str": "1.63s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25516856, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 9, "templates": [{"name": "project.newIndex (\\resources\\views\\project\\newIndex.blade.php)", "param_count": 1, "params": ["projects"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 9, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "projects", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "POST projects", "middleware": "web", "controller": "App\\Http\\Controllers\\ProjectController@store", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "projects.store", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProjectController.php&line=75\">\\app\\Http\\Controllers\\ProjectController.php:75-220</a>"}, "queries": {"nb_statements": 98, "nb_failed_statements": 0, "accumulated_duration": 0.08374000000000002, "accumulated_duration_str": "83.74ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 0.633}, {"sql": "select count(*) as aggregate from `projects` where `proj_name` = 'SAgile'", "type": "query", "params": [], "bindings": ["SAgile"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 447}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "sagile", "start_percent": 0.633, "width_percent": 0.478}, {"sql": "insert into `projects` (`proj_name`, `proj_desc`, `start_date`, `end_date`, `team_name`, `updated_at`, `created_at`) values ('SAgile', 'aa', '2025-08-19', '2025-12-31', 'Team 888', '2025-08-19 14:27:15', '2025-08-19 14:27:15')", "type": "query", "params": [], "bindings": ["SAgile", "aa", "2025-08-19", "2025-12-31", "Team 888", "2025-08-19 14:27:15", "2025-08-19 14:27:15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 98}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00116, "duration_str": "1.16ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:98", "connection": "sagile", "start_percent": 1.111, "width_percent": 1.385}, {"sql": "insert into `statuses` (`title`, `slug`, `order`, `project_id`) values ('Backlog', 'backlog', 1, 46)", "type": "query", "params": [], "bindings": ["Backlog", "backlog", "1", "46"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Project.php", "line": 22}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 98}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00098, "duration_str": "980μs", "stmt_id": "\\app\\Project.php:22", "connection": "sagile", "start_percent": 2.496, "width_percent": 1.17}, {"sql": "insert into `statuses` (`title`, `slug`, `order`, `project_id`) values ('Up Next', 'up-next', 2, 46)", "type": "query", "params": [], "bindings": ["Up Next", "up-next", "2", "46"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Project.php", "line": 22}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 98}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00108, "duration_str": "1.08ms", "stmt_id": "\\app\\Project.php:22", "connection": "sagile", "start_percent": 3.666, "width_percent": 1.29}, {"sql": "insert into `statuses` (`title`, `slug`, `order`, `project_id`) values ('In Progress', 'in-progress', 3, 46)", "type": "query", "params": [], "bindings": ["In Progress", "in-progress", "3", "46"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Project.php", "line": 22}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 98}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "\\app\\Project.php:22", "connection": "sagile", "start_percent": 4.956, "width_percent": 1.158}, {"sql": "insert into `statuses` (`title`, `slug`, `order`, `project_id`) values ('Done', 'done', 4, 46)", "type": "query", "params": [], "bindings": ["Done", "done", "4", "46"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Project.php", "line": 22}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 98}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0010500000000000002, "duration_str": "1.05ms", "stmt_id": "\\app\\Project.php:22", "connection": "sagile", "start_percent": 6.114, "width_percent": 1.254}, {"sql": "insert into `roles` (`role_name`, `project_id`, `updated_at`, `created_at`) values ('Project Manager', 46, '2025-08-19 14:27:15', '2025-08-19 14:27:15')", "type": "query", "params": [], "bindings": ["Project Manager", "46", "2025-08-19 14:27:15", "2025-08-19 14:27:15"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 104}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:104", "connection": "sagile", "start_percent": 7.368, "width_percent": 1.182}, {"sql": "insert into `roles` (`role_name`, `project_id`, `updated_at`, `created_at`) values ('Developer', 46, '2025-08-19 14:27:15', '2025-08-19 14:27:15')", "type": "query", "params": [], "bindings": ["Developer", "46", "2025-08-19 14:27:15", "2025-08-19 14:27:15"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 110}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:110", "connection": "sagile", "start_percent": 8.55, "width_percent": 1.111}, {"sql": "select `id` from `permission` where `key` in ('view_roles', 'addLane_kanban', 'addTask_kanban', 'editLane_kanban', 'deleteLane_kanban', 'deleteTask_kanban', 'addComment_kanban', 'updateTaskStatus_kanban', 'editTask_kanban', 'addUserStory_backlog', 'beginSprint_backlog', 'addToSprint_backlog', 'endSprint_backlog', 'add_userstory', 'edit_userstory', 'delete_userstory', 'editStatus_userstory', 'view_task', 'add_task', 'edit_task', 'delete_task', 'viewCalendar_task', 'viewComments_task', 'add_roles', 'edit_roles', 'delete_roles', 'updateUserRole_roles', 'add_status', 'edit_status', 'delete_status', 'edit_details', 'delete_details', 'share_details', 'view_sprintArchive', 'viewKanbanArchive_sprintArchive', 'viewBurndownArchive_sprintArchive', 'view_kanban', 'view_burndown', 'view_backlog', 'view_userstory', 'view_forum', 'view_bugtracking', 'view_status', 'view_details')", "type": "query", "params": [], "bindings": ["view_roles", "addLane_kanban", "addTask_kanban", "editLane_kanban", "deleteLane_kanban", "deleteTask_kanban", "addComment_kanban", "updateTaskStatus_kanban", "editTask_kanban", "addUserStory_backlog", "beginSprint_backlog", "addToSprint_backlog", "endSprint_backlog", "add_userstory", "edit_userstory", "delete_userstory", "editStatus_userstory", "view_task", "add_task", "edit_task", "delete_task", "viewCalendar_task", "viewComments_task", "add_roles", "edit_roles", "delete_roles", "updateUserRole_roles", "add_status", "edit_status", "delete_status", "edit_details", "delete_details", "share_details", "view_sprintArchive", "viewKanbanArchive_sprintArchive", "viewBurndownArchive_sprintArchive", "view_kanban", "view_burndown", "view_backlog", "view_userstory", "view_forum", "view_bugtracking", "view_status", "view_details"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 148}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:148", "connection": "sagile", "start_percent": 9.661, "width_percent": 0.609}, {"sql": "select `id` from `permission` where `key` in ('view_roles', 'addLane_kanban', 'addTask_kanban', 'editLane_kanban', 'deleteLane_kanban', 'deleteTask_kanban', 'addComment_kanban', 'updateTaskStatus_kanban', 'editTask_kanban', 'addUserStory_backlog', 'beginSprint_backlog', 'addToSprint_backlog', 'endSprint_backlog', 'add_userstory', 'edit_userstory', 'delete_userstory', 'editStatus_userstory', 'view_task', 'add_task', 'edit_task', 'delete_task', 'viewCalendar_task', 'viewComments_task', 'view_sprintArchive', 'viewKanbanArchive_sprintArchive', 'viewBurndownArchive_sprintArchive', 'view_kanban', 'view_burndown', 'view_backlog', 'view_userstory', 'view_forum', 'view_bugtracking', 'view_status', 'view_details')", "type": "query", "params": [], "bindings": ["view_roles", "addLane_kanban", "addTask_kanban", "editLane_kanban", "deleteLane_kanban", "deleteTask_kanban", "addComment_kanban", "updateTaskStatus_kanban", "editTask_kanban", "addUserStory_backlog", "beginSprint_backlog", "addToSprint_backlog", "endSprint_backlog", "add_userstory", "edit_userstory", "delete_userstory", "editStatus_userstory", "view_task", "add_task", "edit_task", "delete_task", "viewCalendar_task", "viewComments_task", "view_sprintArchive", "viewKanbanArchive_sprintArchive", "viewBurndownArchive_sprintArchive", "view_kanban", "view_burndown", "view_backlog", "view_userstory", "view_forum", "view_bugtracking", "view_status", "view_details"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 151}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:151", "connection": "sagile", "start_percent": 10.27, "width_percent": 0.621}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (1, 39)", "type": "query", "params": [], "bindings": ["1", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0013, "duration_str": "1.3ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 10.891, "width_percent": 1.552}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (2, 39)", "type": "query", "params": [], "bindings": ["2", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 12.443, "width_percent": 1.099}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (3, 39)", "type": "query", "params": [], "bindings": ["3", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 13.542, "width_percent": 1.075}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (4, 39)", "type": "query", "params": [], "bindings": ["4", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 14.617, "width_percent": 1.123}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (5, 39)", "type": "query", "params": [], "bindings": ["5", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 15.739, "width_percent": 1.182}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (6, 39)", "type": "query", "params": [], "bindings": ["6", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 16.921, "width_percent": 1.158}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (7, 39)", "type": "query", "params": [], "bindings": ["7", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 18.08, "width_percent": 1.111}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (8, 39)", "type": "query", "params": [], "bindings": ["8", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 19.19, "width_percent": 1.23}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (9, 39)", "type": "query", "params": [], "bindings": ["9", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 20.42, "width_percent": 1.182}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (10, 39)", "type": "query", "params": [], "bindings": ["10", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 21.603, "width_percent": 1.123}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (11, 39)", "type": "query", "params": [], "bindings": ["11", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 22.725, "width_percent": 1.158}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (12, 39)", "type": "query", "params": [], "bindings": ["12", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00095, "duration_str": "950μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 23.883, "width_percent": 1.134}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (13, 39)", "type": "query", "params": [], "bindings": ["13", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 25.018, "width_percent": 1.206}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (14, 39)", "type": "query", "params": [], "bindings": ["14", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00114, "duration_str": "1.14ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 26.224, "width_percent": 1.361}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (15, 39)", "type": "query", "params": [], "bindings": ["15", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 27.585, "width_percent": 1.111}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (16, 39)", "type": "query", "params": [], "bindings": ["16", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 28.696, "width_percent": 0.979}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (17, 39)", "type": "query", "params": [], "bindings": ["17", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 29.675, "width_percent": 0.92}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (18, 39)", "type": "query", "params": [], "bindings": ["18", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 30.595, "width_percent": 0.943}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (19, 39)", "type": "query", "params": [], "bindings": ["19", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 31.538, "width_percent": 0.955}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (20, 39)", "type": "query", "params": [], "bindings": ["20", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 32.493, "width_percent": 0.896}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (21, 39)", "type": "query", "params": [], "bindings": ["21", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 33.389, "width_percent": 0.943}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (22, 39)", "type": "query", "params": [], "bindings": ["22", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 34.332, "width_percent": 0.896}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (23, 39)", "type": "query", "params": [], "bindings": ["23", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 35.228, "width_percent": 0.86}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (24, 39)", "type": "query", "params": [], "bindings": ["24", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 36.088, "width_percent": 0.943}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (25, 39)", "type": "query", "params": [], "bindings": ["25", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 37.031, "width_percent": 0.92}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (26, 39)", "type": "query", "params": [], "bindings": ["26", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 37.951, "width_percent": 0.872}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (27, 39)", "type": "query", "params": [], "bindings": ["27", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 38.823, "width_percent": 0.967}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (28, 39)", "type": "query", "params": [], "bindings": ["28", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00074, "duration_str": "740μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 39.79, "width_percent": 0.884}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (29, 39)", "type": "query", "params": [], "bindings": ["29", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 40.674, "width_percent": 0.979}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (30, 39)", "type": "query", "params": [], "bindings": ["30", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 41.653, "width_percent": 0.908}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (31, 39)", "type": "query", "params": [], "bindings": ["31", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 42.56, "width_percent": 0.896}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (32, 39)", "type": "query", "params": [], "bindings": ["32", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 43.456, "width_percent": 0.943}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (33, 39)", "type": "query", "params": [], "bindings": ["33", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 44.399, "width_percent": 0.943}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (34, 39)", "type": "query", "params": [], "bindings": ["34", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00074, "duration_str": "740μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 45.343, "width_percent": 0.884}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (35, 39)", "type": "query", "params": [], "bindings": ["35", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 46.226, "width_percent": 0.92}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (36, 39)", "type": "query", "params": [], "bindings": ["36", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00156, "duration_str": "1.56ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 47.146, "width_percent": 1.863}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (37, 39)", "type": "query", "params": [], "bindings": ["37", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 49.009, "width_percent": 1.087}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (38, 39)", "type": "query", "params": [], "bindings": ["38", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 50.096, "width_percent": 0.896}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (39, 39)", "type": "query", "params": [], "bindings": ["39", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 50.991, "width_percent": 0.979}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (40, 39)", "type": "query", "params": [], "bindings": ["40", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 51.97, "width_percent": 0.991}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (41, 39)", "type": "query", "params": [], "bindings": ["41", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00085, "duration_str": "850μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 52.962, "width_percent": 1.015}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (42, 39)", "type": "query", "params": [], "bindings": ["42", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 53.977, "width_percent": 1.206}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (43, 39)", "type": "query", "params": [], "bindings": ["43", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00107, "duration_str": "1.07ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 55.183, "width_percent": 1.278}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (44, 39)", "type": "query", "params": [], "bindings": ["44", "39"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 155}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00095, "duration_str": "950μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:155", "connection": "sagile", "start_percent": 56.46, "width_percent": 1.134}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (1, 40)", "type": "query", "params": [], "bindings": ["1", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 57.595, "width_percent": 1.099}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (2, 40)", "type": "query", "params": [], "bindings": ["2", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 58.694, "width_percent": 1.194}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (3, 40)", "type": "query", "params": [], "bindings": ["3", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 59.888, "width_percent": 1.158}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (4, 40)", "type": "query", "params": [], "bindings": ["4", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 61.046, "width_percent": 1.158}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (5, 40)", "type": "query", "params": [], "bindings": ["5", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 62.204, "width_percent": 1.421}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (6, 40)", "type": "query", "params": [], "bindings": ["6", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00098, "duration_str": "980μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 63.626, "width_percent": 1.17}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (7, 40)", "type": "query", "params": [], "bindings": ["7", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00102, "duration_str": "1.02ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 64.796, "width_percent": 1.218}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (8, 40)", "type": "query", "params": [], "bindings": ["8", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 66.014, "width_percent": 1.194}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (9, 40)", "type": "query", "params": [], "bindings": ["9", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 67.208, "width_percent": 1.158}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (10, 40)", "type": "query", "params": [], "bindings": ["10", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008900000000000001, "duration_str": "890μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 68.366, "width_percent": 1.063}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (11, 40)", "type": "query", "params": [], "bindings": ["11", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008900000000000001, "duration_str": "890μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 69.429, "width_percent": 1.063}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (12, 40)", "type": "query", "params": [], "bindings": ["12", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 70.492, "width_percent": 1.099}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (13, 40)", "type": "query", "params": [], "bindings": ["13", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 71.591, "width_percent": 1.206}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (14, 40)", "type": "query", "params": [], "bindings": ["14", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00085, "duration_str": "850μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 72.797, "width_percent": 1.015}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (15, 40)", "type": "query", "params": [], "bindings": ["15", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 73.812, "width_percent": 1.099}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (16, 40)", "type": "query", "params": [], "bindings": ["16", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 74.91, "width_percent": 1.206}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (17, 40)", "type": "query", "params": [], "bindings": ["17", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0012, "duration_str": "1.2ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 76.117, "width_percent": 1.433}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (18, 40)", "type": "query", "params": [], "bindings": ["18", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 77.55, "width_percent": 0.967}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (19, 40)", "type": "query", "params": [], "bindings": ["19", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 78.517, "width_percent": 0.979}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (20, 40)", "type": "query", "params": [], "bindings": ["20", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 79.496, "width_percent": 0.955}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (21, 40)", "type": "query", "params": [], "bindings": ["21", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 80.451, "width_percent": 0.848}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (22, 40)", "type": "query", "params": [], "bindings": ["22", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 81.299, "width_percent": 0.848}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (23, 40)", "type": "query", "params": [], "bindings": ["23", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 82.147, "width_percent": 0.812}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (24, 40)", "type": "query", "params": [], "bindings": ["24", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 82.959, "width_percent": 0.967}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (25, 40)", "type": "query", "params": [], "bindings": ["25", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 83.926, "width_percent": 0.931}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (26, 40)", "type": "query", "params": [], "bindings": ["26", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 84.858, "width_percent": 0.92}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (27, 40)", "type": "query", "params": [], "bindings": ["27", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 85.777, "width_percent": 0.979}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (28, 40)", "type": "query", "params": [], "bindings": ["28", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 86.757, "width_percent": 0.967}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (37, 40)", "type": "query", "params": [], "bindings": ["37", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 87.724, "width_percent": 0.955}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (38, 40)", "type": "query", "params": [], "bindings": ["38", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 88.679, "width_percent": 0.824}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (39, 40)", "type": "query", "params": [], "bindings": ["39", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 89.503, "width_percent": 0.943}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (40, 40)", "type": "query", "params": [], "bindings": ["40", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 90.447, "width_percent": 0.8}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (41, 40)", "type": "query", "params": [], "bindings": ["41", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 91.247, "width_percent": 0.931}, {"sql": "insert into `permission_role` (`permission_id`, `role_id`) values (43, 40)", "type": "query", "params": [], "bindings": ["43", "40"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 160}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:160", "connection": "sagile", "start_percent": 92.178, "width_percent": 0.908}, {"sql": "select * from `teammappings` where `team_name` = 'Team 888'", "type": "query", "params": [], "bindings": ["Team 888"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 171}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:171", "connection": "sagile", "start_percent": 93.086, "width_percent": 0.537}, {"sql": "insert into `teammappings` (`username`, `team_name`, `project_id`, `invitation_status`, `role_name`, `updated_at`, `created_at`) values ('ivlyn', 'Team 888', 46, 'accepted', 'Project Manager', '2025-08-19 14:27:16', '2025-08-19 14:27:16')", "type": "query", "params": [], "bindings": ["ivlyn", "Team 888", "46", "accepted", "Project Manager", "2025-08-19 14:27:16", "2025-08-19 14:27:16"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 190}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:190", "connection": "sagile", "start_percent": 93.623, "width_percent": 1.051}, {"sql": "insert into `teammappings` (`username`, `team_name`, `project_id`, `invitation_status`, `role_name`, `updated_at`, `created_at`) values ('tay', 'Team 888', 46, 'accepted', 'Developer', '2025-08-19 14:27:16', '2025-08-19 14:27:16')", "type": "query", "params": [], "bindings": ["tay", "Team 888", "46", "accepted", "Developer", "2025-08-19 14:27:16", "2025-08-19 14:27:16"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 190}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:190", "connection": "sagile", "start_percent": 94.674, "width_percent": 0.8}, {"sql": "select * from `teammappings` where `project_id` = 46", "type": "query", "params": [], "bindings": ["46"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 341}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 194}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:341", "connection": "sagile", "start_percent": 95.474, "width_percent": 0.549}, {"sql": "select * from `users` where `username` = 'ivlyn' limit 1", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 344}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 194}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:344", "connection": "sagile", "start_percent": 96.023, "width_percent": 0.585}, {"sql": "select * from `users` where `username` = 'tay' limit 1", "type": "query", "params": [], "bindings": ["tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 344}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 194}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:344", "connection": "sagile", "start_percent": 96.609, "width_percent": 0.454}, {"sql": "insert into `project_user` (`project_id`, `user_id`, `project_access`, `sprint_access`, `forum_access`, `userstory_access`, `secfeature_access`, `updated_at`, `created_at`) values (46, 30, 1, 1, 1, 1, 1, '2025-08-19 14:27:16', '2025-08-19 14:27:16')", "type": "query", "params": [], "bindings": ["46", "30", "1", "1", "1", "1", "1", "2025-08-19 14:27:16", "2025-08-19 14:27:16"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 205}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00132, "duration_str": "1.32ms", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:205", "connection": "sagile", "start_percent": 97.062, "width_percent": 1.576}, {"sql": "select `project_id` from `teammappings` where `username` = 'iv<PERSON>' and `project_id` is not null", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 212}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:212", "connection": "sagile", "start_percent": 98.639, "width_percent": 0.812}, {"sql": "select * from `projects` where `id` in (42, 46)", "type": "query", "params": [], "bindings": ["42", "46"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectController.php", "line": 216}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectController.php:216", "connection": "sagile", "start_percent": 99.451, "width_percent": 0.549}]}, "models": {"data": {"App\\Project": 1, "App\\TeamMapping": 4, "App\\Permission": 78, "App\\User": 3}, "count": 86}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aYGigeJtZIOxG53eFrHOwTgZM6VE6gXcgZTLIKtJ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/projects/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/projects", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-654437461 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-654437461\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1036425875 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aYGigeJtZIOxG53eFrHOwTgZM6VE6gXcgZTLIKtJ</span>\"\n  \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">SAgile</span>\"\n  \"<span class=sf-dump-key>proj_desc</span>\" => \"<span class=sf-dump-str title=\"2 characters\">aa</span>\"\n  \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-19</span>\"\n  \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-12-31</span>\"\n  \"<span class=sf-dump-key>team</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Team 888</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1036425875\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1251555017 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">689</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryS2BSsEuvdO5diCko</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/projects/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Iml2bXYrUFVOZ1lhRGx6N3NXNGgzU2c9PSIsInZhbHVlIjoiWC9kRWFpY09tNWhEN2FjbGZFRkNONGhadHZDaXQrVCtmbGkyNFlHK1h4NUMzZUxyZ3dZYXp1R0c1TmRVdDliYUNxenB4Zi84YXRua0xXT0NWeGZkNkg3ZkNLUWU4RlQ3dys4czZPSTVwalJMdlBNTXpjVDZtZTAwQzRzemowNUYiLCJtYWMiOiI2YjI5N2FmNTRiNWY5ZGI5ZDMzMDgyNzJhMzkyYzI2MjRlNzllM2MyNjY2ZjIxYWE2YzhlNTBjMDkzODI3MjcyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjRTV1gvTkFXZmpQam9TeVNDRkx4a1E9PSIsInZhbHVlIjoiRnFmaDRoeTNpUVJpL0U4bVB5ZVdBWVdkTFBCakxjemhVcG8wdjkwOGRxdXduWno2WG9UOWhBbDF2K1FKak9YWmVxQURzT2FsN3JiRTJadTh3Z2JJalRqakFnaVlzUmFBbmpnT3pCT3gwRGptU1gvbkNpcFQ3d1FnbUd3WnVDUGIiLCJtYWMiOiJhYTA1MjM1YjVhOWJlZmFkZjlhYzg2NWE2ODU3NTRkOTdkZTZlOGExMWQ1NGQyYThmMTg5ODIyYTAwZTBiODRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1251555017\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1700249367 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56090</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/projects</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/projects</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/index.php/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">689</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">689</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryS2BSsEuvdO5diCko</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryS2BSsEuvdO5diCko</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/projects/create</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Iml2bXYrUFVOZ1lhRGx6N3NXNGgzU2c9PSIsInZhbHVlIjoiWC9kRWFpY09tNWhEN2FjbGZFRkNONGhadHZDaXQrVCtmbGkyNFlHK1h4NUMzZUxyZ3dZYXp1R0c1TmRVdDliYUNxenB4Zi84YXRua0xXT0NWeGZkNkg3ZkNLUWU4RlQ3dys4czZPSTVwalJMdlBNTXpjVDZtZTAwQzRzemowNUYiLCJtYWMiOiI2YjI5N2FmNTRiNWY5ZGI5ZDMzMDgyNzJhMzkyYzI2MjRlNzllM2MyNjY2ZjIxYWE2YzhlNTBjMDkzODI3MjcyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjRTV1gvTkFXZmpQam9TeVNDRkx4a1E9PSIsInZhbHVlIjoiRnFmaDRoeTNpUVJpL0U4bVB5ZVdBWVdkTFBCakxjemhVcG8wdjkwOGRxdXduWno2WG9UOWhBbDF2K1FKak9YWmVxQURzT2FsN3JiRTJadTh3Z2JJalRqakFnaVlzUmFBbmpnT3pCT3gwRGptU1gvbkNpcFQ3d1FnbUd3WnVDUGIiLCJtYWMiOiJhYTA1MjM1YjVhOWJlZmFkZjlhYzg2NWE2ODU3NTRkOTdkZTZlOGExMWQ1NGQyYThmMTg5ODIyYTAwZTBiODRjIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584834.9562</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584834</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1700249367\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1876008199 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aYGigeJtZIOxG53eFrHOwTgZM6VE6gXcgZTLIKtJ</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">622yZ1gXtfSvGfodSVAwZB2kv0pPcpdla66n0PI6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1876008199\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1570855118 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:27:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Imp4YmdteVBYVTNVNHVINENaWUJ3UGc9PSIsInZhbHVlIjoiR2ZIYXlzdEtGS1p5U2xHYnp0SjFNekpkVjkvV2hSTExjK0ZhejU4ejdwWm9jWU5rc3JXcGVvcmN3Z1ZEaUxWdDlYeTlzdFNxNkoxWllReG05U0g2WkVsa005WFVMaWlTc3haQXdxVkozUlpkc1RhMGsrUVFyL1QweGxSUzZIT3MiLCJtYWMiOiI5NmVmYzVkZjUyYTI5NThkMGUxZGYyYjY3OWEyMjEzMTQ1YzllMmUxYzk2NDg4YTZlOTk1NzFiYjQ5MjgzODhhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:27:16 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkNmSGVvbHltM3V6R2J2MlIrRU5OY2c9PSIsInZhbHVlIjoiaDVqeTgvVnVRWnhNNEtmY0k4R204SWJmNHpMd1hZOFBSdHVMVi9CTmtRbHEyaE9BeWQ5dGNrbXNYMDBOaG5rdm0wWXBTaUt3eDh6YWtBNmVpL1BnRVYvNDdWYnpQM3dRYmNUVUIwalNPRnRqQStDTmMzSktNMXJ1T0V4NFlKZHkiLCJtYWMiOiIzYzEyYmY2YmViNDY3YjlhNTNhMzk3ZmMxNWYyYzUzMjJhZjNmNDEyMDU2OGY4MjM3ZTE3MzRiNDgyZmIwNDBhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:27:16 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Imp4YmdteVBYVTNVNHVINENaWUJ3UGc9PSIsInZhbHVlIjoiR2ZIYXlzdEtGS1p5U2xHYnp0SjFNekpkVjkvV2hSTExjK0ZhejU4ejdwWm9jWU5rc3JXcGVvcmN3Z1ZEaUxWdDlYeTlzdFNxNkoxWllReG05U0g2WkVsa005WFVMaWlTc3haQXdxVkozUlpkc1RhMGsrUVFyL1QweGxSUzZIT3MiLCJtYWMiOiI5NmVmYzVkZjUyYTI5NThkMGUxZGYyYjY3OWEyMjEzMTQ1YzllMmUxYzk2NDg4YTZlOTk1NzFiYjQ5MjgzODhhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:27:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkNmSGVvbHltM3V6R2J2MlIrRU5OY2c9PSIsInZhbHVlIjoiaDVqeTgvVnVRWnhNNEtmY0k4R204SWJmNHpMd1hZOFBSdHVMVi9CTmtRbHEyaE9BeWQ5dGNrbXNYMDBOaG5rdm0wWXBTaUt3eDh6YWtBNmVpL1BnRVYvNDdWYnpQM3dRYmNUVUIwalNPRnRqQStDTmMzSktNMXJ1T0V4NFlKZHkiLCJtYWMiOiIzYzEyYmY2YmViNDY3YjlhNTNhMzk3ZmMxNWYyYzUzMjJhZjNmNDEyMDU2OGY4MjM3ZTE3MzRiNDgyZmIwNDBhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:27:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1570855118\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1768020166 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aYGigeJtZIOxG53eFrHOwTgZM6VE6gXcgZTLIKtJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/projects/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1768020166\", {\"maxDepth\":0})</script>\n"}}