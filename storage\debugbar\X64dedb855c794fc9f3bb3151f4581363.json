{"__meta": {"id": "X64dedb855c794fc9f3bb3151f4581363", "datetime": "2025-08-18 23:42:03", "utime": 1755531723.722488, "method": "GET", "uri": "/nfr/general/2/specific/2/details", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:42:03] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755531723.470214, "xdebug_link": null, "collector": "log"}, {"message": "[23:42:03] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/nfr/general/2/specific/2/details", "message_html": null, "is_string": false, "label": "debug", "time": 1755531723.547352, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755531723.130228, "end": 1755531723.722515, "duration": 0.5922870635986328, "duration_str": "592ms", "measures": [{"label": "Booting", "start": 1755531723.130228, "relative_start": 0, "end": 1755531723.448455, "relative_end": 1755531723.448455, "duration": 0.31822705268859863, "duration_str": "318ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755531723.448469, "relative_start": 0.3182408809661865, "end": 1755531723.722518, "relative_end": 2.86102294921875e-06, "duration": 0.2740490436553955, "duration_str": "274ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25453840, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 19, "templates": [{"name": "nfr.viewSpecific (\\resources\\views\\nfr\\viewSpecific.blade.php)", "param_count": 7, "params": ["specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.dashboard (\\resources\\views\\inc\\dashboard.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\resources\\views\\vendor\\pagination\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET nfr/general/{general_nfr_id}/specific/{nfr_id}/details", "middleware": "web", "controller": "App\\Http\\Controllers\\SpecificNFRController@viewSpecific", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "nfr.viewSpecific", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\SpecificNFRController.php&line=22\">\\app\\Http\\Controllers\\SpecificNFRController.php:22-65</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00307, "accumulated_duration_str": "3.07ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 19.544}, {"sql": "select * from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 28}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Http\\Controllers\\SpecificNFRController.php:28", "connection": "sagile", "start_percent": 19.544, "width_percent": 17.59}, {"sql": "select * from `projects`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 38}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\SpecificNFRController.php:38", "connection": "sagile", "start_percent": 37.134, "width_percent": 15.309}, {"sql": "select * from `nfr` where `nfr_id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\SpecificNFR.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 44}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\SpecificNFR.php:55", "connection": "sagile", "start_percent": 52.443, "width_percent": 13.681}, {"sql": "select * from `sprint`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Sprint.php", "line": 76}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Sprint.php:76", "connection": "sagile", "start_percent": 66.124, "width_percent": 15.635}, {"sql": "select count(distinct `user_story_general_nfr`.`user_story_id`) as aggregate from `user_story_general_nfr` inner join `user_stories` on `user_story_general_nfr`.`user_story_id` = `user_stories`.`u_id` inner join `projects` on `user_stories`.`proj_id` = `projects`.`id` inner join `sprint` on `user_stories`.`sprint_id` = `sprint`.`sprint_id` where `user_story_general_nfr`.`specific_nfr_id` = '2'", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\UserStoryGeneralNfr.php", "line": 77}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 55}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "\\app\\UserStoryGeneralNfr.php:77", "connection": "sagile", "start_percent": 81.759, "width_percent": 18.241}]}, "models": {"data": {"App\\Sprint": 27, "App\\SpecificNFR": 1, "App\\Project": 20, "App\\TeamMapping": 2, "App\\User": 1}, "count": 51}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/nfr/general/2/specific/2/details\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/nfr/general/2/specific/2/details", "status_code": "<pre class=sf-dump id=sf-dump-1554324298 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1554324298\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1770924652 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1770924652\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1753535836 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1753535836\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-187298762 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/nfr/general/2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImdNVDROMDdNNjVMYWE0WFFPZlpNRGc9PSIsInZhbHVlIjoiOGVuaDE5ZVBDZE93bkZRclpndUt2U25qck5WdHNTNFV2RHE5QWkxazBCOFpwTG0wcERHOXdOTkxFSE1DLzhYM0R6eFp0Y2FyWEJubE1NckwrMTBqWEpLcWlZWjZWRHJaLzB4SXdvYkJXWHJqOWVvNUozYzR5K29nUHB6T2Q0QXoiLCJtYWMiOiI1YTQ4OWFiMzI5OTYwNGMxZGEzZmM3M2YzMzc2MTc0MTEwZTRhMjlkN2ExMWZkM2I3OTdhNzYzMjM4NDVhZTAzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InVaelJETEg3LzQzNS9lalNudVR4RlE9PSIsInZhbHVlIjoiaHFYNzZiT09Ea2lQUGlpMnE2RGZ4UnRmVXJQdnZKNC94THh2cGQ1MHhITXdFVjI5dVlLQjVyeHNtcmlYUVBMa05rMllUdW12Q3ZPRXJlZTZYaGRVbnF4Um9jK1FvenFTcmJpZTF6UmxxM2ljcWs1dW1DM3NUTlBhTDVkV2Y4MW4iLCJtYWMiOiI5OTdmYjVlYzA4MjBlNWNkNGQxM2JhYzVlNGVhZDI2NGM3M2JlMDE4YTgyZTc5NjlkZGMyMjg3ZjUyMTkyYTA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-187298762\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-615035845 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">63800</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/nfr/general/2/specific/2/details</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/nfr/general/2/specific/2/details</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"43 characters\">/index.php/nfr/general/2/specific/2/details</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/nfr/general/2</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImdNVDROMDdNNjVMYWE0WFFPZlpNRGc9PSIsInZhbHVlIjoiOGVuaDE5ZVBDZE93bkZRclpndUt2U25qck5WdHNTNFV2RHE5QWkxazBCOFpwTG0wcERHOXdOTkxFSE1DLzhYM0R6eFp0Y2FyWEJubE1NckwrMTBqWEpLcWlZWjZWRHJaLzB4SXdvYkJXWHJqOWVvNUozYzR5K29nUHB6T2Q0QXoiLCJtYWMiOiI1YTQ4OWFiMzI5OTYwNGMxZGEzZmM3M2YzMzc2MTc0MTEwZTRhMjlkN2ExMWZkM2I3OTdhNzYzMjM4NDVhZTAzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InVaelJETEg3LzQzNS9lalNudVR4RlE9PSIsInZhbHVlIjoiaHFYNzZiT09Ea2lQUGlpMnE2RGZ4UnRmVXJQdnZKNC94THh2cGQ1MHhITXdFVjI5dVlLQjVyeHNtcmlYUVBMa05rMllUdW12Q3ZPRXJlZTZYaGRVbnF4Um9jK1FvenFTcmJpZTF6UmxxM2ljcWs1dW1DM3NUTlBhTDVkV2Y4MW4iLCJtYWMiOiI5OTdmYjVlYzA4MjBlNWNkNGQxM2JhYzVlNGVhZDI2NGM3M2JlMDE4YTgyZTc5NjlkZGMyMjg3ZjUyMTkyYTA3IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755531723.1302</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755531723</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-615035845\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-374941851 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-374941851\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1314938448 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:42:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Img4NDY0Ui9GS29sSlhGL3hPTW85Umc9PSIsInZhbHVlIjoiSXN3MUQvQXlHckpkaGFCRGsyLzNxMWR4ZStyV1lTNVpCM2pNMWFJVjVvbVc2LzdJRENSKzI2L2ZmV0JDZWNrN2djN2lTQ1Q4NTJhWWtHcFQvNTBtQWkzd1JnOGdueTAzUHZrd2RxTjh2ellKelBUUWlKYTZSSER4QVZOZHRydGEiLCJtYWMiOiI1ZGUyZGI0ZTU2NDcxM2I0YmQxZjAwNjU4NDNkYjU0NzQ2N2JkYmVmNmRkMWY0YjcwYjI2OTgzMGFiNjlhZWU3IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:42:03 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjY4NU9yYmljb2JiQmx0UWJraC9zSnc9PSIsInZhbHVlIjoiTHdvekgrV3NWay9kc1JOTERZRnI3Zms0S3p2TzBwYlljUDRBRXpDU2ZTWUxmSkVhd3QwaHhKaEhxSlVUYmtISW1ReWw3Skhvcm1qdUNGZkQrOWtsQTBNT0xCeVgvRjYzZ3ltRTdwa3dIUEFjOFgwMVM1YVlHOTk2NU5iMVA5a0giLCJtYWMiOiJkOTJmNzk1MmY1ZTkwNGQ2ZjdhZTZiM2JkNWNjNmZlN2FlOGUyMzg3OGRkZjIyNDYwNDZmZjkwYjlhMjk0NDAzIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:42:03 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Img4NDY0Ui9GS29sSlhGL3hPTW85Umc9PSIsInZhbHVlIjoiSXN3MUQvQXlHckpkaGFCRGsyLzNxMWR4ZStyV1lTNVpCM2pNMWFJVjVvbVc2LzdJRENSKzI2L2ZmV0JDZWNrN2djN2lTQ1Q4NTJhWWtHcFQvNTBtQWkzd1JnOGdueTAzUHZrd2RxTjh2ellKelBUUWlKYTZSSER4QVZOZHRydGEiLCJtYWMiOiI1ZGUyZGI0ZTU2NDcxM2I0YmQxZjAwNjU4NDNkYjU0NzQ2N2JkYmVmNmRkMWY0YjcwYjI2OTgzMGFiNjlhZWU3IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:42:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjY4NU9yYmljb2JiQmx0UWJraC9zSnc9PSIsInZhbHVlIjoiTHdvekgrV3NWay9kc1JOTERZRnI3Zms0S3p2TzBwYlljUDRBRXpDU2ZTWUxmSkVhd3QwaHhKaEhxSlVUYmtISW1ReWw3Skhvcm1qdUNGZkQrOWtsQTBNT0xCeVgvRjYzZ3ltRTdwa3dIUEFjOFgwMVM1YVlHOTk2NU5iMVA5a0giLCJtYWMiOiJkOTJmNzk1MmY1ZTkwNGQ2ZjdhZTZiM2JkNWNjNmZlN2FlOGUyMzg3OGRkZjIyNDYwNDZmZjkwYjlhMjk0NDAzIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:42:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1314938448\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-463818886 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"54 characters\">http://127.0.0.1:8000/nfr/general/2/specific/2/details</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-463818886\", {\"maxDepth\":0})</script>\n"}}