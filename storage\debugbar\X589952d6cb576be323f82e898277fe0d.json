{"__meta": {"id": "X589952d6cb576be323f82e898277fe0d", "datetime": "2025-08-19 10:42:45", "utime": 1755571365.171609, "method": "GET", "uri": "/sprint/35/end_sprint", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[10:42:44] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755571364.63834, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:44] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/sprint/35/end_sprint", "message_html": null, "is_string": false, "label": "debug", "time": 1755571364.729291, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755571364.279708, "end": 1755571365.171645, "duration": 0.8919370174407959, "duration_str": "892ms", "measures": [{"label": "Booting", "start": 1755571364.279708, "relative_start": 0, "end": 1755571364.613248, "relative_end": 1755571364.613248, "duration": 0.3335402011871338, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755571364.613261, "relative_start": 0.3335530757904053, "end": 1755571365.171649, "relative_end": 4.0531158447265625e-06, "duration": 0.5583879947662354, "duration_str": "558ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23800856, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET sprint/{sprint}/end_sprint", "middleware": "web", "controller": "App\\Http\\Controllers\\SprintController@endSprint", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "sprints.endSprint", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\SprintController.php&line=263\">\\app\\Http\\Controllers\\SprintController.php:263-331</a>"}, "queries": {"nb_statements": 28, "nb_failed_statements": 0, "accumulated_duration": 0.03268000000000001, "accumulated_duration_str": "32.68ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 1.53}, {"sql": "select * from `sprint` where `sprint_id` = '35' limit 1", "type": "query", "params": [], "bindings": ["35"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 55}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 859}, {"index": 18, "namespace": "middleware", "name": "bindings", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 20, "namespace": "middleware", "name": "auth", "line": 44}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php:55", "connection": "sagile", "start_percent": 1.53, "width_percent": 1.408}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System'", "type": "query", "params": [], "bindings": ["Food Ordering System"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 268}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:268", "connection": "sagile", "start_percent": 2.938, "width_percent": 1.53}, {"sql": "select * from `projects` where `proj_name` = 'Food Ordering System' limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 269}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:269", "connection": "sagile", "start_percent": 4.468, "width_percent": 1.377}, {"sql": "select * from `tasks` where `sprint_id` = 35", "type": "query", "params": [], "bindings": ["35"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 272}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:272", "connection": "sagile", "start_percent": 5.845, "width_percent": 1.469}, {"sql": "select * from `statuses` where `project_id` = 42", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 273}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:273", "connection": "sagile", "start_percent": 7.313, "width_percent": 1.408}, {"sql": "select * from `projects` where `id` = 42 limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\BurndownChartController.php", "line": 115}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 283}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\BurndownChartController.php:115", "connection": "sagile", "start_percent": 8.721, "width_percent": 1.408}, {"sql": "select * from `sprint` where `sprint`.`sprint_id` = 35 limit 1", "type": "query", "params": [], "bindings": ["35"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\BurndownChartController.php", "line": 116}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 283}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\BurndownChartController.php:116", "connection": "sagile", "start_percent": 10.129, "width_percent": 1.408}, {"sql": "select * from `tasks` where `sprint_id` = 35", "type": "query", "params": [], "bindings": ["35"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BurndownChartController.php", "line": 117}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 283}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\BurndownChartController.php:117", "connection": "sagile", "start_percent": 11.536, "width_percent": 1.438}, {"sql": "insert into `sprint_archives` (`sprint_id`, `kanban_state`, `burndown_data`, `archived_at`, `updated_at`, `created_at`) values (35, '{\\\"205\\\":[],\\\"206\\\":[],\\\"207\\\":[],\\\"208\\\":[{\\\"id\\\":103,\\\"title\\\":\\\"Task 1\\\",\\\"description\\\":\\\"this is task 1\\\",\\\"order\\\":1,\\\"status_id\\\":\\\"208\\\",\\\"userstory_id\\\":45,\\\"sprint_id\\\":35,\\\"proj_id\\\":42,\\\"start_date\\\":null,\\\"end_date\\\":null,\\\"completion_date\\\":\\\"2025-08-19\\\",\\\"created_at\\\":\\\"2025-08-18T15:29:38.000000Z\\\",\\\"updated_at\\\":\\\"2025-08-19T02:41:18.000000Z\\\",\\\"user_names\\\":\\\"[\\\\\"ivlyn\\\\\"]\\\",\\\"newTask_update\\\":\\\"2025-08-18\\\"},{\\\"id\\\":104,\\\"title\\\":\\\"Task 2\\\",\\\"description\\\":\\\"this is task 2\\\",\\\"order\\\":3,\\\"status_id\\\":\\\"208\\\",\\\"userstory_id\\\":46,\\\"sprint_id\\\":35,\\\"proj_id\\\":42,\\\"start_date\\\":null,\\\"end_date\\\":null,\\\"completion_date\\\":\\\"2025-08-19\\\",\\\"created_at\\\":\\\"2025-08-18T15:34:09.000000Z\\\",\\\"updated_at\\\":\\\"2025-08-19T02:41:26.000000Z\\\",\\\"user_names\\\":\\\"[\\\\\"ivlyn\\\\\"]\\\",\\\"newTask_update\\\":\\\"2025-08-18\\\"},{\\\"id\\\":105,\\\"title\\\":\\\"task 3\\\",\\\"description\\\":\\\"task 3\\\",\\\"order\\\":4,\\\"status_id\\\":\\\"208\\\",\\\"userstory_id\\\":47,\\\"sprint_id\\\":35,\\\"proj_id\\\":42,\\\"start_date\\\":null,\\\"end_date\\\":null,\\\"completion_date\\\":\\\"2025-08-19\\\",\\\"created_at\\\":\\\"2025-08-19T02:37:57.000000Z\\\",\\\"updated_at\\\":\\\"2025-08-19T02:41:26.000000Z\\\",\\\"user_names\\\":\\\"[\\\\\"ivlyn\\\\\"]\\\",\\\"newTask_update\\\":\\\"2025-08-19\\\"},{\\\"id\\\":106,\\\"title\\\":\\\"task 4\\\",\\\"description\\\":\\\"4\\\",\\\"order\\\":2,\\\"status_id\\\":\\\"208\\\",\\\"userstory_id\\\":48,\\\"sprint_id\\\":35,\\\"proj_id\\\":42,\\\"start_date\\\":null,\\\"end_date\\\":null,\\\"completion_date\\\":\\\"2025-08-19\\\",\\\"created_at\\\":\\\"2025-08-19T02:38:15.000000Z\\\",\\\"updated_at\\\":\\\"2025-08-19T02:41:26.000000Z\\\",\\\"user_names\\\":\\\"null\\\",\\\"newTask_update\\\":\\\"2025-08-19\\\"}]}', '{\\\"idealData\\\":[4,3.7333333333333334,3.466666666666667,3.2,2.9333333333333336,2.666666666666667,2.4,2.****************,1.****************,1.6,1.****************,1.****************,0.****************,0.****************,0.****************,0],\\\"actualData\\\":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}', '2025-08-19 10:42:44', '2025-08-19 10:42:44', '2025-08-19 10:42:44')", "type": "query", "params": [], "bindings": ["35", "{&quot;205&quot;:[],&quot;206&quot;:[],&quot;207&quot;:[],&quot;208&quot;:[{&quot;id&quot;:103,&quot;title&quot;:&quot;Task 1&quot;,&quot;description&quot;:&quot;this is task 1&quot;,&quot;order&quot;:1,&quot;status_id&quot;:&quot;208&quot;,&quot;userstory_id&quot;:45,&quot;sprint_id&quot;:35,&quot;proj_id&quot;:42,&quot;start_date&quot;:null,&quot;end_date&quot;:null,&quot;completion_date&quot;:&quot;2025-08-19&quot;,&quot;created_at&quot;:&quot;2025-08-18T15:29:38.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-08-19T02:41:18.000000Z&quot;,&quot;user_names&quot;:&quot;[\\&quot;ivlyn\\&quot;]&quot;,&quot;newTask_update&quot;:&quot;2025-08-18&quot;},{&quot;id&quot;:104,&quot;title&quot;:&quot;Task 2&quot;,&quot;description&quot;:&quot;this is task 2&quot;,&quot;order&quot;:3,&quot;status_id&quot;:&quot;208&quot;,&quot;userstory_id&quot;:46,&quot;sprint_id&quot;:35,&quot;proj_id&quot;:42,&quot;start_date&quot;:null,&quot;end_date&quot;:null,&quot;completion_date&quot;:&quot;2025-08-19&quot;,&quot;created_at&quot;:&quot;2025-08-18T15:34:09.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-08-19T02:41:26.000000Z&quot;,&quot;user_names&quot;:&quot;[\\&quot;ivlyn\\&quot;]&quot;,&quot;newTask_update&quot;:&quot;2025-08-18&quot;},{&quot;id&quot;:105,&quot;title&quot;:&quot;task 3&quot;,&quot;description&quot;:&quot;task 3&quot;,&quot;order&quot;:4,&quot;status_id&quot;:&quot;208&quot;,&quot;userstory_id&quot;:47,&quot;sprint_id&quot;:35,&quot;proj_id&quot;:42,&quot;start_date&quot;:null,&quot;end_date&quot;:null,&quot;completion_date&quot;:&quot;2025-08-19&quot;,&quot;created_at&quot;:&quot;2025-08-19T02:37:57.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-08-19T02:41:26.000000Z&quot;,&quot;user_names&quot;:&quot;[\\&quot;ivlyn\\&quot;]&quot;,&quot;newTask_update&quot;:&quot;2025-08-19&quot;},{&quot;id&quot;:106,&quot;title&quot;:&quot;task 4&quot;,&quot;description&quot;:&quot;4&quot;,&quot;order&quot;:2,&quot;status_id&quot;:&quot;208&quot;,&quot;userstory_id&quot;:48,&quot;sprint_id&quot;:35,&quot;proj_id&quot;:42,&quot;start_date&quot;:null,&quot;end_date&quot;:null,&quot;completion_date&quot;:&quot;2025-08-19&quot;,&quot;created_at&quot;:&quot;2025-08-19T02:38:15.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-08-19T02:41:26.000000Z&quot;,&quot;user_names&quot;:&quot;null&quot;,&quot;newTask_update&quot;:&quot;2025-08-19&quot;}]}", "{&quot;idealData&quot;:[4,3.7333333333333334,3.466666666666667,3.2,2.9333333333333336,2.666666666666667,2.4,2.****************,1.****************,1.6,1.****************,1.****************,0.****************,0.****************,0.****************,0],&quot;actualData&quot;:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}", "2025-08-19 10:42:44", "2025-08-19 10:42:44", "2025-08-19 10:42:44"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 291}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0033799999999999998, "duration_str": "3.38ms", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:291", "connection": "sagile", "start_percent": 12.974, "width_percent": 10.343}, {"sql": "select * from `user_stories` where `sprint_id` = 35", "type": "query", "params": [], "bindings": ["35"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 294}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:294", "connection": "sagile", "start_percent": 23.317, "width_percent": 1.285}, {"sql": "select * from `tasks` where `userstory_id` = 45", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 297}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:297", "connection": "sagile", "start_percent": 24.602, "width_percent": 1.377}, {"sql": "select * from `statuses` where `statuses`.`id` = '208' limit 1", "type": "query", "params": [], "bindings": ["208"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 302}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:302", "connection": "sagile", "start_percent": 25.979, "width_percent": 1.163}, {"sql": "update `tasks` set `sprint_id` = 0, `tasks`.`updated_at` = '2025-08-19 10:42:44' where `id` = 103", "type": "query", "params": [], "bindings": ["0", "2025-08-19 10:42:44", "103"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 311}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00227, "duration_str": "2.27ms", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:311", "connection": "sagile", "start_percent": 27.142, "width_percent": 6.946}, {"sql": "update `user_stories` set `sprint_id` = 0, `user_stories`.`updated_at` = '2025-08-19 10:42:44' where `u_id` = 45", "type": "query", "params": [], "bindings": ["0", "2025-08-19 10:42:44", "45"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 318}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00221, "duration_str": "2.21ms", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:318", "connection": "sagile", "start_percent": 34.088, "width_percent": 6.763}, {"sql": "select * from `tasks` where `userstory_id` = 46", "type": "query", "params": [], "bindings": ["46"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 297}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:297", "connection": "sagile", "start_percent": 40.851, "width_percent": 1.255}, {"sql": "select * from `statuses` where `statuses`.`id` = '208' limit 1", "type": "query", "params": [], "bindings": ["208"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 302}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:302", "connection": "sagile", "start_percent": 42.105, "width_percent": 1.255}, {"sql": "update `tasks` set `sprint_id` = 0, `tasks`.`updated_at` = '2025-08-19 10:42:44' where `id` = 104", "type": "query", "params": [], "bindings": ["0", "2025-08-19 10:42:44", "104"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 311}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00235, "duration_str": "2.35ms", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:311", "connection": "sagile", "start_percent": 43.36, "width_percent": 7.191}, {"sql": "update `user_stories` set `sprint_id` = 0, `user_stories`.`updated_at` = '2025-08-19 10:42:44' where `u_id` = 46", "type": "query", "params": [], "bindings": ["0", "2025-08-19 10:42:44", "46"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 318}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0022400000000000002, "duration_str": "2.24ms", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:318", "connection": "sagile", "start_percent": 50.551, "width_percent": 6.854}, {"sql": "select * from `tasks` where `userstory_id` = 47", "type": "query", "params": [], "bindings": ["47"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 297}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:297", "connection": "sagile", "start_percent": 57.405, "width_percent": 1.255}, {"sql": "select * from `statuses` where `statuses`.`id` = '208' limit 1", "type": "query", "params": [], "bindings": ["208"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 302}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:302", "connection": "sagile", "start_percent": 58.66, "width_percent": 1.01}, {"sql": "update `tasks` set `sprint_id` = 0, `tasks`.`updated_at` = '2025-08-19 10:42:45' where `id` = 105", "type": "query", "params": [], "bindings": ["0", "2025-08-19 10:42:45", "105"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 311}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00234, "duration_str": "2.34ms", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:311", "connection": "sagile", "start_percent": 59.67, "width_percent": 7.16}, {"sql": "update `user_stories` set `sprint_id` = 0, `user_stories`.`updated_at` = '2025-08-19 10:42:45' where `u_id` = 47", "type": "query", "params": [], "bindings": ["0", "2025-08-19 10:42:45", "47"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 318}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0023799999999999997, "duration_str": "2.38ms", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:318", "connection": "sagile", "start_percent": 66.83, "width_percent": 7.283}, {"sql": "select * from `tasks` where `userstory_id` = 48", "type": "query", "params": [], "bindings": ["48"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 297}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:297", "connection": "sagile", "start_percent": 74.113, "width_percent": 2.02}, {"sql": "select * from `statuses` where `statuses`.`id` = '208' limit 1", "type": "query", "params": [], "bindings": ["208"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 302}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:302", "connection": "sagile", "start_percent": 76.132, "width_percent": 1.01}, {"sql": "update `tasks` set `sprint_id` = 0, `tasks`.`updated_at` = '2025-08-19 10:42:45' where `id` = 106", "type": "query", "params": [], "bindings": ["0", "2025-08-19 10:42:45", "106"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 311}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00249, "duration_str": "2.49ms", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:311", "connection": "sagile", "start_percent": 77.142, "width_percent": 7.619}, {"sql": "update `user_stories` set `sprint_id` = 0, `user_stories`.`updated_at` = '2025-08-19 10:42:45' where `u_id` = 48", "type": "query", "params": [], "bindings": ["0", "2025-08-19 10:42:45", "48"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 318}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0024300000000000003, "duration_str": "2.43ms", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:318", "connection": "sagile", "start_percent": 84.761, "width_percent": 7.436}, {"sql": "update `sprint` set `active_sprint` = 2, `sprint`.`updated_at` = '2025-08-19 10:42:45' where `sprint_id` = 35", "type": "query", "params": [], "bindings": ["2", "2025-08-19 10:42:45", "35"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 324}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0025499999999999997, "duration_str": "2.55ms", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:324", "connection": "sagile", "start_percent": 92.197, "width_percent": 7.803}]}, "models": {"data": {"App\\UserStory": 4, "App\\Status": 8, "App\\Task": 12, "App\\Project": 2, "App\\Sprint": 4, "App\\User": 1}, "count": 31}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/sprint/35/end_sprint\"\n]", "_flash": "array:2 [\n  \"old\" => array:4 [\n    0 => \"title\"\n    1 => \"success\"\n    2 => \"sprints\"\n    3 => \"projects\"\n  ]\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "title": "Sprints for Food Ordering System", "success": "Sprint has been ended and archived successfully. Incomplete items have been moved to the backlog.", "sprints": "Illuminate\\Database\\Eloquent\\Collection {#1851\n  #items: array:2 [\n    0 => App\\Sprint {#1850\n      #connection: \"mysql\"\n      #table: \"sprint\"\n      +primaryKey: \"sprint_id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:10 [\n        \"sprint_id\" => 34\n        \"sprint_name\" => \"Sprint 1\"\n        \"sprint_desc\" => \"this is sprint 1\"\n        \"start_sprint\" => \"2025-08-18\"\n        \"end_sprint\" => \"2025-09-01\"\n        \"active_sprint\" => 2\n        \"proj_name\" => \"Food Ordering System\"\n        \"users_name\" => \"ivlyn\"\n        \"created_at\" => \"2025-08-18 23:30:07\"\n        \"updated_at\" => \"2025-08-18 23:36:15\"\n      ]\n      #original: array:10 [\n        \"sprint_id\" => 34\n        \"sprint_name\" => \"Sprint 1\"\n        \"sprint_desc\" => \"this is sprint 1\"\n        \"start_sprint\" => \"2025-08-18\"\n        \"end_sprint\" => \"2025-09-01\"\n        \"active_sprint\" => 2\n        \"proj_name\" => \"Food Ordering System\"\n        \"users_name\" => \"ivlyn\"\n        \"created_at\" => \"2025-08-18 23:30:07\"\n        \"updated_at\" => \"2025-08-18 23:36:15\"\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:6 [\n        0 => \"sprint_name\"\n        1 => \"proj_name\"\n        2 => \"sprint_desc\"\n        3 => \"start_sprint\"\n        4 => \"end_sprint\"\n        5 => \"users_name\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    1 => App\\Sprint {#1849\n      #connection: \"mysql\"\n      #table: \"sprint\"\n      +primaryKey: \"sprint_id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:10 [\n        \"sprint_id\" => 35\n        \"sprint_name\" => \"Sprint 2\"\n        \"sprint_desc\" => \"2\"\n        \"start_sprint\" => \"2025-08-19\"\n        \"end_sprint\" => \"2025-09-02\"\n        \"active_sprint\" => 1\n        \"proj_name\" => \"Food Ordering System\"\n        \"users_name\" => \"ivlyn\"\n        \"created_at\" => \"2025-08-19 10:36:31\"\n        \"updated_at\" => \"2025-08-19 10:36:31\"\n      ]\n      #original: array:10 [\n        \"sprint_id\" => 35\n        \"sprint_name\" => \"Sprint 2\"\n        \"sprint_desc\" => \"2\"\n        \"start_sprint\" => \"2025-08-19\"\n        \"end_sprint\" => \"2025-09-02\"\n        \"active_sprint\" => 1\n        \"proj_name\" => \"Food Ordering System\"\n        \"users_name\" => \"ivlyn\"\n        \"created_at\" => \"2025-08-19 10:36:31\"\n        \"updated_at\" => \"2025-08-19 10:36:31\"\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:6 [\n        0 => \"sprint_name\"\n        1 => \"proj_name\"\n        2 => \"sprint_desc\"\n        3 => \"start_sprint\"\n        4 => \"end_sprint\"\n        5 => \"users_name\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n  ]\n  #escapeWhenCastingToString: false\n}", "projects": "App\\Project {#1848\n  #connection: \"mysql\"\n  #table: \"projects\"\n  #primaryKey: \"id\"\n  #keyType: \"int\"\n  +incrementing: true\n  #with: []\n  #withCount: []\n  +preventsLazyLoading: false\n  #perPage: 15\n  +exists: true\n  +wasRecentlyCreated: false\n  #escapeWhenCastingToString: false\n  #attributes: array:9 [\n    \"id\" => 42\n    \"team_name\" => \"iv<PERSON>'s team\"\n    \"proj_name\" => \"Food Ordering System\"\n    \"proj_desc\" => \"A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.\"\n    \"start_date\" => \"2025-08-18\"\n    \"end_date\" => \"2026-01-25\"\n    \"shareable_slug\" => null\n    \"created_at\" => \"2025-08-18 23:02:05\"\n    \"updated_at\" => \"2025-08-18 23:02:05\"\n  ]\n  #original: array:9 [\n    \"id\" => 42\n    \"team_name\" => \"i<PERSON><PERSON>'s team\"\n    \"proj_name\" => \"Food Ordering System\"\n    \"proj_desc\" => \"A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.\"\n    \"start_date\" => \"2025-08-18\"\n    \"end_date\" => \"2026-01-25\"\n    \"shareable_slug\" => null\n    \"created_at\" => \"2025-08-18 23:02:05\"\n    \"updated_at\" => \"2025-08-18 23:02:05\"\n  ]\n  #changes: []\n  #casts: []\n  #classCastCache: []\n  #attributeCastCache: []\n  #dates: []\n  #dateFormat: null\n  #appends: []\n  #dispatchesEvents: []\n  #observables: []\n  #relations: []\n  #touches: []\n  +timestamps: true\n  #hidden: []\n  #visible: []\n  #fillable: array:7 [\n    0 => \"user_id\"\n    1 => \"team_name\"\n    2 => \"proj_name\"\n    3 => \"proj_desc\"\n    4 => \"start_date\"\n    5 => \"end_date\"\n    6 => \"shareable_slug\"\n  ]\n  #guarded: array:1 [\n    0 => \"*\"\n  ]\n}", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/sprint/35/end_sprint", "status_code": "<pre class=sf-dump id=sf-dump-1507779951 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1507779951\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1837552260 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1837552260\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1440415468 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1440415468\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-260606779 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlRFNUpRL1d2QmQ0L0NZa3NxZWN0MGc9PSIsInZhbHVlIjoiekZ5U1pmRDVrMFg2RHUxUHhjUlhxbENYZ2JUa1ZRUDZEaFhKRkZ6VG9KN1BvMmR2ZmtuYlRoeUlNaVllUGFRT1pQNXNKUGQ1NmN6YWUwOVdDSVdlbHEyUkUzZjNSTHdMYld0b0JMWFkra1loZlAwdytpQ2xKM2lqTEJ3c1lrTFAiLCJtYWMiOiI1ZDBjMGMwNmI3MDc3MTA0YmQ0ZTcwNDFjOTU2NDZkMTkxOWE0OWYwYzAzMDUzMTg0ZDU5OTc0NWRhMzIyYTRmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ikx0US9yNFROcDhHOGRpc05OY0dCVGc9PSIsInZhbHVlIjoiaitkT0o2MzEySW1BM2dGaEV4Rk0zMi9KM012U2JZTHdoSkJvZjF4MFBwVC9BY1U5bjVNVVIrQUhlaHNGTFc0NFJLRVNHVW5xTlhSdjRuaTRWd2pyZTBkNDJNdjZ6K001V29VU1cvNU5oSGRoaHQ5SUZOVFk1ODNLSVZ1M1d2cUoiLCJtYWMiOiI1YmZiNGIwYTQ4ZjcyYWVhMTc3ZmE0YmIxMmZlNjJkYTUwMzYwNTczZDM0NmZjNjRlOThlMjVmMTg4MWRjZTM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-260606779\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1500693361 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60710</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/sprint/35/end_sprint</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/sprint/35/end_sprint</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/index.php/sprint/35/end_sprint</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlRFNUpRL1d2QmQ0L0NZa3NxZWN0MGc9PSIsInZhbHVlIjoiekZ5U1pmRDVrMFg2RHUxUHhjUlhxbENYZ2JUa1ZRUDZEaFhKRkZ6VG9KN1BvMmR2ZmtuYlRoeUlNaVllUGFRT1pQNXNKUGQ1NmN6YWUwOVdDSVdlbHEyUkUzZjNSTHdMYld0b0JMWFkra1loZlAwdytpQ2xKM2lqTEJ3c1lrTFAiLCJtYWMiOiI1ZDBjMGMwNmI3MDc3MTA0YmQ0ZTcwNDFjOTU2NDZkMTkxOWE0OWYwYzAzMDUzMTg0ZDU5OTc0NWRhMzIyYTRmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ikx0US9yNFROcDhHOGRpc05OY0dCVGc9PSIsInZhbHVlIjoiaitkT0o2MzEySW1BM2dGaEV4Rk0zMi9KM012U2JZTHdoSkJvZjF4MFBwVC9BY1U5bjVNVVIrQUhlaHNGTFc0NFJLRVNHVW5xTlhSdjRuaTRWd2pyZTBkNDJNdjZ6K001V29VU1cvNU5oSGRoaHQ5SUZOVFk1ODNLSVZ1M1d2cUoiLCJtYWMiOiI1YmZiNGIwYTQ4ZjcyYWVhMTc3ZmE0YmIxMmZlNjJkYTUwMzYwNTczZDM0NmZjNjRlOThlMjVmMTg4MWRjZTM1IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755571364.2797</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755571364</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1500693361\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1082875578 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1082875578\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-842160265 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:42:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InZLOUh0Q0hRUW1WeWd6cktNTGY5L3c9PSIsInZhbHVlIjoiT0M3dHNseFduOHZ0UFpBdnhDeHViblVES1RIdk5QNFBUL0JwMHBkWkRvYkRWMy8zZVRxUWU2cWZQbWJCOEhKZkc4Mlo2Zk0yM0lYMjdjT0VvS3hncWd2aWlDNzV6SWx0UFY3WWFkd0taUXY4aXhIYWdiR0p3MXRoNHBJUEpOUXMiLCJtYWMiOiIzYjJkZjA1YWIxODRjN2EwYjAxOWU1ODZmZjZjMTg2ZTcwMjdkYjhmMTA0NjM2ZmYzZDhkMWVlN2Q5ZWQ5MDY2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:42:45 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImVRWHhHM085dndGSFRTcWJmdEQrM2c9PSIsInZhbHVlIjoiNzl3eXBpZ1RLa0FIRW1iOFFDaGgzQXlGR2lvWmxhYzhsamp2d2JOaUE3Y3dOb3FpSE9HbHd1SGdUWEoyenJrSEgzZ3dUTEMxbDR0TjBBVlJ2ZjJPcjdqeWQzN3AwaUZBYncrRFA1ZFJHdmZ2TjBMbUtXMDRnOWQrSkZjTVdtbEwiLCJtYWMiOiI0ZmNhZTRmNzAwYTZjMmQzMjk0MzUzZTVlOTA1OGM5NTM0NmNlZTQwYzdmNzg2MDhmMzllZWE2YjRlNTM0OGZiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:42:45 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InZLOUh0Q0hRUW1WeWd6cktNTGY5L3c9PSIsInZhbHVlIjoiT0M3dHNseFduOHZ0UFpBdnhDeHViblVES1RIdk5QNFBUL0JwMHBkWkRvYkRWMy8zZVRxUWU2cWZQbWJCOEhKZkc4Mlo2Zk0yM0lYMjdjT0VvS3hncWd2aWlDNzV6SWx0UFY3WWFkd0taUXY4aXhIYWdiR0p3MXRoNHBJUEpOUXMiLCJtYWMiOiIzYjJkZjA1YWIxODRjN2EwYjAxOWU1ODZmZjZjMTg2ZTcwMjdkYjhmMTA0NjM2ZmYzZDhkMWVlN2Q5ZWQ5MDY2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:42:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImVRWHhHM085dndGSFRTcWJmdEQrM2c9PSIsInZhbHVlIjoiNzl3eXBpZ1RLa0FIRW1iOFFDaGgzQXlGR2lvWmxhYzhsamp2d2JOaUE3Y3dOb3FpSE9HbHd1SGdUWEoyenJrSEgzZ3dUTEMxbDR0TjBBVlJ2ZjJPcjdqeWQzN3AwaUZBYncrRFA1ZFJHdmZ2TjBMbUtXMDRnOWQrSkZjTVdtbEwiLCJtYWMiOiI0ZmNhZTRmNzAwYTZjMmQzMjk0MzUzZTVlOTA1OGM5NTM0NmNlZTQwYzdmNzg2MDhmMzllZWE2YjRlNTM0OGZiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:42:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-842160265\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1500211232 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/sprint/35/end_sprint</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">sprints</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">projects</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Sprints for Food Ordering System</span>\"\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"97 characters\">Sprint has been ended and archived successfully. Incomplete items have been moved to the backlog.</span>\"\n  \"<span class=sf-dump-key>sprints</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#1851</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Sprint\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Sprint</span> {<a class=sf-dump-ref>#1850</a><samp data-depth=4 class=sf-dump-compact>\n        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">sprint</span>\"\n        +<span class=sf-dump-public title=\"Public property\">primaryKey</span>: \"<span class=sf-dump-str title=\"9 characters\">sprint_id</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>sprint_id</span>\" => <span class=sf-dump-num>34</span>\n          \"<span class=sf-dump-key>sprint_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Sprint 1</span>\"\n          \"<span class=sf-dump-key>sprint_desc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">this is sprint 1</span>\"\n          \"<span class=sf-dump-key>start_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n          \"<span class=sf-dump-key>end_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-01</span>\"\n          \"<span class=sf-dump-key>active_sprint</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n          \"<span class=sf-dump-key>users_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">ivlyn</span>\"\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-18 23:30:07</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-18 23:36:15</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>sprint_id</span>\" => <span class=sf-dump-num>34</span>\n          \"<span class=sf-dump-key>sprint_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Sprint 1</span>\"\n          \"<span class=sf-dump-key>sprint_desc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">this is sprint 1</span>\"\n          \"<span class=sf-dump-key>start_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n          \"<span class=sf-dump-key>end_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-01</span>\"\n          \"<span class=sf-dump-key>active_sprint</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n          \"<span class=sf-dump-key>users_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">ivlyn</span>\"\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-18 23:30:07</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-18 23:36:15</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">sprint_name</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">proj_name</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">sprint_desc</span>\"\n          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"12 characters\">start_sprint</span>\"\n          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">end_sprint</span>\"\n          <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">users_name</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n        </samp>]\n      </samp>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Sprint\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Sprint</span> {<a class=sf-dump-ref>#1849</a><samp data-depth=4 class=sf-dump-compact>\n        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">sprint</span>\"\n        +<span class=sf-dump-public title=\"Public property\">primaryKey</span>: \"<span class=sf-dump-str title=\"9 characters\">sprint_id</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>sprint_id</span>\" => <span class=sf-dump-num>35</span>\n          \"<span class=sf-dump-key>sprint_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Sprint 2</span>\"\n          \"<span class=sf-dump-key>sprint_desc</span>\" => \"<span class=sf-dump-str>2</span>\"\n          \"<span class=sf-dump-key>start_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-19</span>\"\n          \"<span class=sf-dump-key>end_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-02</span>\"\n          \"<span class=sf-dump-key>active_sprint</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n          \"<span class=sf-dump-key>users_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">ivlyn</span>\"\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 10:36:31</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 10:36:31</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>sprint_id</span>\" => <span class=sf-dump-num>35</span>\n          \"<span class=sf-dump-key>sprint_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Sprint 2</span>\"\n          \"<span class=sf-dump-key>sprint_desc</span>\" => \"<span class=sf-dump-str>2</span>\"\n          \"<span class=sf-dump-key>start_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-19</span>\"\n          \"<span class=sf-dump-key>end_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-02</span>\"\n          \"<span class=sf-dump-key>active_sprint</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n          \"<span class=sf-dump-key>users_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">ivlyn</span>\"\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 10:36:31</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 10:36:31</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">sprint_name</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">proj_name</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">sprint_desc</span>\"\n          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"12 characters\">start_sprint</span>\"\n          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">end_sprint</span>\"\n          <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">users_name</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n        </samp>]\n      </samp>}\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n  </samp>}\n  \"<span class=sf-dump-key>projects</span>\" => <span class=sf-dump-note title=\"App\\Project\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Project</span> {<a class=sf-dump-ref>#1848</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">projects</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>42</span>\n      \"<span class=sf-dump-key>team_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">ivlyn&#039;s team</span>\"\n      \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n      \"<span class=sf-dump-key>proj_desc</span>\" => \"<span class=sf-dump-str title=\"213 characters\">A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.</span>\"\n      \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n      \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2026-01-25</span>\"\n      \"<span class=sf-dump-key>shareable_slug</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-18 23:02:05</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-18 23:02:05</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>42</span>\n      \"<span class=sf-dump-key>team_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">ivlyn&#039;s team</span>\"\n      \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n      \"<span class=sf-dump-key>proj_desc</span>\" => \"<span class=sf-dump-str title=\"213 characters\">A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.</span>\"\n      \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-18</span>\"\n      \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2026-01-25</span>\"\n      \"<span class=sf-dump-key>shareable_slug</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-18 23:02:05</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-18 23:02:05</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">team_name</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"9 characters\">proj_name</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">proj_desc</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">start_date</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">end_date</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">shareable_slug</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1500211232\", {\"maxDepth\":0})</script>\n"}}