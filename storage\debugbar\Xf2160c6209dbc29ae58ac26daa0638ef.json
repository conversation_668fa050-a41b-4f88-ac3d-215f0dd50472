{"__meta": {"id": "Xf2160c6209dbc29ae58ac26daa0638ef", "datetime": "2025-08-19 14:21:01", "utime": 1755584461.131315, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[14:21:01] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584461.040507, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.497976, "end": 1755584461.13134, "duration": 0.6333639621734619, "duration_str": "633ms", "measures": [{"label": "Booting", "start": **********.497976, "relative_start": 0, "end": 1755584461.003412, "relative_end": 1755584461.003412, "duration": 0.5054359436035156, "duration_str": "505ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584461.003426, "relative_start": 0.5054500102996826, "end": 1755584461.131342, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "128ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "welcome (\\resources\\views\\welcome.blade.php)", "param_count": 0, "params": [], "type": "blade"}]}, "route": {"uri": "GET /", "middleware": "web", "uses": "Closure() {#1346\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#345 …}\n  file: \"C:\\xampp\\htdocs\\SAgilePMT_UTM\\routes\\web.php\"\n  line: \"97 to 99\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\routes\\web.php&line=97\">\\routes\\web.php:97-99</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ALQ9G5nms9aUQWqleAuJp4HhWdpeZNuPspztbePU", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1281175596 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1281175596\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-490565667 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-490565667\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IldOOFpRa2h3QWJ6Vm16TzZPR3V0VWc9PSIsInZhbHVlIjoiYVNKVlF0SjF5Q0llS2lCM3JiNXVMQWs5ZndmWFNoeGt0c1M5dWNBYmZPbFdPZFpDbjBuT1l3dGpJL2M1WjBhQmFHSnFpWDU3c2lvN1ZPL3BiQnEzMms3NU9ZMDZFZWlWQ2NHSXQrRFlHUDdYZVltOHFzRXRQaXZSUVZYdWhSNnkiLCJtYWMiOiJlZGFhMGZmODUzODQwNTNhNjNlMTNjNmJjOTQ1ZmI2ZGRiYTY4MzZhODY2MmRjNWRhNDBiY2I5NjQ0M2NmYmY4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IllMM0paZHRKVmVmMU1BRjZtT05BaFE9PSIsInZhbHVlIjoiRTFmc1FlcWtpQ21hYmJDUmJxc2p4U1lBRHpxRm1nVGRKdmhNc1Q4YUxuMHZVK1l3Y1RQcktxUVlOZm1mbThjM2dnd3hkVm1tcHEwWlh6Rmhnam02MzlyOGZGd29Ea2JxOGphUTBZc2x2M1hYalVTNFFJeE02M3NkQmE2TEhpVXYiLCJtYWMiOiI2NGNiYmY0NWI2N2Q1OTk5NGRmYmRiZGE4MWY3YTBlNzA3MmFkNWZlYTI5YjVhOWNlZGU2YzlmNGEwZmQ3MWQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-750595843 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51502</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str>/</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/45</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IldOOFpRa2h3QWJ6Vm16TzZPR3V0VWc9PSIsInZhbHVlIjoiYVNKVlF0SjF5Q0llS2lCM3JiNXVMQWs5ZndmWFNoeGt0c1M5dWNBYmZPbFdPZFpDbjBuT1l3dGpJL2M1WjBhQmFHSnFpWDU3c2lvN1ZPL3BiQnEzMms3NU9ZMDZFZWlWQ2NHSXQrRFlHUDdYZVltOHFzRXRQaXZSUVZYdWhSNnkiLCJtYWMiOiJlZGFhMGZmODUzODQwNTNhNjNlMTNjNmJjOTQ1ZmI2ZGRiYTY4MzZhODY2MmRjNWRhNDBiY2I5NjQ0M2NmYmY4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IllMM0paZHRKVmVmMU1BRjZtT05BaFE9PSIsInZhbHVlIjoiRTFmc1FlcWtpQ21hYmJDUmJxc2p4U1lBRHpxRm1nVGRKdmhNc1Q4YUxuMHZVK1l3Y1RQcktxUVlOZm1mbThjM2dnd3hkVm1tcHEwWlh6Rmhnam02MzlyOGZGd29Ea2JxOGphUTBZc2x2M1hYalVTNFFJeE02M3NkQmE2TEhpVXYiLCJtYWMiOiI2NGNiYmY0NWI2N2Q1OTk5NGRmYmRiZGE4MWY3YTBlNzA3MmFkNWZlYTI5YjVhOWNlZGU2YzlmNGEwZmQ3MWQzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.498</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-750595843\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-492978733 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ALQ9G5nms9aUQWqleAuJp4HhWdpeZNuPspztbePU</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">XIZQRqAJ238lsrTy7OYpAGHwzHcBFxgwRx1KwqzH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-492978733\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-127217365 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:21:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkdnaXVBNnNBN1lOLy9tYjJmZUhYdGc9PSIsInZhbHVlIjoid1J4RGFFT1NGc0JRRTVhUnNsZG50anlyZHRGbStoMW5YTS9xZ1dzNE9tOEJFSU1CcWlaTTRCRVJKMmdVVU9GS2dXQzhpWFJ3TUdEOWdmRXFTc2w1TDEvUGo4ZHNXVEdMOE9VWG9meXJpMkFxRlNCaFNHSjExSWVFRGxzQS9nN1QiLCJtYWMiOiJiYWFlNDA4NTU4MTI5YzU4ZDgyNWI4M2ZlYTA1MTAwZmI5NDRkMjc4Yzg5ZTViYWM5NjhmMjdjNDIwZmQ0NDFmIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:01 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkRLWko4R3ZqTGV4TkdZYThxZzJLZmc9PSIsInZhbHVlIjoidFFzaVc5Q3dpY1lXaUh3Z3krcTZPUEZaam1VdkhwbmVybFJRdmhZTmNmZTdJM1VFQm1pTGhPR3N3TkN4OEJEakJyZjRWZk1acHRMUGZqWG42T0h6OGtpUnVTOVpCL1ZKcDdVU25yelhiSHRJV3dORzR6STVxZHVTZzRsbzlXcVEiLCJtYWMiOiIzMzhiZjRmODA4MWI4ZTBiMDJjMjk4Mzc1ZDY4MzA0ZmNlYTI5NTE3NjMwODQ4ZDRhMzg0NWIwOWI0ODVjM2E4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:01 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkdnaXVBNnNBN1lOLy9tYjJmZUhYdGc9PSIsInZhbHVlIjoid1J4RGFFT1NGc0JRRTVhUnNsZG50anlyZHRGbStoMW5YTS9xZ1dzNE9tOEJFSU1CcWlaTTRCRVJKMmdVVU9GS2dXQzhpWFJ3TUdEOWdmRXFTc2w1TDEvUGo4ZHNXVEdMOE9VWG9meXJpMkFxRlNCaFNHSjExSWVFRGxzQS9nN1QiLCJtYWMiOiJiYWFlNDA4NTU4MTI5YzU4ZDgyNWI4M2ZlYTA1MTAwZmI5NDRkMjc4Yzg5ZTViYWM5NjhmMjdjNDIwZmQ0NDFmIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkRLWko4R3ZqTGV4TkdZYThxZzJLZmc9PSIsInZhbHVlIjoidFFzaVc5Q3dpY1lXaUh3Z3krcTZPUEZaam1VdkhwbmVybFJRdmhZTmNmZTdJM1VFQm1pTGhPR3N3TkN4OEJEakJyZjRWZk1acHRMUGZqWG42T0h6OGtpUnVTOVpCL1ZKcDdVU25yelhiSHRJV3dORzR6STVxZHVTZzRsbzlXcVEiLCJtYWMiOiIzMzhiZjRmODA4MWI4ZTBiMDJjMjk4Mzc1ZDY4MzA0ZmNlYTI5NTE3NjMwODQ4ZDRhMzg0NWIwOWI0ODVjM2E4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-127217365\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-425303165 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ALQ9G5nms9aUQWqleAuJp4HhWdpeZNuPspztbePU</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-425303165\", {\"maxDepth\":0})</script>\n"}}