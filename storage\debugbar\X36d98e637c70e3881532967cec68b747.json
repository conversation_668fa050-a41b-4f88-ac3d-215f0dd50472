{"__meta": {"id": "X36d98e637c70e3881532967cec68b747", "datetime": "2025-08-18 23:43:22", "utime": 1755531802.11335, "method": "GET", "uri": "/cig/create/42?filter_sprint_id=", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:43:21] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755531801.830898, "xdebug_link": null, "collector": "log"}, {"message": "[23:43:21] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/cig/create/42", "message_html": null, "is_string": false, "label": "debug", "time": 1755531801.900395, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755531801.542793, "end": 1755531802.113371, "duration": 0.57057785987854, "duration_str": "571ms", "measures": [{"label": "Booting", "start": 1755531801.542793, "relative_start": 0, "end": 1755531801.810328, "relative_end": 1755531801.810328, "duration": 0.2675349712371826, "duration_str": "268ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755531801.810338, "relative_start": 0.2675449848175049, "end": 1755531802.113373, "relative_end": 2.1457672119140625e-06, "duration": 0.30303502082824707, "duration_str": "303ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25696304, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 18, "templates": [{"name": "cig.create (\\resources\\views\\cig\\create.blade.php)", "param_count": 7, "params": ["title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\resources\\views\\vendor\\pagination\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 26, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 26, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 26, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET cig/create/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@create", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "cig.create", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=81\">\\app\\Http\\Controllers\\CIGController.php:81-108</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.00536, "accumulated_duration_str": "5.36ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 8.582}, {"sql": "select * from `projects` where `projects`.`id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Project.php", "line": 86}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Project.php:86", "connection": "sagile", "start_percent": 8.582, "width_percent": 8.209}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System'", "type": "query", "params": [], "bindings": ["Food Ordering System"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Sprint.php", "line": 62}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 85}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Sprint.php:62", "connection": "sagile", "start_percent": 16.791, "width_percent": 8.209}, {"sql": "select * from `user_stories` where `proj_id` = '42'", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 36}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 91}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:36", "connection": "sagile", "start_percent": 25, "width_percent": 9.142}, {"sql": "select * from `user_story_general_nfr` where `user_story_general_nfr`.`user_story_id` in (45, 46)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 91}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:36", "connection": "sagile", "start_percent": 34.142, "width_percent": 9.142}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 91}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:36", "connection": "sagile", "start_percent": 43.284, "width_percent": 8.955}, {"sql": "select count(*) as aggregate from `user_stories` where `proj_id` = '42' and exists (select * from `user_story_general_nfr` where `user_stories`.`u_id` = `user_story_general_nfr`.`user_story_id`)", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\UserStory.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 94}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\app\\UserStory.php:57", "connection": "sagile", "start_percent": 52.239, "width_percent": 9.888}, {"sql": "select `u_id`, `user_story`, `sprint_id` from `user_stories` where `proj_id` = '42' and exists (select * from `user_story_general_nfr` where `user_stories`.`u_id` = `user_story_general_nfr`.`user_story_id`) order by `u_id` asc limit 5 offset 0", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\UserStory.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 94}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\UserStory.php:57", "connection": "sagile", "start_percent": 62.127, "width_percent": 10.075}, {"sql": "select * from `user_story_general_nfr` where `user_story_general_nfr`.`user_story_id` = 45 and `user_story_general_nfr`.`user_story_id` is not null", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 72.201, "width_percent": 8.022}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 80.224, "width_percent": 10.634}, {"sql": "select * from `nfr` where `nfr`.`nfr_id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 90.858, "width_percent": 9.142}]}, "models": {"data": {"App\\SpecificNFR": 3, "App\\GeneralNFR": 4, "App\\UserStoryGeneralNfr": 6, "App\\UserStory": 3, "App\\Sprint": 1, "App\\Project": 1, "App\\User": 1}, "count": 19}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/cig/create/42?filter_sprint_id=\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/cig/create/42", "status_code": "<pre class=sf-dump id=sf-dump-1763825672 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1763825672\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1097216330 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>filter_sprint_id</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1097216330\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1261107433 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>filter_sprint_id</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1261107433\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-887998554 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InpXSkpJZG12WHdRWXg2ZEg0ckVoVVE9PSIsInZhbHVlIjoidWxQZE0zUUdGQ3R3RDcySG5IQ2Jid3FWS1V5UHl0TGVMV056Z05iRll0UDROdDRCWFBBMlJwT0I2M2poN0t1V3dpMGpqQjJXWjIyT1QweFl5c29zalNhbEZ2bkQvWUZqUFdpcE9XOEpKaCs1Rk5KQ3BjTzlDaGpzbDIvb0MwU28iLCJtYWMiOiJkMDFhN2I4NGJmMDE5N2NlN2ExYWJmMDExNWQ5YTNlNGJiZWRhYzFjMGM2MDc5ZGFlYTRkMWNiOWY4MmE5NTc5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlVuZVBkS2dUb3lLT000aWcyU0tQaWc9PSIsInZhbHVlIjoiZXpkMEh0ODU2d3ZJTG50dktzYXNaa3FrbTZnR3JpcG9HOTdWK2EzY0xLNjZVekNJbHlYMExRbjFBclNkQzFpN0lCNWJ5Z1Ria1BiNHlaT0lSQnYvUFpNcUhabU9pRm1TSEtjLzRQQU4wemJoaVI1aFJjYVFocE1xRExrYi85QmkiLCJtYWMiOiI5Y2E4YzJjYzMyNDhhMzAzYTUxNWQ1NDQ4ZTMzZTEwZTIzZTkzNDQ2YmRmMTA5ODQxYTM4OWE3NmVkODlhYWQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-887998554\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1114056271 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">64709</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/cig/create/42?filter_sprint_id=</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"14 characters\">/cig/create/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/index.php/cig/create/42</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"17 characters\">filter_sprint_id=</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=34</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InpXSkpJZG12WHdRWXg2ZEg0ckVoVVE9PSIsInZhbHVlIjoidWxQZE0zUUdGQ3R3RDcySG5IQ2Jid3FWS1V5UHl0TGVMV056Z05iRll0UDROdDRCWFBBMlJwT0I2M2poN0t1V3dpMGpqQjJXWjIyT1QweFl5c29zalNhbEZ2bkQvWUZqUFdpcE9XOEpKaCs1Rk5KQ3BjTzlDaGpzbDIvb0MwU28iLCJtYWMiOiJkMDFhN2I4NGJmMDE5N2NlN2ExYWJmMDExNWQ5YTNlNGJiZWRhYzFjMGM2MDc5ZGFlYTRkMWNiOWY4MmE5NTc5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlVuZVBkS2dUb3lLT000aWcyU0tQaWc9PSIsInZhbHVlIjoiZXpkMEh0ODU2d3ZJTG50dktzYXNaa3FrbTZnR3JpcG9HOTdWK2EzY0xLNjZVekNJbHlYMExRbjFBclNkQzFpN0lCNWJ5Z1Ria1BiNHlaT0lSQnYvUFpNcUhabU9pRm1TSEtjLzRQQU4wemJoaVI1aFJjYVFocE1xRExrYi85QmkiLCJtYWMiOiI5Y2E4YzJjYzMyNDhhMzAzYTUxNWQ1NDQ4ZTMzZTEwZTIzZTkzNDQ2YmRmMTA5ODQxYTM4OWE3NmVkODlhYWQzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755531801.5428</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755531801</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114056271\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-882013061 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-882013061\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-102383228 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:43:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkFBMEc4ZTBLVDNjTi8vZjZiUHJFc1E9PSIsInZhbHVlIjoiN2dIWHhkNXQvS2k1bG5LSzB3Ui9GZ3dtRlUwTHZtY2paK25qcUp4YkZ6bysvemlFSmVIdmRrakNnWUxyd04rS3J0eXNMV1BJYXE4dXpBYmw5UlhxU1h5NWZWcVB4K0pGTW5lNTlpU28vckJZVU1QMk5qUFVFNEFvcWE4KzNmZlciLCJtYWMiOiI1NjAwMTViYzE1OWNmNWM3MzM1ZDY0MDQzNmI5MWFmMzE4MzdmZWQyMDgyNmYzNDcwMDUyMDc5M2M2NTU0MzY3IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:22 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjV6Vnc5UVc1cDFub1dYU2hBQkVIeGc9PSIsInZhbHVlIjoiM09ESVZSYU95ZGd0c0R1TGxnM2tydHBQL3dad1R0RWRnUC92TTBDRGluN1VRWk9NQXJ6QmhZNjBNTElGZWdOWVBxTjlPRHVXMVN5T1ZUOCttcE51dVpHQ0UrbDJZb3dYYzZqVTR0UStzYWVVajRha25CbUUxaDhRbGJOUkQxd1oiLCJtYWMiOiI4ZGZkY2IyMjI1NzM5MGM0OGYzYTY0MWUxOGViODcwNzk3NGI0MjliYmJlMTU1MDA2OGJlMjM2ZGY2YzllM2U1IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:22 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkFBMEc4ZTBLVDNjTi8vZjZiUHJFc1E9PSIsInZhbHVlIjoiN2dIWHhkNXQvS2k1bG5LSzB3Ui9GZ3dtRlUwTHZtY2paK25qcUp4YkZ6bysvemlFSmVIdmRrakNnWUxyd04rS3J0eXNMV1BJYXE4dXpBYmw5UlhxU1h5NWZWcVB4K0pGTW5lNTlpU28vckJZVU1QMk5qUFVFNEFvcWE4KzNmZlciLCJtYWMiOiI1NjAwMTViYzE1OWNmNWM3MzM1ZDY0MDQzNmI5MWFmMzE4MzdmZWQyMDgyNmYzNDcwMDUyMDc5M2M2NTU0MzY3IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjV6Vnc5UVc1cDFub1dYU2hBQkVIeGc9PSIsInZhbHVlIjoiM09ESVZSYU95ZGd0c0R1TGxnM2tydHBQL3dad1R0RWRnUC92TTBDRGluN1VRWk9NQXJ6QmhZNjBNTElGZWdOWVBxTjlPRHVXMVN5T1ZUOCttcE51dVpHQ0UrbDJZb3dYYzZqVTR0UStzYWVVajRha25CbUUxaDhRbGJOUkQxd1oiLCJtYWMiOiI4ZGZkY2IyMjI1NzM5MGM0OGYzYTY0MWUxOGViODcwNzk3NGI0MjliYmJlMTU1MDA2OGJlMjM2ZGY2YzllM2U1IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-102383228\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-691508752 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-691508752\", {\"maxDepth\":0})</script>\n"}}