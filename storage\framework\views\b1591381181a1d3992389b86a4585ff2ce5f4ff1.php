<?php echo $__env->make('inc.kanbanStyle', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Kanban Board - <?php echo e($sprint->sprint_name ?? 'No Active Sprint'); ?></title>
    
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
</head>

<body>
    <div class="kanban-container">
        <?php if(!$hasActiveSprint): ?>
            <div class="no-sprint-container">
                <h3><i class="fas fa-calendar-times"></i> No Active Sprint</h3>
                <p>There is currently no active sprint for this project.</p>
            </div>
        <?php else: ?>
            <div class="kanban-header">
                <h1 class="kanban-title">
                    <i class="fas fa-tasks"></i>
                    <?php echo e($sprint->sprint_name); ?>

                </h1>
                <div class="kanban-actions">
                    <?php if($isSprintOverdue): ?>
                        <div class="overdue-alert">
                            <i class="fas fa-exclamation-triangle"></i>
                            Sprint is overdue!
                        </div>
                    <?php endif; ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('addLane_kanban', $project)): ?>
                    <button id="add-lane-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Add Lane
                    </button>
                    <?php endif; ?>
                </div>
            </div>

            <button id="save-btn" style="display: none">Save</button>

            <div class="kanban-board">
                <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php $taskList = $tasksByStatus[$status->id] ?? []; ?>
                    <div class="swim-lane" data-status-id="<?php echo e($status->id); ?>">
                        <div class="lane-header">
                            <h3 class="lane-title heading"><?php echo e($status->title); ?></h3>
                            <div class="lane-actions">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('editLane_kanban', $project)): ?>
                                <button type="button" class="btn-icon rename-btn" title="Rename Lane">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <?php endif; ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('deleteLane_kanban', $project)): ?>
                                <button type="button" class="btn-icon delete-btn" title="Delete Lane">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="lane-content">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('addTask_kanban', $project)): ?>
                            <form action="<?php echo e(route('kanban.createTask', [], false)); ?>" method="post" class="add-task-form">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="sprintId" value="<?php echo e($sprint->sprint_id); ?>">
                                <input type="hidden" name="statusId" class="status-id-input" value="<?php echo e($status->id); ?>">
                                <button type="submit" class="btn btn-success btn-sm new-submit-btn">
                                    <i class="fas fa-plus"></i>
                                    Add Task
                                </button>
                            </form>
                            <?php endif; ?>

                            <?php $__currentLoopData = $taskList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>                                
                                <div class="task-card task <?php echo e($task->isOverdue ? 'overdue-task' : ''); ?> 
                                                           <?php echo e(Auth::user()->can('updateTaskStatus_kanban', $project) ? '' : 'non-draggable'); ?>" 
                                     draggable="<?php echo e(Auth::user()->can('updateTaskStatus_kanban', $project) ? 'true' : 'false'); ?>" 
                                     data-task-id="<?php echo e($task->id); ?>">
                                    
                                    <?php if($task->isOverdue): ?>
                                        <div class="overdue-badge">
                                            <i class="fas fa-clock"></i> Overdue
                                        </div>
                                    <?php endif; ?>

                                    <div class="task-header">
                                        <h4 class="task-title"><?php echo e($task->title); ?></h4>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('editTask_kanban', $project)): ?>
                                            <button type="button" class="edit-task-btn" title="Edit Task">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('deleteTask_kanban', $project)): ?>
                                            <button type="button" class="delete-task-btn" title="Delete Task">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>

                                    <div class="filter-section">
                                        <div class="filter-row">
                                            <label class="filter-label" for="filter_created_by_<?php echo e($task->id); ?>">Filter:</label>
                                            <select id="filter_created_by_<?php echo e($task->id); ?>" 
                                                    class="form-select filter-created-by-select" 
                                                    data-task-id="<?php echo e($task->id); ?>">
                                                <option value="all">All Creators</option>
                                                <?php $__currentLoopData = $task->comments->unique('created_by'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($comment->created_by); ?>"><?php echo e($comment->created_by); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>

                                        <div class="filter-row">
                                            <label class="filter-label" for="sort_date_<?php echo e($task->id); ?>">Sort:</label>
                                            <form method="GET" 
                                                  action="<?php echo e(route('sprint.kanbanPage', ['proj_id' => $project->id, 'sprint_id' => $sprint->sprint_id])); ?>" 
                                                  class="sort-comments-form" 
                                                  id="sort-comments-form-<?php echo e($task->id); ?>"
                                                  style="flex: 1;">
                                                <select name="sort_date" 
                                                        id="sort_date_<?php echo e($task->id); ?>" 
                                                        class="form-select" 
                                                        onchange="this.form.submit()">
                                                    <option value="desc" <?php echo e(request('sort_date') == 'desc' ? 'selected' : ''); ?>>Newest First</option>
                                                    <option value="asc" <?php echo e(request('sort_date') == 'asc' ? 'selected' : ''); ?>>Oldest First</option>
                                                </select>
                                            </form>
                                        </div>
                                    </div>
                                    <?php if(!$task->isOverdue && !$isSprintOverdue): ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('addComment_kanban', $project)): ?>
                                        <div class="task-actions">
                                            <button type="button" 
                                                    class="btn btn-primary btn-sm add-comment-btn" 
                                                    data-task-id="<?php echo e($task->id); ?>">
                                                <i class="fas fa-comment-plus"></i>
                                                Add Comment
                                            </button>
                                        </div>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <div class="comments-section" id="comments-container-<?php echo e($task->id); ?>">
                                        <?php if($task->comments->isEmpty()): ?>
                                            <div class="comment-item">
                                                <div class="comment-content">No comments yet.</div>
                                            </div>
                                        <?php else: ?>
                                            <?php $__currentLoopData = $task->comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="comment-item comment-wrapper <?php echo e($index > 1 ? 'hidden-comment' : ''); ?>" 
                                                     filter-created-by="<?php echo e($comment->created_by); ?>" 
                                                     data-created-date="<?php echo e($comment->created_at); ?>">
                                                    <div class="comment-content">
                                                        <?php echo e($comment->comment); ?>

                                                        <?php if($comment->updated_at && $comment->updated_at != $comment->created_at): ?>
                                                            <span class="edited-indicator">(edited)</span>
                                                        <?php endif; ?>
                                                    </div>
                                                    
                                                    <div class="comment-meta">
                                                        <div class="comment-info">
                                                            <span><?php echo e(\Carbon\Carbon::parse($comment->created_at)->format('M d, Y')); ?></span>
                                                            <span>By: <?php echo e($comment->created_by); ?></span>
                                                        </div>
                                                        
                                                        <?php if(!$task->isOverdue && !$isSprintOverdue && $comment->created_by === auth()->user()->username): ?>
                                                            <div class="comment-actions">
                                                                <button type="button" 
                                                                        class="btn-icon edit-comment-btn" 
                                                                        data-comment-id="<?php echo e($comment->id); ?>"
                                                                        title="Edit Comment">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <form action="<?php echo e(route('tasks.deleteComment', $comment->id)); ?>" 
                                                                      method="POST" 
                                                                      style="display:inline;">
                                                                    <?php echo csrf_field(); ?>
                                                                    <?php echo method_field('DELETE'); ?>
                                                                    <button type="submit" 
                                                                            class="btn-icon delete-comment-btn"
                                                                            title="Delete Comment">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </form>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                            <?php if($task->comments->count() > 2): ?>
                                                <button class="show-toggle-btn show-more-btn">
                                                    <i class="fas fa-chevron-down"></i> Show More
                                                </button>
                                                <button class="show-toggle-btn show-less-btn" style="display: none;">
                                                    <i class="fas fa-chevron-up"></i> Show Less
                                                </button>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>
        </div>
        <style>
        /* Style for non-draggable tasks */
        .task-card.non-draggable {
            cursor: default;
            opacity: 0.9;
            border-left: 3px solid #888;
        }
        
        .task-card.non-draggable::after {
            content: "";
            position: absolute;
            top: 5px;
            right: 30px;
            font-size: 14px;
        }

        /* Task header styles */
        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;  /* Changed to center alignment */
            margin-bottom: 10px;
            position: relative;
            gap: 8px;  /* Added gap between elements */
        }

        .task-title {
            margin: 0;
            flex: 1;
            padding-right: 8px;  /* Reduced padding */
            font-size: 0.95rem;
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>

    <!-- Edit Comment Modal -->
    <div id="editCommentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Edit Comment</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="form-group">
                <textarea id="editCommentInput" class="form-control" rows="4" placeholder="Enter your comment..."></textarea>
            </div>
            <button id="saveCommentBtn" class="btn btn-primary">
                <i class="fas fa-save"></i>
                Save Changes
            </button>
        </div>
    </div>

    <!-- Create Comment Modal -->
    <div id="createCommentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Add Comment</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="form-group">
                <textarea id="createCommentInput" class="form-control" rows="4" placeholder="Enter your comment..."></textarea>
            </div>
            <button id="saveCreateCommentBtn" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Add Comment
            </button>
        </div>
    </div>    <script>
        // Define variables used in the separated scripts
        const csrfToken = '<?php echo e(csrf_token()); ?>';
        const projectId = "<?php echo e($project->id ?? ''); ?>";
        const sprintId = "<?php echo e($sprint->sprint_id ?? ''); ?>";
        
        // Define all the route variables needed in the scripts
        const kanbanUpdateStatusRoute = '<?php echo e(route("kanban.updateStatus")); ?>';
        const kanbanDeleteStatusRoute = '<?php echo e(route("kanban.deleteStatus")); ?>';
        const kanbanDeleteTaskRoute = '<?php echo e(route("kanban.deleteTask")); ?>';
        const kanbanUpdateTaskStatusRoute = '<?php echo e(route("kanban.updateTaskStatus")); ?>';
        const kanbanCreateStatusRoute = '<?php echo e(route("kanban.createStatus")); ?>';
        const kanbanUpdateTaskPageRoute = '<?php echo e(route("kanban.updateTaskPage", ["taskId" => ":taskId"])); ?>';
        
        // Permission flags for JavaScript usage
        const canUpdateTaskStatus = <?php echo e(Auth::user()->can('updateTaskStatus_kanban', $project) ? 'true' : 'false'); ?>;

        // Function to calculate and send kanban height to parent
        function updateKanbanHeight() {
            const kanbanBoard = document.querySelector('.kanban-board');
            const kanbanContainer = document.querySelector('.kanban-container');
            if (kanbanBoard && kanbanContainer) {
                const lanes = kanbanBoard.querySelectorAll('.swim-lane');
                let maxHeight = 0;
                
                // Calculate the maximum height needed
                lanes.forEach(lane => {
                    const laneContent = lane.querySelector('.lane-content');
                    const totalHeight = Array.from(laneContent.children).reduce((sum, child) => sum + child.offsetHeight, 0);
                    maxHeight = Math.max(maxHeight, totalHeight + 330); // Add padding for header and margins
                });

                // Add height of the header and any margins
                const totalHeight = maxHeight + kanbanContainer.offsetTop + 100;

                // Send message to parent frame
                window.parent.postMessage({
                    type: 'kanbanHeight',
                    height: totalHeight
                }, '*');
            }
        }        // Call on initial load and after any content changes
        window.addEventListener('load', updateKanbanHeight);
        
        // Create a MutationObserver to watch for changes in the kanban board
        const observer = new MutationObserver(updateKanbanHeight);
        
        // Start observing the kanban board for changes
        window.addEventListener('load', () => {
            const kanbanBoard = document.querySelector('.kanban-board');
            if (kanbanBoard) {
                observer.observe(kanbanBoard, {
                    childList: true,
                    subtree: true,
                    attributes: true
                });
            }
            
            // Add disabled message for non-draggable tasks
            if (!canUpdateTaskStatus) {
                const nonDraggableTasks = document.querySelectorAll('.task-card.non-draggable');
                nonDraggableTasks.forEach(task => {
                    task.title = "You don't have permission to move tasks";
                    
                    // Override the dragstart event to prevent dragging
                    task.addEventListener('dragstart', (e) => {
                        e.preventDefault();
                        return false;
                    });
                });
            }
        });
    </script>

    <!-- Include Separated JavaScript Files -->
    <?php echo $__env->make('inc.kanban.kanban-script-js', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('inc.kanban.comments-script-js', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</body>
</html><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/kanban/index.blade.php ENDPATH**/ ?>