{"__meta": {"id": "X76a4d24037fb9c626f39aa7acf886a84", "datetime": "2025-08-18 23:34:41", "utime": 1755531281.703801, "method": "POST", "uri": "/tasks/103/createKanbanComment", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:34:41] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755531281.508409, "xdebug_link": null, "collector": "log"}, {"message": "[23:34:41] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/tasks/103/createKanbanComment", "message_html": null, "is_string": false, "label": "debug", "time": 1755531281.591825, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755531281.05548, "end": 1755531281.703826, "duration": 0.648345947265625, "duration_str": "648ms", "measures": [{"label": "Booting", "start": 1755531281.05548, "relative_start": 0, "end": 1755531281.481476, "relative_end": 1755531281.481476, "duration": 0.4259960651397705, "duration_str": "426ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755531281.481499, "relative_start": 0.42601895332336426, "end": 1755531281.703828, "relative_end": 2.1457672119140625e-06, "duration": 0.22232913970947266, "duration_str": "222ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24482264, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST tasks/{task_id}/createKanbanComment", "middleware": "web", "controller": "App\\Http\\Controllers\\TaskController@createKanbanComment", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TaskController.php&line=450\">\\app\\Http\\Controllers\\TaskController.php:450-477</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00413, "accumulated_duration_str": "4.13ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 15.254}, {"sql": "select * from `tasks` where `tasks`.`id` = '103' limit 1", "type": "query", "params": [], "bindings": ["103"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Task.php", "line": 43}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 456}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "\\app\\Task.php:43", "connection": "sagile", "start_percent": 15.254, "width_percent": 15.012}, {"sql": "insert into `taskComment` (`task_id`, `comment`, `created_by`, `assigned_to`, `created_at`, `updated_at`) values ('103', 'halo', 'ivlyn', '[\\\"ivlyn\\\"]', '2025-08-18 23:34:41', '2025-08-18 23:34:41')", "type": "query", "params": [], "bindings": ["103", "halo", "ivlyn", "[&quot;ivlyn&quot;]", "2025-08-18 23:34:41", "2025-08-18 23:34:41"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 467}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00113, "duration_str": "1.13ms", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:467", "connection": "sagile", "start_percent": 30.266, "width_percent": 27.361}, {"sql": "select * from `projects` where `projects`.`id` = 42 limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Project.php", "line": 86}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 470}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\app\\Project.php:86", "connection": "sagile", "start_percent": 57.627, "width_percent": 13.801}, {"sql": "select * from `sprint` where `sprint_id` = 34 limit 1", "type": "query", "params": [], "bindings": ["34"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Sprint.php", "line": 46}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 471}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\app\\Sprint.php:46", "connection": "sagile", "start_percent": 71.429, "width_percent": 14.286}, {"sql": "select * from `users` where `username` in ('ivlyn')", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Helpers\\NotificationHelper.php", "line": 40}, {"index": 15, "namespace": null, "name": "\\app\\Helpers\\NotificationHelper.php", "line": 17}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 474}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\app\\Helpers\\NotificationHelper.php:40", "connection": "sagile", "start_percent": 85.714, "width_percent": 14.286}]}, "models": {"data": {"App\\Sprint": 1, "App\\Project": 1, "App\\Task": 1, "App\\User": 2}, "count": 5}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/42/kanbanBoard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]"}, "request": {"path_info": "/tasks/103/createKanbanComment", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1773390533 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1773390533\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-119405772 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>comment</span>\" => \"<span class=sf-dump-str title=\"4 characters\">halo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-119405772\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-199170996 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">18</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/42/kanbanBoard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik9ZaVUyeFVlVmJTNXFjK1ZVb0Nvc2c9PSIsInZhbHVlIjoiU3M3V1pQYlNKTWNIZGdaWnpOOG1LWnZ6MG5YcUdKVEtOeUdkZ1ZmWXFJMDNPN1NTT3NCSEcwUTdUczZZT09lSFBLYWZuaXg0MnVwazhRV1RJVGc5N1dkU2tkSHNCTmRLQzBKNFErcU9JcFhQOE1TdmpWK2htZ1ZRWVRKQ2lkUjYiLCJtYWMiOiJmM2EzOTZjNDM3NWU1NzAwZmFiN2M3YjYwOTUxN2NkMWVjODM0OTEzNmExMzljZmFjNmVhZTA5ZDkxZGJkOWM2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkZRYzVCMjVsa1gxbm5HVUIwaFlQRkE9PSIsInZhbHVlIjoiaU1rT1J0dCswS2kvbnhVUUx1OEV6aUpJbFFCaXo4andDZGtiMTZLMzJ6VmpGV0dSR3RBRHFRVVFSQ2xUbFNSODRYN3lOWitsK05BczNXSkJpMkExSFhqay9yb05GRHV4SERKclJlbHovMnRmb2VVY09lZ3pqMDFLY2l6TXpPRnEiLCJtYWMiOiI1MWNjNzNiNjZmYTMyOGI1MjhjOGJjZTE0ODg1ODkyZTI5ZmViN2UxMjliZjNkNmJiZmQ2MmYyNGYxNTk5YjMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-199170996\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-621036459 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">61805</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/tasks/103/createKanbanComment</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/tasks/103/createKanbanComment</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"40 characters\">/index.php/tasks/103/createKanbanComment</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">18</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">18</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/42/kanbanBoard</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik9ZaVUyeFVlVmJTNXFjK1ZVb0Nvc2c9PSIsInZhbHVlIjoiU3M3V1pQYlNKTWNIZGdaWnpOOG1LWnZ6MG5YcUdKVEtOeUdkZ1ZmWXFJMDNPN1NTT3NCSEcwUTdUczZZT09lSFBLYWZuaXg0MnVwazhRV1RJVGc5N1dkU2tkSHNCTmRLQzBKNFErcU9JcFhQOE1TdmpWK2htZ1ZRWVRKQ2lkUjYiLCJtYWMiOiJmM2EzOTZjNDM3NWU1NzAwZmFiN2M3YjYwOTUxN2NkMWVjODM0OTEzNmExMzljZmFjNmVhZTA5ZDkxZGJkOWM2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkZRYzVCMjVsa1gxbm5HVUIwaFlQRkE9PSIsInZhbHVlIjoiaU1rT1J0dCswS2kvbnhVUUx1OEV6aUpJbFFCaXo4andDZGtiMTZLMzJ6VmpGV0dSR3RBRHFRVVFSQ2xUbFNSODRYN3lOWitsK05BczNXSkJpMkExSFhqay9yb05GRHV4SERKclJlbHovMnRmb2VVY09lZ3pqMDFLY2l6TXpPRnEiLCJtYWMiOiI1MWNjNzNiNjZmYTMyOGI1MjhjOGJjZTE0ODg1ODkyZTI5ZmViN2UxMjliZjNkNmJiZmQ2MmYyNGYxNTk5YjMwIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755531281.0555</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755531281</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-621036459\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-263079212 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-263079212\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1185415013 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:34:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkE3RzV5VFZBamNsZGE0R2NmaUtkRVE9PSIsInZhbHVlIjoidk51SDNubGhRQVdMMDNhcjNYU2tONWhrT3A3Rk1jcUNNWTZEb0Y2UzdTOWUrNWtYdkxBblNiOU9tdnJpSU8yRzlrd0Y3dFkwMlA3dnhaYjVRV3BpbXlDcGdQMlZvSko2WnhGdnZTQVhVN2phdUV6VnRqZ1kyS3NLUlFQMEJvQzkiLCJtYWMiOiIwNGFmMjEyNWRlNzUxYTJlMGZkY2Q5NDkwMDIwMjIxYWVkZDM0ZjA4ZTAzZjczM2UzNzM5YTIzZGQ4ODQwOTc0IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:34:41 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IklVSHI1VVJQRElkaDVUNlo3K09FM0E9PSIsInZhbHVlIjoiRHdFQTVDbkY1RnRGSWszN1JHQ2dlYS9BRmMyVTM5UjBUTHQrRDJPeW9LWGNDWktxK1JlOG42cU95T1N0dDNQSXVQVWVMUWhoc00vUmgxTEIyZE81UWtvYUhjbG5YU1l3cktjSW1vdGNnZXFYazhOYmJUK1BoN1ZYRCtMdUtlbEMiLCJtYWMiOiIwN2VmNDlmNmYxNmMzMmYzMTRmYzAwNjBhMTY2MWNkZTIyMDc1OTVhM2I5NmI4MGQxNTA5MzZmMWNkYWEwYzNjIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:34:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkE3RzV5VFZBamNsZGE0R2NmaUtkRVE9PSIsInZhbHVlIjoidk51SDNubGhRQVdMMDNhcjNYU2tONWhrT3A3Rk1jcUNNWTZEb0Y2UzdTOWUrNWtYdkxBblNiOU9tdnJpSU8yRzlrd0Y3dFkwMlA3dnhaYjVRV3BpbXlDcGdQMlZvSko2WnhGdnZTQVhVN2phdUV6VnRqZ1kyS3NLUlFQMEJvQzkiLCJtYWMiOiIwNGFmMjEyNWRlNzUxYTJlMGZkY2Q5NDkwMDIwMjIxYWVkZDM0ZjA4ZTAzZjczM2UzNzM5YTIzZGQ4ODQwOTc0IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:34:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IklVSHI1VVJQRElkaDVUNlo3K09FM0E9PSIsInZhbHVlIjoiRHdFQTVDbkY1RnRGSWszN1JHQ2dlYS9BRmMyVTM5UjBUTHQrRDJPeW9LWGNDWktxK1JlOG42cU95T1N0dDNQSXVQVWVMUWhoc00vUmgxTEIyZE81UWtvYUhjbG5YU1l3cktjSW1vdGNnZXFYazhOYmJUK1BoN1ZYRCtMdUtlbEMiLCJtYWMiOiIwN2VmNDlmNmYxNmMzMmYzMTRmYzAwNjBhMTY2MWNkZTIyMDc1OTVhM2I5NmI4MGQxNTA5MzZmMWNkYWEwYzNjIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:34:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1185415013\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1542865262 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/42/kanbanBoard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1542865262\", {\"maxDepth\":0})</script>\n"}}