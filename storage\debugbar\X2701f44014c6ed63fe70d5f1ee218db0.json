{"__meta": {"id": "X2701f44014c6ed63fe70d5f1ee218db0", "datetime": "2025-08-19 13:36:21", "utime": 1755581781.623552, "method": "GET", "uri": "/nfr", "ip": "1********"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[13:36:21] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755581781.519492, "xdebug_link": null, "collector": "log"}, {"message": "[13:36:21] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://1********:8000/nfr", "message_html": null, "is_string": false, "label": "debug", "time": 1755581781.581485, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755581781.208543, "end": 1755581781.623586, "duration": 0.4150428771972656, "duration_str": "415ms", "measures": [{"label": "Booting", "start": 1755581781.208543, "relative_start": 0, "end": 1755581781.498882, "relative_end": 1755581781.498882, "duration": 0.29033899307250977, "duration_str": "290ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755581781.498896, "relative_start": 0.29035282135009766, "end": 1755581781.623589, "relative_end": 3.0994415283203125e-06, "duration": 0.12469315528869629, "duration_str": "125ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23393104, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET nfr", "middleware": "web", "controller": "App\\Http\\Controllers\\GeneralNFRController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "nfr.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\GeneralNFRController.php&line=21\">\\app\\Http\\Controllers\\GeneralNFRController.php:21-45</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0014200000000000003, "accumulated_duration_str": "1.42ms", "statements": [{"sql": "select * from `users` where `id` = 31 limit 1", "type": "query", "params": [], "bindings": ["31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 36.62}, {"sql": "select `role_name` from `teammappings` where `username` = 'tay'", "type": "query", "params": [], "bindings": ["tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\GeneralNFRController.php", "line": 26}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Http\\Controllers\\GeneralNFRController.php:26", "connection": "sagile", "start_percent": 36.62, "width_percent": 30.986}, {"sql": "select * from `teammappings` where `username` = 'tay'", "type": "query", "params": [], "bindings": ["tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\GeneralNFRController.php", "line": 31}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\GeneralNFRController.php:31", "connection": "sagile", "start_percent": 67.606, "width_percent": 32.394}]}, "models": {"data": {"App\\TeamMapping": 2, "App\\User": 1}, "count": 3}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "7KlLNpCr5MYzWGee1HJ3N3EytvLEVqWKnN5WXPn1", "_previous": "array:1 [\n  \"url\" => \"http://1********:8000/nfr\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "31", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "error": "Only Admins and Project Managers can access the NFR Management.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/nfr", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-641810007 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-641810007\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2053465563 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2053465563\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1787307105 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">1********:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://1********:8000/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjJlUzlkK3grdzN4WlpsSFFGOHJ1eHc9PSIsInZhbHVlIjoiNjhCdkpONjlwbTc5MEVraGRXb0hmYm9QTUtKYWpTek85ekxnaWlVV1Q1b2c2MS9BeFBPbnVaVXB5bHE0QnF6dTJBMTNZWkJPM1dlN21FZmNCYVlXZ3RHWEJHdHN2ZEdPZHlLSzdZTnFXWm5JLzV2czc5MmVUUmVoL043dFhJeGwiLCJtYWMiOiJmNmFmM2JkNTIzYTMxM2EzMzU5NWE0NDgyYTRkMDdjYWIzN2Q4OTBhNGYxMThhNzFmNzk1NTViYTFlYjUyNjk5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjNLRDZYcVV5dUhJN0t6RDZGMTlYN2c9PSIsInZhbHVlIjoiZnI2QUwxbGg1WGpuRmJTQ2VlSmJrbkFTUWNEUmZHWExIUndqL2QxcWNOZHYwdkYzT0FQTDRJWWxSMjFldjA5T1BicklBalRvRDJra1ZnVzdPNXhCVUQydDI1TE1jQ2I4T2FnK3d6V256cFdFNlRUeEg5UEFBdCs3THFZeWE4SXUiLCJtYWMiOiJmNTVlNzZlNTU1MTBkNmRiYzY2NDA5MTQxNWFhYTA3YTJhZmM0ZTg3MmIyNWE0NDYyNzUyMmUyZDc1MGE1N2Q4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1787307105\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1664140733 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">1********</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55580</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">1********</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"4 characters\">/nfr</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"4 characters\">/nfr</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"14 characters\">/index.php/nfr</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">1********:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://1********:8000/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjJlUzlkK3grdzN4WlpsSFFGOHJ1eHc9PSIsInZhbHVlIjoiNjhCdkpONjlwbTc5MEVraGRXb0hmYm9QTUtKYWpTek85ekxnaWlVV1Q1b2c2MS9BeFBPbnVaVXB5bHE0QnF6dTJBMTNZWkJPM1dlN21FZmNCYVlXZ3RHWEJHdHN2ZEdPZHlLSzdZTnFXWm5JLzV2czc5MmVUUmVoL043dFhJeGwiLCJtYWMiOiJmNmFmM2JkNTIzYTMxM2EzMzU5NWE0NDgyYTRkMDdjYWIzN2Q4OTBhNGYxMThhNzFmNzk1NTViYTFlYjUyNjk5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjNLRDZYcVV5dUhJN0t6RDZGMTlYN2c9PSIsInZhbHVlIjoiZnI2QUwxbGg1WGpuRmJTQ2VlSmJrbkFTUWNEUmZHWExIUndqL2QxcWNOZHYwdkYzT0FQTDRJWWxSMjFldjA5T1BicklBalRvRDJra1ZnVzdPNXhCVUQydDI1TE1jQ2I4T2FnK3d6V256cFdFNlRUeEg5UEFBdCs3THFZeWE4SXUiLCJtYWMiOiJmNTVlNzZlNTU1MTBkNmRiYzY2NDA5MTQxNWFhYTA3YTJhZmM0ZTg3MmIyNWE0NDYyNzUyMmUyZDc1MGE1N2Q4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755581781.2085</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755581781</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1664140733\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1093116222 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7KlLNpCr5MYzWGee1HJ3N3EytvLEVqWKnN5WXPn1</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">boiIVhdjwP2DIseezS1E1c9QOvKBq75P2sPyMVSG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1093116222\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1675449578 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:36:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://1********:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlZzR1ZURE1KbDVjang4ZFI3R1NxZnc9PSIsInZhbHVlIjoicWRlRjllRU4yUkFYcm9wUWZKSEZzL0hnT1lQMllxZlJ0eDM3N1hXNnVTZVhvKzlpMnJIUnBLSENwZDM2WVY4NjV1WnpScXlDcDFPcGN0dkhZQlJKdVdmK0szbTJHV2VpZUxOaElJL3BMYnNYTDFteHBWdzhEWUUyeWxGTDNmK1giLCJtYWMiOiI4YzY4N2Y4NGMxMGUzMGE5NWQ0NjYyNWY2NDU4NWE3YjQxZGI0OTM4OWI0NmExMTFjNTQ4ZmYwNGEyYzAyNDgwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:36:21 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IldXclV1SXFBWmhnTlVRdW9qQ1BvZnc9PSIsInZhbHVlIjoiMW9ac2gzQmlKR2h1aE9mMFp1M0p4cXZxbGcrd3lLaS9qa0VoODFwTXo2am95TzZzbUFrZEdycWViQ2FXMmpEck9aNUkrNUxwN3dWUUQzQTJnbWZaRlNOSE5oajZBekxpK3EwWW9nVEdqMk5VaVE3UmZoVlN2K2dKRndjU1JpZU0iLCJtYWMiOiJjZTNiZTE4YWQ3NDRhNDNiN2NjZmIzOWMwNGY1ZDdkZGZmMTE4ZTU2NDYwOWE4MDA4MTUyZjA1ODRkZTA1N2Y4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:36:21 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlZzR1ZURE1KbDVjang4ZFI3R1NxZnc9PSIsInZhbHVlIjoicWRlRjllRU4yUkFYcm9wUWZKSEZzL0hnT1lQMllxZlJ0eDM3N1hXNnVTZVhvKzlpMnJIUnBLSENwZDM2WVY4NjV1WnpScXlDcDFPcGN0dkhZQlJKdVdmK0szbTJHV2VpZUxOaElJL3BMYnNYTDFteHBWdzhEWUUyeWxGTDNmK1giLCJtYWMiOiI4YzY4N2Y4NGMxMGUzMGE5NWQ0NjYyNWY2NDU4NWE3YjQxZGI0OTM4OWI0NmExMTFjNTQ4ZmYwNGEyYzAyNDgwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:36:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IldXclV1SXFBWmhnTlVRdW9qQ1BvZnc9PSIsInZhbHVlIjoiMW9ac2gzQmlKR2h1aE9mMFp1M0p4cXZxbGcrd3lLaS9qa0VoODFwTXo2am95TzZzbUFrZEdycWViQ2FXMmpEck9aNUkrNUxwN3dWUUQzQTJnbWZaRlNOSE5oajZBekxpK3EwWW9nVEdqMk5VaVE3UmZoVlN2K2dKRndjU1JpZU0iLCJtYWMiOiJjZTNiZTE4YWQ3NDRhNDNiN2NjZmIzOWMwNGY1ZDdkZGZmMTE4ZTU2NDYwOWE4MDA4MTUyZjA1ODRkZTA1N2Y4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:36:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1675449578\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-763683589 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7KlLNpCr5MYzWGee1HJ3N3EytvLEVqWKnN5WXPn1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://1********:8000/nfr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Only Admins and Project Managers can access the NFR Management.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-763683589\", {\"maxDepth\":0})</script>\n"}}