{"__meta": {"id": "X02735973c96e998e6af188c98eb14bbb", "datetime": "2025-08-19 10:41:12", "utime": 1755571272.828362, "method": "GET", "uri": "/42/kanbanBoard", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 104, "messages": [{"message": "[10:41:12] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755571272.506276, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/42/kanbanBoard", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.586986, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: addLane_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.669466, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.670841, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.670918, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: editLane_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.675877, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.676837, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.676888, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: deleteLane_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.677162, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.677976, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.678059, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: addTask_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.678304, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.679093, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.679139, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.681174, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.682215, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.68228, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.682629, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.683431, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.683515, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: editTask_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.683816, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.684898, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.684974, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: deleteTask_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.685323, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.686429, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.686508, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: addComment_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.706027, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.706905, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.706961, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.707501, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.721208, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.721372, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.722376, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.723626, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.723691, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: editTask_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.724314, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.725235, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.725297, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: deleteTask_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.725614, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.726535, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.726595, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: addComment_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.741843, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.743303, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.743422, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: editLane_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.744032, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.745204, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.745306, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: deleteLane_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.745716, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.746811, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.746888, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: addTask_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.747312, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.748373, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.748441, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.748998, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.750225, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.750297, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.750639, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.751474, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.751522, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: editTask_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.751882, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.752937, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.753012, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: deleteTask_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.753448, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.754654, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.754731, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: addComment_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.7711, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.772367, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.77246, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.773013, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.774222, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.774274, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.774584, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.775418, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.775466, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: editTask_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.775762, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.776604, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.776672, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: deleteTask_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.777016, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.778246, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.778329, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: addComment_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.794485, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.795654, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.795713, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: editLane_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.796205, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.797241, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.797299, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: deleteLane_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.797639, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.798808, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.798915, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: addTask_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.799359, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.80053, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.80061, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: editLane_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.801225, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.802253, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.80232, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: deleteLane_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.802673, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.803667, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.803741, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: addTask_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.804083, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.80511, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.805178, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.805924, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.806952, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:12] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571272.807029, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755571272.141051, "end": 1755571272.828561, "duration": 0.6875100135803223, "duration_str": "688ms", "measures": [{"label": "Booting", "start": 1755571272.141051, "relative_start": 0, "end": 1755571272.480047, "relative_end": 1755571272.480047, "duration": 0.33899593353271484, "duration_str": "339ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755571272.480064, "relative_start": 0.33901286125183105, "end": 1755571272.828565, "relative_end": 3.814697265625e-06, "duration": 0.34850096702575684, "duration_str": "349ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24502024, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "kanban.index (\\resources\\views\\kanban\\index.blade.php)", "param_count": 7, "params": ["statuses", "tasksByStatus", "sprint", "project", "sort_date", "isSprintOverdue", "hasActiveSprint"], "type": "blade"}, {"name": "inc.kanbanStyle (\\resources\\views\\inc\\kanbanStyle.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "statuses", "tasksByStatus", "sprint", "project", "sort_date", "isSprintOverdue", "hasActiveSprint"], "type": "blade"}, {"name": "inc.kanban.kanban-script-js (\\resources\\views\\inc\\kanban\\kanban-script-js.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "statuses", "tasksByStatus", "sprint", "project", "sort_date", "isSprintOverdue", "hasActiveSprint", "__currentLoopData", "status", "loop", "taskList", "task"], "type": "blade"}, {"name": "inc.kanban.comments-script-js (\\resources\\views\\inc\\kanban\\comments-script-js.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "statuses", "tasksByStatus", "sprint", "project", "sort_date", "isSprintOverdue", "hasActiveSprint", "__currentLoopData", "status", "loop", "taskList", "task"], "type": "blade"}]}, "route": {"uri": "GET {proj_id}/kanbanBoard", "middleware": "web", "controller": "App\\Http\\Controllers\\TaskController@kanbanIndex", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "sprint.kanbanPage", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TaskController.php&line=86\">\\app\\Http\\Controllers\\TaskController.php:86-176</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.005390000000000001, "accumulated_duration_str": "5.39ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 10.761}, {"sql": "select * from `projects` where `id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 88}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:88", "connection": "sagile", "start_percent": 10.761, "width_percent": 9.276}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` = 1 limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 95}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:95", "connection": "sagile", "start_percent": 20.037, "width_percent": 9.833}, {"sql": "select * from `statuses` where `project_id` = '42' order by `order` asc", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 137}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:137", "connection": "sagile", "start_percent": 29.87, "width_percent": 10.761}, {"sql": "select * from `tasks` where `proj_id` = '42' and `sprint_id` = 35 order by `order` asc", "type": "query", "params": [], "bindings": ["42", "35"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 140}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:140", "connection": "sagile", "start_percent": 40.631, "width_percent": 10.39}, {"sql": "select * from `taskComment` where `taskComment`.`task_id` = 104 and `taskComment`.`task_id` is not null", "type": "query", "params": [], "bindings": ["104"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "b1591381181a1d3992389b86a4585ff2ce5f4ff1", "line": 115}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 24, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "view::b1591381181a1d3992389b86a4585ff2ce5f4ff1:115", "connection": "sagile", "start_percent": 51.02, "width_percent": 9.833}, {"sql": "select * from `taskComment` where `taskComment`.`task_id` = 106 and `taskComment`.`task_id` is not null", "type": "query", "params": [], "bindings": ["106"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "b1591381181a1d3992389b86a4585ff2ce5f4ff1", "line": 115}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 24, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "view::b1591381181a1d3992389b86a4585ff2ce5f4ff1:115", "connection": "sagile", "start_percent": 60.853, "width_percent": 9.833}, {"sql": "select * from `taskComment` where `taskComment`.`task_id` = 103 and `taskComment`.`task_id` is not null", "type": "query", "params": [], "bindings": ["103"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "b1591381181a1d3992389b86a4585ff2ce5f4ff1", "line": 115}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 24, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.00102, "duration_str": "1.02ms", "stmt_id": "view::b1591381181a1d3992389b86a4585ff2ce5f4ff1:115", "connection": "sagile", "start_percent": 70.686, "width_percent": 18.924}, {"sql": "select * from `taskComment` where `taskComment`.`task_id` = 105 and `taskComment`.`task_id` is not null", "type": "query", "params": [], "bindings": ["105"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "b1591381181a1d3992389b86a4585ff2ce5f4ff1", "line": 115}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 24, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "view::b1591381181a1d3992389b86a4585ff2ce5f4ff1:115", "connection": "sagile", "start_percent": 89.61, "width_percent": 10.39}]}, "models": {"data": {"App\\Task": 4, "App\\Status": 4, "App\\Sprint": 1, "App\\Project": 1, "App\\User": 1}, "count": 11}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 34, "messages": [{"message": "[\n  ability => addLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-878890850 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">addLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-878890850\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.674888, "xdebug_link": null}, {"message": "[\n  ability => editLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1767833607 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">editLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1767833607\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.677027, "xdebug_link": null}, {"message": "[\n  ability => deleteLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-773958462 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">deleteLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-773958462\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.678179, "xdebug_link": null}, {"message": "[\n  ability => addTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-242177917 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">addTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-242177917\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.679256, "xdebug_link": null}, {"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2021142780 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2021142780\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.682469, "xdebug_link": null}, {"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1126333301 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1126333301\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.683654, "xdebug_link": null}, {"message": "[\n  ability => editTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-581065003 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">editTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-581065003\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.685145, "xdebug_link": null}, {"message": "[\n  ability => deleteTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1464788303 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">deleteTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1464788303\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.686656, "xdebug_link": null}, {"message": "[\n  ability => addComment_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-31641174 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">addComment_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31641174\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.707157, "xdebug_link": null}, {"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2008470423 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2008470423\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.721713, "xdebug_link": null}, {"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1327429703 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1327429703\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.724003, "xdebug_link": null}, {"message": "[\n  ability => editTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-344020651 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">editTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-344020651\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.725447, "xdebug_link": null}, {"message": "[\n  ability => deleteTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-864233350 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">deleteTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-864233350\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.726761, "xdebug_link": null}, {"message": "[\n  ability => addComment_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-989831773 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">addComment_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-989831773\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.743716, "xdebug_link": null}, {"message": "[\n  ability => editLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-240459252 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">editLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-240459252\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.745504, "xdebug_link": null}, {"message": "[\n  ability => deleteLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1599600626 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">deleteLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1599600626\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.747094, "xdebug_link": null}, {"message": "[\n  ability => addTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1280181813 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">addTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1280181813\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.748624, "xdebug_link": null}, {"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1689375426 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1689375426\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.750477, "xdebug_link": null}, {"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1810785802 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1810785802\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.751673, "xdebug_link": null}, {"message": "[\n  ability => editTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-293947074 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">editTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-293947074\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.753222, "xdebug_link": null}, {"message": "[\n  ability => deleteTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-730924172 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">deleteTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-730924172\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.754973, "xdebug_link": null}, {"message": "[\n  ability => addComment_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1886027411 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">addComment_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1886027411\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.772716, "xdebug_link": null}, {"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1398822782 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1398822782\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.77443, "xdebug_link": null}, {"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1504544108 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1504544108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.775602, "xdebug_link": null}, {"message": "[\n  ability => editTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-725275392 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">editTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-725275392\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.776853, "xdebug_link": null}, {"message": "[\n  ability => deleteTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2110610562 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">deleteTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2110610562\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.778567, "xdebug_link": null}, {"message": "[\n  ability => addComment_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-192530904 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">addComment_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-192530904\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.795932, "xdebug_link": null}, {"message": "[\n  ability => editLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-801942904 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">editLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-801942904\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.797472, "xdebug_link": null}, {"message": "[\n  ability => deleteLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1403223812 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">deleteLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1403223812\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.799133, "xdebug_link": null}, {"message": "[\n  ability => addTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-57313728 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">addTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57313728\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.800822, "xdebug_link": null}, {"message": "[\n  ability => editLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-376031975 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">editLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-376031975\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.802495, "xdebug_link": null}, {"message": "[\n  ability => deleteLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1237870306 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">deleteLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1237870306\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.803909, "xdebug_link": null}, {"message": "[\n  ability => addTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-517315687 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">addTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-517315687\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.805378, "xdebug_link": null}, {"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1554268752 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554268752\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571272.80723, "xdebug_link": null}]}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/42/kanbanBoard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755570218\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/42/kanbanBoard", "status_code": "<pre class=sf-dump id=sf-dump-2038559057 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2038559057\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1441950555 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1441950555\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1093014642 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1093014642\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlBodkpMTnJhTU9GMTlDSFNtSVVoU1E9PSIsInZhbHVlIjoiQVI5L09QV2RxdVY1aDNqNE9tMStjTmJ4WUtGb2JWWjJwWk1EZWRyN25CLzdXd2VzZEZsUUJoNFk5eHBnY1VIQmVNYnF5SGljRG1ZWDFmZ1N1c1BEODd4SUFLQVFmenhMVGovdCtlejFvSmtGcm9VdW96dEozeThBT3hhVDRybnUiLCJtYWMiOiJkZWMwZDg1NjY0MWM0OWE0ZWE4NTRmNGFiYmUzZDRiZjM3YWE1NDMzOGRjZTg0MWU4MGRiM2YxYTM5NDA3ZGZjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkVVVVIwWisrem0rTVYreE9HRkJibWc9PSIsInZhbHVlIjoiYmRkR3FkNVRTOGdEeTVnM040VVl6ajdqWWJlSFd4YWIvOU96TWNoVDRXWGRVaml6UjNCSVJ0NC9QS1h0bldYWExSR1I1eHhZcGxmNGZyK21IR1BDcHRYQk1UelFmZytkNVRXR1FBU2NLV3RQdGhEMzZvckk5c0dFNWFGa0R5VkIiLCJtYWMiOiI5NTNlMmQyY2VlNWI5ZWEzMmMzYzAzYTAxZjBkNDAwYWE1OTY1NjRmNDgwZGEzZjhiZjc1ZWU4ZjRmZTI1Y2I2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-52442524 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">63202</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/42/kanbanBoard</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/42/kanbanBoard</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/index.php/42/kanbanBoard</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlBodkpMTnJhTU9GMTlDSFNtSVVoU1E9PSIsInZhbHVlIjoiQVI5L09QV2RxdVY1aDNqNE9tMStjTmJ4WUtGb2JWWjJwWk1EZWRyN25CLzdXd2VzZEZsUUJoNFk5eHBnY1VIQmVNYnF5SGljRG1ZWDFmZ1N1c1BEODd4SUFLQVFmenhMVGovdCtlejFvSmtGcm9VdW96dEozeThBT3hhVDRybnUiLCJtYWMiOiJkZWMwZDg1NjY0MWM0OWE0ZWE4NTRmNGFiYmUzZDRiZjM3YWE1NDMzOGRjZTg0MWU4MGRiM2YxYTM5NDA3ZGZjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkVVVVIwWisrem0rTVYreE9HRkJibWc9PSIsInZhbHVlIjoiYmRkR3FkNVRTOGdEeTVnM040VVl6ajdqWWJlSFd4YWIvOU96TWNoVDRXWGRVaml6UjNCSVJ0NC9QS1h0bldYWExSR1I1eHhZcGxmNGZyK21IR1BDcHRYQk1UelFmZytkNVRXR1FBU2NLV3RQdGhEMzZvckk5c0dFNWFGa0R5VkIiLCJtYWMiOiI5NTNlMmQyY2VlNWI5ZWEzMmMzYzAzYTAxZjBkNDAwYWE1OTY1NjRmNDgwZGEzZjhiZjc1ZWU4ZjRmZTI1Y2I2IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755571272.1411</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755571272</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-52442524\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1308248178 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1308248178\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:41:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlBZb2dId2RVYVJ3bXdndjNYNUNvOEE9PSIsInZhbHVlIjoiSlY0T3FhbHJUaC9wNndqMmdPNmtMajZjaFBlMElZZVVlSjZObUVrRFpaazhwK3JucHNVaXdob1N5OXVFYTNNMTlRa3VKMDJwTDNNKzdFY2hiQXZUZFNWTHFqQnA0YVQrM3lGaE5XRm9VcGg5UFZGU1J0MXEwWVF3WXdEVWRaQmsiLCJtYWMiOiIzZTczZTY2MmU4NTg1ZDRhMjM5YzdhM2Y5YzkzNmZlYmVhZTM0ZDIzNTY3YjBkMjE5YTAxNGU3MTg4ZGVhNmMzIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:41:12 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImNTSThOMitVMTdwS0pOMm1lZEJUMUE9PSIsInZhbHVlIjoiRmZMOHhPMW4vWDhXRTZNM1VWem5LNU1GM2JEbGhkOThldmpYdVFIOU5IQWtkWUEvMWNiTmc4R290T1lLRU42MDRHSU5TT3hOTzk1Y3B2YmJ5cGFWUFpZSER1enFzVTVubm5RRFlFa2s2SjBML0JicE1XWk9tclpTdVJBRFpuSjIiLCJtYWMiOiIzYTIyZTBkNDBmY2U1ODFkNDlhYjQyMDYxYmNmNGUwYjJjMDNiOTQyMDk1Zjc2MjQ2NDcxZDI5YTA5NmYxM2NkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:41:12 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlBZb2dId2RVYVJ3bXdndjNYNUNvOEE9PSIsInZhbHVlIjoiSlY0T3FhbHJUaC9wNndqMmdPNmtMajZjaFBlMElZZVVlSjZObUVrRFpaazhwK3JucHNVaXdob1N5OXVFYTNNMTlRa3VKMDJwTDNNKzdFY2hiQXZUZFNWTHFqQnA0YVQrM3lGaE5XRm9VcGg5UFZGU1J0MXEwWVF3WXdEVWRaQmsiLCJtYWMiOiIzZTczZTY2MmU4NTg1ZDRhMjM5YzdhM2Y5YzkzNmZlYmVhZTM0ZDIzNTY3YjBkMjE5YTAxNGU3MTg4ZGVhNmMzIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:41:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImNTSThOMitVMTdwS0pOMm1lZEJUMUE9PSIsInZhbHVlIjoiRmZMOHhPMW4vWDhXRTZNM1VWem5LNU1GM2JEbGhkOThldmpYdVFIOU5IQWtkWUEvMWNiTmc4R290T1lLRU42MDRHSU5TT3hOTzk1Y3B2YmJ5cGFWUFpZSER1enFzVTVubm5RRFlFa2s2SjBML0JicE1XWk9tclpTdVJBRFpuSjIiLCJtYWMiOiIzYTIyZTBkNDBmY2U1ODFkNDlhYjQyMDYxYmNmNGUwYjJjMDNiOTQyMDk1Zjc2MjQ2NDcxZDI5YTA5NmYxM2NkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:41:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1373671748 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/42/kanbanBoard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755570218</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1373671748\", {\"maxDepth\":0})</script>\n"}}