

<?php $__env->startSection('content'); ?>
    <?php
        function generateHue($string) {
            // Create a hash of the string
            $hash = md5($string);
            // Take first 4 characters of hash and convert to number between 0 and 360 (hue)
            // 4 char of hash treated as hex value and converted to decimal
            // decimals modulu by 360 to ensure valid hue in HSL colour format
            return hexdec(substr($hash, 0, 4)) % 360;
        }

        function generateGradient($string) {
            $hue1 = generateHue($string);
            // Create a complementary hue by offsetting by 30-60 degrees
            // Modulo by 360 to ensure within valid hue range
            $hue2 = ($hue1 + 40) % 360;
            
            return "linear-gradient(135deg, 
                hsl({$hue1}, 80%, 65%) 0%, 
                hsl({$hue2}, 80%, 65%) 100%)";
        }
    ?>

    <div class="container">
        <!-- Header Section -->
        <div class="d-flex align-items-center mb-4">
            <h1 class="me-3 mb-0">All Projects</h1>
            <div class="me-3 text-muted d-flex align-items-center" style="font-size: 1.5rem;">|</div>
            <a href="<?php echo e(route('projects.create')); ?>" class="btn btn-success d-flex align-items-center">
                <i class="bx bx-plus me-1"></i> New Project
            </a>
        </div>

        <!-- Pending Invitations Alert -->
        <?php if(isset($pendingInvitations) && $pendingInvitations->count() > 0): ?>
            <div class="alert alert-info mb-4">
                <h6 class="alert-heading">Pending Team Invitations</h6>
                <p class="mb-0">You have <?php echo e($pendingInvitations->count()); ?> pending team invitation(s). Check your email or teams tab to accept or decline them to see team projects.</p>
            </div>
        <?php endif; ?>

        <!-- Projects Grid -->
        <div class="row row-cols-1 row-cols-md-3 g-4">
            <?php $__empty_1 = true; $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="col">
                    <a href="<?php echo e(route('projects.details', $project->id)); ?>" class="text-decoration-none">
                        <div class="card h-100 border-0 shadow-sm hover-shadow transition-all">
                            <!-- Dynamic gradient header based on project name -->
                            <div class="card-img-top" style="height: 100px; background: <?php echo e(generateGradient($project->proj_name . $project->id)); ?>"></div>
                            <div class="card-body">
                                <p class="text-muted small mb-1"><?php echo e($project->team_name); ?></p>
                                <h5 class="card-title text-dark"><?php echo e($project->proj_name); ?></h5>
                                <p class="card-text small text-muted">
                                    <span class="me-1">📅</span> 
                                    <?php echo e(\Carbon\Carbon::parse($project->start_date)->format('j M Y')); ?> → 
                                    <?php echo e(\Carbon\Carbon::parse($project->end_date)->format('j M Y')); ?>

                                </p>
                                <p class="card-text small text-dark">
                                    <?php echo e(\Str::limit($project->proj_desc, 166)); ?>

                                </p>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-12">
                    <div class="alert alert-info">
                        No projects found. Either you haven't accepted any team invitations yet, or there are no projects in your accepted teams.
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <style>
        .hover-shadow:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
            transform: translateY(-3px);
        }
        .transition-all {
            transition: all 0.3s ease;
        }
    </style>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts/contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/project/newIndex.blade.php ENDPATH**/ ?>