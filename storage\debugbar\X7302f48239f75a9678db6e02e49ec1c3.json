{"__meta": {"id": "X7302f48239f75a9678db6e02e49ec1c3", "datetime": "2025-08-18 23:34:45", "utime": 1755531285.893171, "method": "DELETE", "uri": "/tasks/comment/5", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:34:45] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755531285.744905, "xdebug_link": null, "collector": "log"}, {"message": "[23:34:45] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/tasks/comment/5", "message_html": null, "is_string": false, "label": "debug", "time": 1755531285.830763, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755531285.316255, "end": 1755531285.893206, "duration": 0.5769507884979248, "duration_str": "577ms", "measures": [{"label": "Booting", "start": 1755531285.316255, "relative_start": 0, "end": 1755531285.716668, "relative_end": 1755531285.716668, "duration": 0.40041279792785645, "duration_str": "400ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755531285.716683, "relative_start": 0.40042781829833984, "end": 1755531285.893209, "relative_end": 3.0994415283203125e-06, "duration": 0.17652606964111328, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23448176, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "DELETE tasks/comment/{comment_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\TaskController@deleteComment", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "tasks.deleteComment", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TaskController.php&line=534\">\\app\\Http\\Controllers\\TaskController.php:534-553</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00865, "accumulated_duration_str": "8.65ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 7.399}, {"sql": "select * from `taskComment` where `taskComment`.`id` = '5' limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\taskComment.php", "line": 33}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 537}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\app\\taskComment.php:33", "connection": "sagile", "start_percent": 7.399, "width_percent": 6.821}, {"sql": "delete from `taskComment` where `id` = 5", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 549}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0074199999999999995, "duration_str": "7.42ms", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:549", "connection": "sagile", "start_percent": 14.22, "width_percent": 85.78}]}, "models": {"data": {"App\\taskComment": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/42/kanbanBoard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "success": "Comment deleted successfully.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/tasks/comment/5", "status_code": "<pre class=sf-dump id=sf-dump-2113991164 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2113991164\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1762931899 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1762931899\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-357432572 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"6 characters\">DELETE</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-357432572\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2141119930 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">62</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/42/kanbanBoard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IndWY2xlWDY2bXdNbk1ySW4yaFVIR1E9PSIsInZhbHVlIjoiNFY1c09WSnBNOTZrQjNQOGN6OVNsdCtoNGFwL0dsVC9GdkFzQmorSDZ2MDZISnpNcGtvOG5zK2ZLdUx4Zkp3TURuS2wwcXMzSGxRWlFhOFNmM2ZVNGJoVkdUUU1Ua3VwZ3htaTE4K3RCc0w5ZXI2T2RrQnJlekUvc0hEeWx5K1IiLCJtYWMiOiJjZTdhZTE3MDViYTA3NDIxNGRiYjM4YmQ2NjJlNDg2ZDQzNjQzZWRlNmE4MTkyMzZkMjUzN2Q4MThiNDY3YTU1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImtyZFFBWUZNejJYanRnNGY4bjFmRVE9PSIsInZhbHVlIjoiclFnY0lnaXVWSnQya29mMkw1Y0EwdDZRRmR3L1ZXR05PMGJxdWJ1OWQrcHV4L3FINElaQ2VMQXNKWHZ3MW91M0dlUFo1d1VJcDZmTTRVbHpmd08xQnY2K2pkVS9yMFFxSExlNXNpS1FjVnRYNUl2YkNPaXpaL0owWVZ1Mm4wdXgiLCJtYWMiOiJmMjFiOWUwYTVmOTFmMjU5ODliZDBjYzk3OTk4N2EyYTc2YmRhYWMyMDlmMGU5NDc2NTgzNzM2MmMyZWZmZjg3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2141119930\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-450105665 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">62095</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/tasks/comment/5</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/tasks/comment/5</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/index.php/tasks/comment/5</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">62</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">62</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/42/kanbanBoard</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IndWY2xlWDY2bXdNbk1ySW4yaFVIR1E9PSIsInZhbHVlIjoiNFY1c09WSnBNOTZrQjNQOGN6OVNsdCtoNGFwL0dsVC9GdkFzQmorSDZ2MDZISnpNcGtvOG5zK2ZLdUx4Zkp3TURuS2wwcXMzSGxRWlFhOFNmM2ZVNGJoVkdUUU1Ua3VwZ3htaTE4K3RCc0w5ZXI2T2RrQnJlekUvc0hEeWx5K1IiLCJtYWMiOiJjZTdhZTE3MDViYTA3NDIxNGRiYjM4YmQ2NjJlNDg2ZDQzNjQzZWRlNmE4MTkyMzZkMjUzN2Q4MThiNDY3YTU1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImtyZFFBWUZNejJYanRnNGY4bjFmRVE9PSIsInZhbHVlIjoiclFnY0lnaXVWSnQya29mMkw1Y0EwdDZRRmR3L1ZXR05PMGJxdWJ1OWQrcHV4L3FINElaQ2VMQXNKWHZ3MW91M0dlUFo1d1VJcDZmTTRVbHpmd08xQnY2K2pkVS9yMFFxSExlNXNpS1FjVnRYNUl2YkNPaXpaL0owWVZ1Mm4wdXgiLCJtYWMiOiJmMjFiOWUwYTVmOTFmMjU5ODliZDBjYzk3OTk4N2EyYTc2YmRhYWMyMDlmMGU5NDc2NTgzNzM2MmMyZWZmZjg3IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755531285.3163</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755531285</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-450105665\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1227380131 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1227380131\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1045597129 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:34:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/42/kanbanBoard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im1Yd0dwZDFsOE5qYXVxT0NVZXM4Nmc9PSIsInZhbHVlIjoiTjZMdkVERFI1SXhWdlJSZEhJY3k4WVl3aXYzNDFNYUZoNXNXMjVRZXRWUUxZbmphZzBrWnNBamNucERTcHhOaWhBMElvSmZDcVFSODlnTVpKOHc1bURSME5rMVErTmdaY0lZNktWemlzVWltT1dkWWlMTDl1YkxLb3JjNC9mS0ciLCJtYWMiOiI5ODA3YjM1ODhkZDNmOGE3Nzg1ZWQ0NTIwZDc2MGRmZDBmZjAxY2ExNGU2MTQyYjgwOTk5ZmUzYmE5M2Q0ZWIzIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:34:45 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Ilo3SVowZ3hvb3RRQTVzQlVHZCtGTWc9PSIsInZhbHVlIjoiRWthOFVWM3dmdmlIbGw5TDNSNFAxMlhhWFdoQkluKzhzSm84Rjh4RVB6Yyt5VUpJNGVDd0IrQnNmeXZXL0hGU3dCQjlHRkxMWWRHc3VFRkl2aXhkTEVkbWJEYjY5eDBjZm1DcGVPUzQ1ZnNBSTQ0cHlHOEM5YnYvWmlQNFBPR1kiLCJtYWMiOiI5ZjRlODdiMDhmMDRjOTIwN2FiYjA4NDYyYTZhZjY1ODc3NzEyNDUyYWE5ODI1NWZhNjgxMWVmZmFmYmNhMmU4IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:34:45 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im1Yd0dwZDFsOE5qYXVxT0NVZXM4Nmc9PSIsInZhbHVlIjoiTjZMdkVERFI1SXhWdlJSZEhJY3k4WVl3aXYzNDFNYUZoNXNXMjVRZXRWUUxZbmphZzBrWnNBamNucERTcHhOaWhBMElvSmZDcVFSODlnTVpKOHc1bURSME5rMVErTmdaY0lZNktWemlzVWltT1dkWWlMTDl1YkxLb3JjNC9mS0ciLCJtYWMiOiI5ODA3YjM1ODhkZDNmOGE3Nzg1ZWQ0NTIwZDc2MGRmZDBmZjAxY2ExNGU2MTQyYjgwOTk5ZmUzYmE5M2Q0ZWIzIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:34:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Ilo3SVowZ3hvb3RRQTVzQlVHZCtGTWc9PSIsInZhbHVlIjoiRWthOFVWM3dmdmlIbGw5TDNSNFAxMlhhWFdoQkluKzhzSm84Rjh4RVB6Yyt5VUpJNGVDd0IrQnNmeXZXL0hGU3dCQjlHRkxMWWRHc3VFRkl2aXhkTEVkbWJEYjY5eDBjZm1DcGVPUzQ1ZnNBSTQ0cHlHOEM5YnYvWmlQNFBPR1kiLCJtYWMiOiI5ZjRlODdiMDhmMDRjOTIwN2FiYjA4NDYyYTZhZjY1ODc3NzEyNDUyYWE5ODI1NWZhNjgxMWVmZmFmYmNhMmU4IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:34:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1045597129\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1023158627 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/42/kanbanBoard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Comment deleted successfully.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1023158627\", {\"maxDepth\":0})</script>\n"}}