 
<?php echo $__env->make('inc.style', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('inc.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->startSection('content'); ?>
<!-- Your form and HTML content -->
<div>
    <?php echo $__env->make('inc.title', ['title' => 'Create New Team'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <br>
    <form action="<?php echo e(route('teams.store')); ?>" method="post" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>
        <h3 class="mt-4 mb-3">Fill in your team details:</h3>

        <!-- Team Name Input -->
        <div class="mb-3">
            <label for="team_name_input" class="form-label">Team Name:</label>
            <input type="text" name="team_name" id="team_name_input" class="form-control <?php $__errorArgs = ['team_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" value="<?php echo e(old('team_name')); ?>" maxlength="100">
            <?php echo $__env->make('inc.character-counter', 
                    [ 'inputId' => 'team_name_input', 
                            'counterId' => 'team_name_char_count', 
                            'maxLength' => 100], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php $__errorArgs = ['team_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="invalid-feedback">
                    <?php echo e($message); ?>

                </div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
        

        <!-- Button to Open Modal -->
        <!-- <button class="open-button" onclick="openForm(event)">Add Team Member</button> -->

        <!-- Div to Display Added Team Members -->
        <!-- <div id="teamMembers"> -->
            <!-- Here, JavaScript will dynamically add the team members' emails -->
        <!-- </div> -->

        <!-- Submit Button -->
         <button type="submit" class="btn btn-success" onclick="submitTeam(event)">Create Team</button>
    </form> 

    <!-- Pop-up form for adding team member -->
    <!-- <div class="form-popup" id="addMemberPopup" style="display: none;">
        <form class="form-container" onsubmit="saveMember(event)">
            <h1>Add Team Member</h1>
            <label for="email"><b>Email</b></label>
            <input type="email" placeholder="Enter Email" name="email" id="email" required>
             Select dropdown for choosing role 
            <label for="role"><b>Role</b></label>
            <select name="role" id="role">
                <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($role->id); ?>"><?php echo e($role->role_name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>

            <button type="button" class="btn" onclick="saveMember(event)">Save</button>
            <button type="button" class="btn cancel" onclick="closePopup()">Close</button>
        </form>
    </div> -->

    <!-- Table to display added team members -->
    <!-- <div id="teamMembersTable">
        <table id="membersTable">
            <thead>
                <tr>
                    <th>Email</th>
                    <th>Role</th> 
                </tr>
            </thead>
            <tbody>

            </tbody>
        </table>
    </div> -->

</div>

<!-- Include necessary scripts -->
<script>
    function openForm(event) {
        event.preventDefault();
        document.getElementById("addMemberPopup").style.display = "block";
    }

    function saveMember(event) {
        event.preventDefault();

        var email = document.getElementById('email').value;
        var role = document.getElementById('role').options[document.getElementById('role').selectedIndex].text;

        var tableBody = document.querySelector('#membersTable tbody');

        var newRow = tableBody.insertRow();

        var cell1 = newRow.insertCell(0);
        var cell2 = newRow.insertCell(1);

        cell1.innerHTML = email;
        cell2.innerHTML = role; // Display role

        sendEmail(email, role);

        document.getElementById('email').value = '';
        document.getElementById('role').value = 'role1';
    }



    function sendEmail(email, role) {
        // Send an AJAX request to Laravel to handle the email sending
        axios.post('<?php echo e(route("Team.invitationEmailTest")); ?>', {
            email: email,
            role: role,
        })
            .then(function (response) {
                console.log('Email sent successfully');
            })
            .catch(function (error) {
                console.error('Error sending email:', error);
            });
    }

    function deleteRow(btn) {
        var row = btn.parentNode.parentNode;
        row.parentNode.removeChild(row);
    }

    function closePopup() {
        document.getElementById("addMemberPopup").style.display = "none";
    }

    function submitTeam(event) {
        // Add logic for submitting the team if needed
        window.location.href = "<?php echo e(route('Team.invitationEmailTest')); ?>";
    }
    
</script>

<!-- Include CSS styles for the pop-up -->
<style>
    /* Styles for the pop-up form */
    .form-popup {
        display: none;
        position: fixed;
        top: 50%;
        /* Align the top of the popup to the middle of the viewport */
        left: 50%;
        /* Align the left edge of the popup to the middle of the viewport */
        transform: translate(-50%, -50%);
        /* Center the popup precisely */
        border: 3px solid #f1f1f1;
        z-index: 9;
        background-color: #fefefe;
        width: 300px;
        padding: 20px;
    }

    .form-container {
        max-width: 100%;
        padding: 10px;
        background-color: white;
    }

    /* Full-width input fields */
    .form-container input[type=text],
    .form-container select {
        width: 100%;
        padding: 10px;
        margin: 5px 0 15px 0;
        border: 1px solid #ccc;
    }

    /* Set a style for the buttons */
    .form-container .btn {
        background-color: #4a82b0;
        color: white;
        padding: 10px 15px;
        border: none;
        cursor: pointer;
        width: 100%;
        margin-bottom: 10px;
    }

    .form-container .btn:hover {
        background-color: #000080;
    }

    .cancel {
        background-color: #f44336;
    }
</style>
<?php $__env->stopSection(); ?>

<!-- Include necessary scripts -->
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<?php echo $__env->make('layouts.app2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/team/create.blade.php ENDPATH**/ ?>