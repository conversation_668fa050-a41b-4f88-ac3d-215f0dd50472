{"__meta": {"id": "X1112924d6853556d7b2851df5df285f0", "datetime": "2025-08-19 14:21:10", "utime": 1755584470.802175, "method": "GET", "uri": "/project-assignments", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[14:21:10] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584470.604134, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584470.277504, "end": 1755584470.802195, "duration": 0.524691104888916, "duration_str": "525ms", "measures": [{"label": "Booting", "start": 1755584470.277504, "relative_start": 0, "end": 1755584470.582254, "relative_end": 1755584470.582254, "duration": 0.3047499656677246, "duration_str": "305ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584470.582266, "relative_start": 0.3047621250152588, "end": 1755584470.802198, "relative_end": 2.86102294921875e-06, "duration": 0.21993184089660645, "duration_str": "220ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25394944, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 14, "templates": [{"name": "project-assignments.index (\\resources\\views\\project-assignments\\index.blade.php)", "param_count": 3, "params": ["projects", "projectAssignments", "managedTeams"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET project-assignments", "middleware": "web", "controller": "App\\Http\\Controllers\\ProjectAssignmentController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "project-assignments.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProjectAssignmentController.php&line=18\">\\app\\Http\\Controllers\\ProjectAssignmentController.php:18-52</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.0030900000000000003, "accumulated_duration_str": "3.09ms", "statements": [{"sql": "select * from `users` where `id` = 31 limit 1", "type": "query", "params": [], "bindings": ["31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 16.828}, {"sql": "select `team_name` from `teammappings` where `project_id` is null and `username` = 'tay' and `invitation_status` = 'accepted'", "type": "query", "params": [], "bindings": ["tay", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 26}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:26", "connection": "sagile", "start_percent": 16.828, "width_percent": 13.916}, {"sql": "select * from `projects` where `team_name` in ('Team 888', 'Team AD')", "type": "query", "params": [], "bindings": ["Team 888", "Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 29}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:29", "connection": "sagile", "start_percent": 30.744, "width_percent": 13.592}, {"sql": "select * from `teammappings` where `project_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 34}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:34", "connection": "sagile", "start_percent": 44.337, "width_percent": 13.592}, {"sql": "select * from `users` where `users`.`username` in ('UAT_1', 'ammarjmldnout', 'ivlyn', 'tay')", "type": "query", "params": [], "bindings": ["UAT_1", "ammarjmldnout", "ivlyn", "tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 34}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:34", "connection": "sagile", "start_percent": 57.929, "width_percent": 16.181}, {"sql": "select * from `projects` where `projects`.`id` in (41, 42, 43, 44, 45)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 34}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:34", "connection": "sagile", "start_percent": 74.11, "width_percent": 13.269}, {"sql": "select * from `teams` where `teams`.`team_name` in ('Team 888', 'Team AD', 'ivlyn\\'s team', 'uatTestTeam1')", "type": "query", "params": [], "bindings": ["Team 888", "Team AD", "ivlyn&#039;s team", "uatTestTeam1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 34}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:34", "connection": "sagile", "start_percent": 87.379, "width_percent": 12.621}]}, "models": {"data": {"App\\Team": 2, "App\\TeamMapping": 8, "App\\Project": 2, "App\\User": 5}, "count": 17}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Gf8WwtCrGWaxQlU79zc0pNXq8iCcBZ5LNC6rM5bb", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/project-assignments\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "31", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/project-assignments", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1684582106 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1684582106\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-714980517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-714980517\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-5994360 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlBVNUlNU0V4VTJlL09CZkJuM01SQ0E9PSIsInZhbHVlIjoiRHNtM2JpLzBGZ1R4azAwUzJ1eHNkMDh4TE9nV1oxUHNGdXo0ekVwcHBNM2ZsNTBzNlpGOWVzS2tNRUhHY3F0VjBPMDI1MS8rdC9qMnd2ZzVvUVk3NzZSdlUrUWFoRjZBSHh6UjNWQnlKRTk1MEhSNm10KzR5UGJPLzF3QjRsREkiLCJtYWMiOiI1Y2M5ODBiMjc3NzE2M2ZlMTYyZTg0MDdlNTZkYThhNjgzYjAzMDMxMGJiMTFlODIyODhmNTkwYWViZmYzMmFmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjZIQmcvVHJhZFFPTjdkOHpCRU1KVmc9PSIsInZhbHVlIjoiSzdYZGJwSlJqQjBNS2xQQjRnU3VKbWYvRm5OVHFsS1J3Z0h5aTBVeXdLSHgwYTRvRmZlNWJWTmdBSzRmOVUrNkhkL0xpalNkK1VWMElESEtLOW5NOWdraHhuSk5LelFxeW5BSnhxNFdxaU9NL0xMWlJObi8xT2U0ajY2S3BhOFAiLCJtYWMiOiJjYzkxY2M2YTcxNTllMzc2MTc0YWM5OGVmZTM5Yjk1MmZkNjZjOTcwYzNkZGIzNjRhZGI5NjNhMDA3MmY4OTlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-5994360\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1682905532 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56340</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/project-assignments</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/project-assignments</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/index.php/project-assignments</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlBVNUlNU0V4VTJlL09CZkJuM01SQ0E9PSIsInZhbHVlIjoiRHNtM2JpLzBGZ1R4azAwUzJ1eHNkMDh4TE9nV1oxUHNGdXo0ekVwcHBNM2ZsNTBzNlpGOWVzS2tNRUhHY3F0VjBPMDI1MS8rdC9qMnd2ZzVvUVk3NzZSdlUrUWFoRjZBSHh6UjNWQnlKRTk1MEhSNm10KzR5UGJPLzF3QjRsREkiLCJtYWMiOiI1Y2M5ODBiMjc3NzE2M2ZlMTYyZTg0MDdlNTZkYThhNjgzYjAzMDMxMGJiMTFlODIyODhmNTkwYWViZmYzMmFmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjZIQmcvVHJhZFFPTjdkOHpCRU1KVmc9PSIsInZhbHVlIjoiSzdYZGJwSlJqQjBNS2xQQjRnU3VKbWYvRm5OVHFsS1J3Z0h5aTBVeXdLSHgwYTRvRmZlNWJWTmdBSzRmOVUrNkhkL0xpalNkK1VWMElESEtLOW5NOWdraHhuSk5LelFxeW5BSnhxNFdxaU9NL0xMWlJObi8xT2U0ajY2S3BhOFAiLCJtYWMiOiJjYzkxY2M2YTcxNTllMzc2MTc0YWM5OGVmZTM5Yjk1MmZkNjZjOTcwYzNkZGIzNjRhZGI5NjNhMDA3MmY4OTlhIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584470.2775</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584470</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1682905532\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-55546061 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gf8WwtCrGWaxQlU79zc0pNXq8iCcBZ5LNC6rM5bb</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Y5RUSXSwBgBrFq8oNyWObubNEtQ5lYLQfTmmXryF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-55546061\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-943731619 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:21:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlFlSy92SDBwRWVnT1g2Y2dQdnBFQWc9PSIsInZhbHVlIjoiZ1pzZ2xhR1NpSm4yais0a3FhajRoVzdTSkM4VnpKTnhlalZMTVBjZURaNHJuVWhPV0hSRHhidEtHdThrbVBuSG9DMnZKZ3ZXNng2VzAwS2dVaFMwQm9FY1hJUS9Bd2JqNG5naFhkRHJTTC9tSjI3K0dsUnJSaDcvRTZGcmV6ckUiLCJtYWMiOiIwMDgyM2FlODdjMzI0ZjgxMmU0YTE2YjA4NmQxMmYyZWI3M2M1MTQxMTI3ZjI4ODNlY2JiNzFhZjMzNTA3YmZlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:10 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjVlOHpXZ2RkQ05vaXdCT1k2ejV2L3c9PSIsInZhbHVlIjoiMGFtd21rNEF4aTBGVzZ3VnFmM2hGb1B5blRnRDJ0S0h6TlVoNjVKRHZVK3Erc3FLUmVXeHhTTlU4K3ZBWlJaTFpidTB2QXhjbThhRmtQcmkyTHVoekhQU3NVWE52Vk5QaXk3a1M1dEIvM1lFZkJoNUFZOEFVK0xydWw1ai8vQlUiLCJtYWMiOiJhZTM2N2RmNjI0OTYyMGFiYmM4ZjAxZDdkZjhjZGNhNTg5ZmQ2ZWQ1MWQ4NTY5NDQ4NmYxNjJiMTY2NzBjMWViIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:10 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlFlSy92SDBwRWVnT1g2Y2dQdnBFQWc9PSIsInZhbHVlIjoiZ1pzZ2xhR1NpSm4yais0a3FhajRoVzdTSkM4VnpKTnhlalZMTVBjZURaNHJuVWhPV0hSRHhidEtHdThrbVBuSG9DMnZKZ3ZXNng2VzAwS2dVaFMwQm9FY1hJUS9Bd2JqNG5naFhkRHJTTC9tSjI3K0dsUnJSaDcvRTZGcmV6ckUiLCJtYWMiOiIwMDgyM2FlODdjMzI0ZjgxMmU0YTE2YjA4NmQxMmYyZWI3M2M1MTQxMTI3ZjI4ODNlY2JiNzFhZjMzNTA3YmZlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjVlOHpXZ2RkQ05vaXdCT1k2ejV2L3c9PSIsInZhbHVlIjoiMGFtd21rNEF4aTBGVzZ3VnFmM2hGb1B5blRnRDJ0S0h6TlVoNjVKRHZVK3Erc3FLUmVXeHhTTlU4K3ZBWlJaTFpidTB2QXhjbThhRmtQcmkyTHVoekhQU3NVWE52Vk5QaXk3a1M1dEIvM1lFZkJoNUFZOEFVK0xydWw1ai8vQlUiLCJtYWMiOiJhZTM2N2RmNjI0OTYyMGFiYmM4ZjAxZDdkZjhjZGNhNTg5ZmQ2ZWQ1MWQ4NTY5NDQ4NmYxNjJiMTY2NzBjMWViIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-943731619\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-373619208 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gf8WwtCrGWaxQlU79zc0pNXq8iCcBZ5LNC6rM5bb</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/project-assignments</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-373619208\", {\"maxDepth\":0})</script>\n"}}