{"__meta": {"id": "X7f9ba3c40d1092d827266055bd23710f", "datetime": "2025-08-19 14:21:58", "utime": 1755584518.219981, "method": "GET", "uri": "/role/45", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 23, "messages": [{"message": "[14:21:58] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584518.034328, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/role/45", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.118691, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: Gate check for permission: add_roles on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.194828, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.195846, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: Using team role map for team Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.1959, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: Gate check for permission: edit_roles on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.200462, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.20137, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: Using team role map for team Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.201428, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: Gate check for permission: delete_roles on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.201775, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.202546, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: Using team role map for team Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.202593, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: Gate check for permission: edit_roles on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.202852, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.203646, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: Using team role map for team Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.20369, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: Gate check for permission: delete_roles on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.203954, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.204692, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: Using team role map for team Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.204737, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: Gate check for permission: updateUserRole_roles on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.204988, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.205725, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: Using team role map for team Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.205769, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: Gate check for permission: updateUserRole_roles on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.206045, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.206791, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:58] LOG.debug: Using team role map for team Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755584518.206854, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584517.699761, "end": 1755584518.220029, "duration": 0.5202682018280029, "duration_str": "520ms", "measures": [{"label": "Booting", "start": 1755584517.699761, "relative_start": 0, "end": 1755584518.007022, "relative_end": 1755584518.007022, "duration": 0.30726099014282227, "duration_str": "307ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584518.007041, "relative_start": 0.3072800636291504, "end": 1755584518.220031, "relative_end": 1.9073486328125e-06, "duration": 0.21299004554748535, "duration_str": "213ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23957808, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "role.index (\\resources\\views\\role\\index.blade.php)", "param_count": 4, "params": ["roles", "pro", "teamMembers", "title"], "type": "blade"}]}, "route": {"uri": "GET role/{project_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\RoleController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "role.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\RoleController.php&line=20\">\\app\\Http\\Controllers\\RoleController.php:20-51</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00247, "accumulated_duration_str": "2.47ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 21.862}, {"sql": "select `team_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\RoleController.php", "line": 27}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Http\\Controllers\\RoleController.php:27", "connection": "sagile", "start_percent": 21.862, "width_percent": 17.814}, {"sql": "select * from `projects` where `team_name` in ('iv<PERSON>\\'s team', 'iv<PERSON>\\'s team', 'Team 888', 'Team 888', 'Team AD', 'Team AD', 'Team AD')", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team", "ivlyn&#039;s team", "Team 888", "Team 888", "Team AD", "Team AD", "Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\RoleController.php", "line": 28}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Http\\Controllers\\RoleController.php:28", "connection": "sagile", "start_percent": 39.676, "width_percent": 19.838}, {"sql": "select * from `roles` where `project_id` = '45'", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\RoleController.php", "line": 31}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\RoleController.php:31", "connection": "sagile", "start_percent": 59.514, "width_percent": 19.028}, {"sql": "select * from `teammappings` where `team_name` = 'Team AD' and `project_id` = 45 and `invitation_status` = 'accepted'", "type": "query", "params": [], "bindings": ["Team AD", "45", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\RoleController.php", "line": 42}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\app\\Http\\Controllers\\RoleController.php:42", "connection": "sagile", "start_percent": 78.543, "width_percent": 21.457}]}, "models": {"data": {"App\\TeamMapping": 2, "App\\Role": 2, "App\\Project": 1, "App\\User": 1}, "count": 6}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 7, "messages": [{"message": "[\n  ability => add_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">add_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584518.199057, "xdebug_link": null}, {"message": "[\n  ability => edit_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1058419290 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">edit_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1058419290\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584518.201574, "xdebug_link": null}, {"message": "[\n  ability => delete_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1047285734 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1047285734\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584518.202709, "xdebug_link": null}, {"message": "[\n  ability => edit_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1167907622 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">edit_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1167907622\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584518.2038, "xdebug_link": null}, {"message": "[\n  ability => delete_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1157280695 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1157280695\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584518.204844, "xdebug_link": null}, {"message": "[\n  ability => updateUserRole_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1618935003 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">updateUserRole_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1618935003\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584518.205875, "xdebug_link": null}, {"message": "[\n  ability => updateUserRole_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2033010065 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">updateUserRole_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2033010065\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584518.206996, "xdebug_link": null}]}, "session": {"_token": "cqS7hb8grzH44B1UcFTbOg1T2CdhTv8vyWR49MZl", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/role/45\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755584492\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/role/45", "status_code": "<pre class=sf-dump id=sf-dump-1177589288 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1177589288\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2009567827 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2009567827\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-282636311 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-282636311\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1199082131 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik4xL1ZqeGNSMDRBM1dHUVFSL2tFaWc9PSIsInZhbHVlIjoid1JkQ3F4Sjk2aDJ3LzE2eWMvaGpodm1XV0NvSXlEb2xVSzhIbUdpN3pOd1RMaW8zN3lvWk53TzAyRG92dHdIMDNEOE9SMmt2dWxjTmp1VW91WjNPQ3dWSTh6NitNL1VrQlFGVTU2OVhwOHhkczVpVU9ubDFVZ1NiR1BFSTUzb3oiLCJtYWMiOiIyNmZhZGQ2N2M2YmU2N2MzNGVlNjUzNDM2ZWJmYjBkM2NlMzlmMGJiZTEwMTYyYTJiMDFjNzMyZjQ4ZjA0ZWE1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InlOWCtJZXRVaUpKd1Y5ZllnNngxRUE9PSIsInZhbHVlIjoicG8yNXlPeHhiT1AxelY3dHJOVlZqWVlXVlR6Wms4TVU5MU9GMGFXazBGcnhpZUc1Y1pCblBtYjlkU2pzcmZWTkNOZ1R4MUFjMEE0S3k4djRoMmErR3ZyWWVxaUNVc2hJSU5YZ3V1NEtieEJSMHZ6SXlVL3NXTDRWRmVIQmI1aHQiLCJtYWMiOiIzNjY2NDU3ODhiNjg4ODY3NDgyYWRlYjdhNzYyOGZmYjYxZWY2NTRhMGU1ZTBiNzUyOTUwOTkyYTNmMTJiZTM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1199082131\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-840051456 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58879</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"8 characters\">/role/45</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"8 characters\">/role/45</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"18 characters\">/index.php/role/45</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/45</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik4xL1ZqeGNSMDRBM1dHUVFSL2tFaWc9PSIsInZhbHVlIjoid1JkQ3F4Sjk2aDJ3LzE2eWMvaGpodm1XV0NvSXlEb2xVSzhIbUdpN3pOd1RMaW8zN3lvWk53TzAyRG92dHdIMDNEOE9SMmt2dWxjTmp1VW91WjNPQ3dWSTh6NitNL1VrQlFGVTU2OVhwOHhkczVpVU9ubDFVZ1NiR1BFSTUzb3oiLCJtYWMiOiIyNmZhZGQ2N2M2YmU2N2MzNGVlNjUzNDM2ZWJmYjBkM2NlMzlmMGJiZTEwMTYyYTJiMDFjNzMyZjQ4ZjA0ZWE1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InlOWCtJZXRVaUpKd1Y5ZllnNngxRUE9PSIsInZhbHVlIjoicG8yNXlPeHhiT1AxelY3dHJOVlZqWVlXVlR6Wms4TVU5MU9GMGFXazBGcnhpZUc1Y1pCblBtYjlkU2pzcmZWTkNOZ1R4MUFjMEE0S3k4djRoMmErR3ZyWWVxaUNVc2hJSU5YZ3V1NEtieEJSMHZ6SXlVL3NXTDRWRmVIQmI1aHQiLCJtYWMiOiIzNjY2NDU3ODhiNjg4ODY3NDgyYWRlYjdhNzYyOGZmYjYxZWY2NTRhMGU1ZTBiNzUyOTUwOTkyYTNmMTJiZTM2IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584517.6998</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584517</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-840051456\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1753828974 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cqS7hb8grzH44B1UcFTbOg1T2CdhTv8vyWR49MZl</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">co2imEHpZDVn0C4qNGAhqOZceT25o2GYegmjcLbi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1753828974\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-209328923 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:21:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkhMSTRORnpEZHZac2hhcmVlNXhocnc9PSIsInZhbHVlIjoiZUw4djc5T29TUjllQlFCL29qWXp4bnlpY0h5b24rNExQMFNtVTVHa2x2eUFPOURhTkx4dzF6Q1ErQmNMQzdPUlJrUWFZZjBBczZReUx5dkpMcFphRkJNNFhWa3R1R0NmUXpRYmdvRC9yUmRHRnJ6Rkg3eFYvUFlrcWhnNnFUUGwiLCJtYWMiOiJkYjcwYWY0NTUwOWUzNmZiNmRlZTljNGIwMDEyYmVhZjAyZGY5MzZmNGVjMDNhZDg0YzQyZTA1ZjAyMmE5OGM4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:58 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InZBek94YlowQUJiT2JqVkpZc3VKOHc9PSIsInZhbHVlIjoic05MbUFFTVJxdm9haDVBMG8zUStvWmRVUytKU2x1dFU3ZXlUZGpPcHIxdnpJMm1HbFZyQlBJQ3o2bG9LZDlNOVlCSHY0ZWNQMU9GZmVBRGNJeVVFcVNpOVdkZTFENjVXQ1JEeFhEWlRvVEYvRDkxb3llQ0RoWGhyVW9aYi9wNmwiLCJtYWMiOiJhYjc4ODk4ZjIyOTk1ZTY1OTY5NTYwNmZiNmM2OTVjZGNlYmVmYTkzZTlmMzFhNDUzZWQzNGUyMWM4ZDAwZThkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:58 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkhMSTRORnpEZHZac2hhcmVlNXhocnc9PSIsInZhbHVlIjoiZUw4djc5T29TUjllQlFCL29qWXp4bnlpY0h5b24rNExQMFNtVTVHa2x2eUFPOURhTkx4dzF6Q1ErQmNMQzdPUlJrUWFZZjBBczZReUx5dkpMcFphRkJNNFhWa3R1R0NmUXpRYmdvRC9yUmRHRnJ6Rkg3eFYvUFlrcWhnNnFUUGwiLCJtYWMiOiJkYjcwYWY0NTUwOWUzNmZiNmRlZTljNGIwMDEyYmVhZjAyZGY5MzZmNGVjMDNhZDg0YzQyZTA1ZjAyMmE5OGM4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InZBek94YlowQUJiT2JqVkpZc3VKOHc9PSIsInZhbHVlIjoic05MbUFFTVJxdm9haDVBMG8zUStvWmRVUytKU2x1dFU3ZXlUZGpPcHIxdnpJMm1HbFZyQlBJQ3o2bG9LZDlNOVlCSHY0ZWNQMU9GZmVBRGNJeVVFcVNpOVdkZTFENjVXQ1JEeFhEWlRvVEYvRDkxb3llQ0RoWGhyVW9aYi9wNmwiLCJtYWMiOiJhYjc4ODk4ZjIyOTk1ZTY1OTY5NTYwNmZiNmM2OTVjZGNlYmVmYTkzZTlmMzFhNDUzZWQzNGUyMWM4ZDAwZThkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-209328923\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-141241159 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cqS7hb8grzH44B1UcFTbOg1T2CdhTv8vyWR49MZl</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/role/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755584492</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-141241159\", {\"maxDepth\":0})</script>\n"}}