{"__meta": {"id": "Xce6d32cdce503ae2c46fd6ebd13f8a1d", "datetime": "2025-08-19 13:34:30", "utime": 1755581670.941627, "method": "GET", "uri": "/teams/create", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[13:34:30] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755581670.793816, "xdebug_link": null, "collector": "log"}, {"message": "[13:34:30] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/teams/create", "message_html": null, "is_string": false, "label": "debug", "time": 1755581670.851131, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755581670.508092, "end": 1755581670.94165, "duration": 0.43355798721313477, "duration_str": "434ms", "measures": [{"label": "Booting", "start": 1755581670.508092, "relative_start": 0, "end": 1755581670.775322, "relative_end": 1755581670.775322, "duration": 0.2672300338745117, "duration_str": "267ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755581670.775334, "relative_start": 0.2672419548034668, "end": 1755581670.941652, "relative_end": 2.1457672119140625e-06, "duration": 0.16631817817687988, "duration_str": "166ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25303456, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 17, "templates": [{"name": "team.create (\\resources\\views\\team\\create.blade.php)", "param_count": 5, "params": ["teams", "project", "title", "current_project", "roles"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 9, "params": ["__env", "app", "menuData", "errors", "teams", "project", "title", "current_project", "roles"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 9, "params": ["__env", "app", "menuData", "errors", "teams", "project", "title", "current_project", "roles"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 9, "params": ["__env", "app", "menuData", "errors", "teams", "project", "title", "current_project", "roles"], "type": "blade"}, {"name": "inc.character-counter (\\resources\\views\\inc\\character-counter.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "teams", "project", "title", "current_project", "roles", "inputId", "counterId", "max<PERSON><PERSON><PERSON>"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "teams", "project", "title", "current_project", "roles", "__currentLoopData", "role", "loop"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "teams", "project", "title", "current_project", "roles", "__currentLoopData", "role", "loop"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "teams", "project", "title", "current_project", "roles", "__currentLoopData", "role", "loop"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "teams", "project", "title", "current_project", "roles", "__currentLoopData", "role", "loop"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "teams", "project", "title", "current_project", "roles", "__currentLoopData", "role", "loop"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "teams", "project", "title", "current_project", "roles", "__currentLoopData", "role", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "teams", "project", "title", "current_project", "roles", "__currentLoopData", "role", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "teams", "project", "title", "current_project", "roles", "__currentLoopData", "role", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "teams", "project", "title", "current_project", "roles", "__currentLoopData", "role", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 21, "params": ["__env", "app", "menuData", "errors", "teams", "project", "title", "current_project", "roles", "__currentLoopData", "role", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 21, "params": ["__env", "app", "menuData", "errors", "teams", "project", "title", "current_project", "roles", "__currentLoopData", "role", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 21, "params": ["__env", "app", "menuData", "errors", "teams", "project", "title", "current_project", "roles", "__currentLoopData", "role", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET teams/create", "middleware": "web", "controller": "App\\Http\\Controllers\\TeamController@create", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "teams.create", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TeamController.php&line=58\">\\app\\Http\\Controllers\\TeamController.php:58-84</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0015499999999999997, "accumulated_duration_str": "1.55ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 28.387}, {"sql": "select * from `projects` where `team_name` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamController.php", "line": 66}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Http\\Controllers\\TeamController.php:66", "connection": "sagile", "start_percent": 28.387, "width_percent": 23.226}, {"sql": "select * from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamController.php", "line": 70}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Http\\Controllers\\TeamController.php:70", "connection": "sagile", "start_percent": 51.613, "width_percent": 25.161}, {"sql": "select * from `teams`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamController.php", "line": 76}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Http\\Controllers\\TeamController.php:76", "connection": "sagile", "start_percent": 76.774, "width_percent": 23.226}]}, "models": {"data": {"App\\Team": 15, "App\\Role": 19, "App\\User": 1}, "count": 35}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/teams/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/teams/create", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-746085659 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-746085659\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1773948210 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1773948210\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-484984138 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/team</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkowRHRiaFVHL1BrTThob0VyQnAya2c9PSIsInZhbHVlIjoiRjFHbHdHRzRaSFlZWmNwV2ZNdWdheXAwSEUvSFN3a1lybmZDbXQvR2NpY1ZUVFVweEdwckxoOVJWcC9MRDRubU9KOVU4d3EybmdxZ1FhRVVia1BGdVJ2Z1g2YjBYS3BvMFZCTk9Udlo4SXAzSmNZSkVGelVDZmZXV1IwR1UvZEoiLCJtYWMiOiJiNjdmMzlhNmE3ZWQwNmIwYzk5YmQ5MzEzZWE5NmU5Y2ZmZjFlNzU5NGVmMjRkYWM5NTVhMzgxYThiYzQ2MjM0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlAxUDgrYXY1Z1lLZk5mb2RRVVZKTUE9PSIsInZhbHVlIjoiUHZmYlF0UHBTKzU1Qi82RGFTeHVhd25rZVVUemxiQ0tWanpHYjE3Qm1WbHBpaU1rdzJ1WmEwZUxmVkxtcVRxUVQrdXoxMVhVSW1TQkJQU083Nkt3bEhQSEthZ0hxcjNaUW80RlUwOTI4a2RaSWRDYU9COTJ5UWJMZ29KVlJ4dUoiLCJtYWMiOiI1OWM5MDViMDgxZDJkNDdhMWY1MTRmZTBhNTIzMTIzNTY3YmIxMDMxOWY1NTgwODVjOTAwMmI4YTRkM2JiMTc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-484984138\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1091504218 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51696</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"13 characters\">/teams/create</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"13 characters\">/teams/create</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/index.php/teams/create</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/team</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkowRHRiaFVHL1BrTThob0VyQnAya2c9PSIsInZhbHVlIjoiRjFHbHdHRzRaSFlZWmNwV2ZNdWdheXAwSEUvSFN3a1lybmZDbXQvR2NpY1ZUVFVweEdwckxoOVJWcC9MRDRubU9KOVU4d3EybmdxZ1FhRVVia1BGdVJ2Z1g2YjBYS3BvMFZCTk9Udlo4SXAzSmNZSkVGelVDZmZXV1IwR1UvZEoiLCJtYWMiOiJiNjdmMzlhNmE3ZWQwNmIwYzk5YmQ5MzEzZWE5NmU5Y2ZmZjFlNzU5NGVmMjRkYWM5NTVhMzgxYThiYzQ2MjM0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlAxUDgrYXY1Z1lLZk5mb2RRVVZKTUE9PSIsInZhbHVlIjoiUHZmYlF0UHBTKzU1Qi82RGFTeHVhd25rZVVUemxiQ0tWanpHYjE3Qm1WbHBpaU1rdzJ1WmEwZUxmVkxtcVRxUVQrdXoxMVhVSW1TQkJQU083Nkt3bEhQSEthZ0hxcjNaUW80RlUwOTI4a2RaSWRDYU9COTJ5UWJMZ29KVlJ4dUoiLCJtYWMiOiI1OWM5MDViMDgxZDJkNDdhMWY1MTRmZTBhNTIzMTIzNTY3YmIxMDMxOWY1NTgwODVjOTAwMmI4YTRkM2JiMTc1IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755581670.5081</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755581670</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091504218\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1769751080 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GKxRuD042xMQ4k3v5EHCZLsF1sfAXXnsz4rkTCog</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1769751080\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-827100814 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:34:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjlvcE9DZEcvaW5Ud0pvYVJpcysvVWc9PSIsInZhbHVlIjoidVpIbVVXZEkvdFgxdVZkMHcwNTRpOW1pc1NxZk81ZlJpS3B4amZqbzAxazBtclBqQS8vUlp4RjlMZytwOUtQN05razdOaGgyRGtvd2VqODhjdWJMVVgzNFBQSGEzSjdRcmNuUklhQlVjRHpMUHFaWUV4TGgvQy9aUXVwdzBuQmMiLCJtYWMiOiJmNjEzNTU3OTdkYzNiZTAzODE4NzI2OWYyYjEyMjA3Y2U2NTM5NzIxYmFlZTcwNGViM2M3YTE2NjMwNjgwZWViIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:34:30 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImsxbEFOcnRUZW1WTUJMU2NycUwyTGc9PSIsInZhbHVlIjoicmZwUHY1NWtNcFNUOEtQYmZxUXpMODdpckZ0a0FwbVZubG1QUXl6dW84bHZmN3ROUmcxYXg4Mk5hU2l0L25tdXJqbDhxNjloajdqa013VnJnWXBoQlN4U0hnamExZ21USThzUGdpRWVQaW94TlhneEZubHVBMG1SSHRwQkpnYlUiLCJtYWMiOiI0NDQxNzE0MmJjNjRlZWE1Njc2OGMzYmZiYjY4Nzc1ZDgxZTUxYzNmODQ0ZmI2YTMxYjgxYmIwZWI2OTk1NmQ2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:34:30 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjlvcE9DZEcvaW5Ud0pvYVJpcysvVWc9PSIsInZhbHVlIjoidVpIbVVXZEkvdFgxdVZkMHcwNTRpOW1pc1NxZk81ZlJpS3B4amZqbzAxazBtclBqQS8vUlp4RjlMZytwOUtQN05razdOaGgyRGtvd2VqODhjdWJMVVgzNFBQSGEzSjdRcmNuUklhQlVjRHpMUHFaWUV4TGgvQy9aUXVwdzBuQmMiLCJtYWMiOiJmNjEzNTU3OTdkYzNiZTAzODE4NzI2OWYyYjEyMjA3Y2U2NTM5NzIxYmFlZTcwNGViM2M3YTE2NjMwNjgwZWViIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:34:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImsxbEFOcnRUZW1WTUJMU2NycUwyTGc9PSIsInZhbHVlIjoicmZwUHY1NWtNcFNUOEtQYmZxUXpMODdpckZ0a0FwbVZubG1QUXl6dW84bHZmN3ROUmcxYXg4Mk5hU2l0L25tdXJqbDhxNjloajdqa013VnJnWXBoQlN4U0hnamExZ21USThzUGdpRWVQaW94TlhneEZubHVBMG1SSHRwQkpnYlUiLCJtYWMiOiI0NDQxNzE0MmJjNjRlZWE1Njc2OGMzYmZiYjY4Nzc1ZDgxZTUxYzNmODQ0ZmI2YTMxYjgxYmIwZWI2OTk1NmQ2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:34:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-827100814\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-740828545 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/teams/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-740828545\", {\"maxDepth\":0})</script>\n"}}