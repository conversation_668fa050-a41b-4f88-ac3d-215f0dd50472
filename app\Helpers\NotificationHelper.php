<?php

namespace App\Helpers;

use App\User;
use App\TeamMapping;
use App\Mail\CommentCreated;
use Illuminate\Support\Facades\Mail;

class NotificationHelper
{
    /**
     * Send Gmail notifications for a task comment.
     */
    public static function sendGmailNotifications($comment, $task, $project, $sprint, $action)
    {
        $users = self::getRecipients($comment);

        // Filter out the comment creator
        $users = self::excludeCommentCreator($users, $comment->created_by);

        $subject = $comment->created_by . ' ' . $action . ' a comment on ' . $task->title;

        foreach ($users as $user) {
            Mail::to($user->email)->queue(new CommentCreated($comment, $task, $project, $sprint, $subject));
        }
    }

    /**
     * Get the users who should receive the email notifications.
     */
    private static function getRecipients($comment)
    {
        $assignedTo = json_decode($comment->assigned_to, true);

        if (!$assignedTo || empty($assignedTo)) {
            return \App\TeamMapping::getAllTeamMembers(\Auth::user()->username);
        }

        return User::whereIn('username', $assignedTo)->get();
    }

    /**
     * Exclude the comment creator from the list of recipients.
     */
    private static function excludeCommentCreator($users, $creatorUsername)
    {
        return $users->reject(fn($user) => $user->username === $creatorUsername);
    }
}