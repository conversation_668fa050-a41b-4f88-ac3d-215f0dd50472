{"__meta": {"id": "X244512acdaa36da7fb724c0b5c4655e3", "datetime": "2025-08-19 14:20:49", "utime": 1755584449.787522, "method": "GET", "uri": "/teammapping/Team%20AD", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[14:20:49] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584449.645608, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584449.292924, "end": 1755584449.787544, "duration": 0.49462008476257324, "duration_str": "495ms", "measures": [{"label": "Booting", "start": 1755584449.292924, "relative_start": 0, "end": 1755584449.62411, "relative_end": 1755584449.62411, "duration": 0.33118605613708496, "duration_str": "331ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584449.624122, "relative_start": 0.33119797706604004, "end": 1755584449.787546, "relative_end": 1.9073486328125e-06, "duration": 0.16342401504516602, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25346968, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 15, "templates": [{"name": "teammapping.index (\\resources\\views\\teammapping\\index.blade.php)", "param_count": 3, "params": ["teammappings", "teams", "title"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET teammapping/{team_name}", "middleware": "web", "controller": "App\\Http\\Controllers\\TeamMappingController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "teammapping.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TeamMappingController.php&line=59\">\\app\\Http\\Controllers\\TeamMappingController.php:59-76</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00133, "accumulated_duration_str": "1.33ms", "statements": [{"sql": "select * from `users` where `id` = 31 limit 1", "type": "query", "params": [], "bindings": ["31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 33.835}, {"sql": "select * from `teams` where `team_name` = 'Team AD' limit 1", "type": "query", "params": [], "bindings": ["Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 64}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:64", "connection": "sagile", "start_percent": 33.835, "width_percent": 33.835}, {"sql": "select * from `teammappings` where `project_id` is null and `team_name` = 'Team AD'", "type": "query", "params": [], "bindings": ["Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 70}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:70", "connection": "sagile", "start_percent": 67.669, "width_percent": 32.331}]}, "models": {"data": {"App\\TeamMapping": 2, "App\\Team": 1, "App\\User": 1}, "count": 4}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/teammapping/Team%20AD\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "31", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/teammapping/Team%20AD", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-811751653 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-811751653\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-974598328 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-974598328\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-266794466 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/team</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImVtd081bzdibHd1alY0MEhsWHVSSlE9PSIsInZhbHVlIjoiR0ZqTlpaZTRsS1NDK3VkaUlRQUs2N0xFTlV5WE9wS1J3Yk1MS3NSbGNubzVRTzRCUmcwRFdRMXJFanFPa0hDTmhCeE9zK1YxWHdENldZUU9LYk1wRlp3c2pkMUt3Z2FZQ0NSSjcvNE1wTE1DNEVINEFxemJ3UHNHNjNzeGs5OHoiLCJtYWMiOiI4Y2VlMjVmMzczNGJjMmY0MDg3Y2Y5NTkyMzU4ODdmZmFlMzExMGRkNThhODVmNTNjMzNmZDZjMmZhZGRmOWYyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik5WTHVocGwxMkl4ZHdXL2xpbGMyVlE9PSIsInZhbHVlIjoidFpWblFDVVJtcEh1dUIxMVV6bFpJelgxaDA2R0hxMEI0Mnl1eXR2VDhMa0dXTS9IOHRJMUZYc0hkaTkwZG5ia2JvTis4bExXMWJaTk03M1pBMW83a0Y2azY4SGxKQUR2LzJkcVoycXBaQnVqUzZDOGhJR1M5SHk1M2lLNnV1VUIiLCJtYWMiOiI2ODc3NjA4MTY5MzIzZGRiNzk5ZWE0NWU3NDI3ZmM5MmNkMjhlODg3M2VhNDkyMzQ0ZTgyNWVmYTJjNmI4YmRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-266794466\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-195813436 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">49488</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/teammapping/Team%20AD</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/teammapping/Team AD</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/index.php/teammapping/Team AD</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/team</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImVtd081bzdibHd1alY0MEhsWHVSSlE9PSIsInZhbHVlIjoiR0ZqTlpaZTRsS1NDK3VkaUlRQUs2N0xFTlV5WE9wS1J3Yk1MS3NSbGNubzVRTzRCUmcwRFdRMXJFanFPa0hDTmhCeE9zK1YxWHdENldZUU9LYk1wRlp3c2pkMUt3Z2FZQ0NSSjcvNE1wTE1DNEVINEFxemJ3UHNHNjNzeGs5OHoiLCJtYWMiOiI4Y2VlMjVmMzczNGJjMmY0MDg3Y2Y5NTkyMzU4ODdmZmFlMzExMGRkNThhODVmNTNjMzNmZDZjMmZhZGRmOWYyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik5WTHVocGwxMkl4ZHdXL2xpbGMyVlE9PSIsInZhbHVlIjoidFpWblFDVVJtcEh1dUIxMVV6bFpJelgxaDA2R0hxMEI0Mnl1eXR2VDhMa0dXTS9IOHRJMUZYc0hkaTkwZG5ia2JvTis4bExXMWJaTk03M1pBMW83a0Y2azY4SGxKQUR2LzJkcVoycXBaQnVqUzZDOGhJR1M5SHk1M2lLNnV1VUIiLCJtYWMiOiI2ODc3NjA4MTY5MzIzZGRiNzk5ZWE0NWU3NDI3ZmM5MmNkMjhlODg3M2VhNDkyMzQ0ZTgyNWVmYTJjNmI4YmRkIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584449.2929</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584449</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-195813436\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1166816241 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CfapZDSBofQwHxJsJhdmHULLiISKd6PUgVWjQqhr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1166816241\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1407155631 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:20:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjdVRkpVdy95ejhDa3Jsa3VXVERkRnc9PSIsInZhbHVlIjoiTjdEbHhzNVFCMXhySm0rZ08vN3VtYWRkQkRCRWtxVFVOZXV1c2dyRFE5dExZc0szcUdFcTRDK3p5ZzBSQk96UGwvSnRmVDVRY2lYeVc0MlQvSmFKS3M1RC9nMUpnNzRtQ1QrQWtoZ3NValdZa0N6Tmk3VUEwaGx0OVIrVUl2NGwiLCJtYWMiOiIzZGVhMzdlZTExNWViMzYzOGEyMzhmMjg1YWQ1ODM0ZmY1ZTc4MDZiYWJjNTJhNjY1MTllZmFjYTZiODU5YjFkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:20:49 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImI0emhCZEdiUU16R3ZUK2FYNHp6SEE9PSIsInZhbHVlIjoiWmR3RW1xdXFPSG9tZEFub3ZwS3VPZjBhUVZFNWd4Q1VyZE1zOG5jMDYzaGN4T3V1cnFBYkZ3Qk1FS0FSWlFOWmRKMW55TlE1YnZlb25jWkUwY1RWZndmVkJaQmE0Q2M1YlBaQlJLaTdySHErSGs1cUM4Z3B5Vk1pVS90aVpMSjkiLCJtYWMiOiI1OGI3NGVmZWZmZWVkODI3OTc0MmM1NDhmYWEwMDRhYTdiZGE2MzIwMjhmNzA0MWMxMmE3NDY5ZGRhYTZjYWJiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:20:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjdVRkpVdy95ejhDa3Jsa3VXVERkRnc9PSIsInZhbHVlIjoiTjdEbHhzNVFCMXhySm0rZ08vN3VtYWRkQkRCRWtxVFVOZXV1c2dyRFE5dExZc0szcUdFcTRDK3p5ZzBSQk96UGwvSnRmVDVRY2lYeVc0MlQvSmFKS3M1RC9nMUpnNzRtQ1QrQWtoZ3NValdZa0N6Tmk3VUEwaGx0OVIrVUl2NGwiLCJtYWMiOiIzZGVhMzdlZTExNWViMzYzOGEyMzhmMjg1YWQ1ODM0ZmY1ZTc4MDZiYWJjNTJhNjY1MTllZmFjYTZiODU5YjFkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:20:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImI0emhCZEdiUU16R3ZUK2FYNHp6SEE9PSIsInZhbHVlIjoiWmR3RW1xdXFPSG9tZEFub3ZwS3VPZjBhUVZFNWd4Q1VyZE1zOG5jMDYzaGN4T3V1cnFBYkZ3Qk1FS0FSWlFOWmRKMW55TlE1YnZlb25jWkUwY1RWZndmVkJaQmE0Q2M1YlBaQlJLaTdySHErSGs1cUM4Z3B5Vk1pVS90aVpMSjkiLCJtYWMiOiI1OGI3NGVmZWZmZWVkODI3OTc0MmM1NDhmYWEwMDRhYTdiZGE2MzIwMjhmNzA0MWMxMmE3NDY5ZGRhYTZjYWJiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:20:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1407155631\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1136960370 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/teammapping/Team%20AD</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1136960370\", {\"maxDepth\":0})</script>\n"}}