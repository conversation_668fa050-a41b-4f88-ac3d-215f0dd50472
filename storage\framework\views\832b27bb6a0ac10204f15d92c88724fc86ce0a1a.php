
<?php echo $__env->make('inc.success', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('inc.style', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


<?php $__env->startSection('navbar'); ?>
  <?php echo $__env->make('inc.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<?php echo $__env->make('inc.title', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<br>

<?php if($user->user_type == 1 || in_array('Project Manager', $roles)): ?>

<?php if($generalNFR && $generalNFR->isNotEmpty()): ?>
    
<table>
  <tr>
      <th>General NFR</th>
      <th>Description</th>
      <th>List</th>
      <th>View</th>
      <th>Edit</th>
      <th>Delete</th>
  </tr>

  <?php $__currentLoopData = $generalNFR; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $nfr): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td><?php echo e($nfr->general_nfr); ?></td>
                    <td><?php echo e($nfr->general_nfr_desc); ?></td>
                    <td><a href="<?php echo e(route('nfr.show', ['general_nfr_id' => $nfr->general_nfr_id])); ?>" class="btn btn-primary">List</a></td>
                    <td><a href="<?php echo e(route('nfr.viewGeneral', ['general_nfr_id' => $nfr->general_nfr_id])); ?>" class="btn btn-primary">View</a></td>
                    <?php if($user->user_type == 1 && $nfr->created_by == $user->id): ?>
                    <td><a href="<?php echo e(route('nfr.edit', ['general_nfr_id' => $nfr->general_nfr_id])); ?>" class="btn btn-warning">Edit</a></td>
                    <td>
                    <br>
                        <form action="<?php echo e(route('nfr.destroy', ['general_nfr_id' => $nfr->general_nfr_id])); ?>" method="post">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure want to delete this non-functional requirement?')">Delete</button>
                        </form>
                    </td>
                    <?php else: ?>
                    <td colspan="2">Only creator can edit or delete</td>
                    <?php endif; ?>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

</table>

<!-- Pagination Links -->
<div class="pagination-container">
    <?php echo e($generalNFR->links('pagination::bootstrap-4')); ?>

</div>

<?php else: ?>
    <p>No General NFRs found.</p>
<?php endif; ?>

<br><br><br>
<?php if($user->user_type == 1): ?>
    <a href="<?php echo e(route('nfr.create')); ?>" class="btn btn-success">Add General NFR</a>
<?php endif; ?>
<!-- <?php else: ?>
    <div>Only Project Managers and Admins can view this page.</div>
<?php endif; ?> -->

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/nfr/index.blade.php ENDPATH**/ ?>