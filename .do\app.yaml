name: monkfish-app
region: sgp
services:
  - name: sagilepmt-utm
    github:
      repo: ivlyntay/SAgilePMT_UTM
      branch: Ivlyn_SAgile
      deploy_on_push: true
    environment_slug: php
    build_command: npm run build
    run_command: heroku-php-apache2 public/
    source_dir: /
    instance_count: 1
    instance_size_slug: apps-s-1vcpu-0.5gb
    http_port: 8080
    envs:
      - key: APP_NAME
        value: Laravel
        scope: RUN_AND_BUILD_TIME
      - key: APP_ENV
        value: local
        scope: RUN_AND_BUILD_TIME
      - key: APP_KEY
        value: base64:hITQN2YFGUvdW0O4aTYyWgICp6Y+6olDr8ZiRJamWvo=
        scope: RUN_AND_BUILD_TIME
      - key: APP_DEBUG
        value: "true"
        scope: RUN_AND_BUILD_TIME
      - key: APP_URL
        value: http://localhost
        scope: RUN_AND_BUILD_TIME
      - key: LOG_CHANNEL
        value: stack
        scope: RUN_AND_BUILD_TIME
      - key: DB_CONNECTION
        value: mysql
        scope: RUN_AND_BUILD_TIME
      - key: DB_HOST
        value: mydb.cnkkmm26y6hj.us-east-1.rds.amazonaws.com
        scope: RUN_AND_BUILD_TIME
      - key: DB_PORT
        value: "3306"
        scope: RUN_AND_BUILD_TIME
      - key: DB_DATABASE
        value: kanban
        scope: RUN_AND_BUILD_TIME
      - key: DB_USERNAME
        value: kanban
        scope: RUN_AND_BUILD_TIME
      - key: DB_PASSWORD
        value: sagilePass!
        scope: RUN_AND_BUILD_TIME
      - key: CACHE_DRIVER
        value: file
        scope: RUN_AND_BUILD_TIME
      - key: SESSION_DRIVER
        value: file
        scope: RUN_AND_BUILD_TIME
      - key: SESSION_LIFETIME
        value: "120"
        scope: RUN_AND_BUILD_TIME
      - key: REDIS_HOST
        value: 127.0.0.1
        scope: RUN_AND_BUILD_TIME
      - key: REDIS_PASSWORD
        value: "null"
        scope: RUN_AND_BUILD_TIME
      - key: REDIS_PORT
        value: "6379"
        scope: RUN_AND_BUILD_TIME
      - key: MAIL_MAILER
        value: smtp
        scope: RUN_AND_BUILD_TIME
      - key: MAIL_HOST
        value: smtp.gmail.com
        scope: RUN_AND_BUILD_TIME
      - key: MAIL_PORT
        value: "587"
        scope: RUN_AND_BUILD_TIME
      - key: MAIL_USERNAME
        value: <EMAIL>
        scope: RUN_AND_BUILD_TIME
      - key: MAIL_PASSWORD
        value: qbyyflqplnmghvhq
        scope: RUN_AND_BUILD_TIME
      - key: MAIL_ENCRYPTION
        value: tls
        scope: RUN_AND_BUILD_TIME
      - key: MAIL_FROM_ADDRESS
        value: <EMAIL>
        scope: RUN_AND_BUILD_TIME
      - key: MAIL_FROM_NAME
        value: Laravel
        scope: RUN_AND_BUILD_TIME
      - key: PUSHER_APP_CLUSTER
        value: mt1
        scope: RUN_AND_BUILD_TIME
      - key: MIX_PUSHER_APP_CLUSTER
        value: mt1
        scope: RUN_AND_BUILD_TIME
