{"__meta": {"id": "X721c5edbb3ca8ad6ef2e7ecabdb8ace2", "datetime": "2025-08-19 10:45:05", "utime": 1755571505.021492, "method": "POST", "uri": "/upload-radar-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[10:45:04] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755571504.920151, "xdebug_link": null, "collector": "log"}, {"message": "[10:45:05] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/upload-radar-chart", "message_html": null, "is_string": false, "label": "debug", "time": 1755571505.008236, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755571504.506188, "end": 1755571505.021521, "duration": 0.5153331756591797, "duration_str": "515ms", "measures": [{"label": "Booting", "start": 1755571504.506188, "relative_start": 0, "end": 1755571504.892856, "relative_end": 1755571504.892856, "duration": 0.38666796684265137, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755571504.89287, "relative_start": 0.38668203353881836, "end": 1755571505.021524, "relative_end": 2.86102294921875e-06, "duration": 0.12865400314331055, "duration_str": "129ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23475816, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST upload-radar-chart", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@uploadRadarChart", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=141\">\\app\\Http\\Controllers\\CIGController.php:141-151</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00053, "accumulated_duration_str": "530μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/radar-data/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"path_info": "/upload-radar-chart", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1374432082 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"63914 characters\">data:image/png;base64,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</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1374432082\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-354373979 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">63926</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkxDdGNNVXJOUFVXL1RXMDRRcmlHZVE9PSIsInZhbHVlIjoid3VoNTFOdmNhME84OHZCaElEYmpERUEzd0VXYnNTS1JYN1VwOFZ4a3RneWdjMURoa3lyNVQ1enMveTI3RlhWMkhnNGRhS0NDUmZDYWhnN3cvN2hobVgzOHNvYUdOYitwSjllU3g1NldUQTFaNytRUnN3ZXBJTzFtNlA1Q2tsMXAiLCJtYWMiOiIyYWNkYTM1N2FlMTc1ZGFmMDA4MzJhZjkxZGQ1ZjRkMmVmYjZhOGJkNmRmYzdiMTgwZDAzNzQwNDgwY2ZhODA2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InQ5UDVhaXJRMmhVUTFSR25EMlhmNGc9PSIsInZhbHVlIjoiZWJ6QkFSQStJekNaRmJxTDhGS2M4aUhlUXpFTWdvRnorY2UzbWdjYjgyYnFQbWtCekZGc05HWlFFdnQzWGZLZHlUdVBMYVJoZytYb2dlbG4wNlJoMFNPTm1RV1RQS0FtNFRselZzMVZRZGxWWllQbVlrNVZ0eWVMZzVUUjRaWWwiLCJtYWMiOiIwZDJiYjY2YjRlNjhhZDlhZGRmZTE3NjZmMTVjMzg1ZDRlMGYwZjMyZjBjMGVkMWNjOWE0ZGE5YzJkOWU5MTI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-354373979\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-932003595 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">61552</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/index.php/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">63926</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">63926</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkxDdGNNVXJOUFVXL1RXMDRRcmlHZVE9PSIsInZhbHVlIjoid3VoNTFOdmNhME84OHZCaElEYmpERUEzd0VXYnNTS1JYN1VwOFZ4a3RneWdjMURoa3lyNVQ1enMveTI3RlhWMkhnNGRhS0NDUmZDYWhnN3cvN2hobVgzOHNvYUdOYitwSjllU3g1NldUQTFaNytRUnN3ZXBJTzFtNlA1Q2tsMXAiLCJtYWMiOiIyYWNkYTM1N2FlMTc1ZGFmMDA4MzJhZjkxZGQ1ZjRkMmVmYjZhOGJkNmRmYzdiMTgwZDAzNzQwNDgwY2ZhODA2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InQ5UDVhaXJRMmhVUTFSR25EMlhmNGc9PSIsInZhbHVlIjoiZWJ6QkFSQStJekNaRmJxTDhGS2M4aUhlUXpFTWdvRnorY2UzbWdjYjgyYnFQbWtCekZGc05HWlFFdnQzWGZLZHlUdVBMYVJoZytYb2dlbG4wNlJoMFNPTm1RV1RQS0FtNFRselZzMVZRZGxWWllQbVlrNVZ0eWVMZzVUUjRaWWwiLCJtYWMiOiIwZDJiYjY2YjRlNjhhZDlhZGRmZTE3NjZmMTVjMzg1ZDRlMGYwZjMyZjBjMGVkMWNjOWE0ZGE5YzJkOWU5MTI4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755571504.5062</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755571504</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-932003595\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1114623483 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114623483\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1621965004 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:45:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImhzcFRNSm05WTRvaUdieHZsMk1STUE9PSIsInZhbHVlIjoiMGEzNDRYMG1kVDhQeEtzSFRQa0drSEpZQXZhT3pvT21aQXNQSURWZFI0TThGVVpkdE12bU9SM1ovbGtTRGVxZ1dISEUvTHlhc0tWM2hVOU9yMUVQcWY5aU1ZVUtDYVYvVjZObFBONjlUUTEwZEtrNFgxeis4T1dXVFFJRDB0U2QiLCJtYWMiOiJhOTVkYzA3MzE3NjcxM2FjZmRjMmFhYzM5YTZmZjNlMjMyNGU5NWE4YWVkNzJiZjcwYjA3ZmExNDExNTI1Y2M0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:45:05 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlZPRkhsL0ZBRUNsalU0UHNlZUxvZFE9PSIsInZhbHVlIjoiSnNCeUFEVk9XeXg3S3BKNEE1eFhHVXpoaXRKVWRaZGpnWmNCVnJHSnNjVGFrTTNjQnROdXJYUi9EZUlYWmNPdjlRVUQrYUpzdmlKY0RjTytoRnJMYm1ySkRCSlYydm9oZXBqZFdPNkZ0WlNpYmJqNHM1OXlzN3FaQWhQSDRPaTkiLCJtYWMiOiIwYjNlMDI1ZWRiOTcyZTZlZDE3ZGMwODc1Yjg2YWRmNDMyMmQ3YTI0ZGFhZjhkNTMyOGQ5MGRlYTQ3NDliNjlkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:45:05 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImhzcFRNSm05WTRvaUdieHZsMk1STUE9PSIsInZhbHVlIjoiMGEzNDRYMG1kVDhQeEtzSFRQa0drSEpZQXZhT3pvT21aQXNQSURWZFI0TThGVVpkdE12bU9SM1ovbGtTRGVxZ1dISEUvTHlhc0tWM2hVOU9yMUVQcWY5aU1ZVUtDYVYvVjZObFBONjlUUTEwZEtrNFgxeis4T1dXVFFJRDB0U2QiLCJtYWMiOiJhOTVkYzA3MzE3NjcxM2FjZmRjMmFhYzM5YTZmZjNlMjMyNGU5NWE4YWVkNzJiZjcwYjA3ZmExNDExNTI1Y2M0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:45:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlZPRkhsL0ZBRUNsalU0UHNlZUxvZFE9PSIsInZhbHVlIjoiSnNCeUFEVk9XeXg3S3BKNEE1eFhHVXpoaXRKVWRaZGpnWmNCVnJHSnNjVGFrTTNjQnROdXJYUi9EZUlYWmNPdjlRVUQrYUpzdmlKY0RjTytoRnJMYm1ySkRCSlYydm9oZXBqZFdPNkZ0WlNpYmJqNHM1OXlzN3FaQWhQSDRPaTkiLCJtYWMiOiIwYjNlMDI1ZWRiOTcyZTZlZDE3ZGMwODc1Yjg2YWRmNDMyMmQ3YTI0ZGFhZjhkNTMyOGQ5MGRlYTQ3NDliNjlkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:45:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1621965004\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-919456677 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/radar-data/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-919456677\", {\"maxDepth\":0})</script>\n"}}