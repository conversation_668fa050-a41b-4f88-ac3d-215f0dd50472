{"__meta": {"id": "X6f6288290ec127a7ec710853a8e10b22", "datetime": "2025-08-19 14:26:57", "utime": 1755584817.047659, "method": "GET", "uri": "/team", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[14:26:56] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584816.883328, "xdebug_link": null, "collector": "log"}, {"message": "[14:26:56] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/team", "message_html": null, "is_string": false, "label": "debug", "time": 1755584816.944219, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584816.555273, "end": 1755584817.047684, "duration": 0.49241089820861816, "duration_str": "492ms", "measures": [{"label": "Booting", "start": 1755584816.555273, "relative_start": 0, "end": 1755584816.862271, "relative_end": 1755584816.862271, "duration": 0.30699801445007324, "duration_str": "307ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584816.862286, "relative_start": 0.30701303482055664, "end": 1755584817.047687, "relative_end": 3.0994415283203125e-06, "duration": 0.18540096282958984, "duration_str": "185ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25224384, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 15, "templates": [{"name": "team.index (\\resources\\views\\team\\index.blade.php)", "param_count": 4, "params": ["teams", "pros", "managedTeams", "title"], "type": "blade"}, {"name": "inc.dashboard (\\resources\\views\\inc\\dashboard.blade.php)", "param_count": 8, "params": ["__env", "app", "menuData", "errors", "teams", "pros", "managedTeams", "title"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 8, "params": ["__env", "app", "menuData", "errors", "teams", "pros", "managedTeams", "title"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "teams", "pros", "managedTeams", "title", "__empty_1", "__currentLoopData", "team", "loop"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "teams", "pros", "managedTeams", "title", "__empty_1", "__currentLoopData", "team", "loop"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "teams", "pros", "managedTeams", "title", "__empty_1", "__currentLoopData", "team", "loop"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "teams", "pros", "managedTeams", "title", "__empty_1", "__currentLoopData", "team", "loop"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "teams", "pros", "managedTeams", "title", "__empty_1", "__currentLoopData", "team", "loop"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "teams", "pros", "managedTeams", "title", "__empty_1", "__currentLoopData", "team", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "teams", "pros", "managedTeams", "title", "__empty_1", "__currentLoopData", "team", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "teams", "pros", "managedTeams", "title", "__empty_1", "__currentLoopData", "team", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "teams", "pros", "managedTeams", "title", "__empty_1", "__currentLoopData", "team", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 21, "params": ["__env", "app", "menuData", "errors", "teams", "pros", "managedTeams", "title", "__empty_1", "__currentLoopData", "team", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 21, "params": ["__env", "app", "menuData", "errors", "teams", "pros", "managedTeams", "title", "__empty_1", "__currentLoopData", "team", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 21, "params": ["__env", "app", "menuData", "errors", "teams", "pros", "managedTeams", "title", "__empty_1", "__currentLoopData", "team", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET team", "middleware": "web", "controller": "App\\Http\\Controllers\\TeamController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "team.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TeamController.php&line=22\">\\app\\Http\\Controllers\\TeamController.php:22-55</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.0019299999999999999, "accumulated_duration_str": "1.93ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 24.352}, {"sql": "select `team_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamController.php", "line": 28}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\TeamController.php:28", "connection": "sagile", "start_percent": 24.352, "width_percent": 21.244}, {"sql": "select * from `projects` where `team_name` in ('iv<PERSON>\\'s team', 'iv<PERSON>\\'s team', 'Team 888')", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team", "ivlyn&#039;s team", "Team 888"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamController.php", "line": 31}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Http\\Controllers\\TeamController.php:31", "connection": "sagile", "start_percent": 45.596, "width_percent": 17.617}, {"sql": "select * from `teams` where `team_name` in ('iv<PERSON>\\'s team', 'iv<PERSON>\\'s team', 'Team 888')", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team", "ivlyn&#039;s team", "Team 888"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamController.php", "line": 34}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Http\\Controllers\\TeamController.php:34", "connection": "sagile", "start_percent": 63.212, "width_percent": 20.207}, {"sql": "select `team_name` from `teammappings` where `project_id` is null and `username` = 'ivlyn' and `role_name` = 'Team Manager' and `invitation_status` = 'accepted'", "type": "query", "params": [], "bindings": ["ivlyn", "Team Manager", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamController.php", "line": 47}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Http\\Controllers\\TeamController.php:47", "connection": "sagile", "start_percent": 83.42, "width_percent": 16.58}]}, "models": {"data": {"App\\Team": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aYGigeJtZIOxG53eFrHOwTgZM6VE6gXcgZTLIKtJ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/team\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/team", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1663534140 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1663534140\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1869399609 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1869399609\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-893607407 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImV0NHc0YVg4OWZlUWkxV1IvemtJSnc9PSIsInZhbHVlIjoiVTV3bWg3WWdqTlNVOG8zVUhtZ09pODBRcnppY1p6OVJodDRRZ2lmRndHR2F5aDIxUElHL3YxdDZaQmdKT3RVS1c3emJ2aE1zVnIvcFN5RGgxa3dFbE1RT3oxeW5uM00rNkg4bDFFU2JCVDI4VnEvUFErS0J5WVNzVTFsb280bXciLCJtYWMiOiJlMTUyOWY4NzkwZGU2MjgwYmY0NzhiNGVhNDNhMmQ0OGYyZDVlMzVkZDllMTZiMjM3M2U3OWY0MjI0MjI5YmM2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkdIK3J5aVRGMzlCdFFBZmludk5EY0E9PSIsInZhbHVlIjoiRU05dlIxSko2K29zMGphZmJiOGg3NlllakhqVHFMdzdJN2Z2YmVYZzIwWnVzMW9zeElaQlRHblJGWTFUTkhhZFR6ZWR2eUdCaE9heGNSMHhkL1BGOFNDUFRTcTUvdFU0QisxTXFXVHVIY09JTHMrRTBLbG9TQjVSUFcyTWE2MjUiLCJtYWMiOiJjNGRlZWY2MmY3NTc1MjlhNTk4NzU1ZGY4NzI3YjE2NGY3YzIzNGFhOTkxYWFhZmM1MGNjN2NlZTcxOWUwNWQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-893607407\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-607884099 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">62435</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"5 characters\">/team</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"5 characters\">/team</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/index.php/team</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImV0NHc0YVg4OWZlUWkxV1IvemtJSnc9PSIsInZhbHVlIjoiVTV3bWg3WWdqTlNVOG8zVUhtZ09pODBRcnppY1p6OVJodDRRZ2lmRndHR2F5aDIxUElHL3YxdDZaQmdKT3RVS1c3emJ2aE1zVnIvcFN5RGgxa3dFbE1RT3oxeW5uM00rNkg4bDFFU2JCVDI4VnEvUFErS0J5WVNzVTFsb280bXciLCJtYWMiOiJlMTUyOWY4NzkwZGU2MjgwYmY0NzhiNGVhNDNhMmQ0OGYyZDVlMzVkZDllMTZiMjM3M2U3OWY0MjI0MjI5YmM2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkdIK3J5aVRGMzlCdFFBZmludk5EY0E9PSIsInZhbHVlIjoiRU05dlIxSko2K29zMGphZmJiOGg3NlllakhqVHFMdzdJN2Z2YmVYZzIwWnVzMW9zeElaQlRHblJGWTFUTkhhZFR6ZWR2eUdCaE9heGNSMHhkL1BGOFNDUFRTcTUvdFU0QisxTXFXVHVIY09JTHMrRTBLbG9TQjVSUFcyTWE2MjUiLCJtYWMiOiJjNGRlZWY2MmY3NTc1MjlhNTk4NzU1ZGY4NzI3YjE2NGY3YzIzNGFhOTkxYWFhZmM1MGNjN2NlZTcxOWUwNWQ4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584816.5553</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584816</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-607884099\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1277296049 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aYGigeJtZIOxG53eFrHOwTgZM6VE6gXcgZTLIKtJ</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">622yZ1gXtfSvGfodSVAwZB2kv0pPcpdla66n0PI6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1277296049\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-14354591 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:26:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InlDTStaTmltY2MzaEF4K3FFZjBzWXc9PSIsInZhbHVlIjoiTUxzZVd1SWt0YU5UWVJFZWRwTzR0K0JIRHZuOThKWll4R1dSWU52cTRHTTkrbHlHTWp6TytqSFcwVTlHcVgwclI2UzE4SlFWc215eFp0Mmtjd3FCMVJKYjNFek0rUWZKaTVjaUZ5c2xKMFYrbHoxWTJ3RDNqV1ZTdkhHaDRtcGEiLCJtYWMiOiI3MmEyNWEwYTEyYTdjZDhmMTVmMmJkYzA4Yzc5Y2NiNzJiOThhZDFhNjBlMDY3NjU0NWRjZjVlZWYzN2NkMmI5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:26:57 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlFqS1J5K29oS25JdGFUenQ0bzRhZHc9PSIsInZhbHVlIjoibnp5ekMvQ1hHR29PM1ZDSU1LOU4zTEVPYWhVeUVzS3RDK1Q2YkJhTWpLVjM2UnBjNU9rMFlYZm9hVVNLRXZZaUkrbUFXd3lqTTRuTkJrREpZZVBxbWZVK3ZKdnRoNmtxdUlZUkhsUVllR2hDSUtBZFlOL0lVYldnTEMzMHEvZC8iLCJtYWMiOiI4MDVhYzA4MWY3Yjc3OWMxMTMwNmE3YmM0MDJmYzk4ZmU1MmI3ODRjZDQ3MTAzYmFjYzFmYjgzOTIxMjkwZjc2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:26:57 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InlDTStaTmltY2MzaEF4K3FFZjBzWXc9PSIsInZhbHVlIjoiTUxzZVd1SWt0YU5UWVJFZWRwTzR0K0JIRHZuOThKWll4R1dSWU52cTRHTTkrbHlHTWp6TytqSFcwVTlHcVgwclI2UzE4SlFWc215eFp0Mmtjd3FCMVJKYjNFek0rUWZKaTVjaUZ5c2xKMFYrbHoxWTJ3RDNqV1ZTdkhHaDRtcGEiLCJtYWMiOiI3MmEyNWEwYTEyYTdjZDhmMTVmMmJkYzA4Yzc5Y2NiNzJiOThhZDFhNjBlMDY3NjU0NWRjZjVlZWYzN2NkMmI5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:26:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlFqS1J5K29oS25JdGFUenQ0bzRhZHc9PSIsInZhbHVlIjoibnp5ekMvQ1hHR29PM1ZDSU1LOU4zTEVPYWhVeUVzS3RDK1Q2YkJhTWpLVjM2UnBjNU9rMFlYZm9hVVNLRXZZaUkrbUFXd3lqTTRuTkJrREpZZVBxbWZVK3ZKdnRoNmtxdUlZUkhsUVllR2hDSUtBZFlOL0lVYldnTEMzMHEvZC8iLCJtYWMiOiI4MDVhYzA4MWY3Yjc3OWMxMTMwNmE3YmM0MDJmYzk4ZmU1MmI3ODRjZDQ3MTAzYmFjYzFmYjgzOTIxMjkwZjc2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:26:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14354591\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1041277525 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aYGigeJtZIOxG53eFrHOwTgZM6VE6gXcgZTLIKtJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/team</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1041277525\", {\"maxDepth\":0})</script>\n"}}