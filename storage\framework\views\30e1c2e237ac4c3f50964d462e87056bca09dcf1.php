

<?php echo $__env->make('inc.breadcrumbStyle', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->startSection('content'); ?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header Section with Breadcrumb -->
            <div class="mb-4">
                <div class="d-flex align-items-center">
                    <h1 class="mb-0"><a href="<?php echo e(route('project-assignments.index')); ?>" class="breadcrumb-link">Project Assignments</a></h1>
                    <h1 class="mb-0"><span class="breadcrumb-separator">/</span></h1>
                    <h1 class="mb-0"><a href="<?php echo e(route('project-assignments.show', $project->id)); ?>" class="breadcrumb-link"><?php echo e($project->proj_name); ?></a></h1>
                    <h1 class="mb-0"><span class="breadcrumb-separator">/</span></h1>
                    <h1 class="mb-0 breadcrumb-current">Assign Member</h1>
                </div>
                <hr class="my-3" style="border-top: 2px solid #e3e6f0;">
                <div class="mt-2">
                    <h2 class="h3 mb-0">Assign Member to Project</h2>
                    <p class="text-muted">Project: <strong><?php echo e($project->proj_name); ?></strong></p>
                </div>
            </div>

            <?php if(session('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo e(session('error')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user-plus me-2"></i>
                                Assign Team Member
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="<?php echo e(route('project-assignments.store')); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="project_id" value="<?php echo e($project->id); ?>">

                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-1"></i>
                                        Select Team Member
                                    </label>
                                    <select name="username" id="username" class="form-select <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                                        <option value="">Choose a team member...</option>
                                        <?php $__currentLoopData = $teamMembers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($member->username); ?>" <?php echo e(old('username') == $member->username ? 'selected' : ''); ?>>
                                                <?php echo e($member->username); ?> (<?php echo e($member->role_name); ?>)
                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    <?php if($teamMembers->count() == 0): ?>
                                        <div class="form-text text-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            No available team members to assign to this project.
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="mb-3">
                                    <label for="role_name" class="form-label">
                                        <i class="fas fa-user-tag me-1"></i>
                                        Project Role
                                    </label>
                                    <select name="role_name" id="role_name" class="form-select <?php $__errorArgs = ['role_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                                        <option value="">Choose a project role...</option>
                                        <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($role->role_name); ?>" <?php echo e(old('role_name') == $role->role_name ? 'selected' : ''); ?>>
                                                <?php echo e($role->role_name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['role_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-success" <?php echo e($teamMembers->count() == 0 ? 'disabled' : ''); ?>>
                                        <i class="fas fa-plus me-1"></i>
                                        Assign to Project
                                    </button>
                                    <a href="<?php echo e(route('project-assignments.index')); ?>" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        Back to Projects
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-1"></i>
                                Project Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <small class="text-muted">Project Name</small>
                                <div class="fw-bold"><?php echo e($project->proj_name); ?></div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Team</small>
                                <div class="fw-bold"><?php echo e($project->team_name); ?></div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Description</small>
                                <div><?php echo e($project->proj_desc ?: 'No description available'); ?></div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Duration</small>
                                <div>
                                    <?php echo e($project->start_date ? \Carbon\Carbon::parse($project->start_date)->format('M d, Y') : 'TBD'); ?> 
                                    - 
                                    <?php echo e($project->end_date ? \Carbon\Carbon::parse($project->end_date)->format('M d, Y') : 'TBD'); ?>

                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if($teamMembers->count() > 0): ?>
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-users me-1"></i>
                                    Available Team Members
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php $__currentLoopData = $teamMembers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <div class="fw-bold"><?php echo e($member->username); ?></div>
                                            <small class="text-muted">Team Role: <?php echo e($member->role_name); ?></small>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/project-assignments/create.blade.php ENDPATH**/ ?>