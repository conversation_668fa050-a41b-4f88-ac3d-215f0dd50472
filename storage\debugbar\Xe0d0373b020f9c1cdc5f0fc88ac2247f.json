{"__meta": {"id": "Xe0d0373b020f9c1cdc5f0fc88ac2247f", "datetime": "2025-08-19 14:21:22", "utime": 1755584482.297406, "method": "GET", "uri": "/project-assignments/create?project_id=45", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[14:21:22] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584482.122113, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584481.803174, "end": 1755584482.297438, "duration": 0.4942638874053955, "duration_str": "494ms", "measures": [{"label": "Booting", "start": 1755584481.803174, "relative_start": 0, "end": 1755584482.100149, "relative_end": 1755584482.100149, "duration": 0.29697489738464355, "duration_str": "297ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584482.100161, "relative_start": 0.29698705673217773, "end": 1755584482.297441, "relative_end": 3.0994415283203125e-06, "duration": 0.1972799301147461, "duration_str": "197ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25314144, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 14, "templates": [{"name": "project-assignments.create (\\resources\\views\\project-assignments\\create.blade.php)", "param_count": 3, "params": ["project", "teamMembers", "roles"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "project", "teamMembers", "roles"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "project", "teamMembers", "roles", "__currentLoopData", "loop", "role"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "project", "teamMembers", "roles", "__currentLoopData", "loop", "role"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "project", "teamMembers", "roles", "__currentLoopData", "loop", "role"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "project", "teamMembers", "roles", "__currentLoopData", "loop", "role"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "project", "teamMembers", "roles", "__currentLoopData", "loop", "role"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "project", "teamMembers", "roles", "__currentLoopData", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "project", "teamMembers", "roles", "__currentLoopData", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "project", "teamMembers", "roles", "__currentLoopData", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "project", "teamMembers", "roles", "__currentLoopData", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "project", "teamMembers", "roles", "__currentLoopData", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "project", "teamMembers", "roles", "__currentLoopData", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "project", "teamMembers", "roles", "__currentLoopData", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET project-assignments/create", "middleware": "web", "controller": "App\\Http\\Controllers\\ProjectAssignmentController@create", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "project-assignments.create", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProjectAssignmentController.php&line=57\">\\app\\Http\\Controllers\\ProjectAssignmentController.php:57-82</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00208, "accumulated_duration_str": "2.08ms", "statements": [{"sql": "select * from `users` where `id` = 31 limit 1", "type": "query", "params": [], "bindings": ["31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 22.115}, {"sql": "select * from `projects` where `projects`.`id` = '45' limit 1", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 60}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:60", "connection": "sagile", "start_percent": 22.115, "width_percent": 25}, {"sql": "select * from `teammappings` where `project_id` is null and `team_name` = 'Team AD' and `invitation_status` = 'accepted' and `username` not in (select `username` from `teammappings` where `project_id` = '45')", "type": "query", "params": [], "bindings": ["Team AD", "accepted", "45"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 77}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:77", "connection": "sagile", "start_percent": 47.115, "width_percent": 32.212}, {"sql": "select * from `roles` where `project_id` = '45'", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 79}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:79", "connection": "sagile", "start_percent": 79.327, "width_percent": 20.673}]}, "models": {"data": {"App\\Role": 2, "App\\Project": 1, "App\\User": 1}, "count": 4}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Gf8WwtCrGWaxQlU79zc0pNXq8iCcBZ5LNC6rM5bb", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/project-assignments/create?project_id=45\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "31", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/project-assignments/create", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1073464060 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>project_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">45</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1073464060\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-249631816 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>project_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">45</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-249631816\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1387633175 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/project-assignments</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImxDVWI0WllJczhnd2lLaStQSHVuUmc9PSIsInZhbHVlIjoiV2lKRFJhMzRwYWNzdHVhbVU1ZnhKeFlCT2o4c1FyWXJqUUpmR3I3T29qNkhJSEVvTEZjVlM5U2RWNTh3dkZCTGJtUEJHNkxrQjJPMU5sdHZKc0NaNURqMmZjTFVKYWQ5bTk2R1dPR3lVa21XL0VmNUU1TGhJNGNkc05LbXA5WHkiLCJtYWMiOiIxNTJiMzcxNmJhZTJiNmE4NDVkNGY3MjczN2U4MDE4YWM2ODQ4Y2U4N2M1MmFhY2RlMjBhYzVmNGQ1NWFmMjg2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik9JdFZjdHdackRaZTVPcExJdkgwWHc9PSIsInZhbHVlIjoiTlh1ZGJKamFrN2RVdXliZ1ZZandCV3ZPakJNM0RDcjBJMXZ3K3FVWEVJV1FXeDRJcHc4QU5oUXNUTEp2RllNU25iM1NDZnJiK1dZK202U3JLMkRXTjVlRnExM3ZaQ3FhTDdRSzVweS9renJYZDNicmxWd1ZjSVBGRDExQzlFUEgiLCJtYWMiOiIyOTAzZDNjMDMyMTMyN2Q3OWQxMWExZWRhN2FjODlkNjQyMWUyODQ5MmExNzJkZTQzYWQ3OGM3ODZhODMxNDY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1387633175\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1930944732 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51558</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"41 characters\">/project-assignments/create?project_id=45</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/project-assignments/create</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/index.php/project-assignments/create</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">project_id=45</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/project-assignments</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImxDVWI0WllJczhnd2lLaStQSHVuUmc9PSIsInZhbHVlIjoiV2lKRFJhMzRwYWNzdHVhbVU1ZnhKeFlCT2o4c1FyWXJqUUpmR3I3T29qNkhJSEVvTEZjVlM5U2RWNTh3dkZCTGJtUEJHNkxrQjJPMU5sdHZKc0NaNURqMmZjTFVKYWQ5bTk2R1dPR3lVa21XL0VmNUU1TGhJNGNkc05LbXA5WHkiLCJtYWMiOiIxNTJiMzcxNmJhZTJiNmE4NDVkNGY3MjczN2U4MDE4YWM2ODQ4Y2U4N2M1MmFhY2RlMjBhYzVmNGQ1NWFmMjg2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik9JdFZjdHdackRaZTVPcExJdkgwWHc9PSIsInZhbHVlIjoiTlh1ZGJKamFrN2RVdXliZ1ZZandCV3ZPakJNM0RDcjBJMXZ3K3FVWEVJV1FXeDRJcHc4QU5oUXNUTEp2RllNU25iM1NDZnJiK1dZK202U3JLMkRXTjVlRnExM3ZaQ3FhTDdRSzVweS9renJYZDNicmxWd1ZjSVBGRDExQzlFUEgiLCJtYWMiOiIyOTAzZDNjMDMyMTMyN2Q3OWQxMWExZWRhN2FjODlkNjQyMWUyODQ5MmExNzJkZTQzYWQ3OGM3ODZhODMxNDY0IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584481.8032</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584481</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1930944732\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-514585121 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gf8WwtCrGWaxQlU79zc0pNXq8iCcBZ5LNC6rM5bb</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Y5RUSXSwBgBrFq8oNyWObubNEtQ5lYLQfTmmXryF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-514585121\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1205022791 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:21:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Imttd2EzMHQvcUwxNVBkSU1ta2xCK3c9PSIsInZhbHVlIjoiakNWTXhoMHhMdDFuY2xURW4wL01HUFV6MGtLTDdjakN0VXpXSmFSZUNaWjBJb2VNWHdsbTRJU1NGL3M2SnFTN2U3MUNWOVNmTTN6VnhrMzFrd2hBWTBwTzBOYkowVzdzMko4TStkVjBydnJwQkpjRWVBOVpHN2VkdFVrbzV3ZHAiLCJtYWMiOiI4ZjEwYjRhMDcyMzVmMDY2NDg2OTdmMzQ4Y2JiOGYwOTU1M2ZhOTNhMzE1MTgzYzg3Y2MyZGIwM2QyNGM5M2E0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:22 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im94MnFuWjZEYnV2Y2RvVytkTlNlY3c9PSIsInZhbHVlIjoiS3owaUhmZkJlbEZjTTR5U0tYOHYrNEVKcnlZOVFmQjRXWDF1clFwemowMndmeXRLVDFhTVhKYUtvTkZmSGhBQURrcFdhM0YwZ0MxSkNOd2tveW9SaHN6eHU4ODhkckZFLzloWVBCOXZwT0t2NmtZMTR2V3AyVVVtdzRSMjBxVWQiLCJtYWMiOiI2NDQyNTUyMjMxZTEwMWQ2MzIyYTZkNTZlMzI0YTBhMmJjNDkzZWUyM2IwZWM3NGYyNGY1ODRiNDQxYjcxZGU0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:22 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Imttd2EzMHQvcUwxNVBkSU1ta2xCK3c9PSIsInZhbHVlIjoiakNWTXhoMHhMdDFuY2xURW4wL01HUFV6MGtLTDdjakN0VXpXSmFSZUNaWjBJb2VNWHdsbTRJU1NGL3M2SnFTN2U3MUNWOVNmTTN6VnhrMzFrd2hBWTBwTzBOYkowVzdzMko4TStkVjBydnJwQkpjRWVBOVpHN2VkdFVrbzV3ZHAiLCJtYWMiOiI4ZjEwYjRhMDcyMzVmMDY2NDg2OTdmMzQ4Y2JiOGYwOTU1M2ZhOTNhMzE1MTgzYzg3Y2MyZGIwM2QyNGM5M2E0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im94MnFuWjZEYnV2Y2RvVytkTlNlY3c9PSIsInZhbHVlIjoiS3owaUhmZkJlbEZjTTR5U0tYOHYrNEVKcnlZOVFmQjRXWDF1clFwemowMndmeXRLVDFhTVhKYUtvTkZmSGhBQURrcFdhM0YwZ0MxSkNOd2tveW9SaHN6eHU4ODhkckZFLzloWVBCOXZwT0t2NmtZMTR2V3AyVVVtdzRSMjBxVWQiLCJtYWMiOiI2NDQyNTUyMjMxZTEwMWQ2MzIyYTZkNTZlMzI0YTBhMmJjNDkzZWUyM2IwZWM3NGYyNGY1ODRiNDQxYjcxZGU0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:21:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1205022791\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1111645758 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gf8WwtCrGWaxQlU79zc0pNXq8iCcBZ5LNC6rM5bb</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"62 characters\">http://127.0.0.1:8000/project-assignments/create?project_id=45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1111645758\", {\"maxDepth\":0})</script>\n"}}