{"__meta": {"id": "Xc32d11e8b8aca19c1fa63ab6003f3e8e", "datetime": "2025-08-19 13:18:12", "utime": 1755580692.52755, "method": "GET", "uri": "/register", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[13:18:12] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755580692.459946, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755580692.184084, "end": 1755580692.527569, "duration": 0.34348511695861816, "duration_str": "343ms", "measures": [{"label": "Booting", "start": 1755580692.184084, "relative_start": 0, "end": 1755580692.44096, "relative_end": 1755580692.44096, "duration": 0.25687599182128906, "duration_str": "257ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755580692.440972, "relative_start": 0.25688815116882324, "end": 1755580692.527571, "relative_end": 1.9073486328125e-06, "duration": 0.08659887313842773, "duration_str": "86.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23364680, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "auth.register (\\resources\\views\\auth\\register.blade.php)", "param_count": 0, "params": [], "type": "blade"}, {"name": "inc.page-auth (\\resources\\views\\inc\\page-auth.blade.php)", "param_count": 4, "params": ["__env", "app", "menuData", "errors"], "type": "blade"}, {"name": "layouts.blankLayout (\\resources\\views\\layouts\\blankLayout.blade.php)", "param_count": 4, "params": ["__env", "app", "menuData", "errors"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 4, "params": ["__env", "app", "menuData", "errors"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 5, "params": ["__env", "app", "menuData", "errors", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 5, "params": ["__env", "app", "menuData", "errors", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 5, "params": ["__env", "app", "menuData", "errors", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET register", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\RegisterController@showRegistrationForm", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "register", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\Auth\\RegisterController.php&line=18\">\\app\\Http\\Controllers\\Auth\\RegisterController.php:18-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "BkrRPIr4Izbs1esgnOnLniu1ROlR55WioisLwo42", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/register\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/register", "status_code": "<pre class=sf-dump id=sf-dump-1807846728 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1807846728\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1091156254 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1091156254\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-858398392 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-858398392\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1569580089 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlAydzhXb1lic2oyYkpXb0hOOVh0Z1E9PSIsInZhbHVlIjoidE45WWtNcmJGaG43WUhreEl3eFZBS282WnVxSFBPQzQzajFNbEh6QWNXL3ViZXp1MFZJY1RTdit1NTVlUkRjcW5rbnNSNzRqNHhyaVllWWdzbFplSlp4S0hucmpYSVFpY1V0Q2wwbU5MQ3dSeS9JWU5CYll4Nmx5QytGbEdoaXAiLCJtYWMiOiI3MTY1ZTJkYjI3NGRmM2QyZTUwZDFlNzhkYzRiMjQzNGQ5MDhiYmYzMzhhOWRiNmE3ZGNlZGM5Y2I0MTQxNDk2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkNmeTM0N1hxdjE5TjdKYUl0RFppNUE9PSIsInZhbHVlIjoiZkdvNEVZTDRTcnZ4NjJCU1QwVzhYb0duS2hhMVI3ZXdQYlBiTFo2cEYzS1dnd3JnVWZKaDVPcGg1WENnSFkrdkJiVCt2V3ZEaXNuUFlTYlY1YTZET3I3UE9hckxDQ3BOajk4MkhJM21RazYzMVZzdmRYMTRKQkJsSGNrYmFlMkgiLCJtYWMiOiIzYjZmNDQzOTAxNWUzZWMzNzAwZGI2YzVkYzIyMGViMWM0NjJhMjQwYjc0ZWZkM2YzMWIzYTdiOTQ2NzRjN2QyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1569580089\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-2094734001 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58191</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/register</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/register</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/index.php/register</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/register</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlAydzhXb1lic2oyYkpXb0hOOVh0Z1E9PSIsInZhbHVlIjoidE45WWtNcmJGaG43WUhreEl3eFZBS282WnVxSFBPQzQzajFNbEh6QWNXL3ViZXp1MFZJY1RTdit1NTVlUkRjcW5rbnNSNzRqNHhyaVllWWdzbFplSlp4S0hucmpYSVFpY1V0Q2wwbU5MQ3dSeS9JWU5CYll4Nmx5QytGbEdoaXAiLCJtYWMiOiI3MTY1ZTJkYjI3NGRmM2QyZTUwZDFlNzhkYzRiMjQzNGQ5MDhiYmYzMzhhOWRiNmE3ZGNlZGM5Y2I0MTQxNDk2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkNmeTM0N1hxdjE5TjdKYUl0RFppNUE9PSIsInZhbHVlIjoiZkdvNEVZTDRTcnZ4NjJCU1QwVzhYb0duS2hhMVI3ZXdQYlBiTFo2cEYzS1dnd3JnVWZKaDVPcGg1WENnSFkrdkJiVCt2V3ZEaXNuUFlTYlY1YTZET3I3UE9hckxDQ3BOajk4MkhJM21RazYzMVZzdmRYMTRKQkJsSGNrYmFlMkgiLCJtYWMiOiIzYjZmNDQzOTAxNWUzZWMzNzAwZGI2YzVkYzIyMGViMWM0NjJhMjQwYjc0ZWZkM2YzMWIzYTdiOTQ2NzRjN2QyIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755580692.1841</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755580692</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2094734001\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BkrRPIr4Izbs1esgnOnLniu1ROlR55WioisLwo42</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2c53gnhjoEvbgROlM1txupNWlHxc45QrlVzMCSWR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:18:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik1YQ0ord0Q5a0lLWDdZVE5FM3pYNWc9PSIsInZhbHVlIjoiUHFhWFFrbEpTa2t3U3FoY2c4eFNyWEpBSkVSTXVGL3hjaUIrWWpROTdSbjRJUG15QytURDFsMkFQK3RGTmlBU3Y3QkZRclpQeVlmckR5b2d6YVdVQTFld3F2T3BQVnRKN1BXTUUvWWxmbWZ4b1lEeExGV0hZNS9EUVZYaWltQXoiLCJtYWMiOiJlMjIxNzRmNDRjMjdiMzlmN2M2ZjlkMTY1MzY2ODA2NzgyMmFkOGU2ZTU3OWIyNmNiYWUxMDcxOThjMzU3YWM3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:18:12 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkJaWEJaRlRwYThYeEMwTEh1V1laK2c9PSIsInZhbHVlIjoib1dRQUlUNy9Zb01wRjd3TnZVcDFRc2N6N1dZYlFGMkJoekJJT0toRTZ4V0F2VUFFMms3RllCUVpLWHFjSmMrRXZ1T3hHb2tBNmJrVGJBTmVGUGtTbks3N1FHVVBPVEVZQkxSVTJhNU9PMlR5d3RCbWRiR3BzZU5aQzZKVzcxR1MiLCJtYWMiOiI5YzljYTkxNjU2MzZjMDJkZWIwNmMxYzE5M2NkOGUyZjcxY2JmYjc3YjNhYTAyMDFjMzhhYmM0NzZhZDM0NjEzIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:18:12 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik1YQ0ord0Q5a0lLWDdZVE5FM3pYNWc9PSIsInZhbHVlIjoiUHFhWFFrbEpTa2t3U3FoY2c4eFNyWEpBSkVSTXVGL3hjaUIrWWpROTdSbjRJUG15QytURDFsMkFQK3RGTmlBU3Y3QkZRclpQeVlmckR5b2d6YVdVQTFld3F2T3BQVnRKN1BXTUUvWWxmbWZ4b1lEeExGV0hZNS9EUVZYaWltQXoiLCJtYWMiOiJlMjIxNzRmNDRjMjdiMzlmN2M2ZjlkMTY1MzY2ODA2NzgyMmFkOGU2ZTU3OWIyNmNiYWUxMDcxOThjMzU3YWM3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:18:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkJaWEJaRlRwYThYeEMwTEh1V1laK2c9PSIsInZhbHVlIjoib1dRQUlUNy9Zb01wRjd3TnZVcDFRc2N6N1dZYlFGMkJoekJJT0toRTZ4V0F2VUFFMms3RllCUVpLWHFjSmMrRXZ1T3hHb2tBNmJrVGJBTmVGUGtTbks3N1FHVVBPVEVZQkxSVTJhNU9PMlR5d3RCbWRiR3BzZU5aQzZKVzcxR1MiLCJtYWMiOiI5YzljYTkxNjU2MzZjMDJkZWIwNmMxYzE5M2NkOGUyZjcxY2JmYjc3YjNhYTAyMDFjMzhhYmM0NzZhZDM0NjEzIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:18:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BkrRPIr4Izbs1esgnOnLniu1ROlR55WioisLwo42</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}