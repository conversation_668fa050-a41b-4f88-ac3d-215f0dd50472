

<!-- Add Font Awesome CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<?php echo $__env->make('inc.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('inc.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Header Section -->
    <div class="mb-4">
        <div class="d-flex align-items-center">
            <h1 class="mb-0">All Teams</h1>
        </div>
    </div>

    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    <div class="table-responsive">
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>Team Name</th>
                    <th>View Members</th>
                    <th>Delete</th>
                </tr>
            </thead>
            <tbody>
                <?php $__empty_1 = true; $__currentLoopData = $teams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $team): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td><?php echo e($team->team_name); ?></td>
                        <td>
                            <a href="<?php echo e(action('TeamMappingController@index', $team['team_name'])); ?>" class="btn btn-primary">View</a>
                        </td>
                        <td>
                            <?php if($managedTeams->contains($team->team_name)): ?>
                                <button type="button" class="btn btn-danger delete-team-btn" 
                                        data-team-id="<?php echo e($team->id); ?>" 
                                        data-team-name="<?php echo e($team->team_name); ?>"
                                        data-team-route="<?php echo e(route('teams.destroy', $team)); ?>">
                                    Delete
                                </button>
                            <?php else: ?>
                                <span class="text-muted">
                                    <i class="fas fa-lock me-1"></i>No Permission
                                </span>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="3" class="text-center">No teams found!</td>
                    </tr>
                <?php endif; ?>
                </tbody>
    </table>
    </div>
    <br><br><br>

    <a href="<?php echo e(route('teams.create')); ?>" class="btn btn-success">Add Team</a>

    <!-- Single Delete Team Modal -->
    <div class="modal fade" id="deleteTeamModal" tabindex="-1" aria-labelledby="deleteTeamModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteTeamModalLabel">Confirm Team Deletion</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="text-danger">Warning: This action cannot be undone!</p>
                    <p>To confirm deletion, please type the team name: <strong id="teamNameToDelete"></strong></p>
                    <div class="mb-3">
                        <input type="text" class="form-control" id="confirmTeamNameInput" placeholder="Type team name">
                    </div>
                    <form id="deleteTeamForm" method="GET">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn" disabled>Delete Team</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let currentTeamData = {};
            
            // Handle delete button clicks
            const deleteButtons = document.querySelectorAll('.delete-team-btn');
            const modal = new bootstrap.Modal(document.getElementById('deleteTeamModal'));
            const teamNameDisplay = document.getElementById('teamNameToDelete');
            const confirmInput = document.getElementById('confirmTeamNameInput');
            const confirmButton = document.getElementById('confirmDeleteBtn');
            const deleteForm = document.getElementById('deleteTeamForm');
            
            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Store current team data
                    currentTeamData = {
                        id: this.getAttribute('data-team-id'),
                        name: this.getAttribute('data-team-name'),
                        route: this.getAttribute('data-team-route')
                    };
                    
                    // Update modal content
                    teamNameDisplay.textContent = currentTeamData.name;
                    deleteForm.action = currentTeamData.route;
                    
                    // Reset input and button state
                    confirmInput.value = '';
                    confirmButton.disabled = true;
                    
                    // Show modal
                    modal.show();
                });
            });
            
            // Handle team name confirmation input
            confirmInput.addEventListener('input', function() {
                confirmButton.disabled = this.value !== currentTeamData.name;
            });
            
            // Handle confirm delete button click
            confirmButton.addEventListener('click', function() {
                if (!this.disabled) {
                    deleteForm.submit();
                }
            });
        });
    </script>

    <style>
        .table {
            background-color: white;
            border-radius: 0.25rem;
        }
        .table th {
            background-color: #f8f9fa;
        }
        
        @keyframes  fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/team/index.blade.php ENDPATH**/ ?>