name: Laravel CI

on:
  push:
    branches:
      - Ivlyn_SAgile  # Trigger CI workflow on push to the Ivlyn_SAgile branch
  pull_request:
    branches:
      - Ivlyn_SAgile  # Trigger CI workflow on PR targeting the Ivlyn_SAgile branch

jobs:
  laravel-tests:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:5.7
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping --silent" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      # Step 1: Checkout the code
      - name: Checkout repository
        uses: actions/checkout@v2

      # Step 2: Set up PHP environment
      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'  # Adjust PHP version based on your Laravel version
          extensions: mbstring, bcmath, xml, curl, pdo, pdo_mysql
          ini-values: post_max_size=256M, max_execution_time=300

      # Step 3: Install Composer dependencies
      - name: Install Composer dependencies
        run: composer install --prefer-dist --no-progress --no-suggest --optimize-autoloader

      # Step 4: Copy .env and configure database for testing
      - name: Copy .env
        run: cp .env.example .env

      # Step 5: Generate the application key
      - name: Generate application key
        run: php artisan key:generate

      # Step 6: Run database migrations
      - name: Run migrations
        run: php artisan migrate --force

      # Step 7: Run tests
      - name: Run tests
        run: php artisan test
