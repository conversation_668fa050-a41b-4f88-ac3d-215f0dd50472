{"__meta": {"id": "X704026bd1f4adc47f68dba422baf94a7", "datetime": "2025-08-19 14:26:36", "utime": 1755584796.380683, "method": "POST", "uri": "/teammappings/104/updateRole", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[14:26:36] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584796.156508, "xdebug_link": null, "collector": "log"}, {"message": "[14:26:36] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/teammappings/104/updateRole", "message_html": null, "is_string": false, "label": "debug", "time": 1755584796.301734, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.66983, "end": 1755584796.380704, "duration": 0.7108738422393799, "duration_str": "711ms", "measures": [{"label": "Booting", "start": **********.66983, "relative_start": 0, "end": 1755584796.125806, "relative_end": 1755584796.125806, "duration": 0.4559760093688965, "duration_str": "456ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584796.125815, "relative_start": 0.45598483085632324, "end": 1755584796.380706, "relative_end": 2.1457672119140625e-06, "duration": 0.25489115715026855, "duration_str": "255ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23554320, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST teammappings/{teammapping}/updateRole", "middleware": "web", "controller": "App\\Http\\Controllers\\TeamMappingController@updateRole", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "teammappings.updateRole", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TeamMappingController.php&line=474\">\\app\\Http\\Controllers\\TeamMappingController.php:474-516</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.0045, "accumulated_duration_str": "4.5ms", "statements": [{"sql": "select * from `users` where `id` = 31 limit 1", "type": "query", "params": [], "bindings": ["31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 11.111}, {"sql": "select exists(select * from `user_role` where `user_id` = 31 and `role_id` = 0) as `exists`", "type": "query", "params": [], "bindings": ["31", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "\\app\\Traits\\HasCachedPermissions.php", "line": 279}, {"index": 10, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 13, "namespace": null, "name": "\\app\\Traits\\HasCachedPermissions.php", "line": 275}, {"index": 14, "namespace": null, "name": "\\app\\User.php", "line": 202}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Traits\\HasCachedPermissions.php:279", "connection": "sagile", "start_percent": 11.111, "width_percent": 8.889}, {"sql": "select `team_name`, `role_name`, `project_id` from `teammappings` where `username` = 'tay'", "type": "query", "params": [], "bindings": ["tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": "middleware", "name": "load.permissions", "line": 81}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 16, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "middleware::load.permissions:81", "connection": "sagile", "start_percent": 20, "width_percent": 8.444}, {"sql": "select `role_id` from `user_role` where `user_id` = 31", "type": "query", "params": [], "bindings": ["31"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": "middleware", "name": "load.permissions", "line": 87}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 16, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "middleware::load.permissions:87", "connection": "sagile", "start_percent": 28.444, "width_percent": 6.889}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Team Manager' and `project_id` is null limit 1", "type": "query", "params": [], "bindings": ["Team Manager"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 35.333, "width_percent": 6.444}, {"sql": "select * from `teammappings` where `teammappings`.`teammapping_id` = '104' limit 1", "type": "query", "params": [], "bindings": ["104"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 477}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:477", "connection": "sagile", "start_percent": 41.778, "width_percent": 8.222}, {"sql": "select exists(select * from `teammappings` where `team_name` = 'Team 888' and `username` = 'tay' and `role_name` = 'Team Manager' and `invitation_status` = 'accepted') as `exists`", "type": "query", "params": [], "bindings": ["Team 888", "tay", "Team Manager", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 35}, {"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 56}, {"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 480}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:35", "connection": "sagile", "start_percent": 50, "width_percent": 13.333}, {"sql": "select * from `users` where `username` = 'tay' limit 1", "type": "query", "params": [], "bindings": ["tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 496}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:496", "connection": "sagile", "start_percent": 63.333, "width_percent": 10.667}, {"sql": "update `teammappings` set `role_name` = 'Team Member', `teammappings`.`updated_at` = '2025-08-19 14:26:36' where `teammapping_id` = 104", "type": "query", "params": [], "bindings": ["Team Member", "2025-08-19 14:26:36", "104"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 503}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00117, "duration_str": "1.17ms", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:503", "connection": "sagile", "start_percent": 74, "width_percent": 26}]}, "models": {"data": {"App\\TeamMapping": 1, "App\\User": 2}, "count": 3}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "lavecFvUe2D1z6a5lMQP4GMqE1oDiISndLrLo35L", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/teammapping/Team%20888\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "31", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"path_info": "/teammappings/104/updateRole", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1313722173 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1313722173\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-862345492 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>role_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Team Member</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lavecFvUe2D1z6a5lMQP4GMqE1oDiISndLrLo35L</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-862345492\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-121345612 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">284</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarydTqsjZYGVJOojThA</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/teammapping/Team%20888</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InpjdC9nM0wxKzhiQUlTVXpkZ1QwVkE9PSIsInZhbHVlIjoiU3lhYzhQZjErTkFOdnhYSTJhVXZISjFRWExMa3AwUy9nYkhKUUZrdytmKzlLMVc5OFdUWkcxVmVWdEFIZXJhQXhDdVMvbTdsUmxRUzFaU2MxVUlIQ1hEVDJDY1lCZVd6ZGl5djNYK0dKWTNHSXM4NHlSYjFBNldHOFFoc2FBbkIiLCJtYWMiOiJlNGE0NjJiNWUwMTk2MTY1YjY4NDBiZGM5OGFkZDc0NDBhMzkxOWMzZTM3YTc4MjQzNGM1MWFjZTRiMmQ5YWRhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ii8vUnBCakRjTG5YTWZuQ3hyQlc5Y1E9PSIsInZhbHVlIjoiNUJVR0pQMDdFaHptSlg4Rzd0VFQ2aGlrNGJQUjNBTWhQeUpmQkh3YmNzYmhKZElaWXBSWDlySU9NTlkxSWwxQXN2cFVRUjNhRWhoaW1ZN3YyY1VqRFBmcldHREc3bVZSVUtIczgrb0Y2OUF2Zm1WNy85RHJEWG1qYVo4cnhjbUUiLCJtYWMiOiI4NWQ0YmFiMjkyOWM0YmE0MDE5NGYyZTgyY2MxMDljMWJjOTY5NGFjZTUzODVmMjAyODg4NTczZDg4NzEzNDA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-121345612\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:34</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56423</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"28 characters\">/teammappings/104/updateRole</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"28 characters\">/teammappings/104/updateRole</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"38 characters\">/index.php/teammappings/104/updateRole</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">284</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">284</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarydTqsjZYGVJOojThA</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarydTqsjZYGVJOojThA</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/teammapping/Team%20888</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InpjdC9nM0wxKzhiQUlTVXpkZ1QwVkE9PSIsInZhbHVlIjoiU3lhYzhQZjErTkFOdnhYSTJhVXZISjFRWExMa3AwUy9nYkhKUUZrdytmKzlLMVc5OFdUWkcxVmVWdEFIZXJhQXhDdVMvbTdsUmxRUzFaU2MxVUlIQ1hEVDJDY1lCZVd6ZGl5djNYK0dKWTNHSXM4NHlSYjFBNldHOFFoc2FBbkIiLCJtYWMiOiJlNGE0NjJiNWUwMTk2MTY1YjY4NDBiZGM5OGFkZDc0NDBhMzkxOWMzZTM3YTc4MjQzNGM1MWFjZTRiMmQ5YWRhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ii8vUnBCakRjTG5YTWZuQ3hyQlc5Y1E9PSIsInZhbHVlIjoiNUJVR0pQMDdFaHptSlg4Rzd0VFQ2aGlrNGJQUjNBTWhQeUpmQkh3YmNzYmhKZElaWXBSWDlySU9NTlkxSWwxQXN2cFVRUjNhRWhoaW1ZN3YyY1VqRFBmcldHREc3bVZSVUtIczgrb0Y2OUF2Zm1WNy85RHJEWG1qYVo4cnhjbUUiLCJtYWMiOiI4NWQ0YmFiMjkyOWM0YmE0MDE5NGYyZTgyY2MxMDljMWJjOTY5NGFjZTUzODVmMjAyODg4NTczZDg4NzEzNDA1IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.6698</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-966834230 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lavecFvUe2D1z6a5lMQP4GMqE1oDiISndLrLo35L</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9bhqxpzfdR2feKfBboZDGnWOxgktb90Sl7Mqrqvc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-966834230\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1904686801 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:26:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Imc1SmtnanhLaDBZWklSRnJYUGxtMlE9PSIsInZhbHVlIjoiaGRmeXJUSUhpSVltNGY3M2RnM3ZJY0x0WWFWbjRSdnNGRUtUaEhHZjMvU3J4aXZJTVRxdjVmZzZPd2VwdDdkbGJ4aFRUVDFIcGtpRG05MzdjN2VWREkwNlRPbVpmYUxlZE9hclc5UytIWlRKNkxWWDYxMnFlM3Zpc1ZKTFVFNkMiLCJtYWMiOiI4N2I3NWZhMTY4ZmI1MWVkOTA2ODgzN2NiODcwZTNkZGM0ZDgxNjM4ZDAxYTZhNjY4NTY3YTAwOTQ2NzUyYmZkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:26:36 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InhIdnNBeC9SMjZFWGhsWFFkQ2RxakE9PSIsInZhbHVlIjoiMGQyY2RLWmNYc0pHUTJ3RStFTGdNUm8zSFJtUjZjZFVOT0I0MkZ6dElseTJHUGc3QXJZOU82NzdhWEZSWVhHVG1YL0FVQ3pRbENJRzRqTHJtaGkwLysxeDJrRmY2WUpveW0zU1BHTG1XUVNBc3VDSVpaM3V5UGd5THJxQVdObSsiLCJtYWMiOiIzY2VmNGRlMDMxNGY2ZjY3MjA3NWUzMTdhMTllOWE3YzYxODMwOTcwMzVmMzczNGY5NTRiY2YwMTg0YzUxN2M2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:26:36 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Imc1SmtnanhLaDBZWklSRnJYUGxtMlE9PSIsInZhbHVlIjoiaGRmeXJUSUhpSVltNGY3M2RnM3ZJY0x0WWFWbjRSdnNGRUtUaEhHZjMvU3J4aXZJTVRxdjVmZzZPd2VwdDdkbGJ4aFRUVDFIcGtpRG05MzdjN2VWREkwNlRPbVpmYUxlZE9hclc5UytIWlRKNkxWWDYxMnFlM3Zpc1ZKTFVFNkMiLCJtYWMiOiI4N2I3NWZhMTY4ZmI1MWVkOTA2ODgzN2NiODcwZTNkZGM0ZDgxNjM4ZDAxYTZhNjY4NTY3YTAwOTQ2NzUyYmZkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:26:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InhIdnNBeC9SMjZFWGhsWFFkQ2RxakE9PSIsInZhbHVlIjoiMGQyY2RLWmNYc0pHUTJ3RStFTGdNUm8zSFJtUjZjZFVOT0I0MkZ6dElseTJHUGc3QXJZOU82NzdhWEZSWVhHVG1YL0FVQ3pRbENJRzRqTHJtaGkwLysxeDJrRmY2WUpveW0zU1BHTG1XUVNBc3VDSVpaM3V5UGd5THJxQVdObSsiLCJtYWMiOiIzY2VmNGRlMDMxNGY2ZjY3MjA3NWUzMTdhMTllOWE3YzYxODMwOTcwMzVmMzczNGY5NTRiY2YwMTg0YzUxN2M2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:26:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1904686801\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-742335226 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lavecFvUe2D1z6a5lMQP4GMqE1oDiISndLrLo35L</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/teammapping/Team%20888</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-742335226\", {\"maxDepth\":0})</script>\n"}}