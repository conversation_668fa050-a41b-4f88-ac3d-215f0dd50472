{"__meta": {"id": "Xb51ed4e759ef1aca51045809e18c5204", "datetime": "2025-08-19 14:18:32", "utime": 1755584312.651859, "method": "GET", "uri": "/projects/45", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 45, "messages": [{"message": "[14:18:32] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584312.498541, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_kanban on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.593379, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.594804, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_burndown on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.599794, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.600898, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_backlog on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.601191, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.602208, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_userstory on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.602487, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.603478, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_forum on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.603752, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.604729, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_bugtracking on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.60504, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.605982, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_roles on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.606238, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.607064, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_status on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.607271, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.60802, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_kanban on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.608214, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.609032, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_details on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.609222, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.609936, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_kanban on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.610123, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.610849, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_burndown on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.611086, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.611793, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_backlog on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.612011, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.612811, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_userstory on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.613065, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.6138, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_forum on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.614026, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.614788, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_bugtracking on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.61501, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.615718, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_roles on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.615934, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.616662, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_status on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.616876, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.617571, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_kanban on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.61779, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.618478, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: view_details on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.618698, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.619387, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: edit_details on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.619623, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.620313, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: Gate check for permission: delete_details on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.620643, "xdebug_link": null, "collector": "log"}, {"message": "[14:18:32] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584312.621448, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584312.192786, "end": 1755584312.651918, "duration": 0.45913195610046387, "duration_str": "459ms", "measures": [{"label": "Booting", "start": 1755584312.192786, "relative_start": 0, "end": 1755584312.479059, "relative_end": 1755584312.479059, "duration": 0.2862730026245117, "duration_str": "286ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584312.479069, "relative_start": 0.286283016204834, "end": 1755584312.65192, "relative_end": 2.1457672119140625e-06, "duration": 0.1728510856628418, "duration_str": "173ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24107344, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 10, "templates": [{"name": "project.details (\\resources\\views\\project\\details.blade.php)", "param_count": 1, "params": ["project"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 5, "params": ["__env", "app", "menuData", "errors", "project"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 5, "params": ["__env", "app", "menuData", "errors", "project"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET projects/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\ProductFeatureController@details", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "projects.details", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProductFeatureController.php&line=133\">\\app\\Http\\Controllers\\ProductFeatureController.php:133-153</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00117, "accumulated_duration_str": "1.17ms", "statements": [{"sql": "select * from `users` where `id` = 31 limit 1", "type": "query", "params": [], "bindings": ["31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 40.171}, {"sql": "select `team_name` from `teammappings` where `username` = 'tay'", "type": "query", "params": [], "bindings": ["tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 140}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:140", "connection": "sagile", "start_percent": 40.171, "width_percent": 31.624}, {"sql": "select * from `projects` where `id` = '45' and `team_name` in ('Team 888', 'Team 888', 'Team AD', 'Team AD') limit 1", "type": "query", "params": [], "bindings": ["45", "Team 888", "Team 888", "Team AD", "Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 146}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:146", "connection": "sagile", "start_percent": 71.795, "width_percent": 28.205}]}, "models": {"data": {"App\\Project": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 22, "messages": [{"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.598757, "xdebug_link": null}, {"message": "[\n  ability => view_burndown,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-373849437 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_burndown</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-373849437\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.601026, "xdebug_link": null}, {"message": "[\n  ability => view_backlog,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1640900067 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1640900067\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.602328, "xdebug_link": null}, {"message": "[\n  ability => view_userstory,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1635174246 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1635174246\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.603597, "xdebug_link": null}, {"message": "[\n  ability => view_forum,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-701346262 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_forum</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-701346262\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.604859, "xdebug_link": null}, {"message": "[\n  ability => view_bugtracking,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-181373486 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_bugtracking</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-181373486\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.606094, "xdebug_link": null}, {"message": "[\n  ability => view_roles,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1786294581 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1786294581\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.607149, "xdebug_link": null}, {"message": "[\n  ability => view_status,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1775354553 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1775354553\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.608104, "xdebug_link": null}, {"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1769159337 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1769159337\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.609114, "xdebug_link": null}, {"message": "[\n  ability => view_details,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-405995777 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-405995777\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.610017, "xdebug_link": null}, {"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-898361357 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-898361357\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.610935, "xdebug_link": null}, {"message": "[\n  ability => view_burndown,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2008164646 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_burndown</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2008164646\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.611874, "xdebug_link": null}, {"message": "[\n  ability => view_backlog,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-545790453 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-545790453\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.612906, "xdebug_link": null}, {"message": "[\n  ability => view_userstory,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-593033930 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-593033930\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.613886, "xdebug_link": null}, {"message": "[\n  ability => view_forum,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-977826784 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_forum</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-977826784\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.614872, "xdebug_link": null}, {"message": "[\n  ability => view_bugtracking,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-750259586 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_bugtracking</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-750259586\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.6158, "xdebug_link": null}, {"message": "[\n  ability => view_roles,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1992258436 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992258436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.616743, "xdebug_link": null}, {"message": "[\n  ability => view_status,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-641304710 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-641304710\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.617655, "xdebug_link": null}, {"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1711319133 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1711319133\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.618562, "xdebug_link": null}, {"message": "[\n  ability => view_details,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-269383126 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-269383126\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.619467, "xdebug_link": null}, {"message": "[\n  ability => edit_details,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-711235697 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">edit_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-711235697\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.620396, "xdebug_link": null}, {"message": "[\n  ability => delete_details,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-263440094 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">delete_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-263440094\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584312.621538, "xdebug_link": null}]}, "session": {"_token": "p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/projects/45\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "31", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755584266\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/projects/45", "status_code": "<pre class=sf-dump id=sf-dump-1221853685 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1221853685\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1089037067 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1089037067\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-129522012 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-129522012\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1080549540 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Im9yWWVqdHF6MGsxQnlNTkxJTXZMaHc9PSIsInZhbHVlIjoiNHRQVjA0Q1NKbmFJR3ZEM21hWHZpT1VaR25mTFVESnpKUC9CYnFWWS9vczJRTHpvcWQzbGdlTzhWMlpVNFVWemY5K2FzeVpHckVhU2tiVVBhZ3k2aTA5NUhGZm1yS1Q2cGFBSTVORlhsS3ZqbktJbTJId1FsSkFGTGE3YmczbDMiLCJtYWMiOiJhNTZjMDUzOTViYTBkODQ0ZDJkOGQ5MDc5ZTc0NTkyZjI1MmQzNjI0ZmRkZDlmN2ZhY2RlZGI4ZWRhMjYxMTk0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImRDYUN5UmFnSjhJc0JIYVZCbnZDVlE9PSIsInZhbHVlIjoiUkpzbTlYbDY4enk4OEVOdjdkK0UxeHB2bUxVdE9ERWg5VVhBK0FLd0V4eFdBYzIvTjZlNUtNSE15QlRFSGd2d0phSkVkcWczZXJ0N2U5UVFLa2N6QWwybTROSk9tUy9XbTR2MW1HZ0Z3d0pWVzNsd2FQZW5oMXpSODBXWDU3dWoiLCJtYWMiOiIyMGRmMmJmMDZkMDE1OTM1NjQwYzVhNDc4ZjQxMjlkNWNmZWNmMGM4MjY5NjAxM2ZlZTk5NWZkMGJiNGQ4MDMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1080549540\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1978997917 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51910</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/projects/45</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/projects/45</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/index.php/projects/45</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Im9yWWVqdHF6MGsxQnlNTkxJTXZMaHc9PSIsInZhbHVlIjoiNHRQVjA0Q1NKbmFJR3ZEM21hWHZpT1VaR25mTFVESnpKUC9CYnFWWS9vczJRTHpvcWQzbGdlTzhWMlpVNFVWemY5K2FzeVpHckVhU2tiVVBhZ3k2aTA5NUhGZm1yS1Q2cGFBSTVORlhsS3ZqbktJbTJId1FsSkFGTGE3YmczbDMiLCJtYWMiOiJhNTZjMDUzOTViYTBkODQ0ZDJkOGQ5MDc5ZTc0NTkyZjI1MmQzNjI0ZmRkZDlmN2ZhY2RlZGI4ZWRhMjYxMTk0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImRDYUN5UmFnSjhJc0JIYVZCbnZDVlE9PSIsInZhbHVlIjoiUkpzbTlYbDY4enk4OEVOdjdkK0UxeHB2bUxVdE9ERWg5VVhBK0FLd0V4eFdBYzIvTjZlNUtNSE15QlRFSGd2d0phSkVkcWczZXJ0N2U5UVFLa2N6QWwybTROSk9tUy9XbTR2MW1HZ0Z3d0pWVzNsd2FQZW5oMXpSODBXWDU3dWoiLCJtYWMiOiIyMGRmMmJmMDZkMDE1OTM1NjQwYzVhNDc4ZjQxMjlkNWNmZWNmMGM4MjY5NjAxM2ZlZTk5NWZkMGJiNGQ4MDMzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584312.1928</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584312</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1978997917\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1262971049 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CfapZDSBofQwHxJsJhdmHULLiISKd6PUgVWjQqhr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1262971049\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1273760181 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:18:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImsxRHBwc0I2YytsUFd1UThwWndDVFE9PSIsInZhbHVlIjoibVRkRFZFYUxtUDd2eG5PUmd6N2E1SjdSNkpGbVVpaHVzQ0VGd0t5TFBPNkRGZmpJSTVNNkF5Tkl5a3ZmakxFVHZJd1ZmaG5MZ2xxN2FVWTF2MEE5aWM4L0lSV3B2WUwrVGVCektOT1ErOVVkeEZMdTVlVTF2cmgzdXRZdkZDeXQiLCJtYWMiOiI5YzQ0ZmU3NzgzNjI4M2UwZTEyZDA5ZTE3MWNmZmNlNDI3M2JjNjNhYTRhNGQ0ZGM2NGFkMGM4MjgzN2U0OWJiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:18:32 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjJjSzlXTENZdkUyelRHL1pQZVAvMGc9PSIsInZhbHVlIjoia2drMkZmK2xlUTdJMFlNN25JR0hVRXNNVjBnWXgyQ3lUa2NvQzY5elptREtNN0ZlLzJsUHdtOTFPZUNkdGxreXdHbjhwczlSV3JIVVRxN1hwUHhpV0JsYzd2eDF5V1JnN3cwTk9TamwrOXUyRTFOUE56Slo2L3V0UmJSYnBkZ0siLCJtYWMiOiI4YTE4OTIzMzE4ODdmZWIwYWU1ZmNkY2UzMzQ0YTU2MzhkYWMxMWRkYmQ2NjkzYWVkZmYzOGQ4MTJiY2VmNzlkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:18:32 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImsxRHBwc0I2YytsUFd1UThwWndDVFE9PSIsInZhbHVlIjoibVRkRFZFYUxtUDd2eG5PUmd6N2E1SjdSNkpGbVVpaHVzQ0VGd0t5TFBPNkRGZmpJSTVNNkF5Tkl5a3ZmakxFVHZJd1ZmaG5MZ2xxN2FVWTF2MEE5aWM4L0lSV3B2WUwrVGVCektOT1ErOVVkeEZMdTVlVTF2cmgzdXRZdkZDeXQiLCJtYWMiOiI5YzQ0ZmU3NzgzNjI4M2UwZTEyZDA5ZTE3MWNmZmNlNDI3M2JjNjNhYTRhNGQ0ZGM2NGFkMGM4MjgzN2U0OWJiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:18:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjJjSzlXTENZdkUyelRHL1pQZVAvMGc9PSIsInZhbHVlIjoia2drMkZmK2xlUTdJMFlNN25JR0hVRXNNVjBnWXgyQ3lUa2NvQzY5elptREtNN0ZlLzJsUHdtOTFPZUNkdGxreXdHbjhwczlSV3JIVVRxN1hwUHhpV0JsYzd2eDF5V1JnN3cwTk9TamwrOXUyRTFOUE56Slo2L3V0UmJSYnBkZ0siLCJtYWMiOiI4YTE4OTIzMzE4ODdmZWIwYWU1ZmNkY2UzMzQ0YTU2MzhkYWMxMWRkYmQ2NjkzYWVkZmYzOGQ4MTJiY2VmNzlkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:18:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273760181\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1060863619 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755584266</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1060863619\", {\"maxDepth\":0})</script>\n"}}