{"__meta": {"id": "Xd12a0aea3c54de28d8bbbab1d94ebb97", "datetime": "2025-08-19 10:35:23", "utime": 1755570923.167934, "method": "GET", "uri": "/userstory/42", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 41, "messages": [{"message": "[10:35:22] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755570922.712123, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:22] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/userstory/42", "message_html": null, "is_string": false, "label": "debug", "time": 1755570922.830897, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Gate check for permission: editStatus_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.136231, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.137263, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.137316, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Gate check for permission: view_task on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.14163, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.142478, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.14253, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Gate check for permission: edit_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.143406, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.144191, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.144243, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Gate check for permission: delete_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.144546, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.145359, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.145405, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Gate check for permission: editStatus_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.145724, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.147037, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.147132, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Gate check for permission: view_task on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.147562, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.148463, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.148512, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Gate check for permission: edit_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.148834, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.149668, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.149715, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Gate check for permission: delete_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.150008, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.150853, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.150898, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Gate check for permission: editStatus_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.151206, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.151962, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.152008, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Gate check for permission: view_task on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.152326, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.153187, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.153256, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Gate check for permission: edit_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.15357, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.154484, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.15453, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Gate check for permission: delete_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.154805, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.155569, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.155614, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Gate check for permission: add_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.15588, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.156636, "xdebug_link": null, "collector": "log"}, {"message": "[10:35:23] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570923.156681, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755570922.051957, "end": 1755570923.167996, "duration": 1.1160390377044678, "duration_str": "1.12s", "measures": [{"label": "Booting", "start": 1755570922.051957, "relative_start": 0, "end": 1755570922.669614, "relative_end": 1755570922.669614, "duration": 0.6176571846008301, "duration_str": "618ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755570922.669632, "relative_start": 0.6176750659942627, "end": 1755570923.167999, "relative_end": 3.0994415283203125e-06, "duration": 0.4983670711517334, "duration_str": "498ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23988992, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "userstory.index (\\resources\\views\\userstory\\index.blade.php)", "param_count": 3, "params": ["userstories", "project_id", "statuses"], "type": "blade"}]}, "route": {"uri": "GET userstory/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\UserStoryController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "userstory.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\UserStoryController.php&line=52\">\\app\\Http\\Controllers\\UserStoryController.php:52-69</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00277, "accumulated_duration_str": "2.77ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00061, "duration_str": "610μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 22.022}, {"sql": "select * from `projects` where `id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 55}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:55", "connection": "sagile", "start_percent": 22.022, "width_percent": 19.856}, {"sql": "select * from `user_stories` where `proj_id` = '42'", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 59}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:59", "connection": "sagile", "start_percent": 41.877, "width_percent": 19.495}, {"sql": "select * from `statuses` where `project_id` = '42'", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 62}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:62", "connection": "sagile", "start_percent": 61.372, "width_percent": 20.939}, {"sql": "select * from `projects` where `projects`.`id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "f1cf5c59d1c0fd8ae7e96081883a6947d41f1029", "line": 3}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 24, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "view::f1cf5c59d1c0fd8ae7e96081883a6947d41f1029:3", "connection": "sagile", "start_percent": 82.31, "width_percent": 17.69}]}, "models": {"data": {"App\\Status": 4, "App\\UserStory": 3, "App\\Project": 2, "App\\User": 1}, "count": 10}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 13, "messages": [{"message": "[\n  ability => editStatus_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-296027214 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">editStatus_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-296027214\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755570923.14066, "xdebug_link": null}, {"message": "[\n  ability => view_task,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-400536760 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">view_task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-400536760\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755570923.142668, "xdebug_link": null}, {"message": "[\n  ability => edit_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1446191626 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">edit_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1446191626\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755570923.144376, "xdebug_link": null}, {"message": "[\n  ability => delete_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-928713429 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-928713429\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755570923.145519, "xdebug_link": null}, {"message": "[\n  ability => editStatus_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1804281269 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">editStatus_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1804281269\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755570923.147302, "xdebug_link": null}, {"message": "[\n  ability => view_task,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-15469721 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">view_task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15469721\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755570923.148648, "xdebug_link": null}, {"message": "[\n  ability => edit_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-964357611 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">edit_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-964357611\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755570923.149842, "xdebug_link": null}, {"message": "[\n  ability => delete_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1875315642 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1875315642\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755570923.151013, "xdebug_link": null}, {"message": "[\n  ability => editStatus_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-525961563 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">editStatus_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-525961563\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755570923.152118, "xdebug_link": null}, {"message": "[\n  ability => view_task,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-917503442 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">view_task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-917503442\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755570923.153381, "xdebug_link": null}, {"message": "[\n  ability => edit_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1702273863 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">edit_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1702273863\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755570923.154647, "xdebug_link": null}, {"message": "[\n  ability => delete_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1305860281 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305860281\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755570923.155725, "xdebug_link": null}, {"message": "[\n  ability => add_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1078245515 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">add_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078245515\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755570923.156788, "xdebug_link": null}]}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/userstory/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755570218\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/userstory/42", "status_code": "<pre class=sf-dump id=sf-dump-484942520 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-484942520\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1615788427 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1615788427\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1308914229 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1308914229\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1157782248 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjV1YTdRdE9lb29rSGJwN3BwZzFoY1E9PSIsInZhbHVlIjoiUEhJRXB0Q1U0OGFTYTJpRUFTZXhnZlVFWWt5RkpkVXVTNDdTbkNIbHJleEFoeHRkUlRpTHE5TWt2a0YyaGhPVFdVbTJYbWZmclBOV3I5WkFYMXlnL21FSzVsWFVqdk5GaHhscXRuSVQ3TERmT1dRaXZEeldhSko1OER6M2N5V20iLCJtYWMiOiJmNTIxOWE4Nzc2NzFiZTBkNTYzZjk0ODY3ODFjODJlMDkyNDkxMTA4MDM0ZDJiZTM0NWIyNTIyNTA5NjEzYjJiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InZrVFRCcnAzZUN1R2l5eGVKM3J3TVE9PSIsInZhbHVlIjoiZFVGa1F5MG5oODJkU1V2VC8zM1A3SCsxcEhmN0dZTUllQ2doSFZ2dEJQcHloNmhXZVlSTmJPOUFGTjB1UXVNLzdUdnJobFFqSFl1b2lpaFF1SGpqVE13emF1VHRWYjlYRTZvTWVzQnpoSkJiN1h6eS9OeDFBMjJXNFRXU0ZFdWMiLCJtYWMiOiIxYTdlMTU2Njc3NmY0OGE0ZTEwNjJjZWFiMWU5NWM3ZmU0NTcyMTkxNGU4MTRhMWY1NmFhZDA0ZDI3MzcxYzkwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1157782248\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1185351120 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56347</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"13 characters\">/userstory/42</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"13 characters\">/userstory/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/index.php/userstory/42</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjV1YTdRdE9lb29rSGJwN3BwZzFoY1E9PSIsInZhbHVlIjoiUEhJRXB0Q1U0OGFTYTJpRUFTZXhnZlVFWWt5RkpkVXVTNDdTbkNIbHJleEFoeHRkUlRpTHE5TWt2a0YyaGhPVFdVbTJYbWZmclBOV3I5WkFYMXlnL21FSzVsWFVqdk5GaHhscXRuSVQ3TERmT1dRaXZEeldhSko1OER6M2N5V20iLCJtYWMiOiJmNTIxOWE4Nzc2NzFiZTBkNTYzZjk0ODY3ODFjODJlMDkyNDkxMTA4MDM0ZDJiZTM0NWIyNTIyNTA5NjEzYjJiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InZrVFRCcnAzZUN1R2l5eGVKM3J3TVE9PSIsInZhbHVlIjoiZFVGa1F5MG5oODJkU1V2VC8zM1A3SCsxcEhmN0dZTUllQ2doSFZ2dEJQcHloNmhXZVlSTmJPOUFGTjB1UXVNLzdUdnJobFFqSFl1b2lpaFF1SGpqVE13emF1VHRWYjlYRTZvTWVzQnpoSkJiN1h6eS9OeDFBMjJXNFRXU0ZFdWMiLCJtYWMiOiIxYTdlMTU2Njc3NmY0OGE0ZTEwNjJjZWFiMWU5NWM3ZmU0NTcyMTkxNGU4MTRhMWY1NmFhZDA0ZDI3MzcxYzkwIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755570922.052</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755570922</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1185351120\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1755981747 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1755981747\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1066115607 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:35:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InRTSytFSDRYajZSYzJYT1Y3N21IUUE9PSIsInZhbHVlIjoiS1VRek8wMGRxNmZYY081RHVkMFIvR3hQUkN4a21GdklHaFZ2YWk0SnMxRnB1R2s2ZWt6amM3eVF5UlR5SnE0T2hoeGZQbTVhNmdsN01YTXJvekNwSFVXcmxRcFdxWnQ0TGVsb2tPcFZFd3ExUVMwaDdJYmlMYktzV2Z6V3RmY1AiLCJtYWMiOiI2MmVmYTlmNmVjYTkwNzA2YzgyN2YzNTE4NjE2ZDhhNzUwOTY0ZDg0ZjBlYTdjYzdiNWUzMjc1MGY1NWE5MWY4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:35:23 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im1sN0J0TFhpTGpGcC94ei9nQytQYlE9PSIsInZhbHVlIjoiUE13a0ZibDZwcUs3UHJYdmVad3l4em85WnZjVjIzWWNOOXp4MHBGWUhUNXJHSjg5REIzTXJRNFBidXQvRXZCbTY5UzZpZkkvMW0vY3BUeEhuNm9jdTNYNStjTmszODVUU2YrYU40WkJMZDkvdU45UlIxMHJnb3BjbmYyR2pjcnciLCJtYWMiOiIyMjhlNmYwNDEwZWE1ODAzMTZiYWY4MWZiZjYyYmE4M2FjOWM4NzAyMDVlOGFkMmIwNjA4OGJiODg2YjBiZWM3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:35:23 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InRTSytFSDRYajZSYzJYT1Y3N21IUUE9PSIsInZhbHVlIjoiS1VRek8wMGRxNmZYY081RHVkMFIvR3hQUkN4a21GdklHaFZ2YWk0SnMxRnB1R2s2ZWt6amM3eVF5UlR5SnE0T2hoeGZQbTVhNmdsN01YTXJvekNwSFVXcmxRcFdxWnQ0TGVsb2tPcFZFd3ExUVMwaDdJYmlMYktzV2Z6V3RmY1AiLCJtYWMiOiI2MmVmYTlmNmVjYTkwNzA2YzgyN2YzNTE4NjE2ZDhhNzUwOTY0ZDg0ZjBlYTdjYzdiNWUzMjc1MGY1NWE5MWY4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:35:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im1sN0J0TFhpTGpGcC94ei9nQytQYlE9PSIsInZhbHVlIjoiUE13a0ZibDZwcUs3UHJYdmVad3l4em85WnZjVjIzWWNOOXp4MHBGWUhUNXJHSjg5REIzTXJRNFBidXQvRXZCbTY5UzZpZkkvMW0vY3BUeEhuNm9jdTNYNStjTmszODVUU2YrYU40WkJMZDkvdU45UlIxMHJnb3BjbmYyR2pjcnciLCJtYWMiOiIyMjhlNmYwNDEwZWE1ODAzMTZiYWY4MWZiZjYyYmE4M2FjOWM4NzAyMDVlOGFkMmIwNjA4OGJiODg2YjBiZWM3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:35:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1066115607\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1005251881 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/userstory/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755570218</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1005251881\", {\"maxDepth\":0})</script>\n"}}