<style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #F4F5FA;
            line-height: 1.6;
            overflow: hidden; /* Remove body scrollbar */
        }

        .kanban-container {
            width: 100%;
            max-width: 100vw;
            padding: 20px;
            height: calc(100vh - 40px); /* Account for padding */
        }

        .kanban-header {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .kanban-title {
            font-size: 1.75rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .kanban-actions {
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .overdue-alert {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .no-sprint-container {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .no-sprint-container h3 {
            font-size: 1.5rem;
            color: #374151;
            margin-bottom: 12px;
        }

        .no-sprint-container p {
            color: #6b7280;
            font-size: 1rem;
        }

        .kanban-board {
            display: flex;
            gap: 20px;
            height: calc(100vh - 180px); /* Account for header and padding */
            overflow-x: auto;
            overflow-y: hidden; /* Hide vertical scrollbar */
            padding-bottom: 20px;
        }

        .swim-lane {
            background: #f8fafc;
            border-radius: 12px;
            min-width: 320px;
            max-width: 320px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
            height: 100%;
        }

        .lane-header {
            background: white;
            padding: 16px 20px;
            border-radius: 12px 12px 0 0;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .lane-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1f2937;
            flex: 1;
        }

        .lane-actions {
            display: flex;
            gap: 8px;
        }

        .btn-icon,
        .edit-task-btn,
        .delete-task-btn {
            padding: 8px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: transparent;
            color: #6b7280;
        }

        .btn-icon:hover,
        .edit-task-btn:hover,
        .delete-task-btn:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .lane-content {
            padding: 16px;
            flex: 1;
            overflow-y: auto; /* Allow scrolling within lanes */
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
            gap: 12px;
            min-height: 200px;
        }

        .add-task-form {
            margin-bottom: 16px;
        }

        .task-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
            cursor: pointer;
            transition: box-shadow 0.2s ease, transform 0.2s ease;
            position: relative;
            opacity: 1;
        }

        .task-card:hover:not(.is-dragging) {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .task-card.is-dragging {
            opacity: 0.60 !important;
            transition: none !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            transform: none !important;
        }

        .task-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
            font-size: 0.95rem;
        }

        .task-meta {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 12px;
        }

        .filter-section {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 12px;
        }

        .filter-row {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-label {
            font-size: 0.8rem;
            color: #6b7280;
            font-weight: 500;
            min-width: 80px;
        }

        .form-select {
            flex: 1;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 0.8rem;
            background: white;
        }

        .task-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
        }

        .comments-section {
            border-top: 1px solid #e5e7eb;
            padding-top: 12px;
        }

        .comment-item {
            background: #f9fafb;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 8px;
            border-left: 3px solid #e5e7eb;
        }

        .comment-item.hidden-comment {
            display: none;
        }

        .comment-content {
            font-size: 0.85rem;
            color: #374151;
            margin-bottom: 6px;
        }

        .comment-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.75rem;
            color: #6b7280;
        }

        .comment-actions {
            display: flex;
            gap: 4px;
        }

        .comment-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .edited-indicator {
            color: #9ca3af;
            font-size: 0.7rem;
            font-style: italic;
        }

        .overdue-task {
            border-left: 4px solid #ef4444;
        }

        .overdue-task .overdue-badge {
            background: #fef2f2;
            color: #dc2626;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .show-toggle-btn {
            background: none;
            border: none;
            color: #3b82f6;
            font-size: 0.8rem;
            cursor: pointer;
            padding: 4px 0;
            text-decoration: underline;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 24px;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }

        .close-modal {
            color: #9ca3af;
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .close-modal:hover {
            color: #374151;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.95rem;
            transition: border-color 0.2s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Drag and Drop Styles */
        .swim-lane.drag-over {
            background: #e0f2fe;
            border: 2px dashed #0284c7;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .kanban-container {
                padding: 10px;
            }
            
            .kanban-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .kanban-actions {
                justify-content: center;
            }
            
            .kanban-board {
                flex-direction: column;
                gap: 16px;
            }
            
            .swim-lane {
                min-width: 100%;
                max-width: 100%;
            }
        }

        /* Hidden elements */
        #save-btn {
            display: none;
        }

        /* Animation for smooth transitions */
        .task-card {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes  slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/inc/kanbanStyle.blade.php ENDPATH**/ ?>