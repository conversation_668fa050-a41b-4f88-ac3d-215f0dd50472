{"__meta": {"id": "Xbbd734b25dd2bd276c31d21d3d1a3611", "datetime": "2025-08-19 13:55:00", "utime": 1755582900.74847, "method": "POST", "uri": "/sprint", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[13:55:00] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755582900.548211, "xdebug_link": null, "collector": "log"}, {"message": "[13:55:00] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/sprint", "message_html": null, "is_string": false, "label": "debug", "time": 1755582900.606914, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755582900.225076, "end": 1755582900.748499, "duration": 0.5234229564666748, "duration_str": "523ms", "measures": [{"label": "Booting", "start": 1755582900.225076, "relative_start": 0, "end": 1755582900.529208, "relative_end": 1755582900.529208, "duration": 0.30413198471069336, "duration_str": "304ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755582900.52922, "relative_start": 0.30414414405822754, "end": 1755582900.748502, "relative_end": 3.0994415283203125e-06, "duration": 0.21928191184997559, "duration_str": "219ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24396408, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST sprint", "middleware": "web", "controller": "App\\Http\\Controllers\\SprintController@store", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "sprints.store", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\SprintController.php&line=91\">\\app\\Http\\Controllers\\SprintController.php:91-182</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.01159, "accumulated_duration_str": "11.59ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 3.883}, {"sql": "select * from `projects` where `proj_name` = 'Food Ordering System' limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 94}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:94", "connection": "sagile", "start_percent": 3.883, "width_percent": 3.365}, {"sql": "select count(*) as aggregate from `sprint` where `sprint_name` = 'Sprint 2' and `proj_name` = 'Food Ordering System'", "type": "query", "params": [], "bindings": ["Sprint 2", "Food Ordering System"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 855}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 447}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "sagile", "start_percent": 7.248, "width_percent": 3.624}, {"sql": "update `sprint` set `active_sprint` = 0, `sprint`.`updated_at` = '2025-08-19 13:55:00' where `proj_name` = 'Food Ordering System' and `active_sprint` = 1", "type": "query", "params": [], "bindings": ["0", "2025-08-19 13:55:00", "Food Ordering System", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 147}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:147", "connection": "sagile", "start_percent": 10.871, "width_percent": 3.279}, {"sql": "insert into `sprint` (`sprint_name`, `proj_name`, `sprint_desc`, `start_sprint`, `end_sprint`, `active_sprint`, `users_name`, `updated_at`, `created_at`) values ('Sprint 2', 'Food Ordering System', '2', '2025-08-19', '2025-09-02', 1, 'ivlyn', '2025-08-19 13:55:00', '2025-08-19 13:55:00')", "type": "query", "params": [], "bindings": ["Sprint 2", "Food Ordering System", "2", "2025-08-19", "2025-09-02", "1", "ivlyn", "2025-08-19 13:55:00", "2025-08-19 13:55:00"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 156}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0070599999999999994, "duration_str": "7.06ms", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:156", "connection": "sagile", "start_percent": 14.15, "width_percent": 60.915}, {"sql": "select * from `sprint` where `sprint_id` = 46 limit 1", "type": "query", "params": [], "bindings": ["46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\UserStory.php", "line": 111}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 164}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\UserStory.php:111", "connection": "sagile", "start_percent": 75.065, "width_percent": 3.192}, {"sql": "update `user_stories` set `sprint_id` = 46, `user_stories`.`updated_at` = '2025-08-19 13:55:00' where `u_id` in ('58')", "type": "query", "params": [], "bindings": ["46", "2025-08-19 13:55:00", "58"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\UserStory.php", "line": 117}, {"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 164}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0010500000000000002, "duration_str": "1.05ms", "stmt_id": "\\app\\UserStory.php:117", "connection": "sagile", "start_percent": 78.257, "width_percent": 9.06}, {"sql": "update `tasks` set `sprint_id` = 46, `tasks`.`updated_at` = '2025-08-19 13:55:00' where `id` in ('120')", "type": "query", "params": [], "bindings": ["46", "2025-08-19 13:55:00", "120"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 170}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:170", "connection": "sagile", "start_percent": 87.317, "width_percent": 8.887}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System'", "type": "query", "params": [], "bindings": ["Food Ordering System"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintController.php", "line": 173}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Http\\Controllers\\SprintController.php:173", "connection": "sagile", "start_percent": 96.204, "width_percent": 3.796}]}, "models": {"data": {"App\\Sprint": 3, "App\\Project": 1, "App\\User": 1}, "count": 5}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backlogTest/43\"\n]", "_flash": "array:2 [\n  \"old\" => array:4 [\n    0 => \"title\"\n    1 => \"success\"\n    2 => \"sprints\"\n    3 => \"projects\"\n  ]\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "title": "Sprints for Food Ordering System", "success": "Sprint has successfully been created!", "sprints": "Illuminate\\Database\\Eloquent\\Collection {#1819\n  #items: array:2 [\n    0 => App\\Sprint {#1818\n      #connection: \"mysql\"\n      #table: \"sprint\"\n      +primaryKey: \"sprint_id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:10 [\n        \"sprint_id\" => 45\n        \"sprint_name\" => \"Sprint 1\"\n        \"sprint_desc\" => \"this is sprint 1\"\n        \"start_sprint\" => \"2025-08-19\"\n        \"end_sprint\" => \"2025-09-02\"\n        \"active_sprint\" => 2\n        \"proj_name\" => \"Food Ordering System\"\n        \"users_name\" => \"ivlyn\"\n        \"created_at\" => \"2025-08-19 13:39:22\"\n        \"updated_at\" => \"2025-08-19 13:49:04\"\n      ]\n      #original: array:10 [\n        \"sprint_id\" => 45\n        \"sprint_name\" => \"Sprint 1\"\n        \"sprint_desc\" => \"this is sprint 1\"\n        \"start_sprint\" => \"2025-08-19\"\n        \"end_sprint\" => \"2025-09-02\"\n        \"active_sprint\" => 2\n        \"proj_name\" => \"Food Ordering System\"\n        \"users_name\" => \"ivlyn\"\n        \"created_at\" => \"2025-08-19 13:39:22\"\n        \"updated_at\" => \"2025-08-19 13:49:04\"\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:6 [\n        0 => \"sprint_name\"\n        1 => \"proj_name\"\n        2 => \"sprint_desc\"\n        3 => \"start_sprint\"\n        4 => \"end_sprint\"\n        5 => \"users_name\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    1 => App\\Sprint {#1817\n      #connection: \"mysql\"\n      #table: \"sprint\"\n      +primaryKey: \"sprint_id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:10 [\n        \"sprint_id\" => 46\n        \"sprint_name\" => \"Sprint 2\"\n        \"sprint_desc\" => \"2\"\n        \"start_sprint\" => \"2025-08-19\"\n        \"end_sprint\" => \"2025-09-02\"\n        \"active_sprint\" => 1\n        \"proj_name\" => \"Food Ordering System\"\n        \"users_name\" => \"ivlyn\"\n        \"created_at\" => \"2025-08-19 13:55:00\"\n        \"updated_at\" => \"2025-08-19 13:55:00\"\n      ]\n      #original: array:10 [\n        \"sprint_id\" => 46\n        \"sprint_name\" => \"Sprint 2\"\n        \"sprint_desc\" => \"2\"\n        \"start_sprint\" => \"2025-08-19\"\n        \"end_sprint\" => \"2025-09-02\"\n        \"active_sprint\" => 1\n        \"proj_name\" => \"Food Ordering System\"\n        \"users_name\" => \"ivlyn\"\n        \"created_at\" => \"2025-08-19 13:55:00\"\n        \"updated_at\" => \"2025-08-19 13:55:00\"\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:6 [\n        0 => \"sprint_name\"\n        1 => \"proj_name\"\n        2 => \"sprint_desc\"\n        3 => \"start_sprint\"\n        4 => \"end_sprint\"\n        5 => \"users_name\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n  ]\n  #escapeWhenCastingToString: false\n}", "projects": "App\\Project {#1816\n  #connection: \"mysql\"\n  #table: \"projects\"\n  #primaryKey: \"id\"\n  #keyType: \"int\"\n  +incrementing: true\n  #with: []\n  #withCount: []\n  +preventsLazyLoading: false\n  #perPage: 15\n  +exists: true\n  +wasRecentlyCreated: false\n  #escapeWhenCastingToString: false\n  #attributes: array:9 [\n    \"id\" => 43\n    \"team_name\" => \"Team 888\"\n    \"proj_name\" => \"Food Ordering System\"\n    \"proj_desc\" => \"this is a web-based food oerdering system\"\n    \"start_date\" => \"2025-08-19\"\n    \"end_date\" => \"2025-12-31\"\n    \"shareable_slug\" => null\n    \"created_at\" => \"2025-08-19 13:31:41\"\n    \"updated_at\" => \"2025-08-19 13:31:41\"\n  ]\n  #original: array:9 [\n    \"id\" => 43\n    \"team_name\" => \"Team 888\"\n    \"proj_name\" => \"Food Ordering System\"\n    \"proj_desc\" => \"this is a web-based food oerdering system\"\n    \"start_date\" => \"2025-08-19\"\n    \"end_date\" => \"2025-12-31\"\n    \"shareable_slug\" => null\n    \"created_at\" => \"2025-08-19 13:31:41\"\n    \"updated_at\" => \"2025-08-19 13:31:41\"\n  ]\n  #changes: []\n  #casts: []\n  #classCastCache: []\n  #attributeCastCache: []\n  #dates: []\n  #dateFormat: null\n  #appends: []\n  #dispatchesEvents: []\n  #observables: []\n  #relations: []\n  #touches: []\n  +timestamps: true\n  #hidden: []\n  #visible: []\n  #fillable: array:7 [\n    0 => \"user_id\"\n    1 => \"team_name\"\n    2 => \"proj_name\"\n    3 => \"proj_desc\"\n    4 => \"start_date\"\n    5 => \"end_date\"\n    6 => \"shareable_slug\"\n  ]\n  #guarded: array:1 [\n    0 => \"*\"\n  ]\n}", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/sprint", "status_code": "<pre class=sf-dump id=sf-dump-1705450185 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1705450185\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2118264721 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2118264721\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1871079862 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n  \"<span class=sf-dump-key>selected_user_stories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>selected_tasks</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">120</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>start_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-19</span>\"\n  \"<span class=sf-dump-key>end_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-02</span>\"\n  \"<span class=sf-dump-key>sprint_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Sprint 2</span>\"\n  \"<span class=sf-dump-key>sprint_desc</span>\" => \"<span class=sf-dump-str>2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1871079862\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1696842821 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">215</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IklTaTdqMEdEN2g4elFoSkdGMEZJd3c9PSIsInZhbHVlIjoiNENsZUYxbXo0MzI0UEtDOGZKMUhyT3gyMG90UHVNYWhCZWFEZGZyamJDMytYUDRsSytZdEFhSGpzcFVUYklGajl2bFBSSmZ5UEVacmE3S1lWczFISG5ObXBiTmRWcHVwdFVWamV2SEZ5T2pBSGVPMlY5eVB4WHppQ3NFNCtEVlkiLCJtYWMiOiI3YmI4NWY2ZTRkNWQ0YTlhYWMxZjAyYjM4ZTJiMTAyZWMyZDA3ODM3YjZlZjBkNmZjMzA3NWIyNTRiMjQ4N2IzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlNqcXhJeUxJZUJ4Y1NaUzJXQ09pNGc9PSIsInZhbHVlIjoiMlFjUjFWUXkwaGtGeEFIRnFETHdKLzl2Y01Ka3JNcEQ4c3E2d0prMWVVY2ZrQ3N0T0d5OHJEbVc1REtqQ1g3enN6UHFZSkNiQ0tDNFFpbkZQaUxEbVpuU1VyYS9RZVV4SmtmYzQ5dDVzak1vOXBmQ1NZcGxReVRqTXVYWWU4NHAiLCJtYWMiOiJlOTMyY2QwYWQxZDVjNDAzY2RlNTk4YzczMTEyZTFlYjdlMjIwMmUyMjQ5ZmY3MjQzNTQ3ODUwMmUyZmY0NTAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1696842821\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1852374498 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57282</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"7 characters\">/sprint</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"7 characters\">/sprint</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/index.php/sprint</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">215</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">215</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/43</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IklTaTdqMEdEN2g4elFoSkdGMEZJd3c9PSIsInZhbHVlIjoiNENsZUYxbXo0MzI0UEtDOGZKMUhyT3gyMG90UHVNYWhCZWFEZGZyamJDMytYUDRsSytZdEFhSGpzcFVUYklGajl2bFBSSmZ5UEVacmE3S1lWczFISG5ObXBiTmRWcHVwdFVWamV2SEZ5T2pBSGVPMlY5eVB4WHppQ3NFNCtEVlkiLCJtYWMiOiI3YmI4NWY2ZTRkNWQ0YTlhYWMxZjAyYjM4ZTJiMTAyZWMyZDA3ODM3YjZlZjBkNmZjMzA3NWIyNTRiMjQ4N2IzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlNqcXhJeUxJZUJ4Y1NaUzJXQ09pNGc9PSIsInZhbHVlIjoiMlFjUjFWUXkwaGtGeEFIRnFETHdKLzl2Y01Ka3JNcEQ4c3E2d0prMWVVY2ZrQ3N0T0d5OHJEbVc1REtqQ1g3enN6UHFZSkNiQ0tDNFFpbkZQaUxEbVpuU1VyYS9RZVV4SmtmYzQ5dDVzak1vOXBmQ1NZcGxReVRqTXVYWWU4NHAiLCJtYWMiOiJlOTMyY2QwYWQxZDVjNDAzY2RlNTk4YzczMTEyZTFlYjdlMjIwMmUyMjQ5ZmY3MjQzNTQ3ODUwMmUyZmY0NTAwIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755582900.2251</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755582900</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1852374498\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1896769392 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1896769392\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-27079189 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:55:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IloxN3lMb1lkajA0UkVvRHNHVVMwYlE9PSIsInZhbHVlIjoiS1JybW9HbW5mUE9NRExhQUlwYlZqdTIyWjhndTdIM0locEx0RGdzV3UvTWxhTkFiYzdkd0F6azdiV1MzYnJmaDVaYjdhQW9YeEVCV25NeTVJa09wSTU3dFRlcDZiUzJodDFGN2pvWWJvc1R3OVJHdGZnQkVLV1poSnBucEtyMEEiLCJtYWMiOiIwNDY1N2NiNDA2ZTVkM2Y0NmM3Yzk5ZWYwZGMyMmU0YWRkMTE2NTY3ZjljZTIyN2YxZTBjNGFmMmE0NmUzNGIzIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:55:00 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjJQZDZsL1dOMFVrcGNIR0lnOEV5amc9PSIsInZhbHVlIjoiak56OEFmWmlSRStFNDdzdlUwUWxRRi9xZkFXSURTUm1qSnVNclZYMmd6Qk5TOWc2L3lUMFk2QUtxZlNIV3FSeGhDTmZ0QWQyY1Y3bDlhS2xVZklTWVU1UDhHbUhrYjQyWUVxdlM0MmxCSlJMQXRWcDBqWkV2WEY3WGYyUkYzcnQiLCJtYWMiOiI2OTExMTU1NzhmMjg5NWYyNDFkYjE3NTgyMGRiOGQxNmY2YTA3MDYwNDE5MTRmODNjMTAwOGEwMjE4Y2YyZTE0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:55:00 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IloxN3lMb1lkajA0UkVvRHNHVVMwYlE9PSIsInZhbHVlIjoiS1JybW9HbW5mUE9NRExhQUlwYlZqdTIyWjhndTdIM0locEx0RGdzV3UvTWxhTkFiYzdkd0F6azdiV1MzYnJmaDVaYjdhQW9YeEVCV25NeTVJa09wSTU3dFRlcDZiUzJodDFGN2pvWWJvc1R3OVJHdGZnQkVLV1poSnBucEtyMEEiLCJtYWMiOiIwNDY1N2NiNDA2ZTVkM2Y0NmM3Yzk5ZWYwZGMyMmU0YWRkMTE2NTY3ZjljZTIyN2YxZTBjNGFmMmE0NmUzNGIzIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:55:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjJQZDZsL1dOMFVrcGNIR0lnOEV5amc9PSIsInZhbHVlIjoiak56OEFmWmlSRStFNDdzdlUwUWxRRi9xZkFXSURTUm1qSnVNclZYMmd6Qk5TOWc2L3lUMFk2QUtxZlNIV3FSeGhDTmZ0QWQyY1Y3bDlhS2xVZklTWVU1UDhHbUhrYjQyWUVxdlM0MmxCSlJMQXRWcDBqWkV2WEY3WGYyUkYzcnQiLCJtYWMiOiI2OTExMTU1NzhmMjg5NWYyNDFkYjE3NTgyMGRiOGQxNmY2YTA3MDYwNDE5MTRmODNjMTAwOGEwMjE4Y2YyZTE0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:55:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-27079189\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">title</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">sprints</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">projects</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Sprints for Food Ordering System</span>\"\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Sprint has successfully been created!</span>\"\n  \"<span class=sf-dump-key>sprints</span>\" => <span class=sf-dump-note title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#1819</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"App\\Sprint\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Sprint</span> {<a class=sf-dump-ref>#1818</a><samp data-depth=4 class=sf-dump-compact>\n        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">sprint</span>\"\n        +<span class=sf-dump-public title=\"Public property\">primaryKey</span>: \"<span class=sf-dump-str title=\"9 characters\">sprint_id</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>sprint_id</span>\" => <span class=sf-dump-num>45</span>\n          \"<span class=sf-dump-key>sprint_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Sprint 1</span>\"\n          \"<span class=sf-dump-key>sprint_desc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">this is sprint 1</span>\"\n          \"<span class=sf-dump-key>start_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-19</span>\"\n          \"<span class=sf-dump-key>end_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-02</span>\"\n          \"<span class=sf-dump-key>active_sprint</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n          \"<span class=sf-dump-key>users_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">ivlyn</span>\"\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 13:39:22</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 13:49:04</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>sprint_id</span>\" => <span class=sf-dump-num>45</span>\n          \"<span class=sf-dump-key>sprint_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Sprint 1</span>\"\n          \"<span class=sf-dump-key>sprint_desc</span>\" => \"<span class=sf-dump-str title=\"16 characters\">this is sprint 1</span>\"\n          \"<span class=sf-dump-key>start_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-19</span>\"\n          \"<span class=sf-dump-key>end_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-02</span>\"\n          \"<span class=sf-dump-key>active_sprint</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n          \"<span class=sf-dump-key>users_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">ivlyn</span>\"\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 13:39:22</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 13:49:04</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">sprint_name</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">proj_name</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">sprint_desc</span>\"\n          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"12 characters\">start_sprint</span>\"\n          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">end_sprint</span>\"\n          <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">users_name</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n        </samp>]\n      </samp>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"App\\Sprint\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Sprint</span> {<a class=sf-dump-ref>#1817</a><samp data-depth=4 class=sf-dump-compact>\n        #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">sprint</span>\"\n        +<span class=sf-dump-public title=\"Public property\">primaryKey</span>: \"<span class=sf-dump-str title=\"9 characters\">sprint_id</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n        +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n        +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n        +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n        +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>sprint_id</span>\" => <span class=sf-dump-num>46</span>\n          \"<span class=sf-dump-key>sprint_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Sprint 2</span>\"\n          \"<span class=sf-dump-key>sprint_desc</span>\" => \"<span class=sf-dump-str>2</span>\"\n          \"<span class=sf-dump-key>start_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-19</span>\"\n          \"<span class=sf-dump-key>end_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-02</span>\"\n          \"<span class=sf-dump-key>active_sprint</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n          \"<span class=sf-dump-key>users_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">ivlyn</span>\"\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 13:55:00</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 13:55:00</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>sprint_id</span>\" => <span class=sf-dump-num>46</span>\n          \"<span class=sf-dump-key>sprint_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Sprint 2</span>\"\n          \"<span class=sf-dump-key>sprint_desc</span>\" => \"<span class=sf-dump-str>2</span>\"\n          \"<span class=sf-dump-key>start_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-19</span>\"\n          \"<span class=sf-dump-key>end_sprint</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-02</span>\"\n          \"<span class=sf-dump-key>active_sprint</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n          \"<span class=sf-dump-key>users_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">ivlyn</span>\"\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 13:55:00</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 13:55:00</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n        +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n        #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">sprint_name</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">proj_name</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">sprint_desc</span>\"\n          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"12 characters\">start_sprint</span>\"\n          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">end_sprint</span>\"\n          <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">users_name</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n        </samp>]\n      </samp>}\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n  </samp>}\n  \"<span class=sf-dump-key>projects</span>\" => <span class=sf-dump-note title=\"App\\Project\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Project</span> {<a class=sf-dump-ref>#1816</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">projects</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>43</span>\n      \"<span class=sf-dump-key>team_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Team 888</span>\"\n      \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n      \"<span class=sf-dump-key>proj_desc</span>\" => \"<span class=sf-dump-str title=\"41 characters\">this is a web-based food oerdering system</span>\"\n      \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-19</span>\"\n      \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-12-31</span>\"\n      \"<span class=sf-dump-key>shareable_slug</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 13:31:41</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 13:31:41</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>43</span>\n      \"<span class=sf-dump-key>team_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Team 888</span>\"\n      \"<span class=sf-dump-key>proj_name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Food Ordering System</span>\"\n      \"<span class=sf-dump-key>proj_desc</span>\" => \"<span class=sf-dump-str title=\"41 characters\">this is a web-based food oerdering system</span>\"\n      \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-19</span>\"\n      \"<span class=sf-dump-key>end_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-12-31</span>\"\n      \"<span class=sf-dump-key>shareable_slug</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 13:31:41</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-19 13:31:41</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">team_name</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"9 characters\">proj_name</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">proj_desc</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">start_date</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">end_date</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">shareable_slug</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}