{"__meta": {"id": "X950a90658c14d5b961fc4abbde09a991", "datetime": "2025-08-19 13:40:29", "utime": 1755582029.864171, "method": "GET", "uri": "/task/119/edit", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[13:40:29] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755582029.520928, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:29] LOG.debug: LoadUserPermissions: Extracted project ID: 119 from URL: http://127.0.0.1:8000/task/119/edit", "message_html": null, "is_string": false, "label": "debug", "time": 1755582029.582469, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:29] LOG.debug: LoadUserPermissions: No project found for ID: 119", "message_html": null, "is_string": false, "label": "debug", "time": 1755582029.595246, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755582029.239879, "end": 1755582029.864193, "duration": 0.6243140697479248, "duration_str": "624ms", "measures": [{"label": "Booting", "start": 1755582029.239879, "relative_start": 0, "end": 1755582029.500946, "relative_end": 1755582029.500946, "duration": 0.26106715202331543, "duration_str": "261ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755582029.50096, "relative_start": 0.2610812187194824, "end": 1755582029.864195, "relative_end": 2.1457672119140625e-06, "duration": 0.3632349967956543, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24300008, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "tasks.edit (\\resources\\views\\tasks\\edit.blade.php)", "param_count": 6, "params": ["title", "project", "sprint", "statuses", "teamlist", "task"], "type": "blade"}, {"name": "inc.character-counter (\\resources\\views\\inc\\character-counter.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "title", "project", "sprint", "statuses", "teamlist", "task", "inputId", "counterId", "max<PERSON><PERSON><PERSON>"], "type": "blade"}, {"name": "inc.character-counter (\\resources\\views\\inc\\character-counter.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "title", "project", "sprint", "statuses", "teamlist", "task", "inputId", "counterId", "max<PERSON><PERSON><PERSON>"], "type": "blade"}]}, "route": {"uri": "GET task/{id}/edit", "middleware": "web", "controller": "App\\Http\\Controllers\\TaskController@edit", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "tasks.edit", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TaskController.php&line=659\">\\app\\Http\\Controllers\\TaskController.php:659-686</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.0036100000000000004, "accumulated_duration_str": "3.61ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 14.127}, {"sql": "select `id`, `team_name` from `projects` where `id` = '119' limit 1", "type": "query", "params": [], "bindings": ["119"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 167}, {"index": 14, "namespace": "middleware", "name": "load.permissions", "line": 33}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 16, "namespace": "middleware", "name": "bindings", "line": 50}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00041999999*********, "duration_str": "420μs", "stmt_id": "middleware::load.permissions:167", "connection": "sagile", "start_percent": 14.127, "width_percent": 11.634}, {"sql": "select * from `tasks` where `id` = '119' limit 1", "type": "query", "params": [], "bindings": ["119"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 662}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:662", "connection": "sagile", "start_percent": 25.762, "width_percent": 12.742}, {"sql": "select * from `user_stories` where `u_id` = 57 limit 1", "type": "query", "params": [], "bindings": ["57"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 663}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:663", "connection": "sagile", "start_percent": 38.504, "width_percent": 11.911}, {"sql": "select * from `sprint` where `sprint_id` = 45 limit 1", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 664}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:664", "connection": "sagile", "start_percent": 50.416, "width_percent": 12.188}, {"sql": "select * from `projects` where `id` = 43 limit 1", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 665}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:665", "connection": "sagile", "start_percent": 62.604, "width_percent": 11.357}, {"sql": "select `username` from `teammappings` where `project_id` = 43", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 670}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041999999*********, "duration_str": "420μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:670", "connection": "sagile", "start_percent": 73.961, "width_percent": 11.634}, {"sql": "select * from `statuses` where `project_id` = 43", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 677}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:677", "connection": "sagile", "start_percent": 85.596, "width_percent": 14.404}]}, "models": {"data": {"App\\Status": 4, "App\\TeamMapping": 2, "App\\Project": 1, "App\\Sprint": 1, "App\\UserStory": 1, "App\\Task": 1, "App\\User": 1}, "count": 11}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/task/119/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/task/119/edit", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2067499553 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2067499553\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1884905744 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1884905744\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/task/57</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImtJdklQNlFFV2NTbGlUaDUxM3JVa0E9PSIsInZhbHVlIjoiSnlZRVNiUG5aSlAxZ01ITUk4Zkh1T09GcDR4ZjJIV0FNRTJWaUsvbnlNblYyWmVkdjJvdENYNVRwdDYwTjI0OEt4Q251RGRoOEhSZGMwbkhKYkFCYzFtaGRTTXMwWnRCdFYrRkQxMmpsbmpSVk5kc0RxQUN3bzJmQTc4alNMK1UiLCJtYWMiOiJlZWE2N2IxNTNiNjIxNjg0NzAxMTM2NDA0ZGFhYmFmOTgzNWEyMGVmZjIzYmZkMDZlZDg4OTBlNWYzODkzMzM5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkNEVUZRcFg0bmUvYzR1WUp1RnQ4K2c9PSIsInZhbHVlIjoiZjFPZ25PbWZlSXNXODk3NmJPVWVJeTFYSjhFS2ZlWkVaRWx0L2ZrOGV0WWNhRG55MVZQb0hhRnpsKzFpenFYUHhacDVjOTJHUHI0cXYwek9ZQUNOdGVYVjBjNXBwSHRVYmlJMFpNZ0xzamcvOVJOOW5vU2VlOFRhcFZYc2hsa2MiLCJtYWMiOiJmNDUwNmM1YjU2Yjk5MTY2N2ExYzY5MDMyN2EyYzVlODFhYzgyMTE3ODNiYjE2YzUwMWY3ZWJiYzgwMDI2MDhhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">53603</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"14 characters\">/task/119/edit</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"14 characters\">/task/119/edit</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/index.php/task/119/edit</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/task/57</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImtJdklQNlFFV2NTbGlUaDUxM3JVa0E9PSIsInZhbHVlIjoiSnlZRVNiUG5aSlAxZ01ITUk4Zkh1T09GcDR4ZjJIV0FNRTJWaUsvbnlNblYyWmVkdjJvdENYNVRwdDYwTjI0OEt4Q251RGRoOEhSZGMwbkhKYkFCYzFtaGRTTXMwWnRCdFYrRkQxMmpsbmpSVk5kc0RxQUN3bzJmQTc4alNMK1UiLCJtYWMiOiJlZWE2N2IxNTNiNjIxNjg0NzAxMTM2NDA0ZGFhYmFmOTgzNWEyMGVmZjIzYmZkMDZlZDg4OTBlNWYzODkzMzM5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkNEVUZRcFg0bmUvYzR1WUp1RnQ4K2c9PSIsInZhbHVlIjoiZjFPZ25PbWZlSXNXODk3NmJPVWVJeTFYSjhFS2ZlWkVaRWx0L2ZrOGV0WWNhRG55MVZQb0hhRnpsKzFpenFYUHhacDVjOTJHUHI0cXYwek9ZQUNOdGVYVjBjNXBwSHRVYmlJMFpNZ0xzamcvOVJOOW5vU2VlOFRhcFZYc2hsa2MiLCJtYWMiOiJmNDUwNmM1YjU2Yjk5MTY2N2ExYzY5MDMyN2EyYzVlODFhYzgyMTE3ODNiYjE2YzUwMWY3ZWJiYzgwMDI2MDhhIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755582029.2399</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755582029</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1240898808 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:40:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlRLOUI0TEpKblpWbkRtUVVHa3dOQnc9PSIsInZhbHVlIjoiaCtPK2twUC9qSTVhWDNQcUdwUTY1SWxCSFNxRGhPeTlqY3BSb3B1enhURFc5am5EWHFOaEFxMWNLMGNvcDk5cDU0SklEQ0VCT3NjK2RMMGhaZVVRcnlsaXhGd280YWxqMUJIeUZjRHhlcFdOZlFQendEVnJlNGJWdGJiZHhiN2EiLCJtYWMiOiIzNWM0NTFmY2U4Zjk5MTU0OTMxMGI1MTQ4MWYwOGFjNmMwNWE5NjBhYzk0YmJmMDg3ZTg3Y2M3NmM3M2NmMjA3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:40:29 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Ill1STRIWkt2WXJjMWorVnlGY3cyNkE9PSIsInZhbHVlIjoia1p3Ky9Eekhsdm1JUlEwQUx0TnlNWitUTWpZclhLOEtJYTl6VGNjVzFMZXorVDNVZWh5QVRPVjcxbWdQZTFzMmcrOHArcFRNaE8vZTEvbzc0ZW5VR05FVW9saFZQQ0xVejdWS2tyWWFCQ04rRUR4VnVtSU55WGhvd1ZwbnZwWXIiLCJtYWMiOiI2Njk2OGJmMTc1NGVlYTZmZWM5N2I4MzkxNWVmMDg1YjViNWYzNTk2MWJiNGYyN2ZmNDExMWI5Mjg4N2I2ZGI5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:40:29 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlRLOUI0TEpKblpWbkRtUVVHa3dOQnc9PSIsInZhbHVlIjoiaCtPK2twUC9qSTVhWDNQcUdwUTY1SWxCSFNxRGhPeTlqY3BSb3B1enhURFc5am5EWHFOaEFxMWNLMGNvcDk5cDU0SklEQ0VCT3NjK2RMMGhaZVVRcnlsaXhGd280YWxqMUJIeUZjRHhlcFdOZlFQendEVnJlNGJWdGJiZHhiN2EiLCJtYWMiOiIzNWM0NTFmY2U4Zjk5MTU0OTMxMGI1MTQ4MWYwOGFjNmMwNWE5NjBhYzk0YmJmMDg3ZTg3Y2M3NmM3M2NmMjA3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:40:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Ill1STRIWkt2WXJjMWorVnlGY3cyNkE9PSIsInZhbHVlIjoia1p3Ky9Eekhsdm1JUlEwQUx0TnlNWitUTWpZclhLOEtJYTl6VGNjVzFMZXorVDNVZWh5QVRPVjcxbWdQZTFzMmcrOHArcFRNaE8vZTEvbzc0ZW5VR05FVW9saFZQQ0xVejdWS2tyWWFCQ04rRUR4VnVtSU55WGhvd1ZwbnZwWXIiLCJtYWMiOiI2Njk2OGJmMTc1NGVlYTZmZWM5N2I4MzkxNWVmMDg1YjViNWYzNTk2MWJiNGYyN2ZmNDExMWI5Mjg4N2I2ZGI5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:40:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1240898808\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-319967444 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/task/119/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-319967444\", {\"maxDepth\":0})</script>\n"}}