{"__meta": {"id": "Xe5a558c4bb8e838e6171850751e20061", "datetime": "2025-08-19 14:25:42", "utime": 1755584742.848613, "method": "GET", "uri": "/teammappings/Team%20888/create?teams=39", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[14:25:42] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584742.699754, "xdebug_link": null, "collector": "log"}, {"message": "[14:25:42] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/teammappings/Team%20888/create", "message_html": null, "is_string": false, "label": "debug", "time": 1755584742.759336, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584742.381556, "end": 1755584742.848638, "duration": 0.46708202362060547, "duration_str": "467ms", "measures": [{"label": "Booting", "start": 1755584742.381556, "relative_start": 0, "end": 1755584742.680625, "relative_end": 1755584742.680625, "duration": 0.2990689277648926, "duration_str": "299ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584742.680634, "relative_start": 0.29907798767089844, "end": 1755584742.84864, "relative_end": 1.9073486328125e-06, "duration": 0.16800594329833984, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25312264, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 15, "templates": [{"name": "teammapping.create (\\resources\\views\\teammapping\\create.blade.php)", "param_count": 4, "params": ["roles", "users", "team_name", "title"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 8, "params": ["__env", "app", "menuData", "errors", "roles", "users", "team_name", "title"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 8, "params": ["__env", "app", "menuData", "errors", "roles", "users", "team_name", "title"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 8, "params": ["__env", "app", "menuData", "errors", "roles", "users", "team_name", "title"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 8, "params": ["__env", "app", "menuData", "errors", "roles", "users", "team_name", "title"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 8, "params": ["__env", "app", "menuData", "errors", "roles", "users", "team_name", "title"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 8, "params": ["__env", "app", "menuData", "errors", "roles", "users", "team_name", "title"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 8, "params": ["__env", "app", "menuData", "errors", "roles", "users", "team_name", "title"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "roles", "users", "team_name", "title", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "roles", "users", "team_name", "title", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "roles", "users", "team_name", "title", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "roles", "users", "team_name", "title", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "roles", "users", "team_name", "title", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "roles", "users", "team_name", "title", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "roles", "users", "team_name", "title", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET teammappings/{team_name}/create", "middleware": "web", "controller": "App\\Http\\Controllers\\TeamMappingController@create", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "teammappings.create", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TeamMappingController.php&line=78\">\\app\\Http\\Controllers\\TeamMappingController.php:78-103</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00169, "accumulated_duration_str": "1.69ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 26.036}, {"sql": "select exists(select * from `teammappings` where `team_name` = 'Team 888' and `username` = 'ivlyn' and `role_name` = 'Team Manager' and `invitation_status` = 'accepted') as `exists`", "type": "query", "params": [], "bindings": ["Team 888", "ivlyn", "Team Manager", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 35}, {"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 56}, {"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 81}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:35", "connection": "sagile", "start_percent": 26.036, "width_percent": 25.444}, {"sql": "select * from `teammappings` where `team_name` = 'Team 888'", "type": "query", "params": [], "bindings": ["Team 888"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 88}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:88", "connection": "sagile", "start_percent": 51.479, "width_percent": 21.302}, {"sql": "select * from `users` where `username` not in ('ivlyn')", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 89}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:89", "connection": "sagile", "start_percent": 72.781, "width_percent": 27.219}]}, "models": {"data": {"App\\TeamMapping": 1, "App\\User": 18}, "count": 19}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A7qC5dKg3kSvtr41icWUi6S70mrKKeUAGVsmye8B", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/teammappings/Team%20888/create?teams=39\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/teammappings/Team%20888/create", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-226471852 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>teams</span>\" => \"<span class=sf-dump-str title=\"2 characters\">39</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-226471852\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-116192203 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>teams</span>\" => \"<span class=sf-dump-str title=\"2 characters\">39</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-116192203\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-889759300 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/teammapping/Team%20888</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ii9Yb2xQUnZ5b0J4TmpFYk0rWlA0R3c9PSIsInZhbHVlIjoiTm50RVl5MUtEMlNhNTVPNTA4MVczOUthSTlCZWdNWEpaMGZERjlENjZaS3R3blVsQnZTNnUrNVd3NUdUeDdsL0ZlU3F3OFVtVkMyZUU5SjJkVDJSTzAwNDZDc0NxWm1ablZOUW1sT0FWWE5NNExPNnpLZ0N3ZEJ0bmZrdk1wRXUiLCJtYWMiOiIwYjE0MjE0ZDY4MTI0ZWNlNGY1YWUxMGVlNjFhOGEyMjZjMGJkNTljNzNhNGEzZGJmZTk5MjM5YTBlODg1ZDUzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlFDNW5udzFRVDlxTjF1ZEhycExtZEE9PSIsInZhbHVlIjoiaHdqVXB1Q0ZXOE13dlNhRFpWRWNQWGU3WmE3cDN6bDdmaHI4U1FRRWpJc2JCZndXK3NRdzJhWlFkUS9PeVRHcDVJWnF1dEdBOENBNkx1Mjdjc1BYSVQ2bjBkRTc2SEszdTIvRllMUld4aFEwVHl3bks5UmxUWU9iS20welRBdHkiLCJtYWMiOiI1YzEwNDI1NzgzMDRlOTExYjBhNzUzOGE0MWEwOWZhNDk0OThhMzY4NDlkNjlhNTZmNjk4OGZlNjY0ZjAwZTRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-889759300\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1389750241 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55461</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"40 characters\">/teammappings/Team%20888/create?teams=39</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/teammappings/Team 888/create</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"39 characters\">/index.php/teammappings/Team 888/create</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"8 characters\">teams=39</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/teammapping/Team%20888</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ii9Yb2xQUnZ5b0J4TmpFYk0rWlA0R3c9PSIsInZhbHVlIjoiTm50RVl5MUtEMlNhNTVPNTA4MVczOUthSTlCZWdNWEpaMGZERjlENjZaS3R3blVsQnZTNnUrNVd3NUdUeDdsL0ZlU3F3OFVtVkMyZUU5SjJkVDJSTzAwNDZDc0NxWm1ablZOUW1sT0FWWE5NNExPNnpLZ0N3ZEJ0bmZrdk1wRXUiLCJtYWMiOiIwYjE0MjE0ZDY4MTI0ZWNlNGY1YWUxMGVlNjFhOGEyMjZjMGJkNTljNzNhNGEzZGJmZTk5MjM5YTBlODg1ZDUzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlFDNW5udzFRVDlxTjF1ZEhycExtZEE9PSIsInZhbHVlIjoiaHdqVXB1Q0ZXOE13dlNhRFpWRWNQWGU3WmE3cDN6bDdmaHI4U1FRRWpJc2JCZndXK3NRdzJhWlFkUS9PeVRHcDVJWnF1dEdBOENBNkx1Mjdjc1BYSVQ2bjBkRTc2SEszdTIvRllMUld4aFEwVHl3bks5UmxUWU9iS20welRBdHkiLCJtYWMiOiI1YzEwNDI1NzgzMDRlOTExYjBhNzUzOGE0MWEwOWZhNDk0OThhMzY4NDlkNjlhNTZmNjk4OGZlNjY0ZjAwZTRhIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584742.3816</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584742</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389750241\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-974285522 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A7qC5dKg3kSvtr41icWUi6S70mrKKeUAGVsmye8B</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PsfQwzGwhxLcTiW03wKjNrtJR5Amn59fF4M5EzyK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-974285522\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2057009225 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:25:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ikc0ZVdqMmk0dXhQK1FPZmhiYWViUWc9PSIsInZhbHVlIjoiMUpxei9OM2RiSFIvWk8yM1VjdlQ2bTZOdUppdUF6MTRXaWZPSkQvN0w4MUQ2R3kxeG83MC9MSHVOTXc3ZUtaQmxaV2VFdEgyd0ZYY3ljdEhKUEQ4ck1USTJPaGtMSThadUllemtwZ0NEaGQ1UHk1ZmJvT1RyeUZiOFE2MWxmWjAiLCJtYWMiOiIzZjRiNGQ4ZjlkZjM5Y2E2NWMzMjRlZmI0ZTFhNDM2N2RkMGJmOTA2OTQ5NTUwOTQ0YWNlYjA0ZGNlZWQyNzVmIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:25:42 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImVGaUJqRHBMK3AwamxVazI2M0R1U3c9PSIsInZhbHVlIjoiZHRpQVlDaXo2YldVVGliUmF2clpZWHFvYmxPOTJ5Y3VObFlpT1FxUGlGNXo4b3pFR1VNWkN0dSttREtJSGQ1NjM0cTRxSGtVVDFRdzhvb0l6R0FOQVdZOUh5RmJCa2JsRjFKWklLZWZHUkxscm5tdHdFMUZSVnlMM09KSGNjcloiLCJtYWMiOiIzYjM5MWM5NDk1YTMwYzkzZWQ1NjBkNDg4N2MwODk4ODQzZDFjNWM0ZDFjZmExOTAwN2Q1OWU5Y2MwZjU2NjQxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:25:42 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ikc0ZVdqMmk0dXhQK1FPZmhiYWViUWc9PSIsInZhbHVlIjoiMUpxei9OM2RiSFIvWk8yM1VjdlQ2bTZOdUppdUF6MTRXaWZPSkQvN0w4MUQ2R3kxeG83MC9MSHVOTXc3ZUtaQmxaV2VFdEgyd0ZYY3ljdEhKUEQ4ck1USTJPaGtMSThadUllemtwZ0NEaGQ1UHk1ZmJvT1RyeUZiOFE2MWxmWjAiLCJtYWMiOiIzZjRiNGQ4ZjlkZjM5Y2E2NWMzMjRlZmI0ZTFhNDM2N2RkMGJmOTA2OTQ5NTUwOTQ0YWNlYjA0ZGNlZWQyNzVmIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:25:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImVGaUJqRHBMK3AwamxVazI2M0R1U3c9PSIsInZhbHVlIjoiZHRpQVlDaXo2YldVVGliUmF2clpZWHFvYmxPOTJ5Y3VObFlpT1FxUGlGNXo4b3pFR1VNWkN0dSttREtJSGQ1NjM0cTRxSGtVVDFRdzhvb0l6R0FOQVdZOUh5RmJCa2JsRjFKWklLZWZHUkxscm5tdHdFMUZSVnlMM09KSGNjcloiLCJtYWMiOiIzYjM5MWM5NDk1YTMwYzkzZWQ1NjBkNDg4N2MwODk4ODQzZDFjNWM0ZDFjZmExOTAwN2Q1OWU5Y2MwZjU2NjQxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:25:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2057009225\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1698816882 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A7qC5dKg3kSvtr41icWUi6S70mrKKeUAGVsmye8B</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://127.0.0.1:8000/teammappings/Team%20888/create?teams=39</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1698816882\", {\"maxDepth\":0})</script>\n"}}