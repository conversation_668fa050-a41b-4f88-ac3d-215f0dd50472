{"__meta": {"id": "X4da0f78c5df1decd0e040def2c75cce0", "datetime": "2025-08-19 10:41:11", "utime": 1755571271.936888, "method": "GET", "uri": "/projects/42", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 95, "messages": [{"message": "[10:41:11] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755571271.697196, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: LoadUserPermissions: Extracted project ID: 42 from URL: http://127.0.0.1:8000/projects/42", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.785665, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: LoadUserPermissions: Found project 42 with team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.803411, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: LoadUserPermissions: Looking for team role map for team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.803488, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: LoadUserPermissions: Available teams in role map: [\"i<PERSON><PERSON>'s team\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.803545, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: LoadUserPermissions: Found team data structure: multiple_projects", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.803588, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: LoadUserPermissions: Set permissions for user <PERSON><PERSON><PERSON> in team i<PERSON><PERSON>'s team for project 42: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.803699, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.85474, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.856064, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.856145, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.856214, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_burndown on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.862394, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.863455, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.863525, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.863591, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.863912, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.864901, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.865088, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.865195, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.865553, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.866658, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.866725, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.866794, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_forum on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.867172, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.868457, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.868537, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.868604, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_bugtracking on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.868964, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.869974, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.870124, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.870205, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_roles on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.870495, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.87147, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.871534, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.871592, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_status on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.871883, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.872858, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.872921, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.872997, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.873284, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.874286, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.874368, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.874442, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_details on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.874741, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.875787, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.875848, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.875901, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.876185, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.877206, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.87727, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.877329, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_burndown on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.87768, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.878671, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.878751, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.878823, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.879136, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.88012, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.880221, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.880289, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.880616, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.881835, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.881925, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.882002, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_forum on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.882391, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.883475, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.883544, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.883624, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_bugtracking on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.883985, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.885001, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.88508, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.885154, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_roles on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.885556, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.886535, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.886605, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.886671, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_status on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.886994, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.888054, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.888109, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.888169, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.888557, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.889696, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.889777, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.889847, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: view_details on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.890206, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.891264, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.891348, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.891424, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: edit_details on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.891794, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.892878, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.892944, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.893021, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Gate check for permission: delete_details on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.893367, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.894392, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.894468, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:11] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755571271.894541, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755571271.319256, "end": 1755571271.937063, "duration": 0.6178069114685059, "duration_str": "618ms", "measures": [{"label": "Booting", "start": 1755571271.319256, "relative_start": 0, "end": 1755571271.668113, "relative_end": 1755571271.668113, "duration": 0.34885692596435547, "duration_str": "349ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755571271.668127, "relative_start": 0.34887099266052246, "end": 1755571271.93707, "relative_end": 6.9141387939453125e-06, "duration": 0.26894283294677734, "duration_str": "269ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24196824, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 10, "templates": [{"name": "project.details (\\resources\\views\\project\\details.blade.php)", "param_count": 1, "params": ["project"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 5, "params": ["__env", "app", "menuData", "errors", "project"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 5, "params": ["__env", "app", "menuData", "errors", "project"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET projects/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\ProductFeatureController@details", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "projects.details", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProductFeatureController.php&line=133\">\\app\\Http\\Controllers\\ProductFeatureController.php:133-153</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0023599999999999997, "accumulated_duration_str": "2.36ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 27.966}, {"sql": "select `id`, `team_name` from `projects` where `id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 167}, {"index": 14, "namespace": "middleware", "name": "load.permissions", "line": 33}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 16, "namespace": "middleware", "name": "bindings", "line": 50}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "middleware::load.permissions:167", "connection": "sagile", "start_percent": 27.966, "width_percent": 20.763}, {"sql": "select `team_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 140}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:140", "connection": "sagile", "start_percent": 48.729, "width_percent": 25}, {"sql": "select * from `projects` where `id` = '42' and `team_name` in ('iv<PERSON>\\'s team', 'ivlyn\\'s team') limit 1", "type": "query", "params": [], "bindings": ["42", "ivlyn&#039;s team", "ivlyn&#039;s team"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 146}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:146", "connection": "sagile", "start_percent": 73.729, "width_percent": 26.271}]}, "models": {"data": {"App\\Project": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 22, "messages": [{"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.860303, "xdebug_link": null}, {"message": "[\n  ability => view_burndown,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1377148092 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_burndown</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1377148092\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.863749, "xdebug_link": null}, {"message": "[\n  ability => view_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-604864123 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-604864123\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.865348, "xdebug_link": null}, {"message": "[\n  ability => view_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1333075806 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1333075806\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.866922, "xdebug_link": null}, {"message": "[\n  ability => view_forum,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-499851468 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_forum</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-499851468\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.868777, "xdebug_link": null}, {"message": "[\n  ability => view_bugtracking,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-945242175 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_bugtracking</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-945242175\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.870329, "xdebug_link": null}, {"message": "[\n  ability => view_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1272423334 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1272423334\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.871722, "xdebug_link": null}, {"message": "[\n  ability => view_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-969991712 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-969991712\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.873134, "xdebug_link": null}, {"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1424102482 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1424102482\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.874575, "xdebug_link": null}, {"message": "[\n  ability => view_details,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-155463735 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-155463735\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.876007, "xdebug_link": null}, {"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-585417027 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-585417027\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.87746, "xdebug_link": null}, {"message": "[\n  ability => view_burndown,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1978659315 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_burndown</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1978659315\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.878943, "xdebug_link": null}, {"message": "[\n  ability => view_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-868678130 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868678130\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.88041, "xdebug_link": null}, {"message": "[\n  ability => view_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-958873279 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958873279\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.882151, "xdebug_link": null}, {"message": "[\n  ability => view_forum,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2009621430 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_forum</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009621430\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.883776, "xdebug_link": null}, {"message": "[\n  ability => view_bugtracking,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1655479521 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_bugtracking</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1655479521\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.885273, "xdebug_link": null}, {"message": "[\n  ability => view_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-707811973 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-707811973\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.886789, "xdebug_link": null}, {"message": "[\n  ability => view_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-691928160 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-691928160\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.888329, "xdebug_link": null}, {"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-123292573 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-123292573\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.889986, "xdebug_link": null}, {"message": "[\n  ability => view_details,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-413028976 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-413028976\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.891556, "xdebug_link": null}, {"message": "[\n  ability => edit_details,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1478560528 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">edit_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478560528\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.893155, "xdebug_link": null}, {"message": "[\n  ability => delete_details,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-459275566 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">delete_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-459275566\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571271.894698, "xdebug_link": null}]}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/projects/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755570218\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/projects/42", "status_code": "<pre class=sf-dump id=sf-dump-1227438526 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1227438526\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1140066058 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1140066058\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1479008649 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1479008649\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1182545425 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IndHc2hwYjdpdmNwNWFtdmRYOEt5NEE9PSIsInZhbHVlIjoicTVhWnBIaGplME1EODVaVkd6ZW5nSWxPeGV6Zy8yYjhvcXU3b084SllBV01JOGV1MmNIelZTbk15MmFVYjBiNlV0a0tBZ2lKd1p5RTlkWVRBT0krOEVhLzNkWGFHMGxsNnNDK3VBVFNOa21JVlc4dVFUZCtBN294RXhIaGtaU2wiLCJtYWMiOiI5OTgzOWRjODQ4YWNhN2QxZGU4NmVkNWI0NzY3OTljNzdiZjljMzllNTQ0NTU1MDc2NDgwMjViYjA4YjQ0ZmQxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImJ3bDBSYjUra2Y5UVd4b053aG9NeWc9PSIsInZhbHVlIjoiL2tsYmpDbGYvRTRGREVOak9FdFJESzk0aTBmYXh6RVgxdmtpUmFzdkhwSTJhSFR5bytmYTB0dk1tYjRvaTB0OEdpbElramFJSnY1WHpreExUWGdRUVpTUWlqK3pYbkM3UVJ4VHVkaFI0RllmblZtTjVJSUFMLzUrWmFkSTBJdlEiLCJtYWMiOiJiM2E1ZDlmY2E2OTFhNzc4NjJhNzhhZGUyMjM3ZDhjZTJiYjgyYTdiYzJhMmZjMDdhOWRmMWVjMjhlODVmNDIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1182545425\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1834261433 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">49834</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/projects/42</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/projects/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/index.php/projects/42</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IndHc2hwYjdpdmNwNWFtdmRYOEt5NEE9PSIsInZhbHVlIjoicTVhWnBIaGplME1EODVaVkd6ZW5nSWxPeGV6Zy8yYjhvcXU3b084SllBV01JOGV1MmNIelZTbk15MmFVYjBiNlV0a0tBZ2lKd1p5RTlkWVRBT0krOEVhLzNkWGFHMGxsNnNDK3VBVFNOa21JVlc4dVFUZCtBN294RXhIaGtaU2wiLCJtYWMiOiI5OTgzOWRjODQ4YWNhN2QxZGU4NmVkNWI0NzY3OTljNzdiZjljMzllNTQ0NTU1MDc2NDgwMjViYjA4YjQ0ZmQxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImJ3bDBSYjUra2Y5UVd4b053aG9NeWc9PSIsInZhbHVlIjoiL2tsYmpDbGYvRTRGREVOak9FdFJESzk0aTBmYXh6RVgxdmtpUmFzdkhwSTJhSFR5bytmYTB0dk1tYjRvaTB0OEdpbElramFJSnY1WHpreExUWGdRUVpTUWlqK3pYbkM3UVJ4VHVkaFI0RllmblZtTjVJSUFMLzUrWmFkSTBJdlEiLCJtYWMiOiJiM2E1ZDlmY2E2OTFhNzc4NjJhNzhhZGUyMjM3ZDhjZTJiYjgyYTdiYzJhMmZjMDdhOWRmMWVjMjhlODVmNDIzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755571271.3193</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755571271</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1834261433\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-684506036 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-684506036\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:41:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlBodkpMTnJhTU9GMTlDSFNtSVVoU1E9PSIsInZhbHVlIjoiQVI5L09QV2RxdVY1aDNqNE9tMStjTmJ4WUtGb2JWWjJwWk1EZWRyN25CLzdXd2VzZEZsUUJoNFk5eHBnY1VIQmVNYnF5SGljRG1ZWDFmZ1N1c1BEODd4SUFLQVFmenhMVGovdCtlejFvSmtGcm9VdW96dEozeThBT3hhVDRybnUiLCJtYWMiOiJkZWMwZDg1NjY0MWM0OWE0ZWE4NTRmNGFiYmUzZDRiZjM3YWE1NDMzOGRjZTg0MWU4MGRiM2YxYTM5NDA3ZGZjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:41:11 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkVVVVIwWisrem0rTVYreE9HRkJibWc9PSIsInZhbHVlIjoiYmRkR3FkNVRTOGdEeTVnM040VVl6ajdqWWJlSFd4YWIvOU96TWNoVDRXWGRVaml6UjNCSVJ0NC9QS1h0bldYWExSR1I1eHhZcGxmNGZyK21IR1BDcHRYQk1UelFmZytkNVRXR1FBU2NLV3RQdGhEMzZvckk5c0dFNWFGa0R5VkIiLCJtYWMiOiI5NTNlMmQyY2VlNWI5ZWEzMmMzYzAzYTAxZjBkNDAwYWE1OTY1NjRmNDgwZGEzZjhiZjc1ZWU4ZjRmZTI1Y2I2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:41:11 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlBodkpMTnJhTU9GMTlDSFNtSVVoU1E9PSIsInZhbHVlIjoiQVI5L09QV2RxdVY1aDNqNE9tMStjTmJ4WUtGb2JWWjJwWk1EZWRyN25CLzdXd2VzZEZsUUJoNFk5eHBnY1VIQmVNYnF5SGljRG1ZWDFmZ1N1c1BEODd4SUFLQVFmenhMVGovdCtlejFvSmtGcm9VdW96dEozeThBT3hhVDRybnUiLCJtYWMiOiJkZWMwZDg1NjY0MWM0OWE0ZWE4NTRmNGFiYmUzZDRiZjM3YWE1NDMzOGRjZTg0MWU4MGRiM2YxYTM5NDA3ZGZjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:41:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkVVVVIwWisrem0rTVYreE9HRkJibWc9PSIsInZhbHVlIjoiYmRkR3FkNVRTOGdEeTVnM040VVl6ajdqWWJlSFd4YWIvOU96TWNoVDRXWGRVaml6UjNCSVJ0NC9QS1h0bldYWExSR1I1eHhZcGxmNGZyK21IR1BDcHRYQk1UelFmZytkNVRXR1FBU2NLV3RQdGhEMzZvckk5c0dFNWFGa0R5VkIiLCJtYWMiOiI5NTNlMmQyY2VlNWI5ZWEzMmMzYzAzYTAxZjBkNDAwYWE1OTY1NjRmNDgwZGEzZjhiZjc1ZWU4ZjRmZTI1Y2I2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:41:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755570218</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}