

<?php echo $__env->make('inc.breadcrumbStyle', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->startSection('content'); ?>    <div class="container">
        <!-- Header Section -->
        <div class="mb-4">
            <div class="d-flex align-items-center">
                <h1 class="mb-0"><a href="<?php echo e(route('project.newIndex')); ?>" class="breadcrumb-link">All Projects</a></h1>
                <h1 class="mb-0"><span class="breadcrumb-separator">/</span></h1>
                <h1 class="mb-0 breadcrumb-current"><?php echo e($project->proj_name); ?></h1>
            </div>
            <p class="text-muted mb-0"><?php echo e($project->team_name); ?> |
                <?php echo e(\Carbon\Carbon::parse($project->start_date)->format('j.n.Y')); ?> -
                <?php echo e(\Carbon\Carbon::parse($project->end_date)->format('j.n.Y')); ?>

            </p>
        </div>

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs shadow-sm mb-1" id="projectTabs" role="tablist">

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_kanban', $project)): ?>
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="kanban-tab" data-bs-toggle="tab" data-bs-target="#kanban" type="button"
                    role="tab" aria-controls="kanban" aria-selected="true">Kanban</button>
            </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_burndown', $project)): ?>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="burndown-tab" data-bs-toggle="tab" data-bs-target="#burndown" type="button"
                    role="tab" aria-controls="burndown" aria-selected="false">Burndown</button>
            </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_backlog', $project)): ?>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="backlog-tab" data-bs-toggle="tab" data-bs-target="#backlog" type="button"
                    role="tab" aria-controls="backlog" aria-selected="false">Backlog</button>
            </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_userstory', $project)): ?>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="userstory-tab" data-bs-toggle="tab" data-bs-target="#userstory" type="button"
                    role="tab" aria-controls="userstory" aria-selected="false">User Story</button>
            </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_forum', $project)): ?>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="forum-tab" data-bs-toggle="tab" data-bs-target="#forum" type="button"
                    role="tab" aria-controls="forum" aria-selected="false">Forum</button>
            </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_bugtracking', $project)): ?>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="bug-tracking-tab" data-bs-toggle="tab" data-bs-target="#bug-tracking"
                    type="button" role="tab" aria-controls="bug-tracking" aria-selected="false">Bug Tracking</button>
            </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_roles', $project)): ?>
                <li class="nav-item" role="presentation">
                <button class="nav-link" id="roles-tab" data-bs-toggle="tab" data-bs-target="#roles" type="button"
                    role="tab" aria-controls="roles" aria-selected="false">Roles</button>
            </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_status', $project)): ?>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="status-tab" data-bs-toggle="tab" data-bs-target="#status" type="button"
                    role="tab" aria-controls="status" aria-selected="false">Status</button>
            </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_kanban', $project)): ?>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="archives-tab" data-bs-toggle="tab" data-bs-target="#archives" type="button"
                    role="tab" aria-controls="archives" aria-selected="false">Sprint Archives</button>
            </li>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_details', $project)): ?>        
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button"
                    role="tab" aria-controls="details" aria-selected="false">Details</button>
            </li>
            <?php endif; ?>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="projectTabsContent">

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_kanban', $project)): ?>
            <!-- Kanban Tab -->
            <div class="tab-pane fade show active" id="kanban" role="tabpanel" aria-labelledby="kanban-tab">
                <div class="iframe-container">
                    <div class="iframe-loader">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading Kanban...</p>
                    </div>
                    <iframe 
                        data-src="<?php echo e(route('sprint.kanbanPage', ['proj_id' => $project->id])); ?>" 
                        width="100%" 
                        style="border:none; height: calc(100vh - 200px);" 
                        id="kanban-iframe"
                        class="lazy-iframe">
                    </iframe>
                </div>
            </div>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_burndown', $project)): ?>
            <!-- Burndown Tab -->
            <div class="tab-pane fade" id="burndown" role="tabpanel" aria-labelledby="burndown-tab">
                <div class="iframe-container">
                    <div class="iframe-loader">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading Burndown Chart...</p>
                    </div>
                    <iframe 
                        data-src="<?php echo e(route('burnDown.getBurndownData', ['proj_id' => $project->id])); ?>" 
                        width="100%" 
                        style="border:none;" 
                        id="burndown-iframe"
                        class="lazy-iframe">
                    </iframe>
                </div>
            </div>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_backlog', $project)): ?>
            <!-- Backlog Tab -->
             <div class="tab-pane fade" id="backlog" role="tabpanel" aria-labelledby="backlog-tab">
                <div class="iframe-container">
                    <div class="iframe-loader">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading Backlog...</p>
                    </div>
                    <iframe 
                        data-src="<?php echo e(route('backlogTest.index', ['proj_id' => $project->id])); ?>" 
                        width="100%" 
                        style="border:none;" 
                        id="backlog-iframe"
                        class="lazy-iframe">
                    </iframe>
                </div>
            </div>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_userstory', $project)): ?>
            <!-- User Story Tab -->
            <div class="tab-pane fade" id="userstory" role="tabpanel" aria-labelledby="userstory-tab">
                <div class="iframe-container">
                    <div class="iframe-loader">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading User Stories...</p>
                    </div>
                    <iframe 
                        data-src="<?php echo e(route('userstory.index', ['proj_id' => $project->id])); ?>" 
                        width="100%" 
                        style="border:none;" 
                        id="userstory-iframe"
                        class="lazy-iframe">
                    </iframe>
                </div>
            </div>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_forum', $project)): ?>
            <!-- Forum Tab -->
            <div class="tab-pane fade" id="forum" role="tabpanel" aria-labelledby="forum-tab">
                <div class="iframe-container">
                    <div class="iframe-loader">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading Forum...</p>
                    </div>
                    <iframe 
                        data-src="<?php echo e(route('forum.index', ['projectId' => $project->id])); ?>" 
                        width="100%" 
                        style="border:none;" 
                        height="1000px" 
                        id="forum-iframe"
                        class="lazy-iframe">
                    </iframe>
                </div>
            </div>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_bugtracking', $project)): ?>
            <!-- Bug Tracking Tab -->
            <div class="tab-pane fade" id="bug-tracking" role="tabpanel" aria-labelledby="bug-tracking-tab">
                <div class="iframe-container">
                    <div class="iframe-loader">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading Bug Tracking...</p>
                    </div>
                    <iframe 
                        data-src="<?php echo e(route('bugtrack.index', ['projectId' => $project->id])); ?>" 
                        width="100%" 
                        style="border:none;" 
                        height="1000px" 
                        id="bugtrack-iframe"
                        class="lazy-iframe">
                    </iframe>
                </div>
            </div>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_roles', $project)): ?>
                <!-- Roles Tab -->
            <div class="tab-pane fade" id="roles" role="tabpanel" aria-labelledby="roles-tab">
                <div class="iframe-container">
                    <div class="iframe-loader">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading Roles...</p>
                    </div>
                    <iframe 
                        data-src="<?php echo e(route('role.index', ['project_id' => $project->id])); ?>" 
                        width="100%" 
                        style="border:none;" 
                        id="roles-iframe"
                        class="lazy-iframe">
                    </iframe>
                </div>
            </div>
            <?php endif; ?>
            
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_status', $project)): ?>
            <!-- Status Tab -->
            <div class="tab-pane fade" id="status" role="tabpanel" aria-labelledby="status-tab">
                <div class="iframe-container">
                    <div class="iframe-loader">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading Status...</p>
                    </div>
                    <iframe 
                        data-src="<?php echo e(route('status.project', ['proj_ID' => $project->id])); ?>" 
                        width="100%" 
                        style="border:none;" 
                        id="status-iframe"
                        class="lazy-iframe">
                    </iframe>
                </div>
            </div>
            <?php endif; ?>

            <!--TODO Add new key for view sprint archives -->
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_kanban', $project)): ?>
            <!-- Sprint Archives Tab -->
            <div class="tab-pane fade" id="archives" role="tabpanel" aria-labelledby="archives-tab">
                <div class="iframe-container">
                    <div class="iframe-loader">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading Sprint Archives...</p>
                    </div>
                    <iframe 
                        data-src="<?php echo e(route('sprint.archives', ['proj_id' => $project->id])); ?>" 
                        width="100%" 
                        style="border:none;" 
                        id="archives-iframe"
                        class="lazy-iframe">
                    </iframe>
                </div>
            </div>
            <?php endif; ?>            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_details', $project)): ?>        
            <!-- Details Tab -->
            <div class="tab-pane fade" id="details" role="tabpanel" aria-labelledby="details-tab">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Project Details</h5>
                                <p><strong>Project Name:</strong> <?php echo e($project->proj_name); ?></p>
                                <p><strong>Team:</strong> <?php echo e($project->team_name); ?></p>
                                <p><strong>Start Date:</strong> <?php echo e(\Carbon\Carbon::parse($project->start_date)->format('j F Y')); ?></p>
                                <p><strong>End Date:</strong> <?php echo e(\Carbon\Carbon::parse($project->end_date)->format('j F Y')); ?></p>
                                <p><strong>Description:</strong> <?php echo e($project->proj_desc); ?></p>
                                <div class="d-flex mt-3">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit_details', $project)): ?>
                                        <a href="<?php echo e(route('projects.edit', $project->id)); ?>" class="btn btn-primary me-2">Edit</a>
                                    <?php endif; ?>
                                    
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete_details', $project)): ?>
                                        <button type="button" class="btn btn-danger me-2" data-bs-toggle="modal" data-bs-target="#deleteProjectModal">
                                            Delete Project
                                        </button>
                                    <?php endif; ?>
                                    
                                    <button id="shareProjectBtn" class="btn btn-outline-primary">
                                        <i class="fas fa-share-alt me-2"></i>Share Project
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            

        </div>
    </div>

    <!-- Delete Project Modal -->
    <div class="modal fade" id="deleteProjectModal" tabindex="-1" aria-labelledby="deleteProjectModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteProjectModalLabel">Confirm Project Deletion</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="text-danger">Warning: This action cannot be undone!</p>
                    <p>To confirm deletion, please type the project name: <strong><?php echo e($project->proj_name); ?></strong></p>
                    <div class="mb-3">
                        <input type="text" class="form-control" id="confirmProjectName" placeholder="Type project name">
                    </div>
                    <form id="deleteProjectForm" action="<?php echo e(route('projects.destroy', $project->id)); ?>" method="GET">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn" disabled>Delete Project</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Share Project Modal -->
    <div class="modal fade" id="shareProjectModal" tabindex="-1" aria-labelledby="shareProjectModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="shareProjectModalLabel">Share Project</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="shareModalLoading">
                        <div class="d-flex justify-content-center mb-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <p class="text-center">Generating shareable link...</p>
                    </div>
                    
                    <div id="shareModalContent" style="display: none;">
                        <p>Anyone with this link can view this project (must be logged into the system):</p>
                        <div class="input-group mb-3">
                            <input type="text" id="shareableLink" class="form-control" readonly>
                            <button class="btn btn-outline-primary" type="button" id="copyLinkBtn">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                        </div>
                        <div id="linkCopiedAlert" class="alert alert-success" style="display: none;">
                            Link copied to clipboard!
                        </div>
                    </div>
                    
                    <div id="shareModalError" class="alert alert-danger" style="display: none;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Custom modal animation - fade in at center */
        .fade .modal-dialog {
            transform: none !important;
            transition: opacity 0.22s ease-in-out !important;
        }
        
        .fade {
            transition: opacity 0.22s ease-in-out !important;
        }

        /* Iframe loading styles */
        .iframe-container {
            position: relative;
            min-height: 300px;
        }
        
        .iframe-container iframe {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }
        
        .iframe-container iframe.loaded {
            opacity: 1;
        }
        
        .iframe-loader {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 10;
        }
        
        .spinner-border {
            width: 3rem;
            height: 3rem;
        }

        /* Lazy loading styles */
        .lazy-iframe {
            background-color: #f8f9fa;
        }

        .lazy-iframe:not([src]) {
            min-height: 300px;
        }

        /* Specific styles for kanban tab and iframe */
        #kanban.tab-pane {
            min-height: 100vh;
            overflow: hidden;
        }

        #kanban .iframe-container {
            height: 100%;
            min-height: unset;
        }

        #kanban iframe {
            height: 100% !important;
            transition: height 0.3s ease;
            min-height: 300px;
        }

        /* Preserve original tab content behavior for non-kanban tabs */
        .tab-pane:not(#kanban) {
            height: auto;
            overflow: visible;
        }

        .tab-pane:not(#kanban) iframe {
            height: 1000px;
        }
    </style>

    <script>
        // Lazy loading functionality
        class LazyIframeLoader {
            constructor() {
                this.loadedIframes = new Set();
                this.init();
            }

            init() {
                // Load the initially active tab
                const activeTab = document.querySelector('.tab-pane.active');
                if (activeTab) {
                    this.loadIframe(activeTab);
                }

                // Set up tab change listeners
                this.setupTabListeners();
            }

            setupTabListeners() {
                const tabLinks = document.querySelectorAll('[data-bs-toggle="tab"]');
                
                tabLinks.forEach(link => {
                    link.addEventListener('shown.bs.tab', (e) => {
                        const targetId = e.target.getAttribute('data-bs-target');
                        const targetTab = document.querySelector(targetId);
                        
                        if (targetTab) {
                            const iframe = targetTab.querySelector('.lazy-iframe');
                            if (iframe) {
                                if (this.loadedIframes.has(iframe.id)) {
                                    // If iframe was already loaded before, refresh it
                                    this.refreshIframe(iframe.id);
                                } else {
                                    // If iframe hasn't been loaded yet, load it for the first time
                                    this.loadIframe(targetTab);
                                }
                            }
                        }
                    });
                });
            }

            loadIframe(tabPane) {
                const iframe = tabPane.querySelector('.lazy-iframe');
                
                if (!iframe || this.loadedIframes.has(iframe.id)) {
                    return;
                }

                const dataSrc = iframe.getAttribute('data-src');
                
                if (dataSrc) {
                    // Show loader
                    const loader = tabPane.querySelector('.iframe-loader');
                    if (loader) {
                        loader.style.display = 'block';
                    }

                    // Set up load event handler before setting src
                    iframe.onload = () => {
                        this.handleIframeLoad(iframe);
                    };

                    iframe.onerror = () => {
                        this.handleIframeError(iframe);
                    };

                    // Start loading
                    iframe.src = dataSrc;
                    this.loadedIframes.add(iframe.id);
                }
            }

            handleIframeLoad(iframe) {
                // Hide loader and show iframe
                const container = iframe.closest('.iframe-container');
                const loader = container ? container.querySelector('.iframe-loader') : null;
                
                if (loader) {
                    loader.style.display = 'none';
                }
                
                iframe.classList.add('loaded');
                
                // Resize iframe if needed
                this.resizeIframe(iframe);
            }

            handleIframeError(iframe) {
                const container = iframe.closest('.iframe-container');
                const loader = container ? container.querySelector('.iframe-loader') : null;
                
                if (loader) {
                    loader.innerHTML = `
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle"></i>
                            Failed to load content. Please try refreshing the page.
                        </div>
                    `;
                }
            }

            resizeIframe(iframe) {
                try {
                    // Skip automatic resizing for kanban iframe as it's handled by message events
                    if (iframe.id === 'kanban-iframe') {
                        return;
                    }

                    // Original resize logic for other iframes
                    const height = iframe.contentWindow.document.body.scrollHeight;
                    const buffer = 60;
                    iframe.style.height = (height + buffer) + 'px';
                } catch (e) {
                    console.error('Error resizing iframe:', e);
                    // Set a default height if we can't access the content
                    iframe.style.height = '800px';
                }
            }

            // Method to refresh a specific iframe
            refreshIframe(iframeId) {
                const iframe = document.getElementById(iframeId);
                if (iframe && this.loadedIframes.has(iframeId)) {
                    // Reset the iframe
                    iframe.classList.remove('loaded');
                    const container = iframe.closest('.iframe-container');
                    const loader = container ? container.querySelector('.iframe-loader') : null;
                    
                    if (loader) {
                        loader.style.display = 'block';
                        loader.innerHTML = `
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Refreshing content...</p>
                        `;
                    }

                    // Force reload by temporarily removing src and then setting it again
                    const currentSrc = iframe.src;
                    iframe.src = 'about:blank';
                    setTimeout(() => {
                        iframe.src = currentSrc;
                    }, 50);

                    // Set up load event handler
                    iframe.onload = () => {
                        if (iframe.src !== 'about:blank') {
                            this.handleIframeLoad(iframe);
                        }
                    };
                }
            }
        }

        // Listen for messages from the kanban iframe
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'kanbanHeight') {
                const kanbanIframe = document.getElementById('kanban-iframe');
                const kanbanTab = document.getElementById('kanban');
                if (kanbanIframe && kanbanTab) {
                    const newHeight = Math.max(event.data.height, window.innerHeight);
                    kanbanIframe.style.height = newHeight + 'px';
                    kanbanTab.style.height = newHeight + 'px';
                }
            }
        });

        // Initialize lazy loading when DOM is ready
        let lazyLoader;
        document.addEventListener('DOMContentLoaded', function() {
            lazyLoader = new LazyIframeLoader();
        });

        // Project delete confirmation script
        document.addEventListener('DOMContentLoaded', function() {
            const confirmInput = document.getElementById('confirmProjectName');
            const confirmButton = document.getElementById('confirmDeleteBtn');
            const deleteForm = document.getElementById('deleteProjectForm');
            const projectName = "<?php echo e($project->proj_name); ?>";

            // Enable/disable delete button based on input matching project name
            confirmInput.addEventListener('input', function() {
                confirmButton.disabled = confirmInput.value !== projectName;
            });

            // Submit the delete form when confirmed
            confirmButton.addEventListener('click', function() {
                if (confirmInput.value === projectName) {
                    deleteForm.submit();
                }
            });
            
            // Project sharing functionality
            const shareBtn = document.getElementById('shareProjectBtn');
            const shareModal = document.getElementById('shareProjectModal');
            const shareableLink = document.getElementById('shareableLink');
            const copyLinkBtn = document.getElementById('copyLinkBtn');
            const linkCopiedAlert = document.getElementById('linkCopiedAlert');
            const shareModalLoading = document.getElementById('shareModalLoading');
            const shareModalContent = document.getElementById('shareModalContent');
            const shareModalError = document.getElementById('shareModalError');
            
            // Show modal when share button is clicked
            shareBtn.addEventListener('click', function() {
                // Reset modal state
                shareModalLoading.style.display = 'block';
                shareModalContent.style.display = 'none';
                shareModalError.style.display = 'none';
                linkCopiedAlert.style.display = 'none';
                
                // Initialize the modal
                const modalInstance = new bootstrap.Modal(shareModal);
                modalInstance.show();
                
                // Generate shareable link using AJAX
                fetch('<?php echo e(route('projects.generateShareableLink', $project->id)); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        project_id: <?php echo e($project->id); ?>

                    })
                })
                .then(response => response.json())
                .then(data => {
                    shareModalLoading.style.display = 'none';
                    
                    if (data.success) {
                        shareModalContent.style.display = 'block';
                        shareableLink.value = data.url;
                    } else {
                        shareModalError.textContent = data.error || 'An error occurred while generating the shareable link.';
                        shareModalError.style.display = 'block';
                    }
                })
                .catch(error => {
                    shareModalLoading.style.display = 'none';
                    shareModalError.textContent = 'An error occurred while generating the shareable link.';
                    shareModalError.style.display = 'block';
                    console.error('Error:', error);
                });
            });
            
            // Copy link functionality
            copyLinkBtn.addEventListener('click', function() {
                shareableLink.select();
                document.execCommand('copy');
                
                linkCopiedAlert.style.display = 'block';
                setTimeout(() => {
                    linkCopiedAlert.style.display = 'none';
                }, 3000);
            });
        });

        // Expose refresh function globally for debugging
        window.refreshIframe = function(iframeId) {
            if (lazyLoader) {
                lazyLoader.refreshIframe(iframeId);
            }
        };
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts/contentNavbarLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/project/details.blade.php ENDPATH**/ ?>