{"__meta": {"id": "X4d62717f54fd329c0f3f293746009952", "datetime": "2025-08-18 23:43:19", "utime": 1755531799.304408, "method": "GET", "uri": "/radar-data/42/34", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:43:19] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755531799.222918, "xdebug_link": null, "collector": "log"}, {"message": "[23:43:19] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/radar-data/42/34", "message_html": null, "is_string": false, "label": "debug", "time": 1755531799.283616, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755531798.935789, "end": 1755531799.304429, "duration": 0.3686399459838867, "duration_str": "369ms", "measures": [{"label": "Booting", "start": 1755531798.935789, "relative_start": 0, "end": 1755531799.203628, "relative_end": 1755531799.203628, "duration": 0.2678389549255371, "duration_str": "268ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755531799.203639, "relative_start": 0.2678499221801758, "end": 1755531799.304431, "relative_end": 1.9073486328125e-06, "duration": 0.10079193115234375, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23405800, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET radar-data/{proj_id}/{sprint_id?}", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@getRadarData", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=25\">\\app\\Http\\Controllers\\CIGController.php:25-77</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0009600000000000001, "accumulated_duration_str": "960μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 54.167}, {"sql": "select * from `user_stories` where `proj_id` = '42' and `sprint_id` = '34'", "type": "query", "params": [], "bindings": ["42", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 36}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:36", "connection": "sagile", "start_percent": 54.167, "width_percent": 45.833}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/radar-data/42/34\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]"}, "request": {"path_info": "/radar-data/42/34", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1695045109 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1695045109\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-932548045 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-932548045\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkdTSS9qRUhrVEZNcGM0UUNhU1hZK2c9PSIsInZhbHVlIjoiWFlVQldyclZGQWFFMEQ5VHI3cDFRekVybFlLQXFibjVSbFBqTVordTdjVTZsNXNmbDQvZnBDOStTWkpEVUlWYnhFVnBNVTNrMUkyNW5rTjdKekJ5cWNRcGJzZUlGaHpOYzZORjhoMU1jN0htMWt6Rk9aQld5MUM5UU80Rks3Y3QiLCJtYWMiOiI2N2I0ZDg3ZTNhMjM4MTBmOTE3NWUzMWFkNWM4Zjk3N2IyMTJjNWE3Yjk1OTk2NTVjYmE1MDYzMGQ3MjFlMGUxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ilg0QVN1Vm1xc0dkR1FCYnMzWXRGTlE9PSIsInZhbHVlIjoiN1QzWGxsck9SOU9CdnNCVEpNSnlnQ21OM0IxQW5hRnIrM3JheFZ5VkNpaHhMK0h0cjBBQVI3TTJmUk9Ub1JXbHBvdDlXVEZMOC8zUnBYSmc1ck5DMGc4YkluYk9ySHNqcjJkWmF5UFJON29Kajh5cEN5K0g3bmpzNlNOL2U2SE4iLCJtYWMiOiIyMzc0NjJiZGJjMzc1YWNhYjVkNjg2ZjRmMTI3ZjYzY2Q1ZWVhMjljOTk4MWRmMDhjZjcxNWZkMGEwNjIwNTc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54285</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/radar-data/42/34</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/radar-data/42/34</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/index.php/radar-data/42/34</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkdTSS9qRUhrVEZNcGM0UUNhU1hZK2c9PSIsInZhbHVlIjoiWFlVQldyclZGQWFFMEQ5VHI3cDFRekVybFlLQXFibjVSbFBqTVordTdjVTZsNXNmbDQvZnBDOStTWkpEVUlWYnhFVnBNVTNrMUkyNW5rTjdKekJ5cWNRcGJzZUlGaHpOYzZORjhoMU1jN0htMWt6Rk9aQld5MUM5UU80Rks3Y3QiLCJtYWMiOiI2N2I0ZDg3ZTNhMjM4MTBmOTE3NWUzMWFkNWM4Zjk3N2IyMTJjNWE3Yjk1OTk2NTVjYmE1MDYzMGQ3MjFlMGUxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ilg0QVN1Vm1xc0dkR1FCYnMzWXRGTlE9PSIsInZhbHVlIjoiN1QzWGxsck9SOU9CdnNCVEpNSnlnQ21OM0IxQW5hRnIrM3JheFZ5VkNpaHhMK0h0cjBBQVI3TTJmUk9Ub1JXbHBvdDlXVEZMOC8zUnBYSmc1ck5DMGc4YkluYk9ySHNqcjJkWmF5UFJON29Kajh5cEN5K0g3bmpzNlNOL2U2SE4iLCJtYWMiOiIyMzc0NjJiZGJjMzc1YWNhYjVkNjg2ZjRmMTI3ZjYzY2Q1ZWVhMjljOTk4MWRmMDhjZjcxNWZkMGEwNjIwNTc1IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755531798.9358</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755531798</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1726840002 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1726840002\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2131607969 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:43:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InlVZllsY0xFUWNMTXhsWW5aT3ZLSGc9PSIsInZhbHVlIjoiK1VvdGdWdGhVN2JXRnU3ZGVzY1Z3UWVNMzI1dHI3bVdYWHFaOGRxc2ttR3hWVkJpeFpEdEZMTURJeHFXcW1MZDNoV1JoeTFLbUtBd2xmYnYrV1J5aXNtK1VUb3MzazZ4c1pPUVZ6RUV4UGZPeC9XSFNlZ1JtRzZDbml6SHJjRk8iLCJtYWMiOiI5NTdkNjU0MGE0OGNkNjcyOTZlNWM0NDNiMjcxNDNiMjZkYTA5NzJiZmMwNWNiNDA0MGViNDE2MzE3MmNjNWI3IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:19 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlNxc3F5N0FSbXlwTkdwY1BmcFJRWHc9PSIsInZhbHVlIjoiYkhwaEQ0RTNIZE9RQXkzdWZmVDdnd3JoUGkvM2h1TEhOSDRDN3NkNjdoTDFIWHUzOGJmZ3lvYjhnLy9XNzE2N2pNUk9YVzRaMHBkSjE5ZkxIS1EyeGs5UlNWZllWM2hoS0RXYi9vOEVSbWRtaVBkV05sbWZEYjJEbUE3R0hqcGkiLCJtYWMiOiI1ZjRiMzQ3MjkyNzg4MmUxZjA5OWJiMDViMzExNjZlNTEzNjMwMThhODNiYmY0ZTNiNTdkMDc4Y2NkY2JjOTUxIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:19 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InlVZllsY0xFUWNMTXhsWW5aT3ZLSGc9PSIsInZhbHVlIjoiK1VvdGdWdGhVN2JXRnU3ZGVzY1Z3UWVNMzI1dHI3bVdYWHFaOGRxc2ttR3hWVkJpeFpEdEZMTURJeHFXcW1MZDNoV1JoeTFLbUtBd2xmYnYrV1J5aXNtK1VUb3MzazZ4c1pPUVZ6RUV4UGZPeC9XSFNlZ1JtRzZDbml6SHJjRk8iLCJtYWMiOiI5NTdkNjU0MGE0OGNkNjcyOTZlNWM0NDNiMjcxNDNiMjZkYTA5NzJiZmMwNWNiNDA0MGViNDE2MzE3MmNjNWI3IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlNxc3F5N0FSbXlwTkdwY1BmcFJRWHc9PSIsInZhbHVlIjoiYkhwaEQ0RTNIZE9RQXkzdWZmVDdnd3JoUGkvM2h1TEhOSDRDN3NkNjdoTDFIWHUzOGJmZ3lvYjhnLy9XNzE2N2pNUk9YVzRaMHBkSjE5ZkxIS1EyeGs5UlNWZllWM2hoS0RXYi9vOEVSbWRtaVBkV05sbWZEYjJEbUE3R0hqcGkiLCJtYWMiOiI1ZjRiMzQ3MjkyNzg4MmUxZjA5OWJiMDViMzExNjZlNTEzNjMwMThhODNiYmY0ZTNiNTdkMDc4Y2NkY2JjOTUxIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2131607969\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1969314043 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/radar-data/42/34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1969314043\", {\"maxDepth\":0})</script>\n"}}