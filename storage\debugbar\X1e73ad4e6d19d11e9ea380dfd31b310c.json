{"__meta": {"id": "X1e73ad4e6d19d11e9ea380dfd31b310c", "datetime": "2025-08-19 14:10:05", "utime": 1755583805.888693, "method": "GET", "uri": "/team-invitation/accept/hVBxVwEUv1cX7dKnqLVMFojh23ISsowb", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[14:10:05] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755583805.732854, "xdebug_link": null, "collector": "log"}, {"message": "[14:10:05] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/team-invitation/accept/hVBxVwEUv1cX7dKnqLVMFojh23ISsowb", "message_html": null, "is_string": false, "label": "debug", "time": 1755583805.83613, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755583805.114536, "end": 1755583805.888721, "duration": 0.7741849422454834, "duration_str": "774ms", "measures": [{"label": "Booting", "start": 1755583805.114536, "relative_start": 0, "end": 1755583805.695663, "relative_end": 1755583805.695663, "duration": 0.5811269283294678, "duration_str": "581ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755583805.695679, "relative_start": 0.5811429023742676, "end": 1755583805.888724, "relative_end": 3.0994415283203125e-06, "duration": 0.19304513931274414, "duration_str": "193ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23572656, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET team-invitation/accept/{token}", "middleware": "web", "controller": "App\\Http\\Controllers\\TeamMappingController@acceptInvitation", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "team-invitation.accept", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TeamMappingController.php&line=550\">\\app\\Http\\Controllers\\TeamMappingController.php:550-553</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00818, "accumulated_duration_str": "8.18ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 6.724}, {"sql": "select * from `teammappings` where `invitation_token` = 'hVBxVwEUv1cX7dKnqLVMFojh23ISsowb' limit 1", "type": "query", "params": [], "bindings": ["hVBxVwEUv1cX7dKnqLVMFojh23ISsowb"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 523}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 552}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:523", "connection": "sagile", "start_percent": 6.724, "width_percent": 5.746}, {"sql": "update `teammappings` set `invitation_status` = 'accepted', `teammappings`.`updated_at` = '2025-08-19 14:10:05' where `teammapping_id` = 101", "type": "query", "params": [], "bindings": ["accepted", "2025-08-19 14:10:05", "101"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 536}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 552}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0071600000000000006, "duration_str": "7.16ms", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:536", "connection": "sagile", "start_percent": 12.469, "width_percent": 87.531}]}, "models": {"data": {"App\\TeamMapping": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/team-invitation/accept/hVBxVwEUv1cX7dKnqLVMFojh23ISsowb\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "success": "You have successfully accepted the team invitation.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/team-invitation/accept/hVBxVwEUv1cX7dKnqLVMFojh23ISsowb", "status_code": "<pre class=sf-dump id=sf-dump-2141726063 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2141726063\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2122090108 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2122090108\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1981919460 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1981919460\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1689756012 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-browser-channel</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">stable</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-browser-year</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2025</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-browser-validation</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">XPdmRdCCj2OkELQ2uovjJFk6aKA=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-browser-copyright</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">Copyright 2025 Google LLC. All rights reserved.</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlV1NVp5czdLckZuVlU5cGpybjcyVnc9PSIsInZhbHVlIjoibms1dFRMV3dEN0h0MXM3b254UjI1ZVUyQWYwOGVNbVptSE53Z0l4MThwdXVUUGt1bSt4YzZSalVvN2Z5SlN4SU4xTnNoNDB1UTdBcXhjZWl5QWlZUHpLZFJUbXFEYTdBUW9tRU9LeGUzY3ZqSG5ISHUxRjVSY29XUFdMV2h0ZVIiLCJtYWMiOiIxNGE3NDE0Mjc2YmE3MmRmYTMxZjczM2M3YzA4ZWFmMjIyMDQ5YzIzMGNiYjBhNWQ4MWJhMGMzYmIwNGNiMzBlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Inc2U09pd3hydkVGb0hvUFJsaE9lSFE9PSIsInZhbHVlIjoiWWh1ZEJWY3ZoRDhQbEVPNjFNOUZBWU5nYmVqdnh3djNwZHpjT3djeWVaRmlTNGQ3Z0YrWVNTRXpGRzhoL05VM04vNHFVdWd2bHExektMUVo0RmN6ZU16Z2RsQ1A3dVFib3VTN3NZenVMMVZxV2NWbVo1QzVGaTJGL2hsNVB2VW0iLCJtYWMiOiJmNzAzNTI1Mjc4MTVlODMwYzRiYWM5MDU3ZWE1MTZiODQ4ZTgxY2UxNWNiMGEyMjU1ZWQyZjllM2NjYjAyNmM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1689756012\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-501301064 data-indent-pad=\"  \"><span class=sf-dump-note>array:34</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">50758</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"56 characters\">/team-invitation/accept/hVBxVwEUv1cX7dKnqLVMFojh23ISsowb</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"56 characters\">/team-invitation/accept/hVBxVwEUv1cX7dKnqLVMFojh23ISsowb</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"66 characters\">/index.php/team-invitation/accept/hVBxVwEUv1cX7dKnqLVMFojh23ISsowb</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_X_BROWSER_CHANNEL</span>\" => \"<span class=sf-dump-str title=\"6 characters\">stable</span>\"\n  \"<span class=sf-dump-key>HTTP_X_BROWSER_YEAR</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2025</span>\"\n  \"<span class=sf-dump-key>HTTP_X_BROWSER_VALIDATION</span>\" => \"<span class=sf-dump-str title=\"28 characters\">XPdmRdCCj2OkELQ2uovjJFk6aKA=</span>\"\n  \"<span class=sf-dump-key>HTTP_X_BROWSER_COPYRIGHT</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Copyright 2025 Google LLC. All rights reserved.</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlV1NVp5czdLckZuVlU5cGpybjcyVnc9PSIsInZhbHVlIjoibms1dFRMV3dEN0h0MXM3b254UjI1ZVUyQWYwOGVNbVptSE53Z0l4MThwdXVUUGt1bSt4YzZSalVvN2Z5SlN4SU4xTnNoNDB1UTdBcXhjZWl5QWlZUHpLZFJUbXFEYTdBUW9tRU9LeGUzY3ZqSG5ISHUxRjVSY29XUFdMV2h0ZVIiLCJtYWMiOiIxNGE3NDE0Mjc2YmE3MmRmYTMxZjczM2M3YzA4ZWFmMjIyMDQ5YzIzMGNiYjBhNWQ4MWJhMGMzYmIwNGNiMzBlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Inc2U09pd3hydkVGb0hvUFJsaE9lSFE9PSIsInZhbHVlIjoiWWh1ZEJWY3ZoRDhQbEVPNjFNOUZBWU5nYmVqdnh3djNwZHpjT3djeWVaRmlTNGQ3Z0YrWVNTRXpGRzhoL05VM04vNHFVdWd2bHExektMUVo0RmN6ZU16Z2RsQ1A3dVFib3VTN3NZenVMMVZxV2NWbVo1QzVGaTJGL2hsNVB2VW0iLCJtYWMiOiJmNzAzNTI1Mjc4MTVlODMwYzRiYWM5MDU3ZWE1MTZiODQ4ZTgxY2UxNWNiMGEyMjU1ZWQyZjllM2NjYjAyNmM4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755583805.1145</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755583805</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-501301064\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1902044745 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1902044745\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-993998127 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:10:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/teammapping/Team%20AD</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImlUMWhuVTJ4b1A0WG9LZk0zZWpkc2c9PSIsInZhbHVlIjoiVm05TDBoeEt1Z25vdHpWdmlXbGgzS3FSOVN6UjNySVFTeEk1cmZ6bGtjenBEa01aZ0hnSXM3WkdaVElqUzBYS3E5QytZSGpXRlpFajFYTlVDTVJING44VnNsNCtkSzVJeXZlTTNMVmpnazQvK3hQYTgxMFpLdnR4Zy8wZitvY3oiLCJtYWMiOiJkMWY4NzlmODhlZDY4OWFkYTZmYzJlOGE5YWUwYWY3OGE2MTllMzE5M2E2ZmE5YTg3MWFlYzZmNTgyZTA4MmFhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:10:05 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImVFVHZaU1pkWGNvL0lSK2NLbm5ESUE9PSIsInZhbHVlIjoiNWdVMTNDdmxHS25OWW52RFltRFVtcE1wMktqK0pONHFPUnlUakdtMlFRL0pvOXVTaGl6c3pDUW9zQTZ2c3VBMFJYbFRjWVZWR0ZCMndOeldFOEF5cXFpQWU3b3ZnbDZTcnJRWFZvc040WnRRcFFlcVJVWko3c2FwZXJMR1h6TmkiLCJtYWMiOiI1NWUwNDdhMTQ4OTJkMTU5NjA4YzY1YTAwY2QwNzhiZjFmZWZkMWVhZmJiZmM0ZjhjNWFjYjc5YmJhY2EwZjk3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:10:05 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImlUMWhuVTJ4b1A0WG9LZk0zZWpkc2c9PSIsInZhbHVlIjoiVm05TDBoeEt1Z25vdHpWdmlXbGgzS3FSOVN6UjNySVFTeEk1cmZ6bGtjenBEa01aZ0hnSXM3WkdaVElqUzBYS3E5QytZSGpXRlpFajFYTlVDTVJING44VnNsNCtkSzVJeXZlTTNMVmpnazQvK3hQYTgxMFpLdnR4Zy8wZitvY3oiLCJtYWMiOiJkMWY4NzlmODhlZDY4OWFkYTZmYzJlOGE5YWUwYWY3OGE2MTllMzE5M2E2ZmE5YTg3MWFlYzZmNTgyZTA4MmFhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:10:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImVFVHZaU1pkWGNvL0lSK2NLbm5ESUE9PSIsInZhbHVlIjoiNWdVMTNDdmxHS25OWW52RFltRFVtcE1wMktqK0pONHFPUnlUakdtMlFRL0pvOXVTaGl6c3pDUW9zQTZ2c3VBMFJYbFRjWVZWR0ZCMndOeldFOEF5cXFpQWU3b3ZnbDZTcnJRWFZvc040WnRRcFFlcVJVWko3c2FwZXJMR1h6TmkiLCJtYWMiOiI1NWUwNDdhMTQ4OTJkMTU5NjA4YzY1YTAwY2QwNzhiZjFmZWZkMWVhZmJiZmM0ZjhjNWFjYjc5YmJhY2EwZjk3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:10:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-993998127\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-428354769 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"77 characters\">http://127.0.0.1:8000/team-invitation/accept/hVBxVwEUv1cX7dKnqLVMFojh23ISsowb</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"51 characters\">You have successfully accepted the team invitation.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-428354769\", {\"maxDepth\":0})</script>\n"}}