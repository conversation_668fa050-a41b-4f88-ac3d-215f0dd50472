{"__meta": {"id": "X83cd03e9fc91c71b9a3b34741f010fc4", "datetime": "2025-08-19 13:40:55", "utime": 1755582055.927199, "method": "GET", "uri": "/backlogTest/43", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 27, "messages": [{"message": "[13:40:55] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755582055.772487, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/backlogTest/43", "message_html": null, "is_string": false, "label": "debug", "time": 1755582055.828825, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.info: BacklogController@index started {\"project_id\":\"43\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755582055.828944, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.info: Project found {\"project\":{\"id\":43,\"team_name\":\"Team 888\",\"proj_name\":\"Food Ordering System\",\"proj_desc\":\"this is a web-based food oerdering system\",\"start_date\":\"2025-08-19\",\"end_date\":\"2025-12-31\",\"shareable_slug\":null,\"created_at\":\"2025-08-19T05:31:41.000000Z\",\"updated_at\":\"2025-08-19T05:31:41.000000Z\"}}", "message_html": null, "is_string": false, "label": "info", "time": 1755582055.840271, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.info: Explicit active sprint check completed {\"proj_name\":\"Food Ordering System\",\"active_sprint_found\":true,\"active_sprint_data\":{\"sprint_id\":45,\"sprint_name\":\"Sprint 1\",\"sprint_desc\":\"this is sprint 1\",\"start_sprint\":\"2025-08-19\",\"end_sprint\":\"2025-09-02\",\"active_sprint\":1,\"proj_name\":\"Food Ordering System\",\"users_name\":\"ivlyn\",\"created_at\":\"2025-08-19T05:39:22.000000Z\",\"updated_at\":\"2025-08-19T05:39:22.000000Z\"}}", "message_html": null, "is_string": false, "label": "info", "time": 1755582055.852536, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.info: Active sprint ID determined {\"active_sprint_id\":45}", "message_html": null, "is_string": false, "label": "info", "time": 1755582055.852642, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.info: Processing user stories with active sprint {\"active_sprint_id\":45}", "message_html": null, "is_string": false, "label": "info", "time": 1755582055.852695, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.info: All non-done user stories retrieved {\"total_user_stories\":1,\"user_story_ids\":[57],\"done_status_id\":212}", "message_html": null, "is_string": false, "label": "info", "time": 1755582055.877861, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.debug: Processing user story {\"user_story_id\":57,\"user_story_sprint_id\":\"45\",\"active_sprint_id\":45}", "message_html": null, "is_string": false, "label": "debug", "time": 1755582055.877944, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.debug: User story is in active sprint - checking tasks {\"user_story_id\":57}", "message_html": null, "is_string": false, "label": "debug", "time": 1755582055.878003, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.debug: Tasks not in sprint check completed {\"user_story_id\":57,\"has_tasks_not_in_sprint\":false}", "message_html": null, "is_string": false, "label": "debug", "time": 1755582055.891294, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.info: User stories filtering completed with active sprint {\"filtered_user_stories_count\":0,\"filtered_user_story_ids\":[]}", "message_html": null, "is_string": false, "label": "info", "time": 1755582055.891358, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.info: Starting task retrieval for user stories", "message_html": null, "is_string": false, "label": "info", "time": 1755582055.891401, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.info: Task retrieval completed {\"total_user_stories_with_tasks\":0,\"total_tasks_retrieved\":0}", "message_html": null, "is_string": false, "label": "info", "time": 1755582055.90263, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.info: BacklogController@index completed successfully {\"project_id\":\"43\",\"active_sprint_id\":45,\"user_stories_count\":0,\"tasks_by_user_story_count\":0,\"note\":\"Filtered out user stories with Done status and tasks with done status\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755582055.902689, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.debug: Gate check for permission: addUserStory_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582055.908581, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582055.909525, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582055.909575, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.debug: Gate check for permission: beginSprint_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582055.914139, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582055.914869, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582055.914915, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.debug: Gate check for permission: addToSprint_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582055.915236, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582055.916105, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582055.916154, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.debug: Gate check for permission: endSprint_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582055.916423, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582055.917154, "xdebug_link": null, "collector": "log"}, {"message": "[13:40:55] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582055.917199, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755582055.322535, "end": 1755582055.927239, "duration": 0.6047039031982422, "duration_str": "605ms", "measures": [{"label": "Booting", "start": 1755582055.322535, "relative_start": 0, "end": 1755582055.753737, "relative_end": 1755582055.753737, "duration": 0.4312019348144531, "duration_str": "431ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755582055.753748, "relative_start": 0.4312129020690918, "end": 1755582055.927241, "relative_end": 2.1457672119140625e-06, "duration": 0.1734931468963623, "duration_str": "173ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24093664, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "backlogTest.index (\\resources\\views\\backlogTest\\index.blade.php)", "param_count": 4, "params": ["project", "userStories", "tasksByUserStory", "activeSprint"], "type": "blade"}]}, "route": {"uri": "GET backlogTest/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\BacklogController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "backlogTest.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\BacklogController.php&line=19\">\\app\\Http\\Controllers\\BacklogController.php:19-239</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.0027, "accumulated_duration_str": "2.7ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 15.185}, {"sql": "select * from `projects` where `projects`.`id` = '43' limit 1", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 24}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:24", "connection": "sagile", "start_percent": 15.185, "width_percent": 14.444}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` = 1 limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 30}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:30", "connection": "sagile", "start_percent": 29.63, "width_percent": 13.333}, {"sql": "select * from `statuses` where `project_id` = '43' and `title` = 'Done' limit 1", "type": "query", "params": [], "bindings": ["43", "Done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 75}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:75", "connection": "sagile", "start_percent": 42.963, "width_percent": 16.296}, {"sql": "select * from `user_stories` where `proj_id` = '43' and `status_id` != 212", "type": "query", "params": [], "bindings": ["43", "212"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 82}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:82", "connection": "sagile", "start_percent": 59.259, "width_percent": 14.074}, {"sql": "select exists(select * from `tasks` where `userstory_id` = 57 and (`sprint_id` != 45 or `sprint_id` is null)) as `exists`", "type": "query", "params": [], "bindings": ["57", "45"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 117}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:117", "connection": "sagile", "start_percent": 73.333, "width_percent": 12.963}, {"sql": "select * from `statuses` where `project_id` = '43' and `slug` = 'done' limit 1", "type": "query", "params": [], "bindings": ["43", "done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 173}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:173", "connection": "sagile", "start_percent": 86.296, "width_percent": 13.704}]}, "models": {"data": {"App\\UserStory": 1, "App\\Status": 2, "App\\Sprint": 1, "App\\Project": 1, "App\\User": 1}, "count": 6}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 4, "messages": [{"message": "[\n  ability => addUserStory_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">addUserStory_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582055.912359, "xdebug_link": null}, {"message": "[\n  ability => beginSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1218430264 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">beginSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218430264\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582055.915033, "xdebug_link": null}, {"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1061573072 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1061573072\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582055.916287, "xdebug_link": null}, {"message": "[\n  ability => endSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1303257025 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">endSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1303257025\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582055.91732, "xdebug_link": null}]}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backlogTest/43\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755581799\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/backlogTest/43", "status_code": "<pre class=sf-dump id=sf-dump-1031177334 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1031177334\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2045760537 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2045760537\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1559180001 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1559180001\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1190094241 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IklkanlUcnFUSFhOa21Nemx5eE9Va0E9PSIsInZhbHVlIjoiaGFOcVpRci9kak9vK0w2d2NLNlpwMTEwNWlaL3l6YmI2QXVuYncwRWFxWkhxeG1oRCt3Um1rWVlTYm5oaFlNWitKYk1zR0ZFbnFjN1JWZW5TZ0RRWVY0RUoxbXRwSWdxOVNpc0F1SEE4bzZtNVBYTzNZVnczWi9TeThqbDdmelUiLCJtYWMiOiI4ZTgwNmQ5ZmUxOTYwYjNiNThhYjhiNTFhYjAyMDg5MDBiYjhlYzYzN2Y0ZTg1MzUxMTBmZmUzM2QzODA3N2I0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InNuUWU1TzVPTzBjZTRLOVI5UzY0bFE9PSIsInZhbHVlIjoiakNBRUQ2UVYyRzJKbWNzMHVyTVFYdGZQNjJHQUpPU3FPVFJpL1dsZDZDeklFVFc2dmx1VWtJTE5pRU95UmF2V1VwbHBEQ1VhL3ZwajBlbGcwM21GRndmYmhnMmRWTE5PUXRxZnZRQVRUTCtKUEdGQXdMVnF0aThTbXJUTjdiOGgiLCJtYWMiOiJjMzkxNTMzMWIzNmM5ODFiYmIwMDA5NDQ1YjlkNzRmMzNiOWJmN2M3MmRiMGIxM2FhYWVhNTA4N2QwMmFiZDFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1190094241\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1728045552 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">53910</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/43</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/43</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/index.php/backlogTest/43</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/43</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IklkanlUcnFUSFhOa21Nemx5eE9Va0E9PSIsInZhbHVlIjoiaGFOcVpRci9kak9vK0w2d2NLNlpwMTEwNWlaL3l6YmI2QXVuYncwRWFxWkhxeG1oRCt3Um1rWVlTYm5oaFlNWitKYk1zR0ZFbnFjN1JWZW5TZ0RRWVY0RUoxbXRwSWdxOVNpc0F1SEE4bzZtNVBYTzNZVnczWi9TeThqbDdmelUiLCJtYWMiOiI4ZTgwNmQ5ZmUxOTYwYjNiNThhYjhiNTFhYjAyMDg5MDBiYjhlYzYzN2Y0ZTg1MzUxMTBmZmUzM2QzODA3N2I0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InNuUWU1TzVPTzBjZTRLOVI5UzY0bFE9PSIsInZhbHVlIjoiakNBRUQ2UVYyRzJKbWNzMHVyTVFYdGZQNjJHQUpPU3FPVFJpL1dsZDZDeklFVFc2dmx1VWtJTE5pRU95UmF2V1VwbHBEQ1VhL3ZwajBlbGcwM21GRndmYmhnMmRWTE5PUXRxZnZRQVRUTCtKUEdGQXdMVnF0aThTbXJUTjdiOGgiLCJtYWMiOiJjMzkxNTMzMWIzNmM5ODFiYmIwMDA5NDQ1YjlkNzRmMzNiOWJmN2M3MmRiMGIxM2FhYWVhNTA4N2QwMmFiZDFiIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755582055.3225</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755582055</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1728045552\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1261105330 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1261105330\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-767067640 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:40:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlhqNVVEOUtoVFkzcmxHZ21VR2hzZVE9PSIsInZhbHVlIjoiQ2ozd3dRcHhWU1VtSXRjU1FRNlZ6TVZ1Mi9TSVlOdEVXb1dFczlhZmppZzJyR3BHU0U3Z2tFR3BvK3l0bTUxUXhVT1NJZE9hZjdQTFZ5YkM2NEphTDZKNTdaQW1xNEZVUXZPY3lnTjFhSklNUlFJZ0ZOelJ4N0k5bXBTSlQ5UG4iLCJtYWMiOiJiNzdiOTBmYzY4MjI3MTQzMDdjODMyMmVhNmZkOGI0OGQzNWJkODg1ZTZkNjdkNmE5Nzg0NGExYmUyM2I4MDA0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:40:55 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InJpSVVaQjcxZWlZOHFTSkJmVkJwWWc9PSIsInZhbHVlIjoidmFwQ28yMnFFSW1HR2tCcndyQWVxMSt2K00xNDZVZE1SSVNwZG45TnhINFphRG50ekZ3YnZqenNiT3ZySjdaSXNjbE02bnpGeW0zOVJ5Wmk3aVI2UlFLalQ5RDdqYW50MTRVRlF4K3h4OGZZay9ydTJCNG91NDBQR1N4UTlNZm0iLCJtYWMiOiJmZmI4M2Q4YWMzZGUyYTUwZTc0NjE0YmRmMTg2NGM4ZTY0OWI5NjU4OTU2OTE4MWJiNDVkYjg1YmIxZmNlMDZjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:40:55 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlhqNVVEOUtoVFkzcmxHZ21VR2hzZVE9PSIsInZhbHVlIjoiQ2ozd3dRcHhWU1VtSXRjU1FRNlZ6TVZ1Mi9TSVlOdEVXb1dFczlhZmppZzJyR3BHU0U3Z2tFR3BvK3l0bTUxUXhVT1NJZE9hZjdQTFZ5YkM2NEphTDZKNTdaQW1xNEZVUXZPY3lnTjFhSklNUlFJZ0ZOelJ4N0k5bXBTSlQ5UG4iLCJtYWMiOiJiNzdiOTBmYzY4MjI3MTQzMDdjODMyMmVhNmZkOGI0OGQzNWJkODg1ZTZkNjdkNmE5Nzg0NGExYmUyM2I4MDA0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:40:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InJpSVVaQjcxZWlZOHFTSkJmVkJwWWc9PSIsInZhbHVlIjoidmFwQ28yMnFFSW1HR2tCcndyQWVxMSt2K00xNDZVZE1SSVNwZG45TnhINFphRG50ekZ3YnZqenNiT3ZySjdaSXNjbE02bnpGeW0zOVJ5Wmk3aVI2UlFLalQ5RDdqYW50MTRVRlF4K3h4OGZZay9ydTJCNG91NDBQR1N4UTlNZm0iLCJtYWMiOiJmZmI4M2Q4YWMzZGUyYTUwZTc0NjE0YmRmMTg2NGM4ZTY0OWI5NjU4OTU2OTE4MWJiNDVkYjg1YmIxZmNlMDZjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:40:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-767067640\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1847011877 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755581799</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1847011877\", {\"maxDepth\":0})</script>\n"}}