{"__meta": {"id": "X3dde97c89e7579bfa3f37420b3b8aa89", "datetime": "2025-08-18 23:42:57", "utime": 1755531777.803644, "method": "GET", "uri": "/tvt/show/42", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:42:57] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755531777.37451, "xdebug_link": null, "collector": "log"}, {"message": "[23:42:57] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/tvt/show/42", "message_html": null, "is_string": false, "label": "debug", "time": 1755531777.444251, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755531777.015013, "end": 1755531777.803667, "duration": 0.788654088973999, "duration_str": "789ms", "measures": [{"label": "Booting", "start": 1755531777.015013, "relative_start": 0, "end": 1755531777.351092, "relative_end": 1755531777.351092, "duration": 0.33607912063598633, "duration_str": "336ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755531777.351109, "relative_start": 0.33609604835510254, "end": 1755531777.803669, "relative_end": 1.9073486328125e-06, "duration": 0.4525599479675293, "duration_str": "453ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25707000, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 17, "templates": [{"name": "tvt.view (\\resources\\views\\tvt\\view.blade.php)", "param_count": 6, "params": ["title", "project", "results", "sprints", "generalNfrs", "specificNfrs"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\resources\\views\\vendor\\pagination\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "result", "sprintNumber"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "result", "sprintNumber"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "result", "sprintNumber"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "result", "sprintNumber"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "result", "sprintNumber"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 27, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "result", "sprintNumber", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 27, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "result", "sprintNumber", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 27, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "result", "sprintNumber", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 27, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "result", "sprintNumber", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 28, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "result", "sprintNumber", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 28, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "result", "sprintNumber", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 28, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "result", "sprintNumber", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET tvt/show/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\TVTController@show", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "tvt.show", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TVTController.php&line=26\">\\app\\Http\\Controllers\\TVTController.php:26-55</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.005320000000000001, "accumulated_duration_str": "5.32ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 9.398}, {"sql": "select * from `projects` where `projects`.`id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Project.php", "line": 86}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\TVTController.php", "line": 29}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\app\\Project.php:86", "connection": "sagile", "start_percent": 9.398, "width_percent": 12.406}, {"sql": "select distinct `sprint_name` from `sprint` where exists (select * from `user_stories` where `sprint`.`sprint_id` = `user_stories`.`sprint_id` and `proj_id` = '42')", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Sprint.php", "line": 57}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TVTController.php", "line": 32}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\app\\Sprint.php:57", "connection": "sagile", "start_percent": 21.805, "width_percent": 10.338}, {"sql": "select `general_nfr`, `general_nfr_id` from `generalnfr`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\GeneralNFR.php", "line": 49}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\TVTController.php", "line": 33}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\GeneralNFR.php:49", "connection": "sagile", "start_percent": 32.143, "width_percent": 6.955}, {"sql": "select `specific_nfr`, `nfr_id` from `nfr`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\SpecificNFR.php", "line": 60}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\TVTController.php", "line": 34}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\SpecificNFR.php:60", "connection": "sagile", "start_percent": 39.098, "width_percent": 6.579}, {"sql": "select count(*) as aggregate from `user_story_general_nfr` where exists (select * from `user_stories` where `user_story_general_nfr`.`user_story_id` = `user_stories`.`u_id` and `proj_id` = '42')", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\UserStoryGeneralNfr.php", "line": 178}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TVTController.php", "line": 44}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\UserStoryGeneralNfr.php:178", "connection": "sagile", "start_percent": 45.677, "width_percent": 9.398}, {"sql": "select * from `user_story_general_nfr` where exists (select * from `user_stories` where `user_story_general_nfr`.`user_story_id` = `user_stories`.`u_id` and `proj_id` = '42') limit 8 offset 0", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\UserStoryGeneralNfr.php", "line": 178}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TVTController.php", "line": 44}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "\\app\\UserStoryGeneralNfr.php:178", "connection": "sagile", "start_percent": 55.075, "width_percent": 10.526}, {"sql": "select * from `user_stories` where `user_stories`.`u_id` in (45)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\UserStoryGeneralNfr.php", "line": 178}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\TVTController.php", "line": 44}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\UserStoryGeneralNfr.php:178", "connection": "sagile", "start_percent": 65.602, "width_percent": 8.459}, {"sql": "select * from `sprint` where `sprint`.`sprint_id` in (0)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\UserStoryGeneralNfr.php", "line": 178}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\TVTController.php", "line": 44}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\UserStoryGeneralNfr.php:178", "connection": "sagile", "start_percent": 74.06, "width_percent": 7.143}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\UserStoryGeneralNfr.php", "line": 178}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\TVTController.php", "line": 44}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\UserStoryGeneralNfr.php:178", "connection": "sagile", "start_percent": 81.203, "width_percent": 7.707}, {"sql": "select * from `nfr` where `nfr`.`nfr_id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\UserStoryGeneralNfr.php", "line": 178}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\TVTController.php", "line": 44}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\app\\UserStoryGeneralNfr.php:178", "connection": "sagile", "start_percent": 88.91, "width_percent": 11.09}]}, "models": {"data": {"App\\SpecificNFR": 3, "App\\GeneralNFR": 2, "App\\UserStory": 1, "App\\UserStoryGeneralNfr": 3, "App\\Project": 1, "App\\User": 1}, "count": 11}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/tvt/show/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/tvt/show/42", "status_code": "<pre class=sf-dump id=sf-dump-900269818 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-900269818\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-323466711 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-323466711\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1562784406 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1562784406\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1612126607 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://127.0.0.1:8000/tvt</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik0xN0xldHBGYmlRQ3NjSi9aeFJIb1E9PSIsInZhbHVlIjoiQXVuc3BDSCtaQU93Z0VRWTA2c0lEb0VoeXFUUW9nQmFlL3Q1QURYZDNabjByenR4K1d0M0NxT3I4bTlwTUoxTmVWK3pDd1JjeTh3eUxOc0F6emd3bkJBTTJSc040TktwMDVXTTdhbWVkTFkySDFsSjJML2twVVFTVTBidXphTjciLCJtYWMiOiI2YmQ4MzhhODk4MTZhMDQxNzMzYjBhYjZhZDg3Y2QzYzk4NmE1MDY1ODZhMzMwMDlmZmY2ZDFjMmY1ZWI2N2JkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IldrYXo5QzkwemR0aTBZbUZ3RjJlVEE9PSIsInZhbHVlIjoiY2NaS3lQc3c5RnNxZUVjeXdXNFB2cUpqYnhFQStvN2IrWE1MQzJST3NkbzJiQWpzelQ5anNMRFRqUkpQVFlidlNkUDNwNkZleUtMZFBCUTJLeXltOGFtemZiKytNSDk1YWQ5bWRRNlFvbHlmNEhma2V4aE9ieng4QUFtWFNjN3YiLCJtYWMiOiJkYzY5YTM4NTBmYWQ3YTVjZTVhZmRkZmI2Mzc1ZGU4NDUxNzA5NTIwYjMwNmM3ODM1YzhmMjlmMjc3OGZkYzkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1612126607\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-974742021 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">64098</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/tvt/show/42</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/tvt/show/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/index.php/tvt/show/42</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://127.0.0.1:8000/tvt</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik0xN0xldHBGYmlRQ3NjSi9aeFJIb1E9PSIsInZhbHVlIjoiQXVuc3BDSCtaQU93Z0VRWTA2c0lEb0VoeXFUUW9nQmFlL3Q1QURYZDNabjByenR4K1d0M0NxT3I4bTlwTUoxTmVWK3pDd1JjeTh3eUxOc0F6emd3bkJBTTJSc040TktwMDVXTTdhbWVkTFkySDFsSjJML2twVVFTVTBidXphTjciLCJtYWMiOiI2YmQ4MzhhODk4MTZhMDQxNzMzYjBhYjZhZDg3Y2QzYzk4NmE1MDY1ODZhMzMwMDlmZmY2ZDFjMmY1ZWI2N2JkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IldrYXo5QzkwemR0aTBZbUZ3RjJlVEE9PSIsInZhbHVlIjoiY2NaS3lQc3c5RnNxZUVjeXdXNFB2cUpqYnhFQStvN2IrWE1MQzJST3NkbzJiQWpzelQ5anNMRFRqUkpQVFlidlNkUDNwNkZleUtMZFBCUTJLeXltOGFtemZiKytNSDk1YWQ5bWRRNlFvbHlmNEhma2V4aE9ieng4QUFtWFNjN3YiLCJtYWMiOiJkYzY5YTM4NTBmYWQ3YTVjZTVhZmRkZmI2Mzc1ZGU4NDUxNzA5NTIwYjMwNmM3ODM1YzhmMjlmMjc3OGZkYzkzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755531777.015</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755531777</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-974742021\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-435940531 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-435940531\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-563493575 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:42:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjFKSG8wc2g0YmFhZE5WQmU5R095Y0E9PSIsInZhbHVlIjoicmkyUjMwQnE3MmtnRXROeEhCcTlEMUQ4SmN2OUJrLzNpVmRaSGJCdU1GdUFqT1YrY0hrdnFQVXpIVmtMY3JGMWgxSEhxK21xbUVtdCswUDl5cnlYZjFyNGlhaWJQUkl4UWlsNzYwZnBGK20wdWhVdTZvWjBaUm5YTk9NTFArQU4iLCJtYWMiOiJmNzBlYWNlZWUxYjk1NjEyZDg2NTZlNTlmZTI5MmUyN2Q3NWI1MDI3MmI2MzFmNGVjYmM4YzMyMDA0ZjFlYjMxIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:42:57 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InN6eFN1bndjM3dVRm4yQUg0VTNqUFE9PSIsInZhbHVlIjoiaURDOWRORStsS2pwQVJyRytyZE1XQUU0amdISWl0YlVwblI4U2VoRFhpWDkzRElCQzZ5WXlQSnZuc2pNdzAyOXNMRVpnWEd5Snh2N2xVTllIS3Q5MUYvbUozTC9xSWNZRnYrK2IySFJvZ1VyanRnaDN6MGxLalhGSTlDWjdaWUciLCJtYWMiOiIzZjA2ZWViMDcwN2Y3NTUyMGNlZWI3ZGYyNzAyZjY5Y2I0ZTFlZTIzZTBlN2M0MjQ5YWE0MzY0ODAyNzY3ZWYxIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:42:57 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjFKSG8wc2g0YmFhZE5WQmU5R095Y0E9PSIsInZhbHVlIjoicmkyUjMwQnE3MmtnRXROeEhCcTlEMUQ4SmN2OUJrLzNpVmRaSGJCdU1GdUFqT1YrY0hrdnFQVXpIVmtMY3JGMWgxSEhxK21xbUVtdCswUDl5cnlYZjFyNGlhaWJQUkl4UWlsNzYwZnBGK20wdWhVdTZvWjBaUm5YTk9NTFArQU4iLCJtYWMiOiJmNzBlYWNlZWUxYjk1NjEyZDg2NTZlNTlmZTI5MmUyN2Q3NWI1MDI3MmI2MzFmNGVjYmM4YzMyMDA0ZjFlYjMxIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:42:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InN6eFN1bndjM3dVRm4yQUg0VTNqUFE9PSIsInZhbHVlIjoiaURDOWRORStsS2pwQVJyRytyZE1XQUU0amdISWl0YlVwblI4U2VoRFhpWDkzRElCQzZ5WXlQSnZuc2pNdzAyOXNMRVpnWEd5Snh2N2xVTllIS3Q5MUYvbUozTC9xSWNZRnYrK2IySFJvZ1VyanRnaDN6MGxLalhGSTlDWjdaWUciLCJtYWMiOiIzZjA2ZWViMDcwN2Y3NTUyMGNlZWI3ZGYyNzAyZjY5Y2I0ZTFlZTIzZTBlN2M0MjQ5YWE0MzY0ODAyNzY3ZWYxIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:42:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-563493575\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-872528148 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/tvt/show/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-872528148\", {\"maxDepth\":0})</script>\n"}}