{"__meta": {"id": "Xc11d3c15831a73de1ea2c2bac7f45d60", "datetime": "2025-08-18 23:11:04", "utime": 1755529864.50928, "method": "GET", "uri": "/projects/42", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 95, "messages": [{"message": "[23:11:04] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755529864.102023, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: LoadUserPermissions: Extracted project ID: 42 from URL: http://127.0.0.1:8000/projects/42", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.280786, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: LoadUserPermissions: Found project 42 with team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.302396, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: LoadUserPermissions: Looking for team role map for team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.302485, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: LoadUserPermissions: Available teams in role map: [\"i<PERSON><PERSON>'s team\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.302562, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: LoadUserPermissions: Found team data structure: multiple_projects", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.302628, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: LoadUserPermissions: Set permissions for user <PERSON><PERSON><PERSON> in team i<PERSON><PERSON>'s team for project 42: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.302747, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.373092, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.374684, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.37484, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.374932, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_burndown on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.384774, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.386014, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.386104, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.38619, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.386616, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.388657, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.388746, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.388825, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.389182, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.390354, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.390438, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.390515, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_forum on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.390893, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.392058, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.392139, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.392214, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_bugtracking on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.392539, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.394401, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.394485, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.394563, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_roles on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.39492, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.396135, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.396216, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.396291, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_status on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.396613, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.397831, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.397919, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.398, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.398323, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.39948, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.39956, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.399634, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_details on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.399943, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.401181, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.401262, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.401416, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.401919, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.403612, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.403727, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.403825, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_burndown on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.404323, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.405787, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.405875, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.405979, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.406421, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.407711, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.407794, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.407869, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.40827, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.40953, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.409616, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.409708, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_forum on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.410279, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.411868, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.412032, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.412139, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_bugtracking on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.412582, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.413898, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.413985, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.41409, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_roles on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.414517, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.415819, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.415903, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.415986, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_status on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.416377, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.417706, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.417797, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.417913, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.418404, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.41972, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.419799, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.419894, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: view_details on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.420447, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.421822, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.421909, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.421996, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: edit_details on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.422454, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.423653, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.423735, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.42382, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Gate check for permission: delete_details on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.424235, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.425548, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Using cached permissions for team i<PERSON><PERSON>'s team: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.425634, "xdebug_link": null, "collector": "log"}, {"message": "[23:11:04] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755529864.425723, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755529863.16894, "end": 1755529864.509462, "duration": 1.340522050857544, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1755529863.16894, "relative_start": 0, "end": 1755529864.04035, "relative_end": 1755529864.04035, "duration": 0.8714098930358887, "duration_str": "871ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755529864.040381, "relative_start": 0.8714408874511719, "end": 1755529864.509465, "relative_end": 2.86102294921875e-06, "duration": 0.4690840244293213, "duration_str": "469ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24248400, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 10, "templates": [{"name": "project.details (\\resources\\views\\project\\details.blade.php)", "param_count": 1, "params": ["project"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 5, "params": ["__env", "app", "menuData", "errors", "project"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 5, "params": ["__env", "app", "menuData", "errors", "project"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET projects/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\ProductFeatureController@details", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "projects.details", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProductFeatureController.php&line=133\">\\app\\Http\\Controllers\\ProductFeatureController.php:133-153</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0036300000000000004, "accumulated_duration_str": "3.63ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 20.661}, {"sql": "select `id`, `team_name` from `projects` where `id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 167}, {"index": 14, "namespace": "middleware", "name": "load.permissions", "line": 33}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 16, "namespace": "middleware", "name": "bindings", "line": 50}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "middleware::load.permissions:167", "connection": "sagile", "start_percent": 20.661, "width_percent": 25.895}, {"sql": "select `team_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 140}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:140", "connection": "sagile", "start_percent": 46.556, "width_percent": 25.344}, {"sql": "select * from `projects` where `id` = '42' and `team_name` in ('iv<PERSON>\\'s team', 'ivlyn\\'s team') limit 1", "type": "query", "params": [], "bindings": ["42", "ivlyn&#039;s team", "ivlyn&#039;s team"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 146}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00102, "duration_str": "1.02ms", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:146", "connection": "sagile", "start_percent": 71.901, "width_percent": 28.099}]}, "models": {"data": {"App\\Project": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 22, "messages": [{"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.38248, "xdebug_link": null}, {"message": "[\n  ability => view_burndown,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1410961143 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_burndown</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1410961143\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.386354, "xdebug_link": null}, {"message": "[\n  ability => view_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2138913046 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138913046\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.388985, "xdebug_link": null}, {"message": "[\n  ability => view_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-446101473 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-446101473\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.390657, "xdebug_link": null}, {"message": "[\n  ability => view_forum,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-685265364 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_forum</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-685265364\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.39236, "xdebug_link": null}, {"message": "[\n  ability => view_bugtracking,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-404847279 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_bugtracking</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-404847279\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.394717, "xdebug_link": null}, {"message": "[\n  ability => view_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-732292765 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-732292765\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.396438, "xdebug_link": null}, {"message": "[\n  ability => view_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1906814447 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1906814447\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.398146, "xdebug_link": null}, {"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-920416412 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920416412\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.399771, "xdebug_link": null}, {"message": "[\n  ability => view_details,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1903818686 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1903818686\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.401665, "xdebug_link": null}, {"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1544777735 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1544777735\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.404031, "xdebug_link": null}, {"message": "[\n  ability => view_burndown,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1850528902 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_burndown</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1850528902\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.40615, "xdebug_link": null}, {"message": "[\n  ability => view_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2062547823 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2062547823\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.408013, "xdebug_link": null}, {"message": "[\n  ability => view_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-795729897 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-795729897\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.409964, "xdebug_link": null}, {"message": "[\n  ability => view_forum,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1750469558 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_forum</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1750469558\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.41231, "xdebug_link": null}, {"message": "[\n  ability => view_bugtracking,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1342746037 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_bugtracking</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342746037\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.414273, "xdebug_link": null}, {"message": "[\n  ability => view_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1603504079 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1603504079\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.416143, "xdebug_link": null}, {"message": "[\n  ability => view_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1409400599 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1409400599\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.418116, "xdebug_link": null}, {"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1980291142 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980291142\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.420143, "xdebug_link": null}, {"message": "[\n  ability => view_details,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1983228558 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1983228558\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.422157, "xdebug_link": null}, {"message": "[\n  ability => edit_details,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1827486410 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">edit_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827486410\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.42396, "xdebug_link": null}, {"message": "[\n  ability => delete_details,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1982603321 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">delete_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1982603321\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755529864.425921, "xdebug_link": null}]}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/projects/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755527110\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/projects/42", "status_code": "<pre class=sf-dump id=sf-dump-196198309 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-196198309\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1753727907 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1753727907\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1892122957 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1892122957\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2091841790 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImJiSmhTOGoxTDE3aHJPQ3U5OWJ1Q1E9PSIsInZhbHVlIjoiUGNaNThOcldBTmJVeGU5R1lkUnMrb1psdFNsWGY5b0tkRjhGVUUveS9OK093bmp0Z0pEMkJHVnlsdFFjNC9OcktjR0p3bFZwUCtYaURob3NWKzJGalE0WUZEMUlhcGxXMmxzZ0NrN3lHTzY4RkR5eCt2K1dUNW1qaTdFWTJLREwiLCJtYWMiOiJmODM4YTliZDAyNGMyNzJmMzRhMDU0ODZkZmVkMjViODFiYWE1OTk3ZjZjYWI3ZGFjNWZhN2YyMTc2YzEzYjcxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im9yY05Cc1RsSHZFUEh6eGQ4b0plVnc9PSIsInZhbHVlIjoic3d6T29JNmZLRVhpK1hQSXhIalUzT3NCN093a1luZm1MUmhJZ3RCdjRNSUl4Szc1Qk5najhWVkgxZEJ3ZE1DcXFxa21ZRlpjTFBzdHh2OHh6ajJGQlk1c200eWQzcEpvWjh3WXY1R2lsNFpvNFFMSmRJVVR2Z0FPQlVKR212SS8iLCJtYWMiOiJmOGIyYjA2NzNkMmQxMjQ0OTQxMWQxMTEyYTBlMTE3YzZiYmU5ODk1ODY5MjAxZjMzY2VhNDEzOWIzYTc4ZDcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091841790\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1676278840 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52218</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/projects/42</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/projects/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/index.php/projects/42</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImJiSmhTOGoxTDE3aHJPQ3U5OWJ1Q1E9PSIsInZhbHVlIjoiUGNaNThOcldBTmJVeGU5R1lkUnMrb1psdFNsWGY5b0tkRjhGVUUveS9OK093bmp0Z0pEMkJHVnlsdFFjNC9OcktjR0p3bFZwUCtYaURob3NWKzJGalE0WUZEMUlhcGxXMmxzZ0NrN3lHTzY4RkR5eCt2K1dUNW1qaTdFWTJLREwiLCJtYWMiOiJmODM4YTliZDAyNGMyNzJmMzRhMDU0ODZkZmVkMjViODFiYWE1OTk3ZjZjYWI3ZGFjNWZhN2YyMTc2YzEzYjcxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im9yY05Cc1RsSHZFUEh6eGQ4b0plVnc9PSIsInZhbHVlIjoic3d6T29JNmZLRVhpK1hQSXhIalUzT3NCN093a1luZm1MUmhJZ3RCdjRNSUl4Szc1Qk5najhWVkgxZEJ3ZE1DcXFxa21ZRlpjTFBzdHh2OHh6ajJGQlk1c200eWQzcEpvWjh3WXY1R2lsNFpvNFFMSmRJVVR2Z0FPQlVKR212SS8iLCJtYWMiOiJmOGIyYjA2NzNkMmQxMjQ0OTQxMWQxMTEyYTBlMTE3YzZiYmU5ODk1ODY5MjAxZjMzY2VhNDEzOWIzYTc4ZDcyIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755529863.1689</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755529863</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1676278840\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-404935420 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-404935420\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1170449761 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:11:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlRmcThSRTRPbVpwTlpNUnZUUnVUQ1E9PSIsInZhbHVlIjoid2poZllwdmVTZWk1TEg0dkhsWEFyZHNXNXcwT3pLczZLMnZtUTdzUlJELzhtWDF6Q0hoaE1YcEFlV3hXeVBPdFBWVnFCaU9wUkljd0tBVS9wb2pERy9Ud1kvMnMvUVRuZUZ0SUY5OTZEN0hwUENHa1YyU3FBZThkUmNQeUY4N0oiLCJtYWMiOiJiZGVmMGU5NWVjMTYzMzY0MGFhY2FjNjZkZDdlMTdiNTY2ZGFhZmQzZmFlYmU4ZGYwYzA5YmY1ZDVhYmU1NTQyIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:11:04 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlBRWU1pR0ZxWkhTNUVSMmF6OTRzM3c9PSIsInZhbHVlIjoiQUh4TDM3NGNGRnBFMVhEQllHUEZmVzRZSjlpSUZ0L0orOUh3Z0tadVQycG9YOGpib0I2V3hrMjRGdEd6Nk1aQ2M0TDRtMzBxVGpiZTdkY0c4TFpNWlZWeEdhNkM0QWxuWU5Obld4eDU4MjBQd1l6TDJmc0NXQStjVTZSdkx5amUiLCJtYWMiOiIyYWVjMzI4MTJjYmMzNDUxMGFjNTY2ZmU5MWU1NjFjMjMyMWI1MTFjNTAxNjQ2MWM5Mjk5YzEyZGE2NTFkYjIzIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:11:04 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlRmcThSRTRPbVpwTlpNUnZUUnVUQ1E9PSIsInZhbHVlIjoid2poZllwdmVTZWk1TEg0dkhsWEFyZHNXNXcwT3pLczZLMnZtUTdzUlJELzhtWDF6Q0hoaE1YcEFlV3hXeVBPdFBWVnFCaU9wUkljd0tBVS9wb2pERy9Ud1kvMnMvUVRuZUZ0SUY5OTZEN0hwUENHa1YyU3FBZThkUmNQeUY4N0oiLCJtYWMiOiJiZGVmMGU5NWVjMTYzMzY0MGFhY2FjNjZkZDdlMTdiNTY2ZGFhZmQzZmFlYmU4ZGYwYzA5YmY1ZDVhYmU1NTQyIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:11:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlBRWU1pR0ZxWkhTNUVSMmF6OTRzM3c9PSIsInZhbHVlIjoiQUh4TDM3NGNGRnBFMVhEQllHUEZmVzRZSjlpSUZ0L0orOUh3Z0tadVQycG9YOGpib0I2V3hrMjRGdEd6Nk1aQ2M0TDRtMzBxVGpiZTdkY0c4TFpNWlZWeEdhNkM0QWxuWU5Obld4eDU4MjBQd1l6TDJmc0NXQStjVTZSdkx5amUiLCJtYWMiOiIyYWVjMzI4MTJjYmMzNDUxMGFjNTY2ZmU5MWU1NjFjMjMyMWI1MTFjNTAxNjQ2MWM5Mjk5YzEyZGE2NTFkYjIzIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:11:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1170449761\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-19114380 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755527110</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-19114380\", {\"maxDepth\":0})</script>\n"}}