
<?php echo $__env->make('inc.style', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('inc.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->startSection('content'); ?>
<?php echo $__env->make('inc.title', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<br>

<!-- Filter Form -->
<form method="GET" action="<?php echo e(route('tvt.show', $project->id)); ?>" class="mb-3">
    <div class="row">
        <!-- Sprint Filter -->
        <div class="col-md-4">
            <div class="form-group">
                <label for="sprint">Filter by Sprint</label>
                <select name="sprint" id="sprint" class="form-control" onchange="this.form.submit()">
                    <option value="">All</option>
                    <?php $__currentLoopData = $sprints; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sprint): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($sprint); ?>" <?php echo e(request()->input('sprint') == $sprint ? 'selected' : ''); ?>>
                            <?php echo e($sprint); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
        </div>

        <!-- Specific NFR Filter -->
        <div class="col-md-4">
            <div class="form-group">
                <label for="nfr">Filter by NFR</label>
                <select name="nfr" id="nfr" class="form-control" onchange="this.form.submit()">
                    <option value="">All</option>
                    <?php $__currentLoopData = $specificNfrs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $specificNfrId => $specificNfrName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($specificNfrId); ?>" <?php echo e(request()->input('nfr') == $specificNfrId ? 'selected' : ''); ?>>
                            <?php echo e($specificNfrName); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
        </div>

        <!-- General NFR Filter -->
        <div class="col-md-4">
            <div class="form-group">
                <label for="general_nfr">Filter by QAW</label>
                <select name="general_nfr" id="general_nfr" class="form-control" onchange="this.form.submit()">
                    <option value="">All</option>
                    <?php $__currentLoopData = $generalNfrs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $generalNfrId => $generalNfrName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($generalNfrId); ?>" <?php echo e(request()->input('general_nfr') == $generalNfrId ? 'selected' : ''); ?>>
                            <?php echo e($generalNfrName); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
        </div>
    </div>
</form>

<!-- Data Table -->
<table id="userStoriesTable" class="table table-striped mt-3">
    <thead>
        <tr>
            <th>Sprint</th>
            <th>Backlog Panel</th>
            <th>User Story Panel</th>
            <th>FR->NFR</th>
            <th>QAW</th>
        </tr>
    </thead>
    <tbody>
        <?php $__empty_1 = true; $__currentLoopData = $results; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $result): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <tr>
            <!-- Check if sprint is not null before displaying sprint_name -->
            <td><?php echo e($result->userStory->sprint ? $result->userStory->sprint->sprint_name : 'No Sprint'); ?></td>
            <td>
                <?php
                    $sprintNumber = $result->userStory->sprint ? preg_replace('/\D/', '', $result->userStory->sprint->sprint_name) : ''; // Extract sprint number if sprint exists
                ?>
                <?php echo e($sprintNumber ? 'BG' . $sprintNumber : 'No Backlog'); ?> <!-- Display formatted backlog panel -->
            </td>
            <td><?php echo e($result->userStory->user_story); ?></td>
            <td>
                 <?php if(isset($result->userStory->means)): ?> 
                    <strong><?php echo e($result->userStory->means); ?></strong> <!-- Display means in bold -->
                <?php endif; ?>
                <br>
                <?php echo e($result->specificNfr->specific_nfr); ?> <!-- Display specific NFR -->
            </td>
            <td><?php echo e($result->generalNfr->general_nfr); ?></td>
        </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <tr>
            <td colspan="5" class="text-center">No data found.</td>
        </tr>
        <?php endif; ?>
    </tbody>
</table>

<!-- Pagination -->
<div class="pagination-container">
    <ul class="pagination justify-content-center">
        <?php echo e($results->appends(request()->query())->links('pagination::bootstrap-4')); ?>

    </ul>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/tvt/view.blade.php ENDPATH**/ ?>