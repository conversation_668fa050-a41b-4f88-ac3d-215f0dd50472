{"__meta": {"id": "Xd0a4db629b2b6ce948cd21c7394d1314", "datetime": "2025-08-19 14:10:35", "utime": 1755583835.460522, "method": "GET", "uri": "/project-assignments", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[14:10:35] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755583835.067561, "xdebug_link": null, "collector": "log"}, {"message": "[14:10:35] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/project-assignments", "message_html": null, "is_string": false, "label": "debug", "time": 1755583835.29941, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755583834.535835, "end": 1755583835.460542, "duration": 0.9247069358825684, "duration_str": "925ms", "measures": [{"label": "Booting", "start": 1755583834.535835, "relative_start": 0, "end": 1755583835.035395, "relative_end": 1755583835.035395, "duration": 0.4995598793029785, "duration_str": "500ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755583835.035407, "relative_start": 0.4995720386505127, "end": 1755583835.460544, "relative_end": 2.1457672119140625e-06, "duration": 0.4251370429992676, "duration_str": "425ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25503344, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 14, "templates": [{"name": "project-assignments.index (\\resources\\views\\project-assignments\\index.blade.php)", "param_count": 3, "params": ["projects", "projectAssignments", "managedTeams"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET project-assignments", "middleware": "web", "controller": "App\\Http\\Controllers\\ProjectAssignmentController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "project-assignments.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProjectAssignmentController.php&line=18\">\\app\\Http\\Controllers\\ProjectAssignmentController.php:18-52</a>"}, "queries": {"nb_statements": 19, "nb_failed_statements": 0, "accumulated_duration": 0.007360000000000001, "accumulated_duration_str": "7.36ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 6.114}, {"sql": "select exists(select * from `user_role` where `user_id` = 30 and `role_id` = 0) as `exists`", "type": "query", "params": [], "bindings": ["30", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "\\app\\Traits\\HasCachedPermissions.php", "line": 279}, {"index": 10, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 13, "namespace": null, "name": "\\app\\Traits\\HasCachedPermissions.php", "line": 275}, {"index": 14, "namespace": null, "name": "\\app\\User.php", "line": 202}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Traits\\HasCachedPermissions.php:279", "connection": "sagile", "start_percent": 6.114, "width_percent": 5.435}, {"sql": "select `team_name`, `role_name`, `project_id` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": "middleware", "name": "load.permissions", "line": 81}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 16, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "middleware::load.permissions:81", "connection": "sagile", "start_percent": 11.549, "width_percent": 6.114}, {"sql": "select `role_id` from `user_role` where `user_id` = 30", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": "middleware", "name": "load.permissions", "line": 87}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 16, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "middleware::load.permissions:87", "connection": "sagile", "start_percent": 17.663, "width_percent": 4.755}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Team Manager' and `project_id` is null limit 1", "type": "query", "params": [], "bindings": ["Team Manager"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 22.418, "width_percent": 5.163}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Project Manager' and `project_id` = 42 limit 1", "type": "query", "params": [], "bindings": ["Project Manager", "42"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 27.582, "width_percent": 3.804}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Team Manager' and `project_id` is null limit 1", "type": "query", "params": [], "bindings": ["Team Manager"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00025, "duration_str": "250μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 31.386, "width_percent": 3.397}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Project Manager' and `project_id` = 43 limit 1", "type": "query", "params": [], "bindings": ["Project Manager", "43"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 34.783, "width_percent": 4.484}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Team Manager' and `project_id` is null limit 1", "type": "query", "params": [], "bindings": ["Team Manager"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 39.266, "width_percent": 3.804}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Project Manager' and `project_id` = 44 limit 1", "type": "query", "params": [], "bindings": ["Project Manager", "44"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 43.071, "width_percent": 4.891}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Project Manager' and `project_id` = 45 limit 1", "type": "query", "params": [], "bindings": ["Project Manager", "45"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 47.962, "width_percent": 4.891}, {"sql": "select `permission`.`key` from `permission_role` inner join `permission` on `permission`.`id` = `permission_role`.`permission_id` where `permission_role`.`role_id` = 37", "type": "query", "params": [], "bindings": ["37"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": "middleware", "name": "load.permissions", "line": 121}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 16, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "middleware::load.permissions:121", "connection": "sagile", "start_percent": 52.853, "width_percent": 8.152}, {"sql": "select `team_name` from `teammappings` where `project_id` is null and `username` = 'ivlyn' and `invitation_status` = 'accepted'", "type": "query", "params": [], "bindings": ["ivlyn", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 26}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:26", "connection": "sagile", "start_percent": 61.005, "width_percent": 6.522}, {"sql": "select * from `projects` where `team_name` in ('i<PERSON><PERSON>\\'s team', 'Team 888', 'Team AD')", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team", "Team 888", "Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 29}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:29", "connection": "sagile", "start_percent": 67.527, "width_percent": 5.707}, {"sql": "select * from `teammappings` where `project_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 34}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:34", "connection": "sagile", "start_percent": 73.234, "width_percent": 5.707}, {"sql": "select * from `users` where `users`.`username` in ('UAT_1', 'ammarjmldnout', 'ivlyn', 'tay')", "type": "query", "params": [], "bindings": ["UAT_1", "ammarjmldnout", "ivlyn", "tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 34}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:34", "connection": "sagile", "start_percent": 78.94, "width_percent": 5.978}, {"sql": "select * from `projects` where `projects`.`id` in (41, 42, 43, 44, 45)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 34}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:34", "connection": "sagile", "start_percent": 84.918, "width_percent": 4.076}, {"sql": "select * from `teams` where `teams`.`team_name` in ('Team 888', 'Team AD', 'ivlyn\\'s team', 'uatTestTeam1')", "type": "query", "params": [], "bindings": ["Team 888", "Team AD", "ivlyn&#039;s team", "uatTestTeam1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 34}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:34", "connection": "sagile", "start_percent": 88.995, "width_percent": 5.842}, {"sql": "select `team_name` from `teammappings` where `project_id` is null and `username` = 'ivlyn' and `role_name` = 'Team Manager' and `invitation_status` = 'accepted'", "type": "query", "params": [], "bindings": ["ivlyn", "Team Manager", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 48}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:48", "connection": "sagile", "start_percent": 94.837, "width_percent": 5.163}]}, "models": {"data": {"App\\Team": 2, "App\\TeamMapping": 7, "App\\Project": 2, "App\\User": 5}, "count": 16}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/project-assignments\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/project-assignments", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-386736441 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-386736441\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-236897319 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-236897319\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1419644329 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/project-assignments/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjB1bW02eFBKb2JQbEh0ZWFFM0RpQ2c9PSIsInZhbHVlIjoiTDJnSFM4MGI5VkpaN0VQeWl3NzdIVHdIOWppSDZQUzRlNHJOYVMrZjBYVXUvalk5V1dGMk9UZnpuT1orUkFmWTZ0ZCtSOTFncWdHekROVE9PNno3ZmlmZTMrZ3FzaXF6ZlFhVTJkUXZaRDY2SVlyamZhaDQwZzVxd2tLUjc3L20iLCJtYWMiOiIwMTdjMTE1Y2E2ODQ0NGU5YzJiNDBlMjJkZmQ4YWJiMjhlMDRmNWRkZWQyYWZkNzJlZTc1MmJlOWFmMTA0MjMxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjYxTzNkRHd0ell0UzRIdkZrcU5yNWc9PSIsInZhbHVlIjoiTG05ZG5KSlN4TC9SN21aMmMwR2JqbVp3QzdiSHcyMkhrYmlOamJYU3E3SC9wa3pBT2RqR0RTbDRGL0g5K1FuYmFvYVN3WmFNLzFZM2xRdk1CcFNMT0xsMDZhSEEyNU9GYmFJcjhOMmw2bGtXOHcvWVVUR3F1bnBPeWNBSWt6eXoiLCJtYWMiOiIyMzQxOGYwNTU2MWYyMTY5YTIyMGZjMGE0OThhZmVjOTYyYmY1OGNlNGQ2MWIzNGExYzVmNWNhYzY0ZDhjNWUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1419644329\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">61696</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/project-assignments</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/project-assignments</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/index.php/project-assignments</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/project-assignments/45</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjB1bW02eFBKb2JQbEh0ZWFFM0RpQ2c9PSIsInZhbHVlIjoiTDJnSFM4MGI5VkpaN0VQeWl3NzdIVHdIOWppSDZQUzRlNHJOYVMrZjBYVXUvalk5V1dGMk9UZnpuT1orUkFmWTZ0ZCtSOTFncWdHekROVE9PNno3ZmlmZTMrZ3FzaXF6ZlFhVTJkUXZaRDY2SVlyamZhaDQwZzVxd2tLUjc3L20iLCJtYWMiOiIwMTdjMTE1Y2E2ODQ0NGU5YzJiNDBlMjJkZmQ4YWJiMjhlMDRmNWRkZWQyYWZkNzJlZTc1MmJlOWFmMTA0MjMxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjYxTzNkRHd0ell0UzRIdkZrcU5yNWc9PSIsInZhbHVlIjoiTG05ZG5KSlN4TC9SN21aMmMwR2JqbVp3QzdiSHcyMkhrYmlOamJYU3E3SC9wa3pBT2RqR0RTbDRGL0g5K1FuYmFvYVN3WmFNLzFZM2xRdk1CcFNMT0xsMDZhSEEyNU9GYmFJcjhOMmw2bGtXOHcvWVVUR3F1bnBPeWNBSWt6eXoiLCJtYWMiOiIyMzQxOGYwNTU2MWYyMTY5YTIyMGZjMGE0OThhZmVjOTYyYmY1OGNlNGQ2MWIzNGExYzVmNWNhYzY0ZDhjNWUwIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755583834.5358</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755583834</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1854939140 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1854939140\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-680698886 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:10:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InpJUEg3bDljc0RSUVNMNzNKT2loQ2c9PSIsInZhbHVlIjoiTUFKSkZ6Z2VOZGRTdGRKSXQ0VnJJWC9VcklyaTdYd2NlNnZ4ck9wZ2RSdkZicjg4d0syY0t6ZzNFWk1WMmwyS0FjRmdwckVFbTUyc0hPOGtweVdjWjNCTW9vRGg2VmRrVzJIV1BsYzk4c1lCNWpIWFZhRWVsejV3Q09GOG5CeEYiLCJtYWMiOiI1MzFhZTczNTFlNjJiYjhjNDY0YmVlMDM0MmE0MmZlMWFiOTRkN2Q5YmY4NGMzNDZmNzQ3YjE4OWRhZjQ3Mjg2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:10:35 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Ik9IcWpMSHRQUmJvK1d6Y2EvT2o2L2c9PSIsInZhbHVlIjoidWp5bEEyUWE1aldxK0QxaHRkNGY5MzJ0Tk9GZ2t0czZKbERETXhySWNITkh0UUFvdndFR2dJbW1TWDE5WDFjdFhOeUVGZzFzK2llVUlsUnBuMWJWcDdjaisxUVdSb21kazM1Nzg0L21ZdGRzKzc5anBwN2FKWGw1c2taOG00TXoiLCJtYWMiOiIxMWY1Mjk0Nzk3OGIwZWU2ZDE3OTA1ODdjOTAwMjQ2N2ZiNDBmYTA1YmE5NTE0NjczZDMxOTcwM2I2NGEwZDRjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:10:35 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InpJUEg3bDljc0RSUVNMNzNKT2loQ2c9PSIsInZhbHVlIjoiTUFKSkZ6Z2VOZGRTdGRKSXQ0VnJJWC9VcklyaTdYd2NlNnZ4ck9wZ2RSdkZicjg4d0syY0t6ZzNFWk1WMmwyS0FjRmdwckVFbTUyc0hPOGtweVdjWjNCTW9vRGg2VmRrVzJIV1BsYzk4c1lCNWpIWFZhRWVsejV3Q09GOG5CeEYiLCJtYWMiOiI1MzFhZTczNTFlNjJiYjhjNDY0YmVlMDM0MmE0MmZlMWFiOTRkN2Q5YmY4NGMzNDZmNzQ3YjE4OWRhZjQ3Mjg2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:10:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Ik9IcWpMSHRQUmJvK1d6Y2EvT2o2L2c9PSIsInZhbHVlIjoidWp5bEEyUWE1aldxK0QxaHRkNGY5MzJ0Tk9GZ2t0czZKbERETXhySWNITkh0UUFvdndFR2dJbW1TWDE5WDFjdFhOeUVGZzFzK2llVUlsUnBuMWJWcDdjaisxUVdSb21kazM1Nzg0L21ZdGRzKzc5anBwN2FKWGw1c2taOG00TXoiLCJtYWMiOiIxMWY1Mjk0Nzk3OGIwZWU2ZDE3OTA1ODdjOTAwMjQ2N2ZiNDBmYTA1YmE5NTE0NjczZDMxOTcwM2I2NGEwZDRjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:10:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-680698886\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-919175995 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/project-assignments</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-919175995\", {\"maxDepth\":0})</script>\n"}}