{"__meta": {"id": "X84e3260547303cce4a43878de08a0194", "datetime": "2025-08-19 13:56:31", "utime": 1755582991.801282, "method": "GET", "uri": "/status/43", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 56, "messages": [{"message": "[13:56:31] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755582991.648098, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/status/43", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.708927, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: add_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.767994, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.769036, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.76909, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.773428, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.774193, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.774241, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.774539, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.775256, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.775303, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.775532, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.776297, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.776342, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: delete_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.77661, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.777441, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.777487, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.777743, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.778456, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.778501, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.778715, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.779453, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.779569, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: delete_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.779827, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.78052, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.780563, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.7808, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.781499, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.781554, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.781767, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.782513, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.782558, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: delete_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.782875, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.783599, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.783644, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.784044, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.784797, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.784843, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.785075, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.785791, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.785835, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: delete_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.786089, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.78678, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.786824, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: delete_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.78706, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.787778, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.787821, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: delete_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.788422, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.789118, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.789162, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: delete_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.789436, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.79019, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.790237, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Gate check for permission: delete_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.79051, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.791353, "xdebug_link": null, "collector": "log"}, {"message": "[13:56:31] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582991.791397, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755582991.354054, "end": 1755582991.801352, "duration": 0.4472980499267578, "duration_str": "447ms", "measures": [{"label": "Booting", "start": 1755582991.354054, "relative_start": 0, "end": 1755582991.626966, "relative_end": 1755582991.626966, "duration": 0.27291202545166016, "duration_str": "273ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755582991.626979, "relative_start": 0.27292513847351074, "end": 1755582991.801354, "relative_end": 1.9073486328125e-06, "duration": 0.17437481880187988, "duration_str": "174ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24015328, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "status.project (\\resources\\views\\status\\project.blade.php)", "param_count": 4, "params": ["statuses", "title", "pros", "project"], "type": "blade"}]}, "route": {"uri": "GET status/{proj_ID}", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\StatusController@indexProjectStatus", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "status.project", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\StatusController.php&line=33\">\\app\\Http\\Controllers\\StatusController.php:33-50</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.0024200000000000003, "accumulated_duration_str": "2.42ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 20.661}, {"sql": "select `team_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 37}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:37", "connection": "sagile", "start_percent": 20.661, "width_percent": 18.182}, {"sql": "select * from `projects` where `team_name` in ('iv<PERSON>\\'s team', 'iv<PERSON>\\'s team', 'Team 888', 'Team 888', 'Team AD', 'Team AD')", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team", "ivlyn&#039;s team", "Team 888", "Team 888", "Team AD", "Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 38}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:38", "connection": "sagile", "start_percent": 38.843, "width_percent": 21.074}, {"sql": "select * from `projects` where `id` = '43' limit 1", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 40}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:40", "connection": "sagile", "start_percent": 59.917, "width_percent": 16.942}, {"sql": "select * from `statuses` where `project_id` = '43' order by `order` asc", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 43}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:43", "connection": "sagile", "start_percent": 76.86, "width_percent": 23.14}]}, "models": {"data": {"App\\Status": 4, "App\\Project": 3, "App\\User": 1}, "count": 8}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 18, "messages": [{"message": "[\n  ability => add_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">add_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.772116, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-494956947 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494956947\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.774364, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1062330757 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1062330757\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.775416, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1467854086 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467854086\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.776449, "xdebug_link": null}, {"message": "[\n  ability => delete_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1349340565 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349340565\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.777597, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1132797858 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1132797858\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.778604, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-884662177 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-884662177\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.779673, "xdebug_link": null}, {"message": "[\n  ability => delete_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-738913647 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-738913647\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.780664, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1381940958 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1381940958\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.781656, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-990095427 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-990095427\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.782663, "xdebug_link": null}, {"message": "[\n  ability => delete_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-233074407 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-233074407\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.783753, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-554693749 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554693749\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.784958, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1584131707 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1584131707\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.785938, "xdebug_link": null}, {"message": "[\n  ability => delete_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1620341464 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1620341464\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.786928, "xdebug_link": null}, {"message": "[\n  ability => delete_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-129356351 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-129356351\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.787922, "xdebug_link": null}, {"message": "[\n  ability => delete_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-708891173 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708891173\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.789264, "xdebug_link": null}, {"message": "[\n  ability => delete_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-281682617 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-281682617\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.79034, "xdebug_link": null}, {"message": "[\n  ability => delete_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-813066693 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-813066693\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582991.791507, "xdebug_link": null}]}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/status/43\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755581799\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/status/43", "status_code": "<pre class=sf-dump id=sf-dump-1950022964 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1950022964\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-801925492 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-801925492\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1948477755 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1948477755\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-495097044 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Imt5ay9YQ2d2a1pZazNNbEMvNEZyZ0E9PSIsInZhbHVlIjoiWUxwRDVvbWdGQUpZUGNzRWNGVkpjSnZNeGtoL2UxejZ6dy83NGhTWTJGVUpMazFxeVBDTEs4dnMxcExPVW42OVRST3U5a0hvQVJ0YUEyaWhBdkFSUWo0RG0vZHdlOGhFeXd2MUtEbEtHOEVUVWZqQTJKM3VWRzBlWnVpVys3bXUiLCJtYWMiOiIwZmFhNzhkMGQ5MzBhOWY4YTkyYWQ5ODhkMzYzOTE2NjFlYzBhY2M1NGY2ZWZlZTc1ZmM0ZDE4MzYwMWI1MGYyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImR2bWdtL0FmbjQ5T2NrNmNBMGxYaUE9PSIsInZhbHVlIjoiMzBVMWpaaWg4dFQrMVlNbDNhOVNBVVF2WEM5UjJ5MUx3eTBjUTRmMzJ1SDFNbjhHUFBMK0hJcytHSk9MTW5uV0lkTkR3b0lOOE82QU9kQmttci9EMnlxTllnV2VtUjNpbEFQa0FhWWx3a2tmdnB6OGp1UlQwTC9vRk5PaFh0N0siLCJtYWMiOiI4N2JjNzFiNjFlNWM5NzYyZmNjZDc3OTYwNzgxZmFlNDQwYTQxNGIwNmVjMTg3M2JhMTc5MTBkZDkwYjg0NTg0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-495097044\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1623356923 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52455</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/status/43</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/status/43</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/index.php/status/43</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/43</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Imt5ay9YQ2d2a1pZazNNbEMvNEZyZ0E9PSIsInZhbHVlIjoiWUxwRDVvbWdGQUpZUGNzRWNGVkpjSnZNeGtoL2UxejZ6dy83NGhTWTJGVUpMazFxeVBDTEs4dnMxcExPVW42OVRST3U5a0hvQVJ0YUEyaWhBdkFSUWo0RG0vZHdlOGhFeXd2MUtEbEtHOEVUVWZqQTJKM3VWRzBlWnVpVys3bXUiLCJtYWMiOiIwZmFhNzhkMGQ5MzBhOWY4YTkyYWQ5ODhkMzYzOTE2NjFlYzBhY2M1NGY2ZWZlZTc1ZmM0ZDE4MzYwMWI1MGYyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImR2bWdtL0FmbjQ5T2NrNmNBMGxYaUE9PSIsInZhbHVlIjoiMzBVMWpaaWg4dFQrMVlNbDNhOVNBVVF2WEM5UjJ5MUx3eTBjUTRmMzJ1SDFNbjhHUFBMK0hJcytHSk9MTW5uV0lkTkR3b0lOOE82QU9kQmttci9EMnlxTllnV2VtUjNpbEFQa0FhWWx3a2tmdnB6OGp1UlQwTC9vRk5PaFh0N0siLCJtYWMiOiI4N2JjNzFiNjFlNWM5NzYyZmNjZDc3OTYwNzgxZmFlNDQwYTQxNGIwNmVjMTg3M2JhMTc5MTBkZDkwYjg0NTg0IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755582991.3541</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755582991</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1623356923\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1140103081 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1140103081\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-501047943 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:56:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ii9kMWhwL0JtN3EzTFl3RE8zeGxCQ1E9PSIsInZhbHVlIjoicVJxa29aU1J4MzhJV25Ya1JmdzhpNHVoQXBUSS9WWno5QmkrcXUzcUFyYzlFSW40NjFBOFcyaTF6eEcyNEgxUElzckkyUTBYb2pwMlhDS3VLdXV5bTl2dWFNMEgxYkRjc2xPMFFMSnFzU2lFR0hqaVlXa0NodlFWWFZmeW5XQSsiLCJtYWMiOiI5OWQ0YTE1NzNiZmE0NTVmYTVhODVjYWYzMjUxNWJlYmU0OWE2MjZjY2Q0NDMxNjMxYzBjZTE4YTAwZGMwYmFlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:56:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlVOZFNTMzAwTVlLSU9HaW9mTmZJenc9PSIsInZhbHVlIjoiZStBU1FKSjhBNzlvRmhQU2swQ2VYV2tXM3ZkOWsxSFVSR01BSzN4cFYzbnNDcUx4cVVmOFg0dDRZd3A1NE43V01zNnV5eUhMSVJ0azFwdUd1RkVNMkV2MHVJazFENWI2VXg2UVpiZlgvU0FtRkxOSVVrNDlJcElxUm55WGVDa20iLCJtYWMiOiIxMjA2MzQ2NWUxZjA4NTZkZGFkOWFkOTBmM2ExOTA5NTY5Nzk0ZmJjNDNlZmFkN2U0ZWIyYTliMDk5YjRiMWQwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:56:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ii9kMWhwL0JtN3EzTFl3RE8zeGxCQ1E9PSIsInZhbHVlIjoicVJxa29aU1J4MzhJV25Ya1JmdzhpNHVoQXBUSS9WWno5QmkrcXUzcUFyYzlFSW40NjFBOFcyaTF6eEcyNEgxUElzckkyUTBYb2pwMlhDS3VLdXV5bTl2dWFNMEgxYkRjc2xPMFFMSnFzU2lFR0hqaVlXa0NodlFWWFZmeW5XQSsiLCJtYWMiOiI5OWQ0YTE1NzNiZmE0NTVmYTVhODVjYWYzMjUxNWJlYmU0OWE2MjZjY2Q0NDMxNjMxYzBjZTE4YTAwZGMwYmFlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:56:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlVOZFNTMzAwTVlLSU9HaW9mTmZJenc9PSIsInZhbHVlIjoiZStBU1FKSjhBNzlvRmhQU2swQ2VYV2tXM3ZkOWsxSFVSR01BSzN4cFYzbnNDcUx4cVVmOFg0dDRZd3A1NE43V01zNnV5eUhMSVJ0azFwdUd1RkVNMkV2MHVJazFENWI2VXg2UVpiZlgvU0FtRkxOSVVrNDlJcElxUm55WGVDa20iLCJtYWMiOiIxMjA2MzQ2NWUxZjA4NTZkZGFkOWFkOTBmM2ExOTA5NTY5Nzk0ZmJjNDNlZmFkN2U0ZWIyYTliMDk5YjRiMWQwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:56:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-501047943\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-902701601 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/status/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755581799</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-902701601\", {\"maxDepth\":0})</script>\n"}}