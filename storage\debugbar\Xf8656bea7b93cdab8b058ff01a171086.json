{"__meta": {"id": "Xf8656bea7b93cdab8b058ff01a171086", "datetime": "2025-08-19 12:44:49", "utime": 1755578689.842189, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[12:44:47] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755578687.827414, "xdebug_link": null, "collector": "log"}, {"message": "[12:44:49] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000", "message_html": null, "is_string": false, "label": "debug", "time": 1755578689.449593, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755578685.685056, "end": 1755578689.842214, "duration": 4.157158136367798, "duration_str": "4.16s", "measures": [{"label": "Booting", "start": 1755578685.685056, "relative_start": 0, "end": 1755578686.970072, "relative_end": 1755578686.970072, "duration": 1.2850160598754883, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755578686.970095, "relative_start": 1.285038948059082, "end": 1755578689.842217, "relative_end": 2.86102294921875e-06, "duration": 2.***************, "duration_str": "2.87s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "welcome (\\resources\\views\\welcome.blade.php)", "param_count": 0, "params": [], "type": "blade"}]}, "route": {"uri": "GET /", "middleware": "web", "uses": "Closure() {#1346\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#345 …}\n  file: \"C:\\xampp\\htdocs\\SAgilePMT_UTM\\routes\\web.php\"\n  line: \"97 to 99\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\routes\\web.php&line=97\">\\routes\\web.php:97-99</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.015960000000000002, "accumulated_duration_str": "15.96ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "load.permissions", "line": 22}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00349, "duration_str": "3.49ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 21.867}, {"sql": "select exists(select * from `user_role` where `user_id` = 30 and `role_id` = 0) as `exists`", "type": "query", "params": [], "bindings": ["30", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "\\app\\Traits\\HasCachedPermissions.php", "line": 279}, {"index": 10, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 13, "namespace": null, "name": "\\app\\Traits\\HasCachedPermissions.php", "line": 275}, {"index": 14, "namespace": null, "name": "\\app\\User.php", "line": 202}], "duration": 0.00292, "duration_str": "2.92ms", "stmt_id": "\\app\\Traits\\HasCachedPermissions.php:279", "connection": "sagile", "start_percent": 21.867, "width_percent": 18.296}, {"sql": "select `team_name`, `role_name`, `project_id` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": "middleware", "name": "load.permissions", "line": 81}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 16, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.0026, "duration_str": "2.6ms", "stmt_id": "middleware::load.permissions:81", "connection": "sagile", "start_percent": 40.163, "width_percent": 16.291}, {"sql": "select `role_id` from `user_role` where `user_id` = 30", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": "middleware", "name": "load.permissions", "line": 87}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 16, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "middleware::load.permissions:87", "connection": "sagile", "start_percent": 56.454, "width_percent": 2.005}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Team Manager' and `project_id` is null limit 1", "type": "query", "params": [], "bindings": ["Team Manager"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.0028799999999999997, "duration_str": "2.88ms", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 58.459, "width_percent": 18.045}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Project Manager' and `project_id` = 42 limit 1", "type": "query", "params": [], "bindings": ["Project Manager", "42"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 76.504, "width_percent": 1.692}, {"sql": "select `permission`.`key` from `permission_role` inner join `permission` on `permission`.`id` = `permission_role`.`permission_id` where `permission_role`.`role_id` = 31", "type": "query", "params": [], "bindings": ["31"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": "middleware", "name": "load.permissions", "line": 121}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 16, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00348, "duration_str": "3.48ms", "stmt_id": "middleware::load.permissions:121", "connection": "sagile", "start_percent": 78.195, "width_percent": 21.805}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755570218\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1443610566 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1443610566\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1662751061 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1662751061\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1017140092 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1017140092\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2064996105 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkZ5VHFPV1hTNitEYjVxS2RWWXZGTFE9PSIsInZhbHVlIjoibDQ0czhKbko2dHkzalZOeFJHQWVqUTBpNHlCSTJtdnZxWWFTREgzYVZSWGk3MkFoNDJReDVHaVI4ZjJZck4rOHF4cjJwR3R4NkdWRlFRSmN1L0MyaWpJblJNK0VPOHVabWJMcWNBenNtVEpOWEwyeFlVaFJyRENiWHZjdGVmMHYiLCJtYWMiOiI0YWZhYTdjYWRmNzMzMmFiMWNmYzFkNGZiNzlmMzFlNmMzZmU4ZDMzMGY2YzM5MjQ0MGZjOTNlZTNlMGNhMjM2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InFzaG1wcDRxR0IvYlVHQTlCV1N3S1E9PSIsInZhbHVlIjoiaVBnWEZNUVBNZm1KeWdaMkp1dk5neHIyQWI0S3gySHE3N2M5SUFTSkllbGNsR015OHRRd1F4dmxVUFFXTXZXTitMOEp3MnR2NmJhb3E1UWdRRWxwM2FFMXpiVHVPOTZFSllCUFpJYlYzWE5Nb3gzVDk1SUN1eXNIejNOM2dMMlciLCJtYWMiOiJkZjEyYmM3NDFkNmVhZmVmZmI2MjE5NmMzMjc3ZWQ0ZmVhNmVmM2RiM2VlZjAxYTE3NmE4MTg4NDZmNDM4NWQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2064996105\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1848831804 data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">50481</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str>/</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkZ5VHFPV1hTNitEYjVxS2RWWXZGTFE9PSIsInZhbHVlIjoibDQ0czhKbko2dHkzalZOeFJHQWVqUTBpNHlCSTJtdnZxWWFTREgzYVZSWGk3MkFoNDJReDVHaVI4ZjJZck4rOHF4cjJwR3R4NkdWRlFRSmN1L0MyaWpJblJNK0VPOHVabWJMcWNBenNtVEpOWEwyeFlVaFJyRENiWHZjdGVmMHYiLCJtYWMiOiI0YWZhYTdjYWRmNzMzMmFiMWNmYzFkNGZiNzlmMzFlNmMzZmU4ZDMzMGY2YzM5MjQ0MGZjOTNlZTNlMGNhMjM2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InFzaG1wcDRxR0IvYlVHQTlCV1N3S1E9PSIsInZhbHVlIjoiaVBnWEZNUVBNZm1KeWdaMkp1dk5neHIyQWI0S3gySHE3N2M5SUFTSkllbGNsR015OHRRd1F4dmxVUFFXTXZXTitMOEp3MnR2NmJhb3E1UWdRRWxwM2FFMXpiVHVPOTZFSllCUFpJYlYzWE5Nb3gzVDk1SUN1eXNIejNOM2dMMlciLCJtYWMiOiJkZjEyYmM3NDFkNmVhZmVmZmI2MjE5NmMzMjc3ZWQ0ZmVhNmVmM2RiM2VlZjAxYTE3NmE4MTg4NDZmNDM4NWQzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755578685.6851</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755578685</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1848831804\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-293605797 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-293605797\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1982424947 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 04:44:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im1Danc2TW85UXZNKzFBQVNrK3ljUWc9PSIsInZhbHVlIjoiS1U3bEluWGt6Z3pKVktvWERreFNzVzBHelcxM2Q5Y2VMcHg1WWlpZTBIM0Q1TG5aMWFvKysvUHV0TUJwdkRWaGcxKzI5YmxCOWc1b1ZLRmNEK1ZUMFkzQndqc0VkdlJXM3pFQkxNcTlNQ051cVpMV1crcUM1SWl0T3lreVZLY2MiLCJtYWMiOiJjM2MzMTkyNDQ0Y2RiOGRlYzc2ZmI1YzlmYjgwMjY1MmQ4YzczMThmMGZlMDVmNTEyMTBjOGJkYjljMDI3ODVkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 06:44:49 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkNHY0JVNStqL0lMN1E0WHd6SXk2eGc9PSIsInZhbHVlIjoiUXYxc0Rua0dyc0QxYnhXZ0dGRDNCUEJndjY0MCtGbkV2dkxxZUR2c081MFVqd3NzdVliK081REVsYXZ2OGZpRWs2aXE5NVR4MUVJak5hTTVhYzFVYlVEQ1I3cFZHNXdOdU9MY3hIOHlNYU04Y0NpUy9xVHAvQk5JUyttZXB0bVciLCJtYWMiOiI5ZjhkYjllNTVlNTU0ZjFlNTcyNjQ3NjBiNDhhM2M0M2JiOWM3MWRiYmI3ODdlNDg4MjI5ZTMyZWVhMTMxZjc2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 06:44:49 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im1Danc2TW85UXZNKzFBQVNrK3ljUWc9PSIsInZhbHVlIjoiS1U3bEluWGt6Z3pKVktvWERreFNzVzBHelcxM2Q5Y2VMcHg1WWlpZTBIM0Q1TG5aMWFvKysvUHV0TUJwdkRWaGcxKzI5YmxCOWc1b1ZLRmNEK1ZUMFkzQndqc0VkdlJXM3pFQkxNcTlNQ051cVpMV1crcUM1SWl0T3lreVZLY2MiLCJtYWMiOiJjM2MzMTkyNDQ0Y2RiOGRlYzc2ZmI1YzlmYjgwMjY1MmQ4YzczMThmMGZlMDVmNTEyMTBjOGJkYjljMDI3ODVkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 06:44:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkNHY0JVNStqL0lMN1E0WHd6SXk2eGc9PSIsInZhbHVlIjoiUXYxc0Rua0dyc0QxYnhXZ0dGRDNCUEJndjY0MCtGbkV2dkxxZUR2c081MFVqd3NzdVliK081REVsYXZ2OGZpRWs2aXE5NVR4MUVJak5hTTVhYzFVYlVEQ1I3cFZHNXdOdU9MY3hIOHlNYU04Y0NpUy9xVHAvQk5JUyttZXB0bVciLCJtYWMiOiI5ZjhkYjllNTVlNTU0ZjFlNTcyNjQ3NjBiNDhhM2M0M2JiOWM3MWRiYmI3ODdlNDg4MjI5ZTMyZWVhMTMxZjc2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 06:44:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1982424947\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1796249654 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755570218</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1796249654\", {\"maxDepth\":0})</script>\n"}}