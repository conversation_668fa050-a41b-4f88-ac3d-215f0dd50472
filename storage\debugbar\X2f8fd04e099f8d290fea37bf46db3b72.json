{"__meta": {"id": "X2f8fd04e099f8d290fea37bf46db3b72", "datetime": "2025-08-19 11:08:12", "utime": 1755572892.519422, "method": "POST", "uri": "/upload-radar-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[11:08:12] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572892.397435, "xdebug_link": null, "collector": "log"}, {"message": "[11:08:12] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/upload-radar-chart", "message_html": null, "is_string": false, "label": "debug", "time": 1755572892.503866, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572891.833268, "end": 1755572892.519459, "duration": 0.6861910820007324, "duration_str": "686ms", "measures": [{"label": "Booting", "start": 1755572891.833268, "relative_start": 0, "end": 1755572892.362091, "relative_end": 1755572892.362091, "duration": 0.5288231372833252, "duration_str": "529ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572892.362111, "relative_start": 0.5288431644439697, "end": 1755572892.519462, "relative_end": 3.0994415283203125e-06, "duration": 0.15735101699829102, "duration_str": "157ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23492264, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST upload-radar-chart", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@uploadRadarChart", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=141\">\\app\\Http\\Controllers\\CIGController.php:141-151</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00068, "accumulated_duration_str": "680μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/radar-data/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"path_info": "/upload-radar-chart", "status_code": "<pre class=sf-dump id=sf-dump-695446635 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-695446635\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-820054263 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-820054263\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1880785950 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"69866 characters\">data:image/png;base64,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</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1880785950\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1135989869 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">69878</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImlkdXBXY1FqWXJ3akRLUWRUMUhmNmc9PSIsInZhbHVlIjoic0dVS09XV2I1bnhpNmUxb2VKT0NoOWNHR21MSGRvUzZHZURRY2VSdUhyNEQ4MWsrbXAxQlozNHlCSSsxV2hjRWZKZG91QWYzUDErLzl6VEdPbWVDeitESFg4bzN0ZGttSWpLMng5RS9MSTJqVWpuTlpYV0ZxUkJxOWxOb1N5MmciLCJtYWMiOiI0ODUxYzA4YWJjNDQ3NGIxZTljNGQxNWNjM2EyMmNkMDQxMjIyYjI2YjM3MTJiN2UyYWFiOWMwYzA2ZTg2Yzg0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkF3MkpUeCtwNXFkNVBUMm5PcERzVUE9PSIsInZhbHVlIjoiUmZOZlR5bU5SVDR0ZkZ0L2NqbDJEUWljcHdDTGVqYUZQOUtlbkVwVWt5WFBzejFBOXozVDdCTDlUSitqQ01mNmtKcndUY20xWjR5OFdRSnVFbERtTEZCdFhUeXhpT04yVEV0c1QzQnllWjhnWWkzUTFablVsZUpQa28rN0xNSmIiLCJtYWMiOiJmODYxNjk4YmM0ZDY0MDY4MzkxZTRlZTQyZGU5ZmUwMDQ4NmQyZDk1NGU2NWY3YzMzMzFkZjBhZmVkNjE0NWI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1135989869\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-414677724 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60478</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/index.php/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">69878</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">69878</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImlkdXBXY1FqWXJ3akRLUWRUMUhmNmc9PSIsInZhbHVlIjoic0dVS09XV2I1bnhpNmUxb2VKT0NoOWNHR21MSGRvUzZHZURRY2VSdUhyNEQ4MWsrbXAxQlozNHlCSSsxV2hjRWZKZG91QWYzUDErLzl6VEdPbWVDeitESFg4bzN0ZGttSWpLMng5RS9MSTJqVWpuTlpYV0ZxUkJxOWxOb1N5MmciLCJtYWMiOiI0ODUxYzA4YWJjNDQ3NGIxZTljNGQxNWNjM2EyMmNkMDQxMjIyYjI2YjM3MTJiN2UyYWFiOWMwYzA2ZTg2Yzg0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkF3MkpUeCtwNXFkNVBUMm5PcERzVUE9PSIsInZhbHVlIjoiUmZOZlR5bU5SVDR0ZkZ0L2NqbDJEUWljcHdDTGVqYUZQOUtlbkVwVWt5WFBzejFBOXozVDdCTDlUSitqQ01mNmtKcndUY20xWjR5OFdRSnVFbERtTEZCdFhUeXhpT04yVEV0c1QzQnllWjhnWWkzUTFablVsZUpQa28rN0xNSmIiLCJtYWMiOiJmODYxNjk4YmM0ZDY0MDY4MzkxZTRlZTQyZGU5ZmUwMDQ4NmQyZDk1NGU2NWY3YzMzMzFkZjBhZmVkNjE0NWI4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572891.8333</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572891</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-414677724\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-839637983 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-839637983\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 03:08:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ii9GMWltc01xb3VyQlZwNHN4NmJsM1E9PSIsInZhbHVlIjoiakQ5SURYeFBramNWUHdTS1d0OVFiQ3RoZU5iMmR6WnZwdUhhTjBUTFhwZFFvOEhlekhUWUxidTdjNDZ5ODM4ZnphRWtZS1RaNkpQNHV2MnFXNktRaWVQM3dkanVaSktRYi8rRzVEQS9CRFBBUjBGZVNIVWxTd0l3aDNIbjJFSnUiLCJtYWMiOiIyMjBhOTQzZmFmOGQ3NTg5MTAxYWIxYTQ5MGZhMTNjYTkzNzA3NTAwZjcyOWM2YzA2MWNmYWYzMjIzZmIyMzY1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:12 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjFXUVZtZ3lKWi92UkwrUzgvTHoybWc9PSIsInZhbHVlIjoieklRNXNOMmIyOThVMks0dzU0bWdtWDNoeFBXY0c2UGp6NTNKYXBCSHFqZ0FLakpPTEswL1V0Z29ZWXYwTGE3KzRGb0VrK2F0bWpUcUxCU1VuNzRzRFlaQWptdHlZV284UlJlQWM2Nldtd0RoQ2FCbzNTUjRUMHN2cW1sOUltYzEiLCJtYWMiOiIwOWU4YmE0ZTc0OWMyM2RkY2I5MjYxYWE3NWJlMmU1NTQxNTdkNjUwNTYxNzI4MGVkNmM0NWZiNmUxM2ExZWRkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:12 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ii9GMWltc01xb3VyQlZwNHN4NmJsM1E9PSIsInZhbHVlIjoiakQ5SURYeFBramNWUHdTS1d0OVFiQ3RoZU5iMmR6WnZwdUhhTjBUTFhwZFFvOEhlekhUWUxidTdjNDZ5ODM4ZnphRWtZS1RaNkpQNHV2MnFXNktRaWVQM3dkanVaSktRYi8rRzVEQS9CRFBBUjBGZVNIVWxTd0l3aDNIbjJFSnUiLCJtYWMiOiIyMjBhOTQzZmFmOGQ3NTg5MTAxYWIxYTQ5MGZhMTNjYTkzNzA3NTAwZjcyOWM2YzA2MWNmYWYzMjIzZmIyMzY1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjFXUVZtZ3lKWi92UkwrUzgvTHoybWc9PSIsInZhbHVlIjoieklRNXNOMmIyOThVMks0dzU0bWdtWDNoeFBXY0c2UGp6NTNKYXBCSHFqZ0FLakpPTEswL1V0Z29ZWXYwTGE3KzRGb0VrK2F0bWpUcUxCU1VuNzRzRFlaQWptdHlZV284UlJlQWM2Nldtd0RoQ2FCbzNTUjRUMHN2cW1sOUltYzEiLCJtYWMiOiIwOWU4YmE0ZTc0OWMyM2RkY2I5MjYxYWE3NWJlMmU1NTQxNTdkNjUwNTYxNzI4MGVkNmM0NWZiNmUxM2ExZWRkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/radar-data/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}