{"__meta": {"id": "X2df7bfdb86a03459d652c677e62f4411", "datetime": "2025-08-19 11:07:19", "utime": 1755572839.590072, "method": "PUT", "uri": "/updateTaskStatus", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[11:07:19] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572839.313423, "xdebug_link": null, "collector": "log"}, {"message": "[11:07:19] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/updateTaskStatus", "message_html": null, "is_string": false, "label": "debug", "time": 1755572839.426075, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572838.749693, "end": 1755572839.590098, "duration": 0.8404049873352051, "duration_str": "840ms", "measures": [{"label": "Booting", "start": 1755572838.749693, "relative_start": 0, "end": 1755572839.270054, "relative_end": 1755572839.270054, "duration": 0.5203611850738525, "duration_str": "520ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572839.270076, "relative_start": 0.5203831195831299, "end": 1755572839.590101, "relative_end": 3.0994415283203125e-06, "duration": 0.3200249671936035, "duration_str": "320ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23464272, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT updateTaskStatus", "middleware": "web", "controller": "App\\Http\\Controllers\\StatusController@updateTaskStatus", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "kanban.updateTaskStatus", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\StatusController.php&line=228\">\\app\\Http\\Controllers\\StatusController.php:228-257</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.01244, "accumulated_duration_str": "12.44ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 5.707}, {"sql": "select * from `tasks` where `tasks`.`id` = '111' limit 1", "type": "query", "params": [], "bindings": ["111"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 234}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:234", "connection": "sagile", "start_percent": 5.707, "width_percent": 5.707}, {"sql": "select `slug` from `statuses` where `id` = '205'", "type": "query", "params": [], "bindings": ["205"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 239}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:239", "connection": "sagile", "start_percent": 11.415, "width_percent": 5.386}, {"sql": "update `tasks` set `order` = 1, `tasks`.`updated_at` = '2025-08-19 11:07:19' where `id` = 111", "type": "query", "params": [], "bindings": ["1", "2025-08-19 11:07:19", "111"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 250}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00753, "duration_str": "7.53ms", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:250", "connection": "sagile", "start_percent": 16.801, "width_percent": 60.531}, {"sql": "select * from `tasks` where `tasks`.`id` = '110' limit 1", "type": "query", "params": [], "bindings": ["110"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 234}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:234", "connection": "sagile", "start_percent": 77.331, "width_percent": 5.225}, {"sql": "select `slug` from `statuses` where `id` = '208'", "type": "query", "params": [], "bindings": ["208"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 239}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:239", "connection": "sagile", "start_percent": 82.556, "width_percent": 5.064}, {"sql": "update `tasks` set `status_id` = '208', `completion_date` = '2025-08-19 11:07:19', `tasks`.`updated_at` = '2025-08-19 11:07:19' where `id` = 110", "type": "query", "params": [], "bindings": ["208", "2025-08-19 11:07:19", "2025-08-19 11:07:19", "110"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 250}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0015400000000000001, "duration_str": "1.54ms", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:250", "connection": "sagile", "start_percent": 87.621, "width_percent": 12.379}]}, "models": {"data": {"App\\Task": 2, "App\\User": 1}, "count": 3}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/42/kanbanBoard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"path_info": "/updateTaskStatus", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1644428807 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1644428807\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-756311327 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>positions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>taskId</span>\" => \"<span class=sf-dump-str title=\"3 characters\">111</span>\"\n      \"<span class=sf-dump-key>statusId</span>\" => \"<span class=sf-dump-str title=\"3 characters\">205</span>\"\n      \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>taskId</span>\" => \"<span class=sf-dump-str title=\"3 characters\">110</span>\"\n      \"<span class=sf-dump-key>statusId</span>\" => \"<span class=sf-dump-str title=\"3 characters\">208</span>\"\n      \"<span class=sf-dump-key>position</span>\" => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-756311327\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-534188532 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">109</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/42/kanbanBoard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImlNYWFTT3JPdS9xVWxVbnZmOFFpRXc9PSIsInZhbHVlIjoiRWs3cDdjNkI2VW5tNGRHeEoyOEtGQXBrT3BkczdWUnVvZXJGNDUrZmY0dkQ1SXVVcWZiNkVhU0JvMUlqZjlBejlFZlBpV2xOSm9lMVR2S0swc21IZWtnMGZRY1oyYVpnYWdVTmduYmxKenRMWXpiSmhGQklhSUo4Nk9qS1pBemoiLCJtYWMiOiIzMDRjYTU0M2NhYjkxYTEzYmVhMDM4YzJlNjI4NjEyNzkyZWMzODY0NmY0OWMxMjBkMmMxOGU2ZjM2NzAzMjUyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkdrcCtBdkMvd09YV1lHYkduTVE4eGc9PSIsInZhbHVlIjoiaTJ5dURjVy9ta0U1ekVRM1VtWlhvd1IyTmthSXVnM3hJODYvclJHZlZ3UE1jaVF2a3R1UmIzYXJxbk1GQ1F2ZGcwUmVTYi9LRExFRTQ2aEVRRFc3Wm5saE5ybWh4YS9RREJKcUNqNzBpNmdIbHA1RGV2allCUWNqUHNiUGVMRHAiLCJtYWMiOiJmOWRkZDMwZjhlMDhlNTE3OTM5OWQ1NDEyOGRmNzcxYjViMWU1NGFhMTRmNjJjY2U0ZTM4N2ZlOTViOGE1YzY5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-534188532\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1173651534 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52208</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/updateTaskStatus</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/updateTaskStatus</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/index.php/updateTaskStatus</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">109</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">109</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/42/kanbanBoard</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImlNYWFTT3JPdS9xVWxVbnZmOFFpRXc9PSIsInZhbHVlIjoiRWs3cDdjNkI2VW5tNGRHeEoyOEtGQXBrT3BkczdWUnVvZXJGNDUrZmY0dkQ1SXVVcWZiNkVhU0JvMUlqZjlBejlFZlBpV2xOSm9lMVR2S0swc21IZWtnMGZRY1oyYVpnYWdVTmduYmxKenRMWXpiSmhGQklhSUo4Nk9qS1pBemoiLCJtYWMiOiIzMDRjYTU0M2NhYjkxYTEzYmVhMDM4YzJlNjI4NjEyNzkyZWMzODY0NmY0OWMxMjBkMmMxOGU2ZjM2NzAzMjUyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkdrcCtBdkMvd09YV1lHYkduTVE4eGc9PSIsInZhbHVlIjoiaTJ5dURjVy9ta0U1ekVRM1VtWlhvd1IyTmthSXVnM3hJODYvclJHZlZ3UE1jaVF2a3R1UmIzYXJxbk1GQ1F2ZGcwUmVTYi9LRExFRTQ2aEVRRFc3Wm5saE5ybWh4YS9RREJKcUNqNzBpNmdIbHA1RGV2allCUWNqUHNiUGVMRHAiLCJtYWMiOiJmOWRkZDMwZjhlMDhlNTE3OTM5OWQ1NDEyOGRmNzcxYjViMWU1NGFhMTRmNjJjY2U0ZTM4N2ZlOTViOGE1YzY5IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572838.7497</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572838</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1173651534\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1576398748 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1576398748\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2051855 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 03:07:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjNuUkpXckoxaXpWdTA3L1FkV3dUb2c9PSIsInZhbHVlIjoiWSt3aWhRblVHSXZPc3M2OVdTVWpBR2ZaSFFpY0QyK2VqV2Zqc2lLYnpTalN6S3RUT3JKa0dwUllKeWk2Qko5Y1JsQWJPR0RWR2dxcEgrNG4rTjh1NGptQ2tjcE53U2VHZkZRWU9Xdk9ueU84YW50R0U5c2cxMlFqVVNYMFcySC8iLCJtYWMiOiJmODM4YjU2MjliMzQ2YzJmMDk0ZDJjZjhmZTQzOGY1MGE3Njg0MzY4YTBlMDM1NDI4ODgyYzZjOTNiZDY3OWFmIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:07:19 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImUzOGZudlRqOGZtT2RLV0JzTG9nL0E9PSIsInZhbHVlIjoiN25zVko2MEtIQ0tSQ01lL2puZm9tNHIxUE5KVWN4U0MwbzVINldnQm5iTzVBV21IeE43cEFNM1Uwa2ozNW4yc090ZUI0WVBRRTRTOUczNkt0cWpwenB2QUt3eTRTTGl0ZHFDcG45WkRhQ0svK281UnZ6LzBGbGlmZTFuTXdSdlciLCJtYWMiOiJmNTAxMDNlNmZhZGQ5MjZhMzhlNTRhMzAxZmQ4YmUwZGFiZDA0Zjg3Y2FmNDExM2M1NmZhYjA2NGQ5YjAyOGY3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:07:19 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjNuUkpXckoxaXpWdTA3L1FkV3dUb2c9PSIsInZhbHVlIjoiWSt3aWhRblVHSXZPc3M2OVdTVWpBR2ZaSFFpY0QyK2VqV2Zqc2lLYnpTalN6S3RUT3JKa0dwUllKeWk2Qko5Y1JsQWJPR0RWR2dxcEgrNG4rTjh1NGptQ2tjcE53U2VHZkZRWU9Xdk9ueU84YW50R0U5c2cxMlFqVVNYMFcySC8iLCJtYWMiOiJmODM4YjU2MjliMzQ2YzJmMDk0ZDJjZjhmZTQzOGY1MGE3Njg0MzY4YTBlMDM1NDI4ODgyYzZjOTNiZDY3OWFmIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:07:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImUzOGZudlRqOGZtT2RLV0JzTG9nL0E9PSIsInZhbHVlIjoiN25zVko2MEtIQ0tSQ01lL2puZm9tNHIxUE5KVWN4U0MwbzVINldnQm5iTzVBV21IeE43cEFNM1Uwa2ozNW4yc090ZUI0WVBRRTRTOUczNkt0cWpwenB2QUt3eTRTTGl0ZHFDcG45WkRhQ0svK281UnZ6LzBGbGlmZTFuTXdSdlciLCJtYWMiOiJmNTAxMDNlNmZhZGQ5MjZhMzhlNTRhMzAxZmQ4YmUwZGFiZDA0Zjg3Y2FmNDExM2M1NmZhYjA2NGQ5YjAyOGY3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:07:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2051855\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1362781451 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/42/kanbanBoard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1362781451\", {\"maxDepth\":0})</script>\n"}}