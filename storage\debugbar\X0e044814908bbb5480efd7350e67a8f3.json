{"__meta": {"id": "X0e044814908bbb5480efd7350e67a8f3", "datetime": "2025-08-19 10:45:04", "utime": 1755571504.311352, "method": "POST", "uri": "/upload-radar-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[10:45:04] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755571504.198801, "xdebug_link": null, "collector": "log"}, {"message": "[10:45:04] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/upload-radar-chart", "message_html": null, "is_string": false, "label": "debug", "time": 1755571504.298509, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755571503.779632, "end": 1755571504.311379, "duration": 0.5317468643188477, "duration_str": "532ms", "measures": [{"label": "Booting", "start": 1755571503.779632, "relative_start": 0, "end": 1755571504.169979, "relative_end": 1755571504.169979, "duration": 0.3903470039367676, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755571504.169993, "relative_start": 0.39036083221435547, "end": 1755571504.311381, "relative_end": 2.1457672119140625e-06, "duration": 0.1413881778717041, "duration_str": "141ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23484008, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST upload-radar-chart", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@uploadRadarChart", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=141\">\\app\\Http\\Controllers\\CIGController.php:141-151</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.0005899999999999999, "accumulated_duration_str": "590μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/radar-data/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"path_info": "/upload-radar-chart", "status_code": "<pre class=sf-dump id=sf-dump-968547309 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-968547309\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-247672467 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"67794 characters\">data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAhIAAAISCAYAAACZPSa/AAAQAElEQVR4Aey9C5wcVZn/XdVzvySTyf1GwiUhCHIzJBEVYUF0F3XVd1HYl6CrCCiiIllQeDcq8l90dcErqMCKDGGF4K4o6F9BEUUlEDCECCTkfp9LJslk7jPdXe/z7XR1qnuqq05P5tLT/eRTZ6rqnOc85zm/qjrPr55zuhKx9J8ioAgoAoqAIqAIKAKDRECJxCCB02qKgCKgCCgCisDII5B/LSqRyL9rohYpAoqAIqAIKAJjBgElEmPmUqmhioAioAgoAiONgLYXjoASiXCMVEIRUAQUAUVAEVAEsiCgRCILMJqtCCgCioAiMNIIaHtjEQElEmPxqqnNioAioAgoAopAniCgRCJPLoSaoQgoAorASCOg7SkCQ4GAEomhQFF1KAKKgCKgCCgCRYqAEokivfDabUVAERhpBLQ9RaAwEVAiUZjXtWh6tfj+XZPGciqaC6UdVQQUgYJFQIlEwV7a4uiYHan8vWVX7BuLyXHKLyiOqzQ6vdRWFQFFYGQQUCIxMjhrK8OIgGM5N1mWLU557CTbstZbAf+uuuqqOZJ2X3nllY9mikn+VyWtllQnKSEne8cnJWREx2U+ZQl5yjL1Z55L3QFtmNTL1DNU59L2oyQTfSJH3xM4mMirjCKgCOSOgBKJ3DHTGnmGgG1HHDvutI2lZAqhbdvvwxmGyLfH4/G333333XZGWiTnbcm6L8h+gpynZBzHWSr6f/Dxj3/8HCnz3ZJtbxfZG926tEU9IRhfPVJJjxQBRaBYEVAiUaxXvoD67ThxecEvoA4d6Uq7HP5enPbXxWnPkeMh3UTv46JwfSQSuUj2AzbaFJmvO0I47rnnngddgXvvvfcZyfuEnH8YGdlb7CXtlpSIdMg+RTIgKnK+/uqrr/687BPlQlAeTea3J/Oom+ijnH+VcpIcu/KX0Y5fEjmiDgk55DlHjr3Yv0KOz5L0qpQl9JMvxwPkRcaSskel7AFJ2JOQkby0tqWMiFCiTI7b6Qd1SXKetQw5KXf7S/0URtTVpAiMVQSUSIzVK6d2FwUC4rDvkI7ukf13ZJ/aRuJA2kxEKsQZP5PZHsRCIhSzJO0Q54iDflZkGuQcUjdXjiEZXkc5SyIZZ1Mu+7eLzguEwDwkcqdImiBpj6RPSkpsUv4+Sa955H+Q6dARJE/kvi7Hcz2y38BpY6P0YamUEY05Wcp3BMmLnLu9X2y8VOTpy9fQn+wjhIk+vUMEE9Ed0f8J+kE5uiX/w5IStnjLKEeOvKTeBEbJOlJFN0Vg7CKgRGLsXju1vAgQEMfTJk7tenFmFwQ4nXHipP4ozoq3XDfxRo2Dz4qS6H6PFJ4k+n8l+wGbtPlGycTBu9Mjcuq7QQCQ+xql4ih3iO4b5Rgy4drQLnm3S54ltr4se9aIQDx2iDz6fyvlb5B8d8P5J/QlIyC/k4IPSkpt0t86sfE6qce0yw4KkJX9/ZK/TPZpm6m86PtdUo+VxMaW/VypT18+IMfXJ222kmRltTQEBrI7slEmcgmydST38JHk0+9ZyBzO0b+KwNhFQInE2L12avmIITC6DSWd2vfEOWab4vBbI5HpwAjvHxRn6BINR/T9QJziRUn9g+6kON43SNotzhFCkNAj5zh2pmYS535/ROZvfvnkSVmaPrH1NUmzxP46ypOJ45mSv0LyU/2Ssi9I8ttylU/TIVgRRZglRCiNtEn770NQ9kwVQai2Y4+X+Ak24NEgMglbpWzAIlp0aFIExiICSiTG4lVTm4sRAd7Oj2aKgzf8RDhewMMhoiv15i15AzZx5jj6mVKAA5Zdfm5i51Jx1KlFpBzLm/77s1mbq3yGHj/SRts3SbttkljgaksbP0+ShtQaCilDhjIWub4PsiGJqZKMJvRUERhbCCiRGFvXqyis1U4ORECcUGqKQ0r/H0mD3kTXDnm7vlQc3QVBjkzKE2sjxCkm1kp4G2QNgtTdw17kBkQLJI9pgHHeOrkcS/206IPYMCDqIfqIgOwRWaZg5DR0y1U+TaFEIrZLBpEc+iaH2bckkWHtx4DFrFL2oFyDxPoL0fAOwTGviZrYqJsiEIiAEolAeLRQEcgfBJJTEN8Ti06UdFSbR9cXJMye9qsEV7E4u8RaB3HUK7wykAdxqr8S5/58Us/3pQ6Ri8SUgjjGOVKHBZCJNRBSNpjtLNHPGg6L9kTfYjlPrLFwlYl9bZL3LTm/FhnZsxiSb2vw3YgBb/q5yqPPm6S+Oz2RmmKir5L43sdlYMSxpATRELJ2mtRnkemvyJOUkJO8hJ2yZ9Hmb0UvBEdOdVMExiYCSiTG5nUbQqtV1RhDgCkOpimO2mxxYDeJIyYEn/VbErw9i0PkVxY/EEeYWIcgJOKPUu8TUpaYPhA9ONizxSAWVzqy580dEnGTHA922yDkYTltJtu7IUla0vSJDfws9XvIICuFByXhnBNtiw6iKpCcXZCNMHmpG7hJX8GMxZWJdRAinPjGBnpJgkuqDJvkPGG31EuRMtdOKWMdSMJO0aObIjBmEVAiMWYvnRpeyAjgeCSdlOk8Jc+dh2cunmNW/w+Q82KDg5N6CXlvPsdS9n4pG5fZDmVuogwZSawFSCSphwN3RSwpww4WeCbK5TzlIJP1Z7KngpRh9yKvDsm7Sc4TxAQZSSzUXCL5CX1SlmpPjt9PEpnEJjKJtQeyT8jKPtW2HLt2pfooeVnl0UtKKJY/2CzyKdsli19qgJnbli3yabaJfLayxJSGWy71vP1FtSZFYEwioERihC+bNqcIKAKKgCKgCBQSAkokCulqFnNfIs6brbGUivlaad8VAUWgoBAocCJRUNdKO5MFAduyP+lY9o1jK1nVWbpT9NkS+mfqwXcqpujBUQAUgTxEQIlEHl4UNckcgecun3za8x+eMncsptX/MnWleU9VUhFQBBSB/ERgSIlEfnZRrVIEFAFFQBFQBBSB4UJAicRwIat6FQFFQBFQBBSB/EZgSKxTIjEkMKoSRUARUAQUAUWgOBFQIlGc1117rQgoAoqAIjDSCBRoe0okCvTCarcUAUVAEVAEFIGRQECJxEigrG0oAoqAIqAIjDQC2t4IIaBEYoSA1mYUAUVAEVAEFIFCRECJRCFeVe2TIqAIKAIjjYC2V7QIKJEo2kuvHVcEFAFFQBFQBI4eASUSR4+halAEFAFFYKQR0PYUgbxBQIlE3lwKNUQRUAQUAUVAERh7CCiRGHvXTC1WBBSBkUZA21MEFIGsCCiRyAqNFigCioAioAgoAopAGAJKJMIQ0nJFQBEYaQS0PUVAERhDCCiRGEMXS01VBBQBRUARUATyDQElEvl2RdQeRWCkEdD2FAFFQBE4CgSUSBwFeFpVEVAEFAFFQBEodgSUSBT7HaD9H2kEtD1FQBFQBAoKASUSBXU5tTOKgCKgCCgCisDIIqBEYmTx1tZGGgFtTxFQBBQBRWBYEVAiMazwqnJFQBFQBBQBRaCwEVAiUdjXd6R7p+0pAoqAIqAIFBkCSiSK7IJrdxUBRUARUAQUgaFEQInEUKI50rq0PUVAEVAEFAFFYJQRUCIxyhdAm1cEFAFFQBFQBMYyAkokzK+eSioCioAioAgoAopABgJKJDIA0VNFQBFQBBQBRUARMEcgf4mEeR9UUhFQBBQBRUARUARGCQElEqMEvDarCCgCioAioAgUAgIukSiEvmgfFAFFQBFQBBQBRWCEEVAiMcKAa3OKgCKgCCgCisDRI5A/GpRI5M+1UEsUAUVAEVAEFIExh4ASiTF3ydRgRUARUAQUgZFGQNvLjoASiezYaIkioAgoAoqAIqAIhCCgRCIEIC1WBBQBRUARGGkEtL2xhIASibF0tdRWRUARUAQUAUUgzxBQIpFnF0TNUQQUAUVgpBHQ9hSBo0FAicTRoKd1FQFFQBFQBBSBIkdAiUSR3wDafUVAERhpBLQ9RaCwEFAiUVjXU3ujCCgCioAioAiMKAJKJEYUbm1MEVAERhoBbU8RUASGFwElEsOLr2pXBBQBRUARUAQKGgElEgV9ebVzisBII6DtKQKKQLEhoESi2K649lcRUAQUAUVAERhCBJRIDCGYqkoRGGkEtD1FQBFQBEYbASUSo30FtH1FQBFQBBQBRWAMI6BEYgxfPDV9pBHQ9hQBRUARUAQyEVAikYmInisCioAioAgoAoqAMQJKJIyhUsGRRkDbUwQUAUVAEch/BJRI5P81UgsVAUVAEVAEFIG8RUCJRN5empE2TNtTBBQBRUARUARyR0CJRO6YaQ1FQBFQBBQBRUARSCKgRCIJxEjvtD1FQBFQBBQBRaAQEFAiUQhXUfugCCgCioAioAiMEgJFQiRGCV1tVhFQBBQBRUARKHAElEgU+AXW7ikCioAioAgoAsOJwLAQieE0WHUrAoqAIqAIKAKKQP4goEQif66FWqIIDAsCV1111VevvPLKRwernLrooP7HP/7xc+S4XZLjTchQPtQp2d56aWvOUOserD6x5ajwHGy7Wk8RGEYEjkq1Eomjgk8rKwJFiUB7PB5/+913322TBIG5tm0vEjJxmRwP6Xbvvfc+I22cJGnHkCpWZYqAIjBkCCiRGDIoVZEikH8IJJ37F8TRv0+OE1EJ2V8mb9VuRGG1HNdhefLt3402tCfPv0pdKf+CyH1V9gM2nLzjOKtF7o0UilydJPQm2qA98kmS/1VJ5LdfffXVd8oxcnUi86gcJ/TLPlFf8i5L2pCISHAuZXskYaNbz7cvIou+B0R2tyQneZ5qG71JexJtIUMSuQQZYi/ntEtK2Esd8qVeGp5yrpsikBsCBSatRKLALqh2RxHwInDPPfc8KOdfE0f/czl+P85QHP43iChI/gRJlpTdL06zLhKJ3CHHnxBiYLMXuWVyfJMc/1zkvsax7AdsUneOyC4Sub9RKPv7Je0WefQslbIf0C5Jyj+SbHu2yCyW81y3cVL/ItG9SPanie4BffEoPF+Ozxa5t4vcBdLeG6QeNv1OzpdJWaLvkj/AVsokLZCyW2UPTuupIxim4SlluikCRY+AEomivwUUgGJCQMjCRdLf3bJ/WZxqmzjKb4mDnFVaWjpe8lMbDlPS+1MZ6QfjpP4fhUDwpu5I0XZJDSL/oOQlSIWcPyLJEt2Pyx4nPEfqDGhbynLdDoke2rNkP0CftDdLbEhEWKRvq6WPO0QO+d1yfjuNicxr7EUuq62US9ojskyttMnxbyXpNjYRUKuHGQElEsMMsKpXBPIQgbPEpoPiSB1xlCvkeGY0GrVl/09y/nXyJTElkG2BY2qNhDjnpVJvj6TvS0ptomeF6IBkHJTMs+Q8Me0h8rz945gle1AbbXnrD+iLaE0QCWkzQRjkPHATOV9bpVJmW5KlmyKgCGQioEQiExE9VwQKHAFx5j+XN/XEQsnkfpbsdyQTx4T/bxQY/kfIQMIpy7HvRhRC9K2WQq9simiITredm0SGCEUqYsB5lkSbM7OUpWVL2759SRMKPslqa3A1Lc2Kujdv8wAAEABJREFUgBYUHQJKJIrukmuHixmBeDz+K3kDX5xcr2BdeeWVLEpcLftThDSsd/PBSJy0UfRA9H1G5GeK/HuEOOyQPVMZiTUI6BO97aL/MtoWuZOk/D2SVyf1rpNz7/YO8qX8HMkMJRLoEx0D+oIOqR+6BdkaWlkFFAFFIIWAEokUFHqgCBQmAuKY/yYON/GrDX5OKec3RCKRxBoHyV8kvf4niSy8Ivm3evK/LmUQBKIITBEE/mpDdDSIPNMirDv4iBwTeXDQJ2XfE/0PJtv+hJQxnXJQ2tstZYlN8mgL8nBQjq+QzBckBW5JfQP6IgTBO/URqEPa8rU1qJLYncIzSG6EyrQZRWDUEVAiMeqXQA1QBIYXAZy4OFdb9onFk7J/kPNkYioj8Y2GbPkid5MkpihuwnnL8Uz2XqslD5mELjluk7RIEnVIiWkN5L1tyHliQabsLZFlaoX6yJ8v54uQpR05TnxHgnM5Rm+KKCTzqEOivtuX94tsol3Zo/skdCXbuknqJbCQMl9bpRyMUm2JXKpOsiyFJzo1KQLFjIASiWK++tp3RUARyA0BlVYEFIEBCCiRGACJZigCisBIIJB8s0+99Y9Em9qGIqAIDD0CSiSGHlPVqAgoAkODgGpRBBSBMYCAEokxcJHUREVAEVAEFAFFIF8RUCKRr1dG7VIERhoBbU8RUAQUgUEgoERiEKBpFUVAEVAEFAFFQBE4jIASicM46F9FYKQR0PYUAUVAESgIBJRIFMRl1E4oAoqAIqAIKAKjg4ASidHBXVsdaQS0vRFHwHGceklvHO404h3TBhUBRSANASUSaXDoiSKgCAwxAt8VfcOZakS/boqAIjCKCCiRGEXwC7hp7ZoioAgoAopAkSCgRKJILrR2UxFQBBQBRUARGA4ElEgMB6ojrVPbUwTGGAIrVqw4/o477nhjkNlNTU2Vy5YtO/vqq68+jxQmH6RLyxQBRWD4EFAiMXzYqmZFQBHwQQAS8cwzz8zxKUpldXR0lN51112nzJo1q/2HP/zh07fffvuf2traKlYIAUkJ6YEioAjkBQJKJHK/DFpDEVAEBoHAunXr6q699tpz1qxZM2369OntQSo6OztLhUyUn3HGGS3I1dbWRufPn3+gubm5mnNNioAikD8IKJHIn2uhligCBY/A5Zdf/qpEF56tq6vrDepsTU1NVMhD30svvTQFOSEVpRs3bqyfOnVqF+eaFAFFIH8QyH8ikT9YqSWKgCJwFAiceuqpbUuWLGk1USEkInrLLbe8iCzrI5YtW/a2c889d9fSpUu3kKdJEVAE8gcBJRL5cy3UEkWgqBGIxWKRaDRaAgjuQksiEO4aiT/84Q+zdcEl6GhSBPILgUwikV/WqTWKgCJQNAj09vaWd3d3VziOY7/yyit1dPzCCy/cw54IxcUXX7xp9+7d41lrQZ4mRUARyA8ElEjkx3VQKxSBokbAjUSUlJTE3eOiBkQ7rwgYIzD6gkokRv8aqAWKQNEjAHkQEhEjMcVx8sknHwKUJ598ciZ7Flv+9Kc/nTdr1qxDrLUgT5MioAjkBwJKJPLjOqgVikDRItDf318qUxqR22677fSVK1fOLS0tjdXX10dvvPHGNfJvmrvYEoCuuuqq9ew1KQKjhYC2OxABJRIDMdEcRUARGGYErr/++r+RWA9BBGLChAm9y5cvX3PppZdug0iQP3ny5D5+KspiSxK/4mCtxDCbpuoVAUUgRwSUSOQImIorAorA0CHQ19dXKtMZcdu2Ha/WsrKyKJEKb54eFyMC2uexgIASibFwldRGRaAAESASQeQB0pDZPZdcsHYis0zPFQFFIL8QUCKRX9dDrVEEigYBSALTGNk6TBkykI1sMpo/tAioNkVgMAgokRgMalpHEVAEjgoBCAIKIAvs/RJRCZIr6yejeYqAIjD6CCiRGP1roBYoAoWMwMekc2mpra3t4z/60Y9ueP311z/uLXvuuec++ctf/vLT3ryurq5/ue+++/513bp1n/Tme46b5XiMbmq2IlAYCCiRKIzrqL1QBPIVgUoxLC29/PLLk+bPn2+deuqptresqqqqsra2Nk22rq6O/wE0tnXr1kle2YxjOdVNEVAERgsBJRKjhby2qwgUBwJ3STdTKRaL/WDRokXfPO+88/7Dm8/xaaed9s1zzz33Gxx70+LFi7/xrne96z9kiuOH3vzk8XjZG20qpAgoAsODgBKJ4cFVtSoCioAPAkIGSlgXkflzTx/RtCzqUDctU08UAUUgLxBQIpEXl0GNUAQKDYGB/ZFoRIRfYEAKBpYG57DoEvKh35YIxklLFYHRQECJxGigrm0qAkWIACTA/WbEihUrjr/jjjveGAQD/7/Gl770pYV8Ipt08803L2psbKyCjATV0zJFQBEYWQSUSIws3tqaIjAsCOS7UqYliCgQWYBEPPPMM3PCbL777rtPQub222//E5/IPvPMMxvvvffeBQcPHqwgX5MioAjkBwJKJPLjOqgVikDBIkAEASKxfv362muvvfYc/iOu6dOntwd1eN26dXW7d+8ef/HFF2+qra2NIrt06dItt9xyywtVVVVxpknI06QIKAKjj4ASidG/BmrBmENADc4FAUgEkYhIJBK//PLLX5UIw7N1dXW9QTrWrl07SQhE73HHHdeZKccaC3Rm5uu5IqAIjA4CSiRGB3dtVREoCgSIHJBw/qeeemrbkiVLWk07Dtl49NFH57A+gsR6CdZNoAsdSiZAQZMiMPoIKJEY/WugFoQgoMVjFwGcPY6f9RFhvYBwIO/KbdiwYTLHrI8gQSy+8Y1vnO6SCWTb29tLkNGkCCgCo4eAEonRw15bVgQKGoGGhoZqOgiRYB+WhEiUQA5YU4HsggUL9rEugmPShRdeuFNIRMXWrVtrmCohbdq0KdEG5ZoUAUVgdBBQIjE6uOdxq2qaIjAkCERWr149zpREQCBoFXLA8cyZMzvb2toqhDiUku+X0N3S0sIntcv9yjVPEVAERgYBJRIjg7O2oggUGwLjJkyY0A8xMOk45EFkYySJTETOOuusA9RjjQR70pNPPnmMdwEm0yWzZ8/ukrJxknRTBBSBUUJAicQoAe82q3tFoAARKJM+1Zx33nkdsg/d+FBVd3d35Lbbbjt95cqVc4k0VFRUODfccMPajRs31rPQkkSEgjwhE4mfg6J4/vz5EAna0ykOANGkCIwCAkokRgF0bVIRKHAEiBB0XnDBBX3Z+nn99df/jcR6CCIQEr3oXb58+ZpLL710G0SCfL4Xccstt7zIQksSx14Sge6ysjJH9nyTgjblUDdFQBEYaQSKjEiMNLzaniJQdAhUSY/58iTOXQ6Dt76+vlKZzogzTeGVFIIQJVLhzQs45lsTMSlXMiEg6KYIjDQCSiRGGnFtTxEobARw5pCIeFg3iUQQeYA0ZMq65IK1E5llWc5pk7azLs7MUk+zFQFF4CgRGFYicZS2aXVFQBEYWwjUirlMNRitjYAkMI0hdXw3ypCBbPgKpGfypcxuyYJMyE43RUARGCkElEiMFNLajiJQ2AjwYSicOJGB0J5CEBCCLLD3S0QlSK6sn0xGHm27UysZRXqqCCgCBggMSkSJxKBg00qKgCKQgQAkgqhAT0b+x+U8Le3bt+/qhoaGz2/evPkqb9nq1as/9ctf/vI6b15bW9sVDzzwwI2vvvrqp7z5nuN9cuxu/JoDMoEtbp7uFQFFYJgRUCIxzACrekWgCBDgg1D8/BInntldIhVpac2aNeNnzpwZPfnkk3H8qTKJTpRUVlYyJqXyJk+ebL3hDW/o2bhxI+QglW9ZlvdYTlMbNlBWk8rRA0UgXxEoELt4aAukK9oNRUARGCUEcPKsi+j3af+HkpdKsVjsnnPOOefb73rXu77hzef4zDPP/M4FF1xwB8fedPbZZ9/x93//97fLFMe93vzkcb3sMzfIBOs17MwCPVcEFIGhR0CJxNBjqhoVgWJCgEgEH4TCeYf2W8hAiUQeYpk/9wyrSB3qhskly/lIFdEOCE4yS3eKgKUQDBMCSiSGCVhVqwgUCQK8+UMi+LVGYJclGhHhFxiQgkBBn0IWXUI+ciAT2IRt+nNQHzw1SxEYSgSUSAwlmqpLESguBHjj53sRfBAqtOd8YMr9ZsSKFSuOv+OOO94YWikp8NRTT027+eabF+3du7caMpLMDtrxVU0iE9gYJKdlw4WA6i0aBJRIFM2l1o4qAkOKAAsacdK8+YcqJpJARIHIAiTimWeemRNaKSnQ1NRU+ctf/vJ4OXUikUgcXXJssmEb/zsoyUReZRQBRWAQCCiRGARoWkURUAQsSAQfgOInn4FwEEHA+a9fv7722muvPWfNmjXTpk+fjpMPrOcWPvjgg/NmzZqVkGdahCkSklsesOez2dTD1gCxgijSTigCo4aAEolRg14bVgTGLAL8XxosssRJh3YCEkEkgmjC5Zdf/urtt9/+bF1dXSgBQTFTGuzPOOOMFvYkyAQ6OTZI/JqEX2/oz0ENwFIRRWAwCCiRGAxqWkcRKG4EeMOHRPDLiEAkiByQcP6nnnpq25IlS1oDK3gKOzo6SletWjXjwgsv3OnJttDFeQ5kAluxeejGOwzQpAgoAgkE9MFKwKB/FAFFwBABIhGsj8A5h1bB2eP4WR8RJgzhQN6Ve/TRR+fIv0MQEDfP3aMT2e7ubpMxjCkYFl9CJlwVulcEFIEhQsDkIRyiplSNIqAIjHEEmCLAGRuRiIaGBkhHKoIQ1nchEiWQA9ZUrFu3rm7Hjh3j3//+9++wLGtAVaZKSBs3bky0MUBgYAY2M73BNy8GlmqOIqAIDBoBJRKDhk4rKgJFhwAkgukMflYZ1nl79erV44gchAlSDoFgDzngeO3atZO2b98+YdmyZW+7+uqrz3v44YffIFMdFbfeeusiSAay6G5sbOQ/6eIT3WQFJb66yc9U6UOQnJYpAopAjggokcgRMBVXBAoeAf8O8mEnPvDEm72/RHruuAkTJvRDDNKz/c8gDyIbI0lkInLZZZdt/eEPf/i0my655JLXamtre5cvX77anepgumT27NmQGlNygO2QDsiHvyGaqwgoAjkjoEQiZ8i0giJQlAjgrHHarDUIA6BMBGrPO+88fjEhh8EbH6pircNtt912+sqVK+cSaejr64O4BFeU0vnz52MT7ZmQAz6eBZmgL1JbN0VAERgKBJRIDAWKqkMRGDwCY6EmH3TCUeOETezFUXddcMEFWUnH9ddf/zcS6yGIQEj0gmjDmksvvXQbRMLNdxs7//zzm/jZ6LRp03rcPPZlZWV8mhu7aJOssMT0BnWIroTJarkioAgYIKBEwgAkFVEEihwBnDTOmg88hUEB6eA7E8iHyVrJKY040xReYSEIUSIV3ryAY8gB0QbsDBBLFWEbsvz6JJWpB4qAIjA4BJRIDA43rTVWEVC7c0WAXzrwaw2cr0ldHDSyoaSDSAQJ0pCpuKSkJOHChcsAABAASURBVEEuIBqZZVnOaZO2TcgBUQ0S8lnUabYioAiYIqBEwhQplVMEig8BxgecLU7apPdMF0A6jNZGQBKYxsimmDJkmObIJuPJ50uZfC8Cez3ZWQ/pEz8dZfFlViEtUAQUgXAEGCjCpVRCERgcAlprbCOAU+ZnkzjosJ4wliCPgw6TTUxpIARZYO+XiEqQIBN+5T55tA05YGrFpzgti5+xQniwOa1ATxQBRSA3BHj4c6uh0oqAIlAMCPBLCKY1cM4m/cUhu1EBr/zVcpKWWltbr3n44Ydv2rp16ye9ZWvWrPnM7373u+u9eYcOHbrqoYceumnz5s2f9uZ7jg/IsbvlSg7oG/2EfLg6dK8IKAI5IqBEIkfA8lpcjVMEhg4BiAGLGLP+8sLTFNMD2UgHiyDT0gsvvFA1YcKEnpNOOolIR6osLv9isVjqXPTHJ02a1H/ccce1r127ll+NpJVRnkyyS22QA9ZJmJADfr2BPH1NKdADRUARyA0BJRK54aXSikAxIIDTZnoAJ2vSXxwxpINpkEz5eyQjlRzHuffcc8/97kUXXXS7N5/jhQsXfu+d73zntzj2pre97W3feve73/1NmeL4kTc/eTxJ9t7NSw5sb0GWY+xmYSh9yCKi2YqAIhCEgBKJIHSCy7RUEShUBHCqkAgiAGF9hHQwPYB8mKzFh6ZYF5H5c8+witQRIkGkIUyUcj5SlQs5wHb6bPQRLBrQpAgoAkcQUCJxBAs9UgQUAcvilxe81bMQ0QQPHDCOOJR0yLRFRCISNqTARLFXhkWXkI8cyAQ20RcTcuCu7aAv3mb1WBFQBAwQGDtEwqAzKqIIKAJHhQBv/DhTnLCJImQhHUwPhMrzgSmXRKxYseL4O+64441BlZqamiqXLVt2Nv9pF+nOO+9cAJGAjATVS5ZBDohMYGMyK3BHn4muMKUTKKiFioAikI6AEol0PPRMEShmBHC6OGA+1hSGQ06kAwJARAEiAYl45pln5gQ1AIn4+te/fqb8a+I/7rr99tv/1NbWVvHII4/MQVdQXU+ZSw742qYn2/eQX3wgDwa+ApqpCCgC/ghkIxL+0pqrCCgChYpAuXSMXzrgTOUwdMPh8quLUNJBBAHnv379+tprr732nDVr1kybPn16YDuvvPJKHRZceOGFe9jX1tZGzz333F2bNm2qa29vL2OahPyQxDoJ2sHWENFEMbIQJH6BksjQP4qAIhCOgBKJcIxUQhEoBgRwtqyL8PvlRWb/Cf8bkw5IBGscIpFI/PLLL39VogvP1tXVEfnI1Js69/tPuvbs2ZNw8OhCZ0o4+ABywK83EnWDRROlyIMFdRIZ+kcRGBsIjJ6VSiRGD3ttWRHIFwQgBca/vBCjcbSQDqYD5DT7RuSAxJTGqaee2rZkyZLW7NLZS5jqIJJBVMIlITmSCWw2Ge9YVwGZQj67QVqiCCgCKQRMHqyUsB4oAopAQSKA0+RNnIWTYR2EdBD+Rz5MNvEpbEgE6yPChCEcfuQAEsF6iVmzZrUTqUAPOpHt7u42GcOYgsmFHNA3fvEBuaI5TYrAAAQ04wgCJg/hEWk9UgQUgUJDABLBWgKTX14Q7kceRxtKOhoaGqpZH4HTNwFNiEQJ5IA6rryXRFx//fV/c/OZ3iBt3LgRYuNmB+2xmekNE3LA1zyJTNDXIJ1apggoAoKAEgkBQTdFoEgR4BsLOEucrAkEyEI6cLJh8vbq1avH+f0X4X4VIRDkQw7c43Xr1tXdeuuti/jlhpdEIEeCoDQ2NvKTTRaKkhWUIAeQJfoQJOeWgQlrQUx+8eHW0f2wIaCK8xkBJRL5fHXUNkVgeBHAqRL2D1z4mDQB0kG4HwebzArcjZswYUI/xCBQKlkIeRDZGEkiExEiET/+8Y9PfvOb37x76dKlW5JiaTumS6ZPn4799COtLMsJtkMOIB9ZRFLZECbkTXWnKuqBIlBsCCiRKLYrrv1VBA4j4DpUnOXhnOC/OFQiESako0xU1Z533nksyJTD4I0PVbHW4bbbbjt95cqVc4k0PPHEE7M7Ojoq+N4EH6NyEx+ogmS4GufPn49NtGdCDvj6Jv2lL66KoD32M50DgQqSK7gy7ZAikAsCSiRyQUtlFYHCQQBnilMN/eWFdJnwPo4aeTkN3dDddcEFFzCd4CvMVAWJ9RBEICR60bt8+fI1l1566TaIxAc/+MEdd9111x/5GJU38dPRadOmpb5dUVVVNRhywPoOU3JAn+mPjpW+V1IzFQHL0odD7wJFoPgQYNGh8S8vBB4cKQ6VcL+cBm6QDqIdyAcKUpic0ogzTcG5myATRCrc85A9ax8gB9gZIpooxjZkwSCREfCHqROiMMgHiB1NkdZVBMY2Akokxvb1U+sVgVwRsKUCThFnKoehG6SDOqbyru5Q0kEkguS3IBMiAbmAaIRaeFgA+2jbhBwQ0ciFHKAbHEwWdR62Rv8qAkWEgBKJIrrY2lVFQBDA2fJNBdYWyGngxviAPI40UDBZyHQBpIO1Bcms7DtIAoQhmwRlyDD9kU3Gkw85IHqAvZ7sgYfJHPrET0dNyAF4EfUw1Z1sQneKQHEgwEBRHD3VXioCigCLEnH2OFETNHCcOFEcdJg8YwnyRrohCCiELLD3SyUlJXGSK+snk5FH25ADplYyigac0i8IDzYPKPTJQDf4sVbEp1izFIHiRYCHv3h7rz1XBIoLAZwmkYisiyA9cOA0CefjQD3ZWQ/RzXRBBumwrpEaaenAgQOf/t///d9/27Fjx7Xespdffvlzf/jDH27w5nV0dHzypz/96b9t3br1Om++5/iQHLsbC0cHQw4gH66ObPtcF3Vm06P5ikDBIaBEouAuqXZIEfBFIKdFkKIBYkA434R0MD2QjXQw5ZCW/vznP5eUl5cfmj9/fpu0kyrr7u7uEeKQOqesvr6+c8aMGa3PP/88ax/SyihPJtmlNogPsibkgAWayNPXlIKAA/Cgjql8gCotUgQKBwElEoVzLbUnioAVAAHOD6cZughSdBC+Z3oAeTkN3dCNk2W6IFP4R5KRSo7j3PeOd7zjrve9733f8uZzvGTJku+/+93v/i7H3nTeeed9V+S/K1Mc93vzk8dTZe/dcPTYjU2s1/CW+R1jN5gg71eemefqhqxklum5IlCUCCiRKMrLrp0uMgRYF4FTJexv0nWcKg6TcH6YfJUIMA2CvBwGb319faXyL8YvMoIl00v5ZYcQCVPnzfTNYMgBX+9Mb3jgGVERpm/AaGCp5igCRYiAEokivOja5aFCYEzo4RnH6Rk5eukRpIO3+iEnHfzUUyISNkRC2slpY9El5CMHMkF/6YsJOXDXdoCTiV3oZuqEqI2JvMooAgWNAINMQXdQO6cIFDkCOEfXUYZBwRs/8jjKMFnKkYV0MD3AeWDiA1MuiVixYsXxd9xxxxuDKnR0dJR+6UtfWuh+HvvOO+9cAJGAjATVS5bRZyIT2JjMCtzRZ6IrJuQg10WdgQ1roSIw1hFQIjHWr2AR2a9dzRmBoEWQfspwujhgwvd+5d68nEgHBICIAkRihZAI/g8NrzK/47vvvvukurq6Xj6Rffvtt/+pra2t4pFHHpmDLj95nzyXHLDQ1Kc4LQtygDwYpBVkOUEWDIhMZBHRbEWgOBBQIlEc11l7WZwI4BSJFvgtgsxEBNKBU8RBZpb5naObtQKhpIMIAs5//fr1tddee+05a9asmTZ9+vTAdtatW1e3e/fu8RdeeOFOGq+trY2ee+65uzZt2lTX3t5exjQJ+SGJdRK0g60hooliZCEH/AIlkRHwh0gM8uhm/UmAqBYpAoWNgBKJwr6+R9E7rTrGESBMb7wIUvqKQ2RdhAnpIPxvTDogEaxxiEQi8csvv/xViS48S6RB2sy6tbS0EEVwpk6dSoQkITdlyhR+HlqxY8eOKnQmMsP/4Oxx9CbkAG3IgwV1OA9KTJ1AVpAPktMyRaCgEVAiUdCXVztXxAjg3HCKJr+8gBQMhnQwHRAIMZEDElMap556atuSJUtaAyt4CiUK0VdTU5NqA1JRUVERFVKC87ZyJBPgYTLeQQ4gU8h7rMl6CMamizqzKtECRWAsI2DyYI3l/o0Z29VQRWAIEcAJEnpnWsNELfI4ROqEyUM6CP8jHyabcPaQCNZHhAlDOEzJATqR7e7uNhnDmIIZDDmAXIWZTcQE8gGGYbJarggUJAImD2FBdlw7pQgUKAI4eZyakaMXDJDlDd+EdBDuRx7doaSjoaGhmvUROH1pJ3QTIlECOaAOwh0dHeWdnZ2pn282NzdX9Pb2Js4lKpH4fzg2btwIsUE8LGEz0xsm5ICveeZCDtDNVBLTMWF2aLkiUHAIFCmRKLjrqB1SBFwEcPS8gYcugpQKOGXkcYRyGrohC+nAyYYJ26tXrx7Hh6TCBCmHQLCHIHDMegg5tyEPsk9srJuQ6Y7e4447LkF6ICiNjY04cBaKJmQC/kAOqEcfAsRSRWDCWhATcgAmyDPFkVKgB4pAsSCgRKJYrrT2sxgQwPHxho5TM+kvThXSQXg+TB7SgaM01j1hwoR+iEGYYsohDyIbI0lkIvLGN77x0KxZsw49+eSTx1Au0YnSP/zhD7Pnz59/QMhEYt0E0yXTp0/HfvqBWFjCdjCCfITJuuQgF92Mp0Q9wnRruSJQUAhw4w97h7QBRUARGBEEcHr88iLhaENadB0qzjVENFGMbiIRJqSD6YPa8847D1sSlYP+8KEq1jrcdtttp69cuXIukQY+pX3VVVet59sRfJBq2bJlb+OXHkuXLt3i1SXEAptoz4QcsPCU/tIXr5psx9jPdI4pOXB1UyebTs1XBAoOASUSBXdJtUNFigCRCNZH4MxMIMCZImtCOgjv46iRN9XddcEFFzCd4Ct//fXX/43EeggiEBK96F2+fPmaSy+9dBtEgvyqqqr4Lbfc8iIfpCIhn6kMGcnDLvojh6Eb5ID1HURXQoVFwNVtMlYSHcllUaeo100RyEsEcjLK5OHISaEKKwKKwKgggCPF6eEkwwwYLOkg3B+mG9JBtANbwmQTv+qQ6Yw40xReYcgE0x3evIBj1j7Q71zJAcQrQG2iCHIAIQLfREbIH/qNHURJQkS1WBEoDASUSBTGddReFDcCODmcPGH+MCQIuyOPwwuTpZywPnVM5V3d2EP9rIlIBMlvQSZEgoo5kAnso20TcsBCVKZokKeZsIRucDAhB5AOiI2p7rC2tbwYEBjjfVQiMcYvoJpf9AiwCBKnhbMzAQNZpjNMSAfjA/KmunG2kA6mD0JtgSS4hMFPmDJkmObwK8/IgxyQsDejyPeUPhGZMfnFB9MVuZADdBOVITrj27hmKgKFhAADRSH1R/uiCBQbAjhOSAFv2GF9h3QQdsfRhclSjm6cKOF9zoMSYwnyRrohCCiDLLD3S0x5kFxZP5mMPNrOhRxAeLA5Q43vKbohHawV8RXwZOa6qNNTVQ9HAAFtYogR4OFg0l1FAAAQAElEQVQfYpWqThFQBEYIAd56cWw4OZMmcZqQDsLvYfKE8Ykw5KIbvZmk41ppKC11dnZ+5tFHH/3izp07P+Mt+9vf/rbsmWee+bw378CBA9f+7Gc/++L27duv9+Z7jiEDcprYiLRwTj8TGSF/6Bv9BMMQUStXcoAd6IS4sdekCBQsAkokCvbSaseKAAEcJs4wdD2CYEGYnYS8nIZu6CacDzkIE8YZZyMdtJeWnnrqqXh/f//+efPm7RPFqbKDBw+2NzU14YBTeVOnTj0wceLERiEYriNPlXnqymFqo5zIC5GJVGaWAxZoIk9fs4ikZYMHdUzJgau7uMfZNAj1pBAR0Bu8EK+q9qkYEMBx8/zirEz6i7NE1pR0EO1A3lQ3TpZpkEz5+yUjlRzHabjwwgvvvvTSS7/jzef4bW97290XX3zxnRx70zve8Y67JP+uWCz2gDc/eTxD9t4NR4/d9Nebn+0YuyEppvKu7uFY1JnNRs1XBPIaAQaivDZQjVMEFIEBCLCgEceHUxtQ6JMB6aAOb/s+xQOyXN042AGFGRlMC7B2wMgWPjTFuojMn3tm6Bxwyi87JIpBpGFAmU8G0zcQJvrhUzwgC9uRNSUHuS7qBH8wGtDwMGSoSkVgxBFQIjHikGuDisBRI4DT4+0/cz2Cn2KeceRxln7lmXlu2H7ISYdEFCISkbAhEpmNhp2z6BLykePCS/ptQj5YqAqWyIeZQjlYMnViQg64TmBpqhv9mhSBMYUAg8yYMliNVQSKHAHWI+DscWYmUODAWOeAowyTZzxA3lQ3djCVwPRAmO7Ex6ceeeSRY/jkNWnZsmVnNzU1sW7Dty5lyCBLuvPOOxdAJCAjvhXSMyEHRCboT3qJ/xl9hhwwpeMvcST3yKLOI3lBR+jmuhG9CZLTMkVgTCLAwDEmDVejFYEiRQDHiIOEHIRBgPMirI4jC5OlHN04YEL3nAclpgGQN9INAXjmmWemvPTSS1O/8pWvrOKT12eeeWbTXXfddQr/IVdmQ+RRNmvWrHZkb7/99j/x/24IEZmDrkz5LOfYhvM2JQfI06cs6tKykSXaAflIK/A5gWwhb6rbR4VmKQL5i4ASify9NmqZIpCJAG/vOEWcUmaZ3zmOi2gB4XW/cm8eYfpcSQeEg+TVM+CYCMKhQ4fKhUjMgDxMmzYtUefCCy/c09/fX7J161baTavX2dlZKmSi/IwzzmihgP/xc/78+QdaWloqmSIhkR+SWCcBVuAQIpooRhaCNBzkgOvAmhNTWxIG6R9FYCwgoERiLFwltVEROIwATghnh4M8nJP9r/smjnx2qSMl6GYu35R04GyNdBNB4H/3hBjMnDkTh5potaamJlpWVhZbu3btpESG5w9lQh76JIIxhWypW7px48b6qVOndrHGAp3kGyRsZJwbQFay1EUeLFicmkUklU1kiGuBfCoz4MDVDVkJENMiRWBsIcADNrYsVmsVgeJEgPUIODecvQkCODccF2/BYfKQDqZB0nVnr4VuZFkrkF1KSogckHD+FRUVsSlTpiSiEVJkCVGI1tXVMZXCaVqi7JZbbnmRTNZHLFu27G3nnnvurqVLl25BF/k5kglsBj+qBiXIAf1CPkjOLQNjZJnmcPOy7ekra1WQzyaj+YrAmENAicSYu2RqcBEiwHOK88FpmXQf0sG8PM7eRN7VbUI6iERAOoxswdm7jj/MEAgH8si5Cy2JQLhrJP7whz/MvuOOO95IOTqRJdLBeUjCeRNpoZ8hooli+gaGpuQA8pGLbjBkiirRmP5RBMY6AgxQY70Par8iUAgIBPUBJ+W+zQbJUUbYHHmcIedhCVlIR2rKIaQC8uimTqDoQw89VMX6CJw+gr29vSWsceCYxHQFCyg5JgmRKIEccPzKK6/UsWcdBXsiFBdffPGm3bt3j1+3bl0dPwclyXQHThmRsITNkANIUJgsC1lzJQdEdUzIAdEOCB44htmh5YrAmEBAicSYuExqZBEjMJhFkJCO1BRCAHaDIR2sCTAhHfaqVavGlZWV4Tgtd83Dnj17UmsVWFDJYsvTTz+91SUQkAPJM4kEWBCUxsZGHLgpOcBuUwcO8WBxKykAwkQRmCCfi26wNyVBiUb0jyKQrwgokcjXK6N2DS8CY0c7zgkHSGg+zGpIB84JpxYmSzm6CfubkA6cO/LGuquqqmIQAxoiosAahzVr1kxj2oK8J598cqYQjdhxxx3XCZEQWeQlMBGLnHLKKW2uDHuiFz/96U/nzZo169Cpp56aKLNt25k+fTr2YxdiYQnbiRoMJzlIEaUAY4jmYAt2m6zbCFClRYrA6COgRGL0r4FaoAhkQ8B928bpZJPx5uOYCJubkA4c6mBIB9EOb5t+x5CO2nPOOQdbUuXnn39+Ez///OIXv/hmFlBCKq655ppXKisrHdY63HbbbaevXLlyLpGGCRMmxG688cY18m8asiy2RNFVV121nr2b5s+fzxQEfQErNzvbnjUgYAlO2WS8+diPozchB9RzdVOH86CE3UQyTG0J0qVlisCoIqBEYlThL5rGtaODQwAng3PCAYZpgBQQ4kc+TJZydOMoE1MPZAQk11HnorvroosuGkA6+NUFiydJt99++7NTp07tJRohxKF3+fLlay699NJtEAnWVkyePLkPGWRJ/IqDyIbXTol6gA120R9vUbZj+oyjZ71ENhlvvqvbZKyEHEDiTG1BN3ZAvLxt6rEiMKYQMHk4xlSH1FhFoEAQwBkRAmdaw6RLyOOYqBMmD+lgjh75MFnKXd0mpINpA6IDRrohETKlEWeagobcBJmgzD0P2UMO6DdOOUQ0UYxt9Mlk/GPqZDDkAFKXaCzgD0QL8oEtAWJapAjkNwImD1J+90CtG4iA5ox1BHDyOBccnklfkCVMbkI6eBtHHt043zD9hPWxB/kwWcpd3djDedYUi8UiJEhDppCblwOZwD7axtZMdZnnkAOcOPKZZX7n6AYHE3Iw2F98QMD82tY8RSDvEVAikfeXSA0sQgRwcDi7YVkEKXji5HkTlsPAzUs6AgWThThb6uB4k1nZd5AECENmNMKtQRkyTHO4eQF7sBoMOWCBaoDaRBHkAJLGdUlkhPyh/0wHmZADrgXyprpDmtZiRWDkEVAicfSYqwZFYCgRwAEx9YBzMdGLA4J04ETD5JmLJ/yfi27C+iakg7EEW4x0QxAwFrLA3i8x5UFyZf1kMvJoG+xMyAH9Ggw5YNomo9kBp7mSA+yGgEHEBijTDEUg3xHg4c93G9U+RaCYEMAZM+dvsh4B0oFjwxGZYIRuSIEJ6SCMPxjSAanx2vJZOUlLnZ2dn3v88cdv2b59+/XesldfffXGv/zlLzd789ra2j772GOP3bJz584bvPmeY297kAOwo58iErqBG/0EwzDhfFrUGWarlisCI4rA2CMSIwqPNqYIjCgCvE0zx4+DM2kYh4msCekgzI7DRN5UN6SDsH6YPM6Yt2k/3fulclp64oknetvb25tOPPHEPd6ylpaW/Tt27DjgzZs8eXJzeXn5rt/97neQnzQ9Hjk5TG3YgD1gmcrMcjAYcsC6EghWFpVp2djCNTIZZyFEECHk05ToiSKQ7wiY3OD53ge1TxEoBAQIbeNEcD44q7A+4bgHQzoIu4fphnQQ7cCWMFnKsZtpAj/S8YAIpJLjOCv+4R/+4d6lS5d+z5vP8bnnnnvvpZde+n2Ovek973nP9yX/B7FY7EFvfvJ4luy9G9hhNzZ587MdYzd1TOVd3WCfTaebDzmAAOWim+sKEXJ16F4RyHsEwohE3ndADVQECgQBnA1OnihAWJe8pCNMlnKcE3VwgpyHJWxBFnvCZIlyGJOOvr6+UtY9ZFtgma2xsrKyqOmns0UH5ADb6Yechm70FVkTcpBPizpDO6YCisBIIKBEYiRQ1jYUgWAE8mYRpJhJ2B7SwVoDOQ3dcMA4YqYJAoUlohCRiIQNKQgU9Cl0yUeOCy+xDWx9NKZlQQ6IHiCfVpDlhP4ydTLaizqzmKfZxY3AyPdeicTIY64tKgKZCODAiEQQBs8syzwn7I2zx5lllvmdo5u5dxylX7k3j/EAeVPd2MG0gBHpgAQ88sgjx/DJa9KyZcvOdv/fDa8R7jH/v8aXvvSlhciSbr755kV79+6thoy4MgF7sKTP9CdALFVEnyEHRFdSmVkOwJM+56Kb60b0JovKVDaEDFtMdacq6oEiMFoIMHCMVtvariKgCFgW6xFwMDgPEzxwMJAOv/UImfVxXkxr5KLbdcCZujLPmQbAFiPdkIhnnnlmyksvvTT1K1/5yio+ec3/u3HXXXedAmHIVM753XfffRL722+//U9J+cZ77713wcGDB02cPVXb5Q/YmsizYHUw5ADyIc0EbrmSA+yAoEHUAhVr4egjoBZYlhIJvQsUgdFFwHXGzOmHWQLpwCniIMNkKUc36wVMSAdh+sGQDqYFaCtrIoJw6NChciESMyAP06ZNS9S58MIL9/T395ds3bqVdtPqr1u3rm737t3jL7744k3u/6+xdOnSLV/+8pdf5P/XYJokrYL/CeQArMDBXyI9F1kIkgk5wNEjb6qb60AdU3lXN/akW6lnikCeIaBEIs8uiJpTVAjgQG3pMU5DdqEbTghZE9LhvokjH6pYBNCNsyNsL6eBG6QDZ2ukm2gE/7unRB7KZ86cSRsJ5TU1NdGysrLY2rVrJyUyPH/IEwLRy38x7sm2bNt2+IgVOr35AcfYiDMG6wCxRBGOHnmw4LokMgP+0BeuBfIBYqkiVzf2pDKzHEC2iA6Z6s6iptCytT/5iIASiXy8KmpTMSDAs4eTwLmY9JcwN86NsLeJvKubsHqYPKSDaRBTW9CNHaGkg8gBCedfUVERmzJlCg4yYY8QhWhdXR3OMnGe+YeyRx99dA7rI0islxAyIqpKcd5WjmQCm8Evs5nMc6aN0I98ZpnfOZghW+pXmJFH3wezbgPilqFKTxWB/EGAwSx/rFFLFIHiQQDngyPGsYT1mucUeZxWmCzlkA7ernH2nIclV7cJ6SASYUw6cPbi+XHMYTZYEA7kXcENGzZM5pj1ESSIxTe+8Y3TXTKBLJEOZEIS5ACs6WeIaKIYnMHQhBxAhLiGuegGQ6aoEo0F/MFmrqGp7gBVgyvSWoqACQIMUCZyKqMIKAJDhwCOmFA7DstEK47EdVhh8oTNkc9FN6SDMH2Ybspd3dThPGt66KGHqlgf4RKJ3t7ekpaWFtZ5JOpACNra2lIOVYhECeQgUSh/FixYsI91EXKY2C688MKdUqeCNRXuz0E3btyIU06Uh/wBD8gB2IeIWmAN+aCvYbKUo5uoTqovZGZJrNvIhRygG5tN+5mlWc1WBIYPASUSw4etalYEsiGAg8Jxj+oiSDFuMKSD6AK2S/XAzV61atW4srIyHKfFegiZyujbs2cPBCpRsbOzs5TFlqeffnqrSyAgCJJXyloKSIYQh6xRgfLyenH2FwAAEABJREFU8mhjYyMOHEeb0BnwB6wHQw5SxCdAN33E4XNdA8RSRciCfQY5SJV7DyBsyJvq9tbVY0VgRBBQIjEiMGsjikAKARwfb644h1RmwAEOBMdNmDtALFHEXDrOKRfdhOWZu08oCPiDQ8cWY91VVVUxiAE6hUREzz333F1r1qyZ5n474sknn5wpRCPGgkqIhMgiL4GJWGTRokX8vxoWaySoTxL5Y0RPagEmCy+nT5+O/diFSFjCdrA3IQcQJuRz0Q05SBGlAGO85MBk3QbXH3tMbQloWosUgaFHQInE0GOqGhWBIARwBjgok/UIkA7etpEP0umWoZuwuQnpwKEOhnQQ9nfby7aHdNSec8452JKSOf/885v4+ecXv/jFN7N4ElJxzTXXvFJZWemw1uG22247feXKlXOFUMQl2mDdcMMNazdu3FiPLIkIBXlCJogAJPTOnz+fKAN9AatEXsAfnDFYgpMVIOcWIYujNyEH1EEe3dThPChhN/YgHyTnlrm6wdbN070ikBcIKJHIi8ugRhQJAszR8zaa5mAD+o6TwYGYkA5IwWBIR8opB9jhOmpsCRBLFWF310UXXTSAdCxdunQLCydJt99++7NTp07tJRoxYcKE3uXLl6+59NJLt0mUIsraColoxG+55ZYXkSVx7CURtIaM7LGLNuUwdAN7HD3XIlRYBFzdJmMl5AASZ2oLurHDhByAZS7RFzFdN0VgZBAweThGxhJtRREobAQIe+NgcB4mPUUW0kFY21Qe3dQJk4d0YA/yYbKUYwuyJqSDaQOiA8hTN5n8d5AIIhBMU3glWKBJmTcv4BhyQL9xygFiqSJso08m4x/OezDkAFKXajDLAeQA8oEtWUTSsrEbbCF2aQV6ogiMJgImD9Jo2qdtKwKFggDOAsdhsh4BJ488jsOk/8gSJjchHbyNI49unG+YfsL62IN8mCzlrm7s4TxrisViERKkIVPIzcuBTGAfbWNrprrMc8gB1wL5zDK/c3SDgwk5yKdFnX590TxFYMgRUCIx5JCqQkVgAAIjvghygAVHMnCeOHnehI/k+h95SYe/RHouzpY6ON70Ep8zSAKEITMa4YpShgzTHG5ewB6CNhhywLUJUJsoghxA0sAukRHyh/4TNSA6EyJqcS2Qz0U3ZAmsw3RruSIwIggokRgRmLWRIkcAJ0H4nRB5GBQ4IKYecC5hspSj233D5jwoMRdP+D8X3dhsQjoYS7DFSDcEAUMhC+z9ElMeJFfWTyYjj7bBzoQc0K/BkAOmFjKaHXA6GHIAATMlB/QTrKkzoHHNUARGGgEe/pFuU9tTBAoMgcDu4NgIiTP4BwomC3EQkA6T9QiQDhxbLrohBby5J5vLusPmwZAOSI1X6fVykpa6urr+9Yknnrh17969/+ot27Bhw03PPffccm+eyH7uN7/5za27du36gjffc0y0QE4TG+QA7MAwkRHyB9zoJxiGiFoseEXeVDd24OjBMEw35a5ukzGZa0hfTW1BvyZFYNgQMLlph61xVawIFAECDPY4CZP1CJAOwtbIm0Dj6jYhHYTZcZi56MZheR11NptwxrxN++lulEpp6bHHHmvfs2fPrmOPPXa7t2zHjh2N69evb/Lm1dXV7e7t7d3685//vMObn3Esp6kNG7AHLFOZWQ4GQw64jsNBDiBguZAD+okd9DVL9zRbERgZBJRIjAzO2soQIjCGVOHoCXMTQg8zm7dX5HEQOKsweRz3YEgH9oTphnQQ7cCWMFnKsZs++pGO/xaBVHIc5yfve9/77rviiiu+783n+MILL7zvIx/5yN0ce9M//dM/3S3yd8disYe8+cnjObL3bmCH3djkzc92jN3UMZV3dYN9Np1uPuSA6E8uurmuJuQArCF6prpdm3SvCAw5AkokhhxSVagIJBBgPQKDPI4nkRHyB1mcPM4hRNTyko4wWcpxTtTJxRZksYf6QYkohzHp6OvrK2XdQ7YFltka4tsSfDo7W3lGPuQA28E0o8j3lL4ia0IOxuqiTt+Oa6YiMBQIKJEYChQLWod2bpAI4JjcN9IwFZAOwtQ4tDBZytFNGNyEdPCMI2+qGzsgHUwl0FZYcnUzTRAoKxGFiOM4NqQgUNCn0CUfOS68xDaw9dGYlgU54Fohn1aQ5QQsmToZa4s6s3RHsxWBo0OAQeboNGhtRUARyESAN3Te1HE4mWV+5zgwSAFhcL9ybx5hb5x9LrohHThKrx6/Y8YDbDHVjR1MCxiRDkjAI488cgyfuyYtW7bsbPf/3fAzxpv31FNPTbv55psX7d27txoy4i3LcgyW9Jn+ZBFJy6bPkAOuXVqBzwl40udcdHPduCd81KVlQciwBWzTCrKcYAfEj6hTFhHNVgSGFwEGjuFtQbXnhIAKFwQCOBicQT4vgvQDGrtdB+xX7s1jGgB5+unN9z2GRDzzzDNTXnrppalf+cpXVvHJa/7fjbvuuuuUjo6OwKgBZOOXv/zl8aLYiUQicXTJscmGbThvE3LAtcIp0ydT3ZADyEeYvEsOTHVjBzpNyQT9RLeO56CmacQR0BtvxCHXBgscARwLTpbB3aSrOAtkmdMPkx/KRZCZbRGm560WWzLL/M5xXJAOpgX8ylN5RBAOHTpULkRiBuRh2rRpiToXXnjhnv7+/pKtW7fSbko+8+DBBx+cN2vWrIRdfHeCKRJSppzPOeSAetjqUzwgC1muHddwQGFGBpEY5E11s26DOqbyrm6TMZrIC4svTXVndEVPFYGjQ8DkJj26FvK6thqnCAwpAoSYGcxxAiaKcaA8g6byrm4T0uG+ieeiG2dH2D7MdkgHztZINxGE7u7uiEQeymfOnEkbCf01NTXRsrKy2Nq1ayclMnz+MKVB9hlnnNHCngSZQCfHBgkbIQdgHSaOo0cenLmWYfL0hWuBfJgs5a5u7OE8KEG2IGq56KaPREmC9GqZIjDkCDCIDblSVagIFCkCDPq8BbPeIQwCHBXyOJcwWcqJXFDHDXuTF5SQRzdh9SA5yiAdOCDkOQ9L2I0doaSDyAEJ519RURGbMmUKDjKhn//Js66uDmeZOM/8I8SjdNWqVTMkcrHTW4YuznMkE9gMflQNSlw7yAH4Bcm5ZWCGblNyQPQAebd+0B7dkAOIW5AcZVwLiI2pbupoUgSGBIERJRJDYrEqUQTyEwHm+XE+DP4mFjLgM/jjWMLkeU6RN9WNHejE2bMPS65uE9JBJMKYdODsXccfZgSEA3lX7tFHH50j/w6deuqpbW6eu0cnskQ63LyAPeQArOlngFiqCJyR5ZqmMrMcQIS4hshnEUnLRjcYmqzbwGauYS66IR0Qw7RG9UQRGE4EGKCGU7/qVgSKBQEGexwWc9VhfcYR4+xxKmGylKPbdVicByWeaeRNdWMHIX3eZoP0umWubuq4eb77hx56qIr1ETh9BHp7e0taWlpY58GpRcShra0t5VCFSJRADihct25d3Y4dO8a///3v38F5ZnJ/Drp161ZTpwke9BXsM9VlnoM115K+Zpb5naPblBwQsYIcYIufrsw8dGOzST8hgsib2p3Zlp4rApkIGJ0z6BgJqpAioAhkRQDnSGIQzyrkKWCgx3Gbkg7C27noxhGmphA87WYeEo7Hllx0E/bH9kxdA85XrVo1rqysDMdpsR5CpjL69uzZQ18Ssp2dnaUstjz99NNbhUQkxiIIguSVsm5i+/btE5YtW/Y2fir68MMPv0GIR8Wtt966CJKBgvLy8uiuXbtw4DhasoISWEMOcnHgOO8U0QlQTh/BECwDxFJFyBLtwPZUZpYDCBvyprq5NtQx7WeWZjVbETBHIPHwmourpCKgCPggwCDPYI+T9SlOy4Jw4JyQTyvIcoJunANh7iwiqWzC2jjqXHRDOEgpJVkOcHzYYqy7qqoqBjFAn5CI6LnnnrtrzZo10/g5J3lPPvnkTCEaseOOO64T8iCyyAuniEUuu+yyrfxE1E2XXHLJa6Kjd/ny5avdqQ7btp3p06fnOq0A/iRMCEpcS/pKn4Pk3DJkIWbDRQ6INuRiC7LY49qn+7GAwBi1UYnEGL1wanbeIIDjZhEf4WoToxjgcTo4hjB53oghB8iHyVKObuwwJR04PVPdvOHitIl20FZQSpCOc845B1tScueff34TP//84he/+GaiDJCKa6655pUDsYqy5X/qnPeOnzS95R0rD5z9uT/2LHh2d8+EVMWAg/nz5xNlgJgNFzlgjOQaB1iRKgJLrgH3QyozywF2Q1aQzyKSlu3qNiEHPVKTZKpbxHVTBAaPAA/J4GtrTUWguBHg+WGwZpA3QQJnjFyagyUjS3J1m5IOQvymtqAbOwjLZ2k+lY2jzoV0oLvroosuGkA6li5dusWNMtx+++3PxqonOVc/2XbGgf6Ssn85c+LmK8+s3VxVGol+7vedp/9+e/dE1wJICPLuNyjcfIl6gA19pk03O2hPn3H0w0UOwNPUFtduiFeQzZSBJUQuF91cM4go9TUNREBzhggBBsIhUqVqFIGiQ4BBnbl3BviwzvOsIY/zCJOlHNLBXDfTGpyHJVc3dcJkcTA4L1NbXN04yTDdkA4iKUa6v/vXzuOmV5d0X3NmzZbTp5S1v6G+pHPpyVW7LphbvveHa3uOC2ssWQ454BDM2IclbKNPXJMwWa4tER7kw2QpRzd2gC/nQQlyQGQiF91cOzAO0ksZ1wpcTHVTR5MiMCgETB6kQSnWSopAgSPA2z9vtTgOk64yoOM4CDmHyRO+Rj4X3byZDxfpwJ5cbEGWsH1gP2OxWOSl5mj92TMr9iH4yr5o7R929ieiEBfMqWjZ2hYbt+dQv8mUBdVpE8xMxjTIAQQQeeqGJXRDDrjmYbLozZUcQLxMyQG25GI3hAbyEWb38JdrCwWLgMlDV7Cd144pAkeBAIM5jpu31TA1hJdzJR0QDlKYbpw8tuBgwmQpRxYnj7PjPCgxBYC8qW76yJhiJM9PPWOObZeX2PGXmvrG3/1yzwk/3dg356H13bPKSm2IkdXbHy3lJ6RBRibLwAqihr3JrMAdNmKvKTngWueiGwJECjRCCrkW2JKLbq65CTkgOpWLbjFHN0UgdwR46HOvpTUUgeJGwH2DZJA2QQIngSMyJR04iVx084aNEw2zhbdTbMlFNyHyIScdkAiMPXlS6cEndvROu/vl7vlnTSttvXh++Y6Xm/sn/udzHQumVUe6Z48v7XFlkQ9J9AtyAHELEbW4FlwT8AiTpRzdRA2GixwwFmM7bYUlbMFuiF42WTefawdZQd7N070iMKQIcPMOqUJVpggUAQIMygzmibfmkP5COnjrPRQi5xajm7ltHLibl22PYxtO0kE4n35ma9+bj904Z0iNN/9f5SQtdXV13fjb3/7233ft2vX51t74Tzbtj9ZVltqbD7R3PrRu58GfRWz76aaueFVrT7z9O88fuvfJJ5/896amppsy9STPvTjRPthhixSHbvQN0sE1CvYOZZIAABAASURBVBPmWiNvqhs7cPTDRQ7ot6ktrt0QybB+arkikDMCSiRyhkwrFDkCOFdCxjgKEygY7BnIqRMmDylgsEc+TJZyVzdOhfOgBOnAYeaim7dZ5vyD9FIGUQIXP938Pxlp6ZFHHtm/YcOGbTe8POGM11ujn60qtVZGHadzQ2f5p9f3VH/2YJ+zoL7Cvq08Yj21cotz712tJ5300EMPHZCGdmZJkp3asAF76GsqM8vBYMgBqugr+yPJ/whbuEYm4ywEDCKEvL+29Fx0Ywf3S3rJwDOiVVxLU90DNWiOIhCAgMkNHlBdixSBokKAuWkGYwZxk44jC4EghG4qj27qhMlDOrAnV0JjQjoI3+OIsSXMDsrpJ47Kj3Q8LAKp5DjOyksuuaSh/h0f/8XuDud7lyyo2nbH39Wd8J3z6/q//vaal//9rdV/+94F4/fd9vbx77r97+rmX3xi1e4Wa/w3+5b8y5OxWOwRr67kceYvO8AOu7FJREI3rg11cMqhwiLg6jYZOyEHYGJqC7qxAyIkTQVu6AXzXHRzTSGUgYq1UBHIFQGThyFXnSqvCBQqAgzavN2xsC+sjzh55HEOYbKUI8tcNs6B86BEyBx5dOMEg2QpI7yOPchzHpZc3dgTJgvpwDkZ6Wa9wwPro9Pu/Gv3iZedXL3lvDnlrW4DNWWRWF2FnUZ0Lphbvu/TZ9Zs+N3O6NTP/b795J6ow5jlVsm2hxwQbaAf2WS8+diOLBh58/2OufbcA8j7lWfmoRv8TckBtueiG3LANchsN/Oca4ktproz6+u5IpAVAZOHMmtlLVAEiggB5tKJAjAYm3SbAZs3UhxPmDzhaeRz0Y1jyJV0hNlBOU4PopKLLchiD/WzJokoRO5+uXv2vet6TvjoG6s3v21WGdMVWeXdgpMnl3bcsKh2w/4ep/zyxw8sfLmln7d2tzjbHpvA1JQccJ2Qz6bPm49ucOKe8Ob7HTNdkSs5gJgNBznAbvDAdj9bNU8RGBQCSiQGBZtWKkIEcDJMI+AYwrqPIxgM6eBNN0w3pANHilMIk6Ucu7HZhHQwHiCfrhst/gk7IB3g4i/hyf3Oix3Hrnitd+6sLY9bf17xnyf88M5vn3rw4IGszpgyZL75ja8u/PF3/+O0uZt/4cyujXRd80Tbm36+sXeKR7XfIVhC5OiPX3lmHn3mmmW1x1MBPHMlB+gleuBR43tIJAVbcrGba2BKDlzd1PE1QDMVgVwRYODItY7KKwLFhgAOhtA0g7BJ33ECONe0MH2WipAOHEwuuiEFOMosKlPZ2Iyzz0U3ThIHnFKS5YCxg34a6f76qo7j/+f13tnH7Xgidu0lF6373A03vTh/wUn7f/G/Pz2hp6ebt+S0ZiARDz/YsAAZZD/56ete6uzsLJu594/Ou48v23PbqvZTvv1C59y0SgNPsK1assFYdoEb14prRp8CBZOF6C6TY66d7AI3yEEuupHF0XPtAhUnC7EFu7kmyaysO+4drjHyWYW0QBHIBQGTGy8XfSqrCBQiAgy6DNYm6xFwXDhG5E2wcHXjyJAPSoS7cVy56MZxsDAvSC9lOEXeanPRDZkJJR3/5y8dJ/x6W8/MBU2/73vTnLp9EybUJ+x501mLW6LRaKRp7176hA2ptGP71oQTRYbMysqq2BkLFzbt3rmr9q1TYgc/cXrVxl9u6Z313v/Z/yXry1nXTYAp/QFj1IQlZMGBaxgmCzlA3lQ35ID7J9GvMOVSzs+F0W0yRnMNciEH2I0d9FWa0k0RODoETG7So2tBaysCYxsBBnPm/wllh/WEt0jkGahxGmHyOO7BkA7sCdMN6eBNHFvCZCnHbvqYcPJkBCTC9NgeqvuLf2qf/8ddfdOWHh/dUXpwZ2TylCk4vYTqysrKWGlpaXzz5o11iQzPn9NOf9P+qz/12XUu6aBoX0tLgnBEIpH4KRPtrn89q2ZDZ9Q6ZvHx+1a9uWHPG5DxSdgIxibkgGuGPFj4qBqQBV7UMZV3dWPPAGUZGazZgKjloptrYkIOuMYQTFPdGabpqSKQjoASiXQ89KzYEAjuL+sRGGxxAMGSh0uRxckzSB/Oyf7XSzqySx0pwUlQJxdbkMWeI1r8j3DQgyEdvAX7a5Tcc3/S+q0XGvsnXX1q+dZjx0c6y8rK43XjJ+DEpNSyKiXKUFtbmzpPZGb5w1THxg3rJxKVqK6u6Xccx55YaUX/aV7Ff8rxlrhVtmrJ/fvel6U6OHBtwC+LSCobcgBmyKcyAw5c3cNJDiBuASYkisAR23Oxm2sO4Uwo0D+KwGARUCIxWOS0XjEgwKDMGzRvhmH9LRUBwsU4FjkM3dCNIzYhHTynyJvqxg6cJuH0UENEwNVNuF5OAzdIB2+9gbYsbmhZ0R9z3vLJ0yq2zq0rM+mjFY/HIvF4nL6mGQCJePjBhgVTpkztIlJBYWlpWTQWi5aIvB1x7G/YjnW3YzuPLm7YdyPlGYn2B0MOuKYZqgacEjngHgHDAYU+GeBGdMSEHHB/DIYccI18mk7LAg9sMbU7rbKeKAJeBAY8tN5CPVYEhhiBsaSOtzUGZAZbE7sZkHFYJqQDR4yzz0U3TgWHFWYLzzS2mOrGDsLzQ0Y6Fj+w7xHbtk+bZB368tQqq5+pCIzu7++LtB06mHKgLLLs6OhInQspiMRisbQ3ey+J+H8+9M+b0UMS/Q6p82Dr4Tdqx3rIcpzPWJZzg5CYe5HJSOBBX03IQa/UBWtwlMPQDd2QA+6ZMGGuI1jnopv7hXsxTDdEEFtMdWMHhBNcwnRruSKQFQEGnayFWqAIFDECDMYMylEDDHBmDPTIG4hb6IZ0EI4Ok8eJ1IhQLrpdRyjVAjecNrbkohvSwVuyj2LHFif+mBV3jnP6reucfdu7S0pKefOVaYzKWFVVddRd50Dlnp6eEhZbnnDC/DaZnsChWZCDmEQaKN++dUvtivvuPYVfbnhJBGUkdHe1H6yMdhxIkAPbsf8SiVtXWI51xpIHWv608MdNJyCXTGAC5vQ3mRW4AxOuqQk54B7BKeeim+sK+Qg0QgoHQw64RqbkgH5it/oCAVu3wSGgN8/gcBsbtdTKwSKA48bJMsia6GAgRjbhNEMqQDpwTsiHiCaK0Y3jNiEdvN1jey66cbCE5xONBfwBD2zx1X3efU7loobW31iWPdGOOtf1NK7vj5SWxt1oBOshWN/AOgeiDJb8++sLz08pFZlpM2Z0R6P9pciSiEwg8+tfPXbcyaee1nz+O961W8QHbJCOqtrxvT3NW1IOWTzobtuxro47TmNJxH5uSUPzRZ6K2A454Bp4sn0PIQfI02dfgYxMZMEoZUtGufdUzLSQN9XN9aeOqbyrG3u87fodd0sm94CpbhHXTRFIR0CJRDoeeqYI8GbMoMpgbIIGjps6pvKubhPSgdMbDOkgfB5mO6QDp5eL3TidAaTjjPsOTOgq2feE7cSrSiPWdbG+jr7elh011VNmEwFI2cH6BqIL993zg1P5yBSk4h//n4s3l5dXOH19ffZ/N/x4/h+f/t30SCQSf3H1qmldXV3la9f8dTqybuIDVZAMV2lN3cSeWGdbed+BvfTHzbYicfurQihWOJb9yyUNLdclC8Cc/nINklmBO2RxxlzjQEEpdCQhj27uBzkN3CAH2IN8oGCy0NWNPcmsrDuuUS7kAN30MQ3DrNq1QBHIQECJRAYgR3GqVQsDAQZ2HHGaE8zSNZ4f5BmIs4ikZRNuxskQBk8ryHLi6ia8nUUklQ3pIFxuagu6sYO+ppRkOYDM+JKORfc1T68oif1G5iT6bMe+LhZ14t17X68pHText6K2foBuogt8YIrEzzvr6ib0M5VRXV3Tv/Rfrthw3vkX7mHK4u3nXdB43b9+4a/IeRN1vD8JLSkti5dPmtXV07QFR5huvmM/YMWt68XDf2lxQ8v3k4XgwzUYKJ8UyNghD1bUySgacMo9MxhykJiaGaAtPaNHTiFy2CKHoRt2c81MyAHXCWJjqju0cRUoLgQYCIurx9pbRSA7AjhinD2DcHapIyUMvAzCDPBHcv2PeNaQN9WNHeIDLZy9v8b0XFe3CenAwdBXU1vQjR2E+1OtvuXBA3PtEktIhNNqx5zPU0BkINq+v6Jqxok4JrICE7/UIALBNIVXsKSkNBZLrpXw5vsdV81c0GXF43b3ng30K01EvP8fI5Z9hWSevaih5fcLG/bMkWP6TZ8ichy2QQ64xsiHyVKObq6dCTkgasC9k4tu+gixo62ghM1cs1x0c09ASIP0apkiMAABkwdpQKW8yFAjFIGhR4BBF8dhsh6BQZe3WhyHiSXodh1HmDzha+Rz0Q3pMHLe0rirmzpyGrjhuLAnzZazHmhZEI1Fn7Ase6sVs5ZbyX+9LdurK6bM6SypGsebeTLXf8cCS9ZDRCIlA8hPRKY3qEU5+7BUMe24zr7W3dXxvu4BY5oTd7bbhxdhHipxyp5bdP++t4o+HC04yGHoRt8hB1zzMGGuMfdQLrpx3ibkACI3GHLANQyzm2tAP03tDtOn5UWEwICHroj6rl1VBLwIsACPwZzB1Juf7ZgBF8dtQjoILw+GdBDOzta+m4+Tx5Zc7MbJY7urI9teXugtV3eKdCx+oOn0iGNBItaKg77VrdzTuLnKifZHEhECNzNgT8ShRCIPmdEItwplyEA43Lxs+4pJs3tLaur6eho3ZXWaYvNXHNtZadvOk4t+tPMy0cU1MSEHXOPBkAPuKWkmcIMccO3AOVAwWYgs1zxrP5Ny7LhmyJvq5p6gjqk8bWhSBCxTIqFQKQKFjgCDJ4MuTjasr+4bJPJhspSjm0Gat2DOgxKkAyeRi27C4yakg3A7tuSiGzxwogmbz7p/32LLifxGTp6x485/yD6xObF+u7d1Z3XF5GNSsomCLH/cSIMbefATg2BQHo/HjMapyqnHd/UdaKzqP9SSlRxE4naD5Vg32qWVt5119/YvSrvgIbvQDcwgmibkAMyQz0U35ABiE2YIjt7VDdELk+e+w55cbEEWe8J0a7kikEDA6AFNSOofRaBwESBszaBM2Niklwy0DOaEg8PkIR04NuTDZClHN3aYkA4c22BIB+F32gpKkA5wSdm96MfNb4/Yzm8kQvAriUR801u5e+/G6pKKmmjF1GPTCM2f9/Z9JzP9cWfn9x57ceN9v9+8/05v2W83NP3g1+t23uPN+9Ou7u/8YvWGHz+99eBd3nz3OB4RWpA0pLS2PlpeP727t3kbmCRzB+5sx3pK+nBFpLL6nLPu3fGzk77ws2MHSg3IwRmDBddnQKFPBrLcUybkgOrIo5s6nAclyBr2IB8k55a5urmmbl62PdcPYmqqO5sezR91BEbOACUSI4e1tpSfCPAMMGgy2JpYiHPlrRBnbyLv6jYhHTjAwZAOwuNhtkA6IDWm/cRuHFaCdCz5cdM77Yj9a3HbD0cc+05vY3wQqq91d7WQCORTRRv2RQ89+HL3K5m6L4WRAAAQAElEQVTpR79es/V/nt/5ys+2Vf7VW/aTVTtfe/jZra958x5eH3/5568cWvOjX72wzZvvPU41KAeV0+d1xXo7S3tbd9FfyfHfpA+b7FjsCrukrHv8SWf/aVFDy7n+kmm5XHMcPfdAWkGWE7AGR+6xLCKpbLCDPCKfygw4QDd2mJADrmEu5ADd3IuBGAbYpkVFhoDJDV5kkGh3iwwBBm53oA3rOuFe5Blow2QpRxbSQXiZ87CEPLqpEybLQI89yIfJUu7qNiEdhO9TpEOc7HudSOTXtmXfI2/z96DMm/ggVFndlJ6y8VNwhN6iAccJ0iHTD5mkY4CgJ6NqxvzD5KB5G3Z5SgYeRsqr4hWTjunqbdoaHgmwS/ojdun/F+3tfMK2rKeln1cO1DggB7zB0mTsxHmDCfIDFPlkoBtyAJn0KU7L4p6FfOSim2tqQg64RyBNprrTDCvWk2Lut8nDUMz4aN8LGwHWI+BwGMBNesrAygBO+DdMHiePfC66CVebkA7xewMXQQYYRB+xJxdbkI0tbmj+oDT2CyEQt1txpyGzDX7uyQehqmYuMLHbgnQw/WBCOty2JGrgJMjBvp2QJzc7675y+gndViTi+P0c1K+S09V5T/+Bpq9JP7+3pKHlP/1kPHmQA+4Brq0nO+shOIK/CTkYq4s6s3ZeC4oDASUSxXGdtZf+COAMcIC8NfpLHMmFdODIcAxHcrMfoRunY0I6CE8jn4tuSEdX9uZTJeIfU6QjlRlwgNOjTvuShqbLLcteaTn2bZZjPWT5/ONDUHwQikiAT3FaFtMNkA6mH9IKDE4gB3ZpWdyUHFROO76zt2VHjd/PQTObK6+f0dfXuuuJvv1N10oo6L2LH2h5fPH9uyZlynnOuU7gxD3hyfY9hBxwj3F9fQUyMtFN1CA0+iL1uAeQz0U3hBLbpXrgJlCkPuPN/RAoPPKF2mI+IaBEIp+uhtoykggQ5uUtkYHYpF0Ga8K9JqQDRzAY0tFrYAikg/B3LnZjswnpYDygn+3iTD/uWBF+4fBl23H+18+uhFOPx22JRpjotphuMCUdfu1VTJnbBTmIdbfjDP1EUnmQg9JxE3vFRhOnafGLj+7tL++Mtbd+wnGccsuuWLX4/tazUwrTD8BzMOSAey5d08CzwZADHL1RP6U57huuMXXkNHDjumIP8oGCWljcCDBwFDcC2vtiRYDBkUE1bgAApGAwpIO55jD1kA4cDLaEyVKO3QzwJqQDm3MmHYvvb+Z/0LzHtpybZUrjcRrNTLzp97HActpxONTM4gHn4tCrmW4wJR0DFEhGihzsfb1GTkM3vq7Z39ZSyfRLmHDiFx98anvvFjsSt29yHOv3lh3/y6KG5n/JUpfrBb5cuywiqWzuMeS5dqnMgAMIK46eaxcglipydZuM59w7ECFTW9BdKy1BYGWnmyIwEAGTG29gLc1RBMY2AgyihG6NnKB0FXkGVOrIaeAG6eCNGflAwWShq9uEdBDuxnHlohvHQXg92VzWHU6xZuG9Oz9u2fZ3paP/asXtJ7JJ9zRuquYDUBWTZocSGpd0MN2QTZ9pPuQg2r6/woQc8HVNIiB8bdNEv3dRZ8Sxfiie/Eu2Zf9oUcO+23zqD4YcCKzWcJADptAGQw645j5dS8vi+nIPcZ+mFeiJIuAioETCRUL3xYIATp5BMRdnTHjXhHTYAqKrG6chp4FbjZRiTy62IIs9UjVwg3QQ7UA+UDBZOG7hDzZ9sqS86iuW43wmEreetrL844NPfPiJ6YAsImnZEo2ogXQQUUgrGMQJ5KBiypzOXMgBX9vsNfjFx4BFnXHrl5Zjf9S24pcubmj52Vv/q4Vr67Wae4LrnJnvlfEecy2Q5Zp78zOOE6eQA5w48omMkD/o5n4yIQcQy1zIAbohsNxTIWZocTEioESiGK96cfeZgZlBeswugjS8fPQTBxAzkK960/fWLy+prrsOEmE79l+C6ohTri6vn97NdECQHGWQDqYXTEkHdcIS0yOQg57GzTi3QPEEOZh8TFfvvp3VfH0zUFgKMxd12o7zt4htf0yK6vrLrFWLG1rOkmPvBsZgbUIOuOcGQw7KvQ1mOYYcQGywJYtIWjZ2QzRNyAH3EPKmutMa0pPCR0CJROFfY+3hEQQYOJl6YFA8kpv9iIET0sHgn13qcAlzyIStc9FNOJo3w8Masv/lOcUWU93YQXSEufbsWpMlb/r+xv8sHTfpwxHL/rTtWC8ks313iV9e9HaWmv7yIkE6Js3qCiMdOPn+Q/sqJFUyFeLbuCeTT3H3tpqRg4qpx/YkfvGxdyPX3qPl8GHm34qMRZ1OzGq349YNjmM/J7LPCpn4f2XvbpAD7hGuj5sXtOcaYocJOeD+GAw5CCVYYmCu5AC7uaeIekh13RSBIwhEjhzqkSJQ8Agw2ONcTdYjQDoYkBlATYBBN6TAhHQQfsbZ56Ibp4LDCrOFZxpbjHSf9V+7vl9aNf6fLNv6tBN31oUp55cXFZOO6TL6uadMJ/CVSdYeBOllvUP37vU1sZ6OMicWjTAVQl5QHcgBn+Tm09xBcm4Z5KCvdXd1Tr/4yFjUGXGcO23b+qrofHBRQ8stsnc3sIYccM+4edn2XEfuQa5RNhlvPrq5X7gXvfl+x4NZtwE54F7005eZhy3YzT2WWabnRYyA3hBFfPGLrOsM9ISfGQxNus6AiawJ6SA8zECPvKluSAfh6DB5nAhvgbnohsyEko7F9zffY5eUvjve0/U5eePeEGaIOPhqKxJxCP+HyVLOdAKkwy4pYx0BWZZlHdnh1NHprneomXvawXHzF7fapeXxnr2bart2vVrD1MiRGulHQia6IAd8LTO9ZOBZef2MPr6+2Z1BDgZKHs7JuqgzZv3ccqwrbcv+6OKG5pXn3beVa889MhhywD15uMHsfwdDDsB7OMgB9xREiGcju8VaUnQIKJEouktelB22pdcMfjhjBlk5DdxqpHQwpINwsVQN3HA8vLliS6BgshC7CW+bkA7C5dgeqntxQ8sKy3LO62/Z9YWSytrXk21l3THdwDccTH95AUFgOsGPdCR0SbQCpy5RiNLySbO7q2ad1Jlw9uOn9FfPPrmzcsa8DifaF+lp3FyDLkhHpnF8HRNywNcyM8v8zqtmLujM5RcfFVkWdcr0zxo7FuMnsjNkzubZJffvP03aA3PuGRNywD2IPNdWqoZuXH/qmMq7urEnTDnkAOKZi27uMQhumG4tLxIElEgUyYUu8m4ySOLkiQKEQeElHWGylDOoUofBm/OwhC3IYk+YLFGOwZAO3hoTuv3+LH5g3yNCIk7v2PzSl0uqxm/2k8nME2deUzpuYi/OPrMs8xynD+momDJ3AN5MWaCr72BjpRCYaNWMEzsrJs3uzYxa0A6Ov3zC9B7IBqSjV8gHJMTbHjJ8LRO93ny/Y6ZjKoQc8DVOv/LMPNHd5UT7ITNch/RiO7LfduzrnLj1N8eOrVrc0HyxCHBdub7cD3IauEEOuAeQDxRMFrq6TcgB6zYGQw4gosnmsu4gtNhuandWRVpQOAgokSica6k98UdgzC6ClO4wWONACG/LaeCGs+MtEfmsgovvb3nMijvHdW9fv7ykvHJn2CJIFOGk+eUFTp/zsITTzyQdTFEwVcGUBVMXEqnoxFHzc85s+hKOf+qxPbQL6YB8JEjIgb0ph4cM34rIhRxY8bgtkQ7wytZ0Kj9sUWfEsb5lO863Lcteubhh3+csyxoMOeAelaqBG+SA6AH3RKBgspD7oFqOU1jJcbYN4pkLOUA3BNcIw2yNan7hIKBEonCupfbEHwEGXt6MeUPzlziSiyNmbpmB8khu9iN0MwgzwFuWlV1QSnjWkDfVjR2Es5l7l+qhm6vbl3Qwl7+oofkJy7Yn9h/c8/n+Q01dYYsg3RZ7W7ZX46yDnL4rC+lg+gDnTx7RCXH+1eK4axyZqqiUKQumLpiWoNwk0W7VzAVdkA9ICGQEUuKujaAMckA7Jvoqph3X2du6szozuuFXt0KITOiiTsf+qdT9pER5rl10355vW1UTx8u5CTngnuTe4dpJldCNewdygBMPE+a+5N7JRTf3vwk54B7DFu7RMDu0vAgQYHArgm5qF4sUAdYjMDAy6JlAwKAL6SB8GybPoMu0Ri66XccRppvwNbbkohvSwVvlAN1n3Ld1gszl/0bi7VUlTv/nenZvjlRMOqYrczphQEXJYDrBkfB+LqSD6QO7pNShLtEJpibKZYpCHH5iHYSoHdQG+YCEQEYcISU9jZtqeho3V0EIIAd9rbvNyIFMpUAOeho3VZsYImQidFGnHbdWRxznY3ak9PhFd73y85NvenShiW6R4Rpzj5qQg3xb1CnmW0omQKHIkxKJIr8BxnL3DWxnkGOgJtwcJg7pYDBHPkyWchw9jtuEdBBeHgzpIJxNW0EpkHQs+e+maWUltb+xLLvfjtvXde7eXJFtEaSV8Y9vOyR+eTHZjHT0iFN3hHSUVI2PSnSghqkIpiSqWAchb/ZMQ2Q0MahTd/1Eae3EvmjH/nKJTnCdLYlc9PfkQg4ONFZFOw6ERg4gMGV1U3rCFnU6jt1sO/a1VqRkY+1Jb3ly4V0bP2jQQcgB9xz3k4G4hSzX3IQEQS6RN9XN/UydBJ4Gxri61Y8YgFXIInoDFPLVLe6+4bi5vxnsTJBgsEXWhHS4b5DIm+pmkCbcHCYP6cBJ5KKb8PgA0vGWBw/MdaKR30Rsq9WOOZ/nzT3bIkg/o7r3bqyGdMgb+QDdmfKQju49rxPSt1ivwBQEUxEShegSB2+CaabKwHNIiejvrpw+rxOywvSLE4/Z3Y1bxrMeI7CyFEIOyuund4eRAxFNbNKPTuNFnY79Daev+76S2gmsm7gxoSD4D9cacsA9Gywp8ycigDz3qxyGbtx34G8q7+rGnjDl3BdE2Ux1h+nT8jGKAAPtGDVdzR5ZBMZUaxLFtxjcGBRNDOcNjDrMKZvIu7qZKw6Th3QwDWJqC7qxw4R0EEHxJR1nPdCyIBqLPiHGbbNi1nLZW7y5Zy6CJN8vsbaBqYIKn19eZMpDUA69+sykWPehilhfV6kdKXFI0Y79ZRKZqB7O1H+oGeJlRcorY7Hu9jIrFo20b1g1OdoRHmkQEtJlTA7Kq+KsE+lp2mLi7K1IWXVDX2vjzZYTu3FxQ8u9mZj5nHN/uPehT3FaFtNvkIMEcUsr8T9BN/dVaPRFqkMOSMjLaeiGbjBJXIdQaRUoSASUSBTkZS36TjEI4oh5Uw8Dg2eAAZwBMUyWcmQJ/+LsOQ9L2IJuE9IBKSgThcjLLnRDN3YQHk8J810DceVMZ6y149atFGQugiQvKHXvfb2GcH55/YysUzdOrN/mk9kdm16YII67sqRmQk9pdV0fUQzWRYxkoi+lNRP6SqrH9zpxIRObnp/YI1MtkBzK/FLEJQd7N3FN/UTS8iQq0ZXLok4rHv1Dz67111uOdcbihuY/L/xxplZuKAAAEABJREFU0wlpCtNPIAdcx3Hp2VnPuEew24QcEDXgWUA+q0JPAbq5F03IAc8Z96Cp3Z5m9LBQEGAQLZS+FFQ/tDODRgBHzIDJYGiihAEQZ8lAGybP84K8qW7sgHQQXg7TTTny6KYO50GJgZ7wM/IpubPu37fYsaNPOJbzJzvu/IdbQOi/YsqcTpNphhTpmLkgq93IdO9eX4PesglTe+pOPb+x/ox3NY878c0Ha49feGhU0rxFbRNOv7Cl/sy/31s1fV57NLl+ArID6XGx8O4T5CAScYiaePOzHScWdR7Ym1jgmU3Gza+YNLs31t25q3v7umVyQfeWREqeW9LQfJFb7rPnWnIPmJIDyAf3o4+qAVno5p4hijWgMCMDQpMLOUA3zx3RtwxVeloMCDAwFkM/tY/FgwADKw4QchDWawY/wrIMhGGylKObtztCv5wHJZw88rnoJmqB7UF6KWMaxtUtPoosy1r04+a3R2znN7Zj/UoiEd88nGtZ/HqCRZAJp+lmBuwhB5AO3tgzxaIyZYDTdUP8fOkSvSbfo8jUNVzn2J25fqJ778bq/kMtXO8BzdIHpnGCohduJciBkLGcF3XGDx38hpC7BxzL/uWShtbrXH0Ze+7ZXMkBztuUHHAvct9kNOt7iiyEBvLhK+DJdOQYeVPdIq5bISGgRCJxNfVPgSAw2F9eEJ4Ng4Awb66kA8JBCtM9GNLBHDlOJ6F7yQOtF9oR+9cSRn/Ycuw7E5nyx2H6Yd/Oaj6sJKehG9MBfqQDJ0tZT+Mm/nOt0oopc7vcz1qHKh0lAcgNJAeiYMVjfITK93PbTN+U1NT1Sd9MnKZVOfX4rr4cfvFRnlzUGYlbK6y4db1jxb+0uKHl+1lgwSFzH5OyiKSyuQeQN3XgyHKvmfQzV3IAAYYIm9qS6oQejH0ElEiM/WuoPTiCAIMYgyUD2pFc/yPe5CAHyPtLpOeim3CvKelgsM5FN1MrRDvSWx14xlsi4e+U7kUNLe91HOfXluPcK9GIe7xVeBOPlFfGTH950dsqpGPacTiFhJoEEWndVcFCzahMFfCTy+rZJ3fwZm5n+c+4EhXz6E95/Yw+FlaWT5ie9XPbLjnIFrXwdgeCUp4kB978bMe0HetsK2c6SEJJfxQicYXIni3X7fcLG/bMkWPvNhhywDgOyfXqyXbMfcO9LKZkE0nlQ1SxB/lUZsCBqxuyEiCmRYWGADfgiPdJG1QEhgEBnCtqcfbswxKDIwOfKekgLI58mF7K0Y0dzDVzHpQIS1eLQC66GeATpGNxQ/MHxSP8wo4737Qd+37Rk9pi3e0lfa27q4VEIJ/Kz3YA6eBDTZAEZHCq5DHVwU8sxSF2MmUQKa8ywQwVeZOwWXDI+rntBDmYNKtLpoG4FqF2CxZdgm8Z6y/ChGnb+4uPSDyyXaaerpDr1lZilT236P5978jQwb1jS95wkQPuS+5RaSJ0475EFgIbJsw9CSFGPkxWywsIASUSBXQxi7gr3McMXgx6JjBAOgjdpt68Qyq5uqkTImrhiBh0TW1xdTO4h+mGdBBJSehesmLfUsuyV8pUxm2WZf1EUtrWnfzlBd9MSCvwOYl2HCh1SYc4yJLEOojGzTWWTAkwNcAUAc7Wp+qYyiqpGhejL0KIOvnWhfu57f5DLWV8vTPW21lqSg4qJh3T1du01cjZ0yZAgSt7kpCJWx3LWWnbzpNLGpo/RZ4ncY25N7i3Pdm+hzhvImXI+wpkZKKbZ4D7NKNowCnkACKai26eAe7VAco0Y8wiEGi4yU0aqEALFYE8QIBBjgFvuNYj8AY+XKSDMDADuwmM9BPZ2OL7Wz7uxJ0HLNv6su04/2tl/COMTjhdHJiR3T3NW6r5xkS8p7MEAsJPN8tlKoA373KZGshQP+ZPIVcyRdNZOWNeh5P43Pbmmp6mLVVl4yb1mpIDISPdVg6/+ICQQdZYb+ICGInbDZZj3SgM9T+WNOz7tpsve8gBiy+55nIaunFfQA6InIUJo7dLhHLRDYE1IQcQYmwx1S1m6DbWEVAiMdavoNrPOgfeChm8TNBggINwkMLkcfLI56KbOWUG6TDdhK5z0U0feV7bF9/ffK0QiHtsy77ZjlmP+zUkTrGGcDphdb9ybx6ko3ffLpyQNVyftfa2l0/HkCTIFqQJ8hQXUiH7sq4d6xJ4hNnqRw6y1aEtv0WdtmM95cStj0l04h2LGpqfWHRf8/SkDu47rrspOYA0ck8lqwfu0M2CTlKgoBRyTyOfi26enWqpq9tgEBhjdRiYxpjJaq4ikIYAgxtzyoR20wp8TiAdDG4Mij7FA7LQzZsh0Y4BhRkZhImRz0U3A3ROpGNRQ8syy7a/K2+w/2rFHb5cmWGGZfU0bq6y4nFbHGSobkL67a+vmoISIhh8TIrjvgN7KgjDF0Pq3bejMh7tjfA1TpnWKXNi0Ujntpcn9zZvC3WyLjkQnGrALSxlW9QZsezNkj4m5LDfLok8J9f5XNHFPZ0rOSBqEGq36Obe417lnpXT0A3ZiEgZ9VPkkEe3Lce6FTgC3BgF3kXtXgEjQLiVtzUGLZNuMrBBOgi/hskzIOdKOnDcpqSDN95DYUYky7G7f3FD8/UyKn/VcpzPROLW05bPP8LmvRm/vPARs8Rhlojzq27fsGqyZUec0qpxfbwtIytv5KXFmJx4zC6pGtdfUi1YVI3vbd+0egoYsX4EXLKlqhkndva3tVRCyrLJuPmsMyFSJCSFe8vNTuxlqipqx60vOk78cbnOTwuZuFIKuLchwNzrchq4MQWHPPdLoGCykGdBmrKGgxzwLEBWTG1JmjQqO230KBFQInGUAGr1UUWAQYqBU17QQ+1g4M6VdKDblHQw0CMfaogIYDcDLXPVchq4YXPt4vv2XG9Z9hcsx/q07dh/sbL862ncVI0zrJg025fQJIiGvGm76yBq5p56oP6MdzbWnXr+vlH5GuVofQUzoN26U85rrX/T3zeOW3B2M4RKMK3pkSgP2PnBLnjHspEDP/mwRZ0Rx/6RzbSVZX1vSUPL10UH9xX3jByGbpADhCCq7MOSq9vEFxCdI0piagtEGTuI1oXZoeVjGAGTm2cMd09NL2AEGMwgEIR+TbqJPAMbdcLkIR3M8bqDcpg8uhmQeQMLkyXsnBPpWHjP1s9bkbJP2HHnM7ZjvZCtAd6c+VAS4XM/mb4De8t7hGgU2zoIPyzC8vhGBmSsevbJHXw7I9px+L8r5xcdfFsjsz7kIN7XUyKRBq5vZnHa+WHdIb/4ODxt9VG5Wd+7+IGWh2d94PoJogSnLLvQjXuRe9JkfIccQGiRD1UsAujGDgiunAZu6IUwp+sOrKKFYxEBkxttLPZLbS5sBHDyDE4MaiY9RRYnz6AWJm+LAPLolnFczoI3wsI8R8gHSx4udXVjz+Gc7H8r3/T9179RUlH7QQl3f8ay7JetgH89zVuqy+undxM+94pFOw6Udu/ZUN3D/1wZKXEqp5/QyfoJ3qS9cno8EAEWqwpe3ZXT53WWVNZG+aYG/8cIpMwrnSAHk4Uc7NsJCfUW+R6jk/UoXBdfAcm049YG23KusCy7bNYHvvB/T/r8o39nWRb3vuwCtx4pJSLFvSaHoRv3LvfxcJADdEOcQwlWqJUqkLcIMADmrXFqmCKQBQEGSN6kGDCziKSyCasiz4CWygw4QJbpjFxJR4DKVBGDtS1nRra86Ydb7i6prnuH7UQ+E7Ei66Ve1o035Vh3e5k4vJTdhOJ7JCTf0+j5rPWM+V389DGrIi3wRQByBvniVxoIQMogAZA0zkl88CqMHCDnJj4z3tuyo4br5OYN2Mftbjvm3OxY9m/Hn/LWX5zx3b9dPUDGP4N7jPuN9RX+Ekdy++WQyB73vhyGbkT2KkTKhBxAmLHFVLeo1W2sIaBEYqxdMbWXAYy3PgYnEzQYwCAdvKGFyUM6CNvmMqXBIIz+MN08a9hiZPdZP9r9UElF9VtKnMinLSe+NUw53z4or5/RzRs0oXeIxVj+rHVYf0erXDDu4/8YgQT4rZ8gH3IgpK4kzEZ08e0OISQ4/EDxSNy624pFbymvm/a9s360N/W/ugZU4r7kPuaeCxBLFXFfQjqIHqQysxzkuqgT3RDo0H5maU+z8xwBBrc8N1HNUwTSEGBgZIAkapBW4HMC6WBgZCDzKR6QhW7e6E1IB2FgSEcuuhncQ0nH4gf2PWLb9unR9oPXO5a1e4CVGRniiKqtSMSpmrmgi5A7oXdC8ITiJULRSRgdgpFRTU8HiUBiGmPS7F7v+gm5BjVgX14/o6+sbkoPi1lN1Lu/+KBumLxtlz4Wbd/3yUiJfcnihn0/e+t/tXC/BlXjOeE+5RkIkqMsV3KAbsgBzwD1wxLPCfaqzwlDagyW60UdgxetiE0mEsGbHoOSCQwMXMiakA7CtAy4yJvqhnSwoCxMnsG8RoRCdS9uaPmFE4vO62na+oXycZNCSQRh8b7W3dVl4yf3ijM7vA5CGiIED7EgJC+nug0DApHyqjgkDbImx7Gepi01EgWqKR03uS/avr/ChBywTiXxi4+W7dzboVaWVta92LVxzfVyj9T3l1mr5H45K6BSruSA6Q3hrtZwkAMINESaZzLAZC0aiwgokRiLV604bebth0EIZ8xgF4YCjnswpIM53TDdkA6iHdgSJks5djNIZyUd5923tXJRQ/MTVjw+pWPDs7eU105ppGJYEsdVG4/1R2Jdh8oItRNaJ/RO2DysbgGU50UXSmvro5A2yJsT7Yv0te6q4uNWQuy47qE28osPJ9ofMf3FR0ltfXPHa3/5d8dxnrNs61khE/9vQCPcdzwvRraIHu5pZHl25DRwgxwQvUM+UDBZiG6eS4h1Mkt3hYCAEolCuIrF0QcGK5w8UYCwHntJR5gs5Qxu1GGg4zwsYQuy2BMmS5QjkHQsXLm/rqu09jdiQFXXlpe+WFpVdwDnFKZYSERN187XJsnbaYSfHkZKy+Px3s6SnqYtVeLEqjVtGFEMYt2HSvmPwPiwVby/t0QiErWH1v+5nqhR0LVMTJUkf/HB+pYgWcqIgrCos3f36/fZceurkvfgooaWW2SfbeNe5Z41IQcsYB4MOWB9Rbb23XyINMQGW9w83RcAAkokCuAiFkEX3EWQDIgm3WWgIoxqQjp4BpA31U3YV3y+xRyxqS3oJsw8QH7JfzdNi/TEnrAcK9rXsvvz0e5DtoTKA+3mC4oJErF93aTS2vru0qrxfZHK6mg82hchKjFsqaejKL94mQueQhpKIuWVsZLK2v7S6gk9ffv31Mq1qhVSEehoU7/42LvRaIqDyFNvy46aWFf745blfFxuyI8ubti3ksjWgJvMsiAHRA+4z32KB2Rxv2JHoM3JWjxnuZADdEOsIdhJFbob6wgwiI71Pqj9hY8AAyDOlTelsN4SNsXZM2CFyVKObgZDBlrOgxLPC/KmurGDsLIv6Tjz3j1znWjkNxHbapU3yxv55Wi9v2EAABAASURBVEXFpGO6IjL37mdErLu9hChDT+PmGkdIw/iT3944ceG7G8effM5+/SrlwkP5hAHXpP5N/9BU98bzGuV6xnr2boJQ1EAC/a4teZCDvtbd1VxnzoMSU1eJX3zsfb3GjtsvRRz7Y0IopneV1Kxacv/+06yB/7hnIQc48YGl6Tk8D9yz3OvpJf5n6Oa5MyEHEGrkTXX7t6i5eYUAA2NeGaTGKAIZCLAegQGKwSejyPeUAQrSQRjVV8CTyeDHtEYuuiEzJqSDMDK2+OpefH/LiWXlZU9Ylr3NilnLIQhWJMLHolzdlvtP3nIT8+f8EoC34/IJ03tkTr4TZ+LK6D4/ESgbP6W/evbJnZUz5nVA/iCBXGs/ssD1zPUXH+6iTsdxDthx63OOY69z7NiqxQ3NH8xAhAXHgyEH1Rl6/E5zJQfYAcGGaPvp07wxhoASiTF2wYrQXNcZm6xHgHTwxuXrvH2wQzdhWRPSQZh3MKSDsHJa00vubzrNsi1IxFo77tyaIAoSpmaxXpqgnBASF8dTo5+1FjDG8AZJSJA/IYGQQUgh3/rIXBOBjEsOwrrLLz4qpszp5Ke+rmzEsb5lW/a3LMteuejHzf9mpf/juYDgmpADHD3yPCPpWvzPeI6oYyrv6sYef42aO2YQUCIxZi5VURqK47al5ww6sgvdGMSQNSEdRDmOkI5Q1Ra6GSwJ+4ZJQzoYrLElTfas+/cuduzIbyTzT0IiEh8WgigQpsbZSH5iIwQuc+s1hMRZwFepn7VO4DKW/8gUR5y1EFUzTkx8bruvdVcV3/yALLr9SsgIOehp2sK972Zn3Qvx6HKi/RGJdHA/H5aLO/9jOdYn7Ij9qcUP7Fthfdlxx3kcPfck9zLP1WH57H+533mWkM8udaTE1W1CDiDYRPdMdR9pRY/yDgH3Bss7w9SgokeAe5NBhsHJBAzCpAyOhE1N5F3dhGXD5BmkmQYxtQXd2JFGOhY/0HxOxC79je3Y/1fC0N+kUZxIf1tLJc6Fc0LeQiyqxTEk1kFUSkic0Dghcso1jX0EiCRAACqmzGUKzoI0cM3dz21TZsXjttwD3HehHa7gFx+tO6u90Q3bsV6IOM7HRM8Ji4/ft+rNDfvekFREm4MhByx4TqrIuoMcMDXH/Z9VyFPA8wThhnh7svVwrCHAYD3WbFZ7xz4CJj1gMMIRMzCFyXMfI8/AFCZLOaSDtzOcPedhydVtQjoYGAeQjiUPtF5oOfZvLMd62HKcO90GCUvzQaJIeWWcUDchb0Lf5RICF4ei6yBcoApwTwSKb35AKLjmPY2banoaN1cx1VUx7bjOXiEHHId1nShHSUVNtDvjFx+OYzfbjn2t3Oib45azasn9+96X1MVzwjNgQg6IGvAM8gwkqwfu0F0tEkT7ZBe48XzzDJrqDlSmhaOHAAPw6LWuLSsC/gjgiAntMij5S6TnMhC5A156ycAzwq7I56JbxmKLMO9AbQNzXN3USZQuamh5r+PEfy0E4l7bse5JZMqf3uZtlYSlS2vq+xMhbgl1l1TWRqsk9I1ziGT59YZU1a1AEEh8QyLjc9sypYWTtyJllbGexk045dDeyv3S1de6u9qNangrROLWfzqOdbdjO48ubth3o5TxrBCZ4F6V09CNZ4XoiAk5GM5FnaGGqsDoIKBEYnRwH9lWx15rDHA47hFdBOkD02BIB2FjbE+oW7Ki9WKZb/kFUxm2Y9+fyJQ/hKG79mwYL4eJ0DZ73kwlCtFF6JtzTcWDAKSxcvoJ3ZXT5yXWTxCpovfdjVvG+5EDyryJqS9+8dHTvMWXeEQc6yEhsp+2rPgNixua/0vquuSABcpyGrhBDpDnuQwUTBYiy7Pja0tSxt1BuJE31e3W030eIaBEIo8uhpqSQMB982FwSWSE/GEAwnETJg0RtZiLZXDLRTdhXeZ+w3QTJsaWlO5F9+9b6sTjjziWw9cHf+IqIFx9aP2fJ8Y6D1bG+rpK7UiJw4LKWPehUubKNY3sFynzCe/+Q83coxZfKY11t5dZTtw+tOEvk03IhJDQzlhnWznrbtx7zbsXIvusY9kfk+m10yUy8ccTP//YFCnnnpVd6MZ9DTkgUhgm7IgA8ugWHi1nwRvPLwQc+WBJLc1LBJRIDP1lUY1HhwCDCYOQyXoESAfTIMibtIpu5mRNSAdh3MGQDsLG1uL7Wz5u284Dlm19ORK3/wfjiEKwDqJjy1/r+ttaqktqJvTIVEY/nztmjlyTfjnTvQesSMQprZnQF6mo7neifSXtm56fyFQYJJR7yS8R1WC9DYs3/crJi8StPUIornas+N4Jp5z9u1P//Q8XSr4JORAxi+eMZ8jmJCQxdZILOXB1Q8hDVGtxviGgRCLfrkhx28PcMG8zOHsTJBjUGIBMSAekYDCkg7BumC2QDkgNtlhLGvZ9SgjEPbZl32zHrMepzFsi6yAIWZeNn9w74bR37K0/413N409664F8+iKj2rIwf76QOW9RG/dI/Zl/v7dq+rz2/kMtFRI9qeFe4p7ySxKV6LLicVvkuN/9RBJ5kbj9VcdyHqg65uQHz/zWy8sk08QXQA4g4Tx3UiV043ngmTYhBxBwon+mukMbV4GRQ8Dk5hk5awbTktYpFAQImzKIMPiY9AlZSAdhUVN5dFMnTJ5BGHuQD5OlHFuQjS5qaFkmA/T3bMv6VyvuPEFImkHdfUvko1MM9qW19SYEBd2aihwBIg2sn2BBJVNgfFuka9erWT+3zS8+WHgZFL0A0kjcWiH36efKJk6//qx7d6QWAVMWkLjPIQeQ8gCxRBHkAPLB85HICPmDbgg5xDxEVIvzCQElEvl0NYrbFgYbBh6T9Qg4eeQZeExQQ5YwqwnpkLE18fEpdJuQjhoxAHvaFzc0/39S+auW43wm3tP9R0LRLH4jXJ1YSDnrJP05p4Cl2+AQKDP83HbFpNm9JTV1fUa/+Ihbz0Q7O66yI6VvXnR/8x8WNuyZE2IdC6AHQw6GY1FniKlaPFII5EokRsoubae4EGCBGVEAnLdJzyEGhEF7DIQJqyKfi25IB4NlmHrhDYdJx5IHWr5iW/YXLMf6dP/+xhckCsEbY0Vp9QT+r4UOBnd+6hemUMsVgTAEEt+fmLmgs3zC9B5Iavfe12sgrd4IROXU47v6DjRWyXRIaOSgrGrc5p5d669xYn0dJVbZc4vu3/eOEBt4lipExoQc8CwhzzMoVUI3ZCHmEPRQYRXIDwSUSOTHdSh2KxhkWBfB/GsYFgxg1SLEgCO70A3dkA6iHWHCkA7Ctrno7l/8QMuXnbj1qXhP1/XdO199jdCzXVrO55C7CEkTmg5rWMsVgVwQ4J6SqY4evjlSUlkb5f9igby66yeYOiuvn94tBINnJVR15fR5XR2v/Pk78f6+/7Ft58nEOp/stQZDDmxRZ0oOeP54bqkj1XQ7OgSGv7YSieHHWFsIRoCBjrcmBo9gycOlDDCQDpM1BpAO5lxz0U0kwpd0LG5o+cTi+5ufXXR/y+5FDc2/P+1rf75s0X17b7HizuX9h1pv7Nz28hYn2hepnDGvQz9rffhi6d/hRYBvjrDmRghrJ+QVEuuun4AcxHo7S/mlUJgVEBN+8dG54bmfW078Rst2vra4ofk7AfV4BnH0EO8AsVQRzyDPronP4RnkpQL5lAI9yF8ETC5q/lqvlhUCAgwWDDIm6xEgHYQ9kTfpu6vbhHQQps1KOoQ4fE8a/LJl23+0betWiT5srJhx4vesktLL+vbtvqmvZduWcgk1y6DeWV4/g3lkEddNERgZBDLXTxCJ6Nu/p6KsbmpPb9NWo0iA3LtdViTi9OzeuCoec66wLPuCRQ37nlh0X/N0y/8fzyHPmIkf6RYVuZADdENSeMmQqmNnK0ZLTW6AYsRF+zwyCDAIESYd1kWQhl3BFgYv7EmrsmjFvgtsy/6EbUc+Zcetlf2te9daljPfduKttmVNsMvKeqv0s9aW/ht9BCCxQgg6hVj0RrsOlsX7ukui3YfKiVKYWMevivpad1dbfT1bI5b9MduK99sR+/lFDS3n+tSHHBC949nxKR6QxfMFqTEhB5BxIhOmugc0phkjh0Bk5JrSlhSBNARYj8AgweCSVpDlBFmcPINLFpFUtvj3w4sgUznBBwxu1PG1RcjDO6X6r6xYfBuL10qqx/9nSWnFcf1tTT9wYvENpVXjF/a3NZf3NG6u0qQYjPY90CeRCCces/k6pkTOSq143O7csmaKRCkq5T4O3CAi/OKD9RZO3InacfuLjuU8Jg/H00t+3HKVT2WeGZ4fFkz7FKdlQQ54aeBZTivIcoJupicD7M5SU7NHFAElEiMKtzbmQYDBxH2j8WT7HkI6CHMysPgKZGSimzCqCengGUA+SHeV5Ti9OAi7vOLfrEhptRWJVJbVz1hul0QWWOWVV5RPnfv1ssmzPldaP+2DJTUT3mKXls3ljVDTwTLFYHQwiEf75C6tjpZUj+uLVNb2tW9aPUUIgu9/7OV9fvjFR39bSyWkmfyIY//ItpybnYj1nSUNLf9JnifxnA2GHDCN6FHje8iLA88lz6evgGbmBwIMovlhiVpRTAjwlsFAwiBh0m8GEkgBYdQwecKmgyEdkBo/3XZ/Z9tLMpXxZhxi7ND+r9pO/CrbKnm3FbfeY9lWNFIS+Q+rtPR/7PLK1pKq2lNL6yZ9unzqnBU18896ombewnuq5y38ouyvqD3hTe+snn/WSTUnLKzRL0jm0Rckjy9sW+pOOa914sKL9o5bcHYzPxftaTzy35VbPv8Sv/iYNKurt3lbdao4bj8hxx9zLOu9ixtafrn4/l2T5NzdeI557nim3bxs+7gUIM8zLYehW4dISEDE4pmWQ93yEQElEvl4VQrfJgYRBpOjXgTpAxW6IR2EUX2K07IY/GokB1tkN2CD8Ixf88kTHnMcZ2fNCWcsL59+/ExWuMcd51R5Q/uK7Vj/89zSyV9b/eEpt0u66vkPTzlf0jEllT3VJXbkTY5l32g7zq8ty+51IvZb5Hi5kI/HnIj1Z0n/7djWfzgR5zMSPv6AFbEW2rYz1dJ/isAQI2CXlDl8y6R69skdpbUT+6Id+1k3UcsvOpxYP446rcWqGfO7Er/4aN6Wmlaw49aGSLzkYxKdK7Xs8ucX3996drLSYMiBI3VNyQHPJ8+1+isBLR83vTD5eFUK2yYc92B+eUGYMwwZBj2cPwNPmCzlDE6EZTNJBwSDssR/8y2Ch+LV5f9g2SV7LSu+UgjA83aJ/SPbsf/y3IenXCrlA7ZnP3RM97NLJ65b/eHJ//v8R6b+x/MfnnzF85dPOff5D0+ZWV3pjBMicrYTd5ZbEfspy5LHMGL9neT9n7ht/0r0/1HSA1bE/ppl25+KR6x/FMJxpsh53wIHtKkZhYbA0PcHElw5/YTuyunzOvn+BP/3S/fu9TXu9yfcFg8Tj2O6evftPBKVkELHivXIfX+zY9m/tez4XxbS/mKkAAAQAElEQVQ17PsXyWbjOXLkgOdGdqEbzyiyjAVhwkQLiUYiHyar5aOAgIxgo9CqNlmsCNjScQYDBhE5DN0gHdQxlXd1m5AOwrCZpINBjTYhEKViHWHVQ7LvffFDE9uEBFzWF+uoj1j2G6tjk6uf/8jkT0tZztvTH5ra8cJHpq5Z/S9TVz5/+eTbnv/w5I88/+Gpb139kalT+2Kl9U4sep5lW7dalvOMEIpy27LfJY1I5CL+Gzn/vUQwfuxE7H8XcvEJK2K9W+ROi1v2BJHRTREwQoDpi6qZC7r4lQYVEoRiz4bqWHc7zwBZFoTDLi2Ls64ikeH5E4lbd9tO5Iu2Zf3XkobmryaLeE55BlM6kvl+O75Kmws5QDfPpsmiTr/2NG8YEYgMo25VrQhkIsAgw+Isph4yyzLPuTeRZwDJLPM7J0wq45qF8/crz8xzdcelgHoQCwgEUQ0GOQgEb0K8ZYnI4e2ljx53cNWHJ7/29EdtZA5nDuHflz5af3D1R2esfv7yKT+RaMdXnr988lIhGm9+/iNTJllOxWQ7EnmnbTlfl6mQ54RIjBPj3+eU2N+0I85vnYhF+i/Ldm51SqwrhXD8vWPFTxFSQr+G0MrCVFWMveJXGlX8HzCTZnezfiLzc9v8HzG9LTtqvAQjhZMT/5XcWx+VB+SSxQ/s+9lb/6uFSB7PDM9WSizggGebiIcJOWDcIOphqjugWS0aagQYrIdap+pTBPwQYJDB2TN4+JVn5jFgMHgwMGWWZZ5zHyNvqhs7ZPxLkA6iEjha8qKiGALBgGUS1RDxkdue/8j41ueWTlr13IenPbD6w1O+JOlSSQufv3zKBLs0Pl0Ixnsdy/5W5+7XNrate3pe+4bnPtK1fe0POratfap964u/a9u4asXBV5/55sGXn/h86/M/+5fG39/3Dzv+9z/etOOhL75BU/FisPORW09qevLu41uf+9kx+/708Al7fvnt07b/ZPmZ21fcdFrjb75/woE1v5674+Evn+l3j+x8cHl89/9+42uHXv3T7APbn39p+juv+kd5It4sifUTC2UflE6T8nmS3ibJT06y0zaeb8YRSH9agZ6MLgIMwKNrgbZeLAjg6IlEZK5H8Os/gwVhTAYOv/LMPHQTJjUhHYRdkUcW8sAx+iAQtAd54XxMpef+32klz3146uWrPzz52r/dfO5bN3z9gxNe+/f3Hvrbl9756qu3XvTy61//UNPm7320att/fWb+tgeXX7j7F3dc0fL0A7ccevnJu9tee+ZHB9c99YP9a37z3f0v/vL21uce/eq+Pz/8lZanV/xb0+8bbh6epHrzDdfmp1d8oeXPK6/ft+pnn9n3l59e3/zMT24SG/9t37P/8zlJy+T4/5M04H7Y+3/vuu71Oy6r2fKDa+raX191X9n4ST+Qh+f7kn5okO4QmW9KukdSpvwLkvcLSe73KyQAZ/GMus+sFOmWDwgokciHq1D4NjBdwJs/g4BJbxkoiAqYkA7CormQjjoxADJBPQgL7UAiICJSNCY3BloGXN7qBnTAifb1R7sPtfe37dvX27prd2/Tls3du9a/2rV93Zqu3evX9TXv2BrtONBqxfp77ZKyipKaCZPLJ80+tmL68Qsqpx07T47nlE2YNqNsXP2k0qpx40rKKyos27YHNKQZhYKAIx3BaROV4zqTJMviuWHvm/ra9jVHD7U2lk+cPbds4kw+q23qX2gnm+6Z0ph7f3PM84p9vARIkW75gIDphc4HW9WGsYsAxAASwYAR1gvClhUihLzsQjd0M7j0h0pa1niR4eeV3PcspsQejpmnhYyMxURY+BPSL/qRNWUrd/p7o0IyOvvbmlt79+3c09O4eUv3rtde69z28ktCMl7t3bdrR7zrwAEr1tdvl1ZWloyrn1I+6Zjjq6afcNJhkjFrTtmEqUmSUTu+pLS8UkhGznZks0/zrdHEEgKB05a9Q8IWHL4cy5Xx2fo7Dx7s2797e1ltfX3FtOOOT94LPpJpWZAW2kF3WoHnBBJxZfKcsYHnPkg+Kaq7kUCAG2Mk2tE2ihcBnDMDz2AWQYahBukoEyEGFtll3Yg+jJfSGZJYJEmkAxJB3UrJG8vp38R+nuMhT0Iy+mNdbR19ByEZu4RkbNravfO19Z3b1q7t3rNhfe++3TvjXQcPWvFo1C6rrCoZN2lK+ZQ5xwnJWFAxde5x5ZNmzS6rmzqtrHZCfWlVTa1dWs514F7QZFljCQNx8rYkS5KD8+Zew37L71+st7urr3XXdolulVbNPPGkSEUNz6mfqDeP5zFQrwi/VxJRN55hEmRCsnQbbQS4cKNtg7ZfuAhwf/Gwhzl6FwHClTJYWUNFOkpFMTohEdiyXc63SWqU1Dp8yRpJ3ZOlHywSHdEU7+vpjHUdPNB3sLmxt2Xn9p69m17v3vnqus7tL/+1e8/GV/oO7BGSceiA5cSidnl1Ven4KVMqIRkzTpgvJGNu+cSZM8onTJlcVls/vrSyttIuK+P64Ew0WVY+YiD3ly1RPxKEIjHNwTXzJRTCLaN9rUI0+3t7Kqcff1LZuIn1cp8GbTz3RCbQGSQHkaCcMYVIIuSUc02jiEDYRRtF07TpAkAAEsHbf7dBX7gXkWeAMBBPfDKXwYdpjUx5dDHIQCAYaFjkyToI3mIyZcfyOW9o+WW/4zjxvq6eWIeEuA82NvW27BCSsfH1rh2vQDLWdO/d9Frfgb274j0dhywRtcurakrrJs+onDx3XtWME06qmDrneCEZs8vrpkwtq50woaSypkbebIkc5Vc/i9canjkcPmQHEsGzRuI4ExWnv62lJdaxv4XpMLmuRAQzZbzn6M2my5VzdQixSbxwMGa4ZbofJQS4aKPUtDZb4Agw+DOtYUoMGBBY8Gji7AmvQhISujNwZJqCMogEJAYCAZFgkMoQ1dMRRUCIQ7y3qzvWceCgkInGnubtLsl4uXP7OiEZm9dHDzbtTpAMMcyuqBlXVjd1ZuXUufPkrXZBhUQ0yifOmFVeN3lqWQ0ko6o6UlpaKqK6jTwCLqFgD4nAl5AyLYn3dxw81CcRqtLaiVMqpx17XKaA5xxdPKclnrzMQ9ZKuHk8/1x/nnU3T/ejgIDfhR8FM7TJAkQAYkC0QMKhob0japAr6SDK4SUdEBfaJDEYQSCYIuHNJdQAFRhlBJx4PN7b2dXfvv9A34G9QjK2bevZ8/qGrh1/W9u145WXepq2boi2Ne0VItJh2fKvsnZ8Wd2MWRVTjp1fOf2EEyumHHNsef0MSMaU0pq6upKK6iolGcN+TV3Hz/PGMf4EEgCxcBsnPx7r6erua925zSqpqKiateCkSHk1hN+V8e69kQ5vvt8xuiETPPN+5Zo3Qghw4UeoKW2mMBAw6gWLqyAHPOQmFRgIOkTQlHTwBuLqZuCChBCF4Jh8SATRCFGp21hHwIlHYxKlgGTs79u/Z29P07atPXs2rO/asQ6SsbanacvG6KF9jfG+7k7LjkRKqsZNKJsw/ZgEyZh2vJCM2ZCMmWXjJyVJRmWVSHGvjHVo8sV+HDpkAhKATWCLb3EJBWV2PBqN97Xu2uFE+3orZ8w7qaS2Pttn3dGDDnSFJaKNyDOGhMlq+TAhwMUeJtWqtogR4KHGoTOAhMFQJQJEE5CXw9AN3ZAOBg/qQiB4uyE6gQ72oUqKQeAvf/nLiY7jLGTv7e/DDz88l/xNmzad4s0f7DH60cfeT8c111wzqbOz8wwSxySOSRyTOCZxjI5//Md/HLdz5843futb3/KGsilKSw7eqaejs//QPiEZu/f0NG3d2r17w2tCMl7q2vXq2p7mbZuih/Y3Of3dXVaJ/KsaV19WP3NOxbTjTqycdvz8isnHzC2vny4kY/Lk0urx40sqqiqFZOi4mIay8QmEgueS5x4SAY4kjsnj2Oo72NwU6zywr3LynBPK62fwvYnMBpBFlymZ4LlnXGCaI1OXno8AAokLOwLtaBODRGAMVqsVmxkEOmVvsjEAMBBQJ0yeSASDBdEGCARtMXVBBIL2GMTCdGj5CCNw1113tdbU1LxE4jizefIoI3EMibjnnnuOmzZtGlGtTHHjcyfaH433tHf0H2pp7W3dvae3cesWIRmvdm1ft6Zr12sv97Vs2xzt2Nfi9Pd2W5GSspKauoll9TPmCslYUDntOCEZs+eWTZg+QyIZkxIko7xCSEZEx8zwKwARIPFMQyJczDhPHMsU1v7+g407S8dNmlY5de5cH5U8y8hS36c4LYu1VV2Sw1giO91GGgEu1Ei3qe0VLgK8QfAwQwxMeoksAw4kwES+XoQYjKgnhxYEgrZMpkSQ15QFAaIJRBXc5I1W4NibmppOc8vYI5+pqq6uroSoAuV9fX1vcqMJRBnIJ3GcWY88ykh33nnnLEjE1KlTy+Sf/dnPfnbGK6+88gba9+p0bfLmZeoNOneiff3RbiEZbX5f+3xtXV/Lji3RjgMtVqzP/drnpHK+9jkt82ufk4RkjBtXol/7zISb55Rnm8Rx0tckvkORIAfR7o6O3v27ttllldWVM09cECmvrPAooQ51k/U8Jf6HjANVUuTVIae6jQQCphdpJGzJgzbUhKNEAAfP1AIpTFUupAPZaaKQiATTIAwyvLEQnWB9RLEmBk2e4WxJIEttXpnEQJ4sichUx7Fnn3021y6ZZVknnHBCZZIsRO644445OPZUoRycddZZtUIUZskhemVnWSeffHK1/ONaWZCAK6+8cqqQBL5z4W2PY1KijvzhmCSHliUkJHWcyJA/bW1tsc2bN/egc/HixUShIueff/74+vr60gMHDkR/97vfQUSxY0iS098XjXYf6uxva97v87XPV3r37doe7XS/9llWKXP9U8rDv/YJLkNin0AylvS415NnlmNJDs+t7OV69/f3g6cTj/ZXTp9/Ukn1hDrpn7u5RIL+unnZ9owHkIlx2QQ0f/gQMLlAw9e6ai4kBAhD4+h5mE36xQPfLYKEJWXnuzHY8JZRL6XjJUFQiD4wKOFEWRtRzAnMeYZ9k+uUk/uUTPJc4ExsEfdciEOHbdtr7WR6y1veskUkIvPmzdvg5rF3nfr06dMT7bv1m5ub+9/73vdufq8kjiEVb3/728dRbpok+tB7xRVX7KC++BhHyEoTdjz33HNdnCcJTWTRokW1EAuJnPQ+9thjQ0ok6HO2JNMg0VhXW2e0zf3a5+aMr33u2hl3v/ZZWun92ueJldM8X/scJyzo8Nc+uY+5zws9CaSJD1nx7NJXl1hxbPUfaGqMdR3YL9Mc88onTOelwZWHIHDvch6WGHvQy4tFmKyWDyECphdoCJs8okqPCgoBiAGLIFmzENYxBs9qEeLBl53vhpNCJ2+gkA2+SLlTJJsljeSXI/O5LfADb98kTp9Bm19Lsk/JSD5vegJjYov+8Y9/PNTV1RUTh10rDv90kkQpiDak6ohkVIjG8ZQRrZBz9KInKvrQbz399NNtjz/++EESSaGvoQAAEABJREFUZAOZOXPmlEl5XJKTTByTsp5HIhGcB9VTbfz+979vI/owY8aMMoly1E2bNq0UYrF69epADERJWh+G8/zw1z7bDvQdbG7sbdm5vadx0+GvfW7ja58bXundv2dHvIuvfcb77bKqqtLxk/na57FVM+YV0dc+bXkRIFncO5AIfBB7q7/9QGv/waadpeOnzKiYMneOXCs25Ngjxz4sMd3JuJHQGSas5UODgOnFGZrWVEuhIgApYMqBQd2kjzzoyDLIZ8oT9oQ8EIFgMGBgQFYGoExRPR8KBFjgyELHb3/723txzuj80Ic+NFne9hO/6mAvBGLhzp07e4UMvOiSBOS86ZhjjoEgerOG7PgXv/hFO+0S5finf/qneggFxOKpp57i3hiydoZHkeMIyeiJdRxs6zvY2NTbsn1Hz96Nr3ftSH5SfO/G1/oOf+2zTeZ24lm/9jlhytSy2vr6ksox/7VPiCcEgcQxUQR8kR3tbk+sm4iUV9ZWzph/ol1WwQsFcsiYXB6inIwVjDEm8iqTGwK+0lw83wLNVARyQICHFofPoBBWDdLBoJDpALgXKYNAMHiwChsZohFhOrXcBwEcP9mnn356tbzFT+KYRYrnnXdeYh6a6QPy3HTdddftKS8v/+unPvWpbUQocNYPPvjgXPac/+EPf+ig/rhx40rcOt49kQrKSRxT5trA8dGm559/vgOic/7559dBKCAWEIyj1Tuq9R3HiR/52mdTT/OAr32+Fj3YuDt++JPill1RXVtWN3VGZdavfdZUj6EPcTFeQBKIQPHSwBgQcfr7WDexzYnHYlUz5p9UWlPHiwWyvvedz/Vj3KAOLzc+xZo11Ahw4YZap+orLgQgEQwEOP6wnjNYIM+D7pVlnQMEAiLBTzshJehjkPHK6XEOCOD4IQA43TvvvPNYx3EW/vznPz+RdQb9/f0Ojhl1MmWR+N4E5SRkqbN3797+P//5z0xXWZyT79anXmZCL+UkjmkbGzLlTM9ZA8GvNoiIUIfoA1EIjr32c16QyYnH472d3RLyT37tc/u25Nc+X0587bNxC1/73CNEpIM5ILuydryQjOTXPo9fUMHXPg9/UnyKOOO6koq8/aQ4JIFnnT1jBH4pItMcjTGZCqqYcuz88gnTJso1TuTLPmxjDGH8GBcmmPflY8RALswYMVXNzEMEmIbgYc0kBtlMRdZLOog8QCBIDCQQiA6p7DflIdm65YIAUxb//M//vDkz8oCDl+jDdkl70PeWt7zl9WeffTbtGlLn+uuv34GOxx9//AByJPLvu+++Zhz5woULeesjO5FeffVVUd3F9bUov+eee5qpnyjM4Q9RBtZbZFYhnygE+RAKiAXHxZiceDQmJIOvfR5I/9pn4pPia3uatr6e+Npnb2enZUciJVXj6somzJiNU66cxtc+j+Frn7PKU1/7rK4SKdM3/uGAHBLBGEDiOEEo+ttb9/e3Ne0qrZsqBOkY/sMu0ygD93OFGMpLiux0G04ElEgMJ7qFrxtiwJykyfQDpAPHA1lwjyEQ3IPkkXiTKHzURrCHON9p06a9bNv2i25iPUSmg4dMuOXsqUNdTL3kkku2k0ci/2Mf+9hOpkDmzZv3CuVu3VNOOeU1dCNHuUtUaIt8EsckjkkckzgmcYxOb5tuO+S7CULh2ufm6f4wAk48GpWpkM7+Q3ztc6/7tc/16V/7bOFrn51OifyrGj+hrH7GMRXTjjtRSMb8LF/7HCmSAYmATJA4Lol2tXf279+9PVJRM27iove8c8G//SLoP/46DMLh/4odMsEY5eaF7bV8kAgwiA+yqlYrcgRg+1WCAQ+r7EI3HmhIByQCAkE0gvAjBIKfdYYqUIHiRsBde0G0w52WKW5Ecu+9E+0XktHe0X+otfXI1z7Xv9a1/eWXUl/7bPf92qeQjMNf+6yYMH1G2fiJhz8pXs4nxYfla5+QCMgEyY719bJuYodMvcXq5i15ZlFDy7kGvSe6SWSj1kBWRY4CASUSRwFekVeFGEAiTKYhCC9OELx4qHUdhAChW24IPPzww3PdtRcvvPBChxvtyE2LSgch4ET7+vnVRP+h4K99xmM9vVaknE+KTyqfNPPYimlBX/sMIBlBxhwpg0iQHMtxrPYNz+7c8dCXd8pA8vSShparUmIrnZLye51Ly+92bim9x/mCpPOSZYxRjFXq65KADMdOwR0OVAtfZ410kVAnD6kcBm7Mac5IShDFYA6dtw2O0aPJsgaLARjyDBd8kqmOnTJlsoYkUymb5H4q+D7nUx+dtK997t7T27R5a/eu9a91bpNIxu71ia99xjr377difX12qfu1z9nHVU13ScasOWUTps0oG1c/qbSqdnxJeXmlZduMISbXUXiDoCFbvK938r4/PxzZ9l+f/a0MIt9dfH/L7RCIsoPWHuEZ/25FrEW2bb3Tdqxfld3j/K7yPoePWzH1CpkQDboNBwJcxOHQqzoLFwEeah7KMBLBIIGDnC5QUIf1D/y+m3uuUvI0WdbRYsD0EHhqssSFaAq6D4atzP3aZ39by/7efbv29jQKydj52nohGS9379nwmuQd/tpnrD9qlya/9jlpjpCME2S6JO1rnxNL3a992rYtY0RmkizLETJRv/+vvx6/8dv/8vMD8fJ/FgLxk0gk/qAUfkiOl1uOdYNjW++SdCgWtX5lfXoFUVPGIp4XEdNtqBGIDLVC1VfwCEAiIASsb/DrLA8/ayfGSyGOcr/st0vaLWmfpHz+UuRYsw0yxyCpybIUgzzEIN7X0xXrSn7tc99Oz9c+1/5VSIb7tc/9Qg/67bKqipJxkw9/7XP6CSdWTJ07t3zirJnlE6ZOLqutHy+RjEq7rIwXlGi8r3dc++vPztz6m/sqJ9pdO+eVtF0yLdJ2howviU0GoS6JSvwfOWkrP+2ym2TfKWmcJN2GAQElEsMAagGrLJO+sXAJByaHAzZC7RAIZBjYWUjJA8x0xgBhzVAEChYB7VgYAhJZ6OlJfu2zubdlO1/73Ni9M/m1zz0bXz38tc9DB4VkxO3yqprS8ZNnVE6ee0LVjBNOqpg65/jI+Glnxl/97YT+R5fv7u/vWzOh1Ll3ttX6AW/DMv3xv5IukTzGLMYvXnLkVLehRECJxFCiWfi6YPREIpim8PaWB5QySAT5h+QPDy6RCznUTRFQBBQBQwQcR0hGV3es48DBvgON2b/2Ge3tsQ7sivdufuENPX974u1orykvuelYq+l661ATY5IVcSzW0/BzURZsMiYxTiGqaQgRUCIxhGAWuCqmKYg48DC6XSXMWCMnEIhS2fNzK0gEi5vkVDdFYEQQ0EaKBQEnHo8nv/YZ7djf6ki4orNp6+/2nnr5323vm/D2A139V/T29m+aWxW7qrx102Qn3j9FoHE/qEZ0VAIUlpIJAWUoNyUSQ4lmYevi4YNEuNMUEAvy2PdI1yEQfCeCB1VOdVMEFAFFYBgR6DrUYR+3KFL29R2/pxUZhLqaS6es22tPfHzbwe7/6j3U2ufEo//gRPueknLX1zGGMW7xEiTZug0FAi64Q6FLdRQuAqx5sKV7RBxY+UwEggSpgEDA9DkWEd0KHgHtoCKQDwg48dWRy++51Ypby2zHmpMyqbw6bk85oceas/A8u6zq/dbW1T+UMsYr96WnW84hE7LTbSgQUCIxFCgWtg7uER46Hj4IBQ8keRAI2L2ug8iz619eP6P0aFKedUfNUQT8EHhRMq+OXmnfYtnWryUM+hM5/4xjWxfI8XuEWNxml5Yutxzrw9Gvv+0PUsYaCcYuElOvfBiPqVop0u1oEYgcrQKtX/AI8F9Oc5+weIloBIstIRESSSz4vudDB3O3wY7YZ3z75dMHk8766u9Pzr1BraEIjCgCCRLhtth/pf3JSMT6oBCKCiEQH7Zt6z1OxPpbJG6d1H+V/YDIsTicMYvEWAaJEL5h8bVdS/8dPQKAevRaVEOhIgB758twzCeymJLpC6Y4qqTDLLLUZA36q5RDgR1vVDzDfsn62Qcm5pS+fh6XW66sdfjjTg8//PCxjuMsZC+5qTbe+9731jU1NZ32l7/85UTyr7nmmsmdnZ1nIJuZkEMeHZll7jll6AlKyLjy7GmPdoPqDGfZpk2bTiGZtAFOprIm+vJBxrRPyHG93MR1xH72bp5339fX96Zvfetbs5DJkpol/3pJac9P78ftp/o/bl8hpGKJ7M+T9MXeq+zdGXKMY+4YxngGkZgkMrodJQIMDkepQqsXMAJl0jciD0xf8ADy8DHPWLjJssZS34gQ8QxnJAnwyoVjO9Qbt0wT8smU0CcDvM35u9/97vpPfvKTDLhp+cnyCHtxAI44gCbbttfanjRt2rRXH3vssU5kmpub+4VUbPaWi0PZ/4EPfGDiN7/5zZnSVkJ/5l6c8BuwQYjDDrfuSy+91C3tzQmql6lniM9FXWLztVlKUvn0Xc7ZUnlyMmaP//znP88/++yzme6UbhwmnXIwoD8PPfTQ3LPOOqtWrlPivmDPdeSaXXLJJbvca8me+4L7Y8eOHb3XXXddk5++ZB5E4mieUZ4ZpjmIUjC2YXfiPhf9ug0SAUAcZFWtVgQI8KXHbdLPvZI41mRZ+YQBa1Sicm0ykg3pk+xBbwl9MsDHDx48GO3v748vW7ZsqmhL5EcikYR+KSc8HJU9A7OV3CdkXFl3nyyzknVTMv/93/+978CBA9ElS5YQbk7lu/WEaMyaMWNG2Y033rjj+9//foub/9a3vnUDTkccEFNviXpCNOokUvFGcdynk+Rt+HhXnmMhJAtIlJHQTT7HJMpceY7XrFlzgkRUTqZMiNJp4ghTGIicuyXaRhdybuJcBKLof8tb3lJ7wgknVKLrH//xH4nmJfJd2UzdtP3KK6/M9/bF1YdOdKDLrc8xeZSRaNMty9QdVpapC31uov/oO+OMM6pw+pLPlui/HKTtsefv/u7vxvEfrH3uc5/bRTn72traNew596abb755qpRF7rjjjkZvvs8x6xuG4hmErPDFXe4p7mNpSrfBIqBEYrDIab2hQEB15DkC4jicn/3sZ/vnzJlTIc5s7kibu3Dhwtq9e/f233XXXTiPtObnzZv3ColMIRGTvvGNbxyzdu3aLiEtL37qU5/advrpp1eL42T6BRELZ/7iiy92UP7ss8+2f+hDH5o8derUMs6//e1v74WwiLMkMpKQF4dZ+/TTT7dRLhi0ShvTveUJIfkDLu95z3vqaRNZdHGOrJCI12lr8+bNPRKdefkXv/hFOzZhy/ve977XkUf3lVdeOVX0E/URjZY1f/78qnvuuaeZcuqjj3Jx0OMk/zj0UUZqb2+PiQNO/GoBW+i3a4tXd1AZjWKrayPnfmnFihUtNTU1L9GmX7mbd/7554+DGDz//PP80svN9t3TL2x+/PHHD/hdZ99KmplXCCiRyKvLocYoAvmHwKOPPnqQN0vXmflZWF5ebn/2s5+dIW/CC90kb8DJkA8AABAASURBVNRn4CT85N28yy67bGJ9fX2pn8PBaY4bN67E8/brVhuwX7p06aSOjo741772NaJnFg4Jx4SDcm1Az4MPPsj//WLRXldXVwwZlD311FPt1IdMcE7CWUsInrdWi3pEThYvXswvlyhOJGw877zz6tBDm2RKaH4PhAa8OPcmbHnDG95Q9ZOf/GQfpIIy2oAs0QfOSURb0MMxtrI/8cQTK8CLY7efHAuJaJw0aVIpxIVzb0I3jt+1zbTMK+ceY8/HPvaxne552B4SCp6yfxP3BHs/G+k32INxmE4tz08ElEjk53UZHqtUqyIwSARwXAz2119//XQ/FeIkHN7EeUN2U6YD4+3/5z//+Yk4FTd94AMfmCROrhEn5afXNA/dvCW7zpl6OGfs4tgvUfb6668TKvcrtiAebgF60U87bh772bNnl/PmTXTD7RN7zxoCxFIJMiC4lGSSLiIUKaGAg2OOOaYCG7w43nnnnceik2o4Y64TedhBFIJ8UlAZ5UOdsAmSdfHFF2/kniA6IkQqLaoDEaPvkDYwHmobVN/IIKBEYmRw1lYUgTGNAIM8b9FMcVx11VWTB9MZHLMbzif0TkTA++adqZM2/Zx3plw+nK9cuXIfztKb3GmXTPtYc5JJuqjH1EKmrN+5F0fqkSQi9FfIGJgxPUEezhmCI4SJX0LMDCrza+do8yQiFbvlllt20y66IDKZUZ1cpkDQoSk/EVAiMXzXRTUrAgWFAI7qhRde6HjnO99Zz1v40XROohCtMtffDDFhzUA2XaxpIDwub7Kp9QOuLPWamppO460W58o0CMduOfXEwQ56RT5v/q4u9KKfdtw89rt27eojAkCkgPOw5EZAsC1M1q98586dvWBPJMSv3JsHkYG4ZTpvZILKKD/a5PaTCEyQLqaKwI+ppSA5LctvBJRI5Pf1UesUgbxCgCkOHFN1dTW/yT8q21xiwjSANwTvVcocP1MULKT0kglIBPVYDMkb74oVK1pxsF/4whdmUB9Z1iiwVgHSQl6uCZLjzumzNgH9tOPVQ9vYkPyZY2KhJqQDgoONXlmOsQWbsA0byWMvb+9nZMMAGTfxVo/j9U4xUY/66PEeU8f7xh9UhuxQJvr52muvdf/zP//zZPBANxhmroeBrBF1AkdkNI1NBAqHSIxN/NVqRWBMIcCAzxSHhOeH5CdzhPMJwbNWwnXamYDw9ozzdef9mftnESXTIxAN5HFcN9xww07yKUeWOuinfDAJwsSvKdCHfURQaCdTFzYQqXHXPbB+gT65bbNYElLiOnvyIUfYiG72LNZET6buzHPwZ7qAiAZ1SZAS+o5t6PDqxiZ0Q9qCymgH4gMBch0/eaaJOtRFh1uHfoIDeGAnGIqNqfUw1PGL8rj1dT92EBhxIiHzq3WSVktyfBL5/C58AIIi+1V5qB8dUODJEJmEbpG7zJOdOJSyr0pK0y/ncySt//jHP35OQmiI/4hu9O+WvdtXjhM/06JNyV8vKXFu2jR9kzpp/TCtq3KKQC4I4HiYb8d5eevhlGTK4K84CvLFObTKm+Za8jn3S9l0IQtRQF9Qfdpi3t9NNTU1L9Eu9d3EOfmuDHXcMo69faEtbKYOMvSRcuzknMSbsqsv0z5sJiFHQr/bLnvOySfRFvXR5bZHXeTc5G2XMhJ1SdSnLnvO0cG5W5dj8igjUdctY5+pmzw3ecuwGQzAAj1BiTZIrsz/z979hMpy1XkA74RsY7L2zTjLRBhmNY/BjYuZrIKMD0IgMJmZVcZMkEEYkERwoSKJihJEYhKyiUQQJKgocRNdJGCQ6CYImoWgT54uBE0MKGiIfn+Vrsrt++59ffu+7tNV1R/o86rq1Knz53P6Vf26qu+9dUwdW3X0ebWs7b6tMujHUPtOO6b2jTnl/Nud1+tcfLSfyV97jTpa/uj6ta4H1U7q7s75Wf9m1utaNlzrsl392dl17Gg/T1tvHkg8+eSTryddTLrhrbfeen869pta1nZS5b+evMm/anIziJeSvpJx3VCp1pNeWu7L6uavfCr6auqajdPmAo4gQIDA4QicdM7PNaC7jta+MUg0DyTWDToR13/kQtt/gu+jsLrD8GAi2w9mf3dXImUqKuvLvVER3bq61+2vulNvV2et9+Wr7uS/kVT7hrsKVSZ5ryZV/sN9+VomOKpf3lPB0nO1vUxfXi4v3XjjjV/L+m1JXWCROroIM8uq66+pu8a8qGXyfp1U7b+c7fuy3rnk2NpfEWp3TPKHPqTcUcehz3WMdBgCP//9m4vf/vGtd9KfjqwfzV+uH4bKHEZpDGMXOHbNGK5POUevnOezPZzLl2P6dPLqfD4cszyXr5RLma6e7Lsvxz2bdFuuKc8l/5NJQ9llP76fvBPv9Oe4rbxGFUjUoBMsPJ7nafdmdPUHVRZZf3oZdT2S9W9l/VLw6iL7XynzD4nMbkj+93Lc/2f73K+qM3XUH4updm/N+ruqP5mA92SCvpY27l+29XIaeTb5/cTUX5S7NfseSv7wyjGvZONKli9U3VlfpMzlpAtJX0ygcU/yXk16X7Yvp/6ns16vW7N+b9p/vNqvjKSbU/7OlLuY9T8mda/04eGU6/qc/XV358PVVvLfk/yPV16OqW+tfyUH/G+S14EI3PSn3/3lwe/+YjU9d2z72P6PZftAeNYOs27bV1pbUAECxwRy/r0l5/0v5DzeXzPuz/m4uz4l7+mkK8vzcn3YfHe2P7Cs4kLWb17u+1LVUXUt9522qOvBXdn5as73d2b5VFJdL/4py/qV9JX3w9S50zv9owokgl3fF/hZlt+pgQf10axfDGbll0uXEkzU7f26IF+ujJT5aS23lartpH996qmnXkwfuu9PpI0Xq/5sfz7L+oM1XSCR7XpTXDVJOb679ZT9FRQ8kzFUlDncaUgdwyv7bkn99SZ6tI7L+ney82d5I9WbIKuLP2S9+w17tVHppptuqj/VeEfWn69jln2tgOofk7fyyv6HKq1k2pitwJ9/d+UvP/jQba+cK91/ewXAs7U5z8AcQ+B6BOp6lXSp6qhlpVpPquvGr7PsX2/kelHXl9quu9dDQFAZZ0k5z9eH0rqG1vcm6hr1Lwkwjt4VP0s1G5cZWyCxchEMagUK9YeJVgZWF96kun3TXZyz88Gk63plcr+aCp5Pei11V73DY4Lk1Y91/bLyc0F/IdsXMjkVTdYfKrpmEFP1ZnK770jkmPcnSPjckTsNqap71YRXG91Gyr+esV/pNt7+p95s9aZ7e2v13werX5VS9wdz3Htz/OW0dV/6Wre6aixlVW2sHmlr6gL1vpj6GPSfwCYCo3/P5/xb5+q7cj7+bJ2Xk4ZHy3Xuz3Y9pq6fenotA//npP511QfGfseGy6+n/N25BvR3JXb+4WBUgUQugj8JwPDKRNSdiPr0P+QtV/rAoR4p1K37R5b5py6O110FA13BQH2yr8169FCf3Ku+erxxRz0m6HYsFj/Ksmsrb5IKCm6uOwDJO/VVxyZ13+foC+XCXhN6ZTmuPruW9cYb/oPkjdbdoagd61LGde+yT9WvGxK4dJFv9S/5/W2y51Ouf3Syrkr7pyPQ/V2J6XT3LD1VhsA1Bcbwnl85X/e9zTn2vf16zr39Y+x69P7R5D/7wAMP/H2uAV/I+peyv7/O1LUlWd3rXctr0mK5HK5N3d4z/pPry4tJF9LW/+SQnT/WSBuLsQUSdQfi9kzIB5YX049k/eWgV37196qUchVs1Pclrtp3NCP1VB1d3X1+sOu5VX2P4ZXUc/xHd+o20+WUqUcaw3OsKpc0RJh9XceXdVzSxQQT9X2Obnf6UM/Cqg/Vly6v/sn4ujsQKf+R1H1LXy5vplNvSb355pv13Yzn+2NyXN3Kqn49vIx6V360NOWueeek+iERIEBg5AJ7DySOn6/Lq865Ocf+W9a/vjwXr/w4Zs7pV5bn7BR5+5W8uh4cvSNxcy7+3ePs5bK7Nr1d+uz/pn/1eKPuaF+61jXk7DWuLzmqQKI+RQe3vpjyTLpet33q0cF/Z72+dPmTTFT/Uxv1/KgeBVSZl5L/xaQLy+8NVPGrUtUd1DtT7vFMdN3u/2vW64uKdwS+IsxHarv25eCq94d1TPbVY4J7sq87Lvs+nHruqfysn/pa7n9fjutvb1V7j+fYO6vevFHqOw91t6X7qY2U68aZCl/L+jPlUOWyfeorbTyUcvWGqf7+MusVdD20PO4bObB7HJNlfZdi7V2blPOalkDdxfrE9XTZsQQmJFDv92+Pob915zfn2+7cW9eMnM9fyPb9ya/v79WF/FOVV/tyPv9s0v/lfF0fGB9N/7vH0VnenWO+lX39I/2q7+/qmOyrD8d31TFZX/eq69cbae+5CmiWhevxRn3Pru6CL7N2t9hrIFEXvEC9u5b9EJcT0d2mz77h9yX0+VleSv5w2yjrF5544onPZHnxscce+1UtU6a+79BXOSyrnezvbvdnWW0M9We7+3JklpVfafgpjGPHDY810k71ZSg3NLRcSV1H+1l1Dsce2dd9aTTbK+2n7m4Mtcy+oZ8nbFcfqu7hsUY1n2O6xzRZ1r7h+NonzUrgxxlNpSy8CMxaYFRBc87Fw7m3zrPZ7s7ZNQO1XnnL1J3jj+enzKVKKdN9+Mvy9qT/TKpz9vFjunN4Xz5luutFtitw6daTN1xf0tbdSd0X8bPc+WuvgcTOR6cBAqMS2Eln6lNanWCf3EntKiWwf4F6j38o3RAwB+Far9zN6H6/RO5y1N32ZnehBRLXmhX7CExDoE60FUj8e7pbJ9wKLOoWsLRYMJimQb2f631c7+d6Xwsi8p973St3Jfq7E90djHXlt7VfILEtSfWMTuAAO1QBRZ1w6+JZJ2FpsWAwTYMKJOp9XO/nA/yvPK0hCySmNV96S4AAAQIERiUgkBjVdEy5M/pOgAABAocoIJA4xFk3ZgIECBAgsCUBgcSWIFtXoz0CBAgQIDAGAYHEGGZBHwgQIECAwEQFBBJnmjiFCBAgQIAAgZMEBBInqcgjQIAAAQIEziQwykDiTD1XiAABAgQIENi7gEBi71OgAwQIECBAYLoCNy4W0+28nhMgQIAAAQL7FXBHYr/+WidAgAABApsJjKy0QGJkE6I7BAgQIEBgSgICiSnNlr4SIECAQGsB7a0REEisAbKbAAECBAgQOF1AIHG6jT0ECBAg0FpAe5MTEEhMbsp0mAABAgQIjEdAIDGeudATAgQItBbQHoHrFhBIXDehCggQIECAwOEKCCQOd+6NnACB1gLaIzBDAYHEDCfVkAgQIECAQCsBgUQrae0QINBaQHsECDQQEEg0QNYEAQIECBCYq4BAYq4za1wEWgtojwCBgxQQSBzktBs0AQIECBDYjoBAYjuOaiHQWkB7BAgQGIWAQGIU06ATBAgQIEBgmgICiWnOm163FtAeAQIECJwoIJA4kUUmAQIECBAgcBYBgcRZlJRpLaA9AgQIEJgy1IZgAAAFsElEQVSIgEBiIhOlmwQIECBAYIwCAokxzkrrPmmPAAECBAicU0AgcU44hxEgQIAAAQKLhUCi/btAiwQIECBAYDYCAonZTKWBECBAgACB9gLzDyTam2qRAAECBAgcjIBA4mCm2kAJECBAgMD2BbYdSGy/h2okQIAAAQIERisgkBjt1OgYAQIECBDYtcD11y+QuH5DNRAgQIAAgYMVEEgc7NQbOAECBAi0FphjewKJOc6qMREgQIAAgUYCAolG0JohQIAAgdYC2mshIJBooawNAgQIECAwUwGBxEwn1rAIECDQWkB7hykgkDjMeTdqAgQIECCwFQGBxFYYVUKAAIHWAtojMA4BgcQ45kEvCBAgQIDAJAUEEpOcNp0mQKC1gPYIEDhZQCBxsotcAgQIECBA4AwCAokzIClCgEBrAe0RIDAVAYHEVGZKPwkQIECAwAgFBBIjnBRdItBaQHsECBA4r4BA4rxyjiNAgAABAgQWAglvAgLNBTRIgACB+QgIJOYzl0ZCgAABAgSaCwgkmpNrsLWA9ggQIEBgdwICid3ZqpkAAQIECMxeQCAx+yluPUDtESBAgMAhCQgkDmm2jZUAAQIECGxZQCCxZdDW1WmPAAECBAjsU0AgsU99bRMgQIAAgYkLCCQ2mkCFCRAgQIAAgaMCAomjGtYJECBAgACBjQRGHUhsNBKFCRAgQIAAgeYCAonm5BokQIAAAQLzETgSSMxnUEZCgAABAgQItBEQSLRx1goBAgQIENiuwEhqE0iMZCJ0gwABAgQITFFAIDHFWdNnAgQIEGgtoL1TBAQSp8DIJkCAAAECBNYLCCTWGylBgAABAq0FtDcZAYHEZKZKRwkQIECAwPgEBBLjmxM9IkCAQGsB7RE4t4BA4tx0DiRAgAABAgQEEt4DBAgQaC2gPQIzEhBIzGgyDYUAAQIECLQWEEi0FtceAQKtBbRHgMAOBQQSO8RVNQECBAgQmLuAQGLuM2x8BFoLaI8AgYMSEEgc1HQbLAECBAgQ2K6AQGK7nmoj0FpAewQIENirgEBir/waJ0CAAAEC0xYQSEx7/vS+tYD2CBAgQGBFQCCxwmGDAAECBAgQ2ERAILGJlrKtBbRHgAABAiMXEEiMfIJ0jwABAgQIjFlAIDHm2WndN+0RIECAAIENBQQSG4IpToAAAQIECLwjIJB4x6L1mvYIECBAgMDkBQQSk59CAyBAgAABAvsTOJxAYn/GWiZAgAABArMVEEjMdmoNjAABAgQI7F5gV4HE7nuuBQIECBAgQGDvAgKJvU+BDhAgQIAAgX0LnL99gcT57RxJgAABAgQOXkAgcfBvAQAECBAg0FpgTu0JJOY0m8ZCgAABAgQaCwgkGoNrjgABAgRaC2hvlwICiV3qqpsAAQIECMxcQCAx8wk2PAIECLQW0N5hCQgkDmu+jZYAAQIECGxVQCCxVU6VESBAoLWA9gjsV0AgsV9/rRMgQIAAgUkLCCQmPX06T4BAawHtESCwKiCQWPWwRYAAAQIECGwgIJDYAEtRAgRaC2iPAIGxCwgkxj5D+keAAAECBEYsIJAY8eToGoHWAtojQIDApgICiU3FlCdAgAABAgQGAYHEQGGFQGsB7REgQGD6AgKJ6c+hERAgQIAAgb0JCCT2Rq/h1gLaI0CAAIHtCwgktm+qRgIECBAgcDACAomDmerWA9UeAQIECByCgEDiEGbZGAkQIECAwI4EBBI7gm1drfYIECBAgMA+BAQS+1DXJgECBAgQmImAQOJcE+kgAgQIECBAoAQEEqUgESBAgAABAucSmEQgca6ROYgAAQIECBDYuYBAYufEGiBAgAABAvMVOCGQmO9gjYwAAQIECBDYroBAYrueaiNAgAABAm0F9tyaQGLPE6B5AgQIECAwZQGBxJRnT98JECBAoLWA9o4JCCSOgdgkQIAAAQIEzi7wNwAAAP//KA1v6QAAAAZJREFUAwCiFnu2p2LTpgAAAABJRU5ErkJggg==</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-247672467\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-515943434 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">67806</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImFKQjZqcFUrMDlJRWlwN2Exek5aN0E9PSIsInZhbHVlIjoicThsV3BaSzRVV2EyTWlHYzdZSWZkWGpDMTh5WTFrZmtMRGRxNktDdG9CYkZuaCtwMDlMdmRrbHFKMU5EYmZpcThMRDhPZzBRYXRiN0lpMjRVQngzdmZjbzRxVzByVDQzSUxHMld1S1VXaysxUk8xSFZZcVlkRlk0bEMzVjh3NzEiLCJtYWMiOiJlYTI3ZjI3OTA3NzE5NDI5ZmZjZTYyMzBmMGQ2ZGI5YjM0M2Y0OWZiNjc5MjEyYTZiNzk4MDk5NDA1ZWIzNTNjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjIwRWhwUjFScTJLNldMME8yeTI2Qmc9PSIsInZhbHVlIjoiZUFIK0dEZzRqekNaRnlLMnJWVHhlRDNsK1V2S2pRNlBlUURyVVJ4U0t4VFMyL3ZKblJEV0E4YndyQ2FXcFV0d0FzOUx3NGNLZ2pmM2t5YjdiZkIxcjhNZERqMk1rZHdnL2x4T3FrNGxBSytGblllL1laT203cVNQUVU5Qk1XN0kiLCJtYWMiOiI5YmMxYzRkNDBjNjI1NjgzZjE2Yzk3N2RlOTE3YzU3YmRiYjY1ZTZkN2ZkMDliZWIzNTdlMGJmY2M2MmE4ZWEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-515943434\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1115049838 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60062</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/index.php/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">67806</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">67806</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImFKQjZqcFUrMDlJRWlwN2Exek5aN0E9PSIsInZhbHVlIjoicThsV3BaSzRVV2EyTWlHYzdZSWZkWGpDMTh5WTFrZmtMRGRxNktDdG9CYkZuaCtwMDlMdmRrbHFKMU5EYmZpcThMRDhPZzBRYXRiN0lpMjRVQngzdmZjbzRxVzByVDQzSUxHMld1S1VXaysxUk8xSFZZcVlkRlk0bEMzVjh3NzEiLCJtYWMiOiJlYTI3ZjI3OTA3NzE5NDI5ZmZjZTYyMzBmMGQ2ZGI5YjM0M2Y0OWZiNjc5MjEyYTZiNzk4MDk5NDA1ZWIzNTNjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjIwRWhwUjFScTJLNldMME8yeTI2Qmc9PSIsInZhbHVlIjoiZUFIK0dEZzRqekNaRnlLMnJWVHhlRDNsK1V2S2pRNlBlUURyVVJ4U0t4VFMyL3ZKblJEV0E4YndyQ2FXcFV0d0FzOUx3NGNLZ2pmM2t5YjdiZkIxcjhNZERqMk1rZHdnL2x4T3FrNGxBSytGblllL1laT203cVNQUVU5Qk1XN0kiLCJtYWMiOiI5YmMxYzRkNDBjNjI1NjgzZjE2Yzk3N2RlOTE3YzU3YmRiYjY1ZTZkN2ZkMDliZWIzNTdlMGJmY2M2MmE4ZWEyIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755571503.7796</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755571503</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1115049838\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2065611259 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2065611259\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1893342826 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:45:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkxDdGNNVXJOUFVXL1RXMDRRcmlHZVE9PSIsInZhbHVlIjoid3VoNTFOdmNhME84OHZCaElEYmpERUEzd0VXYnNTS1JYN1VwOFZ4a3RneWdjMURoa3lyNVQ1enMveTI3RlhWMkhnNGRhS0NDUmZDYWhnN3cvN2hobVgzOHNvYUdOYitwSjllU3g1NldUQTFaNytRUnN3ZXBJTzFtNlA1Q2tsMXAiLCJtYWMiOiIyYWNkYTM1N2FlMTc1ZGFmMDA4MzJhZjkxZGQ1ZjRkMmVmYjZhOGJkNmRmYzdiMTgwZDAzNzQwNDgwY2ZhODA2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:45:04 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InQ5UDVhaXJRMmhVUTFSR25EMlhmNGc9PSIsInZhbHVlIjoiZWJ6QkFSQStJekNaRmJxTDhGS2M4aUhlUXpFTWdvRnorY2UzbWdjYjgyYnFQbWtCekZGc05HWlFFdnQzWGZLZHlUdVBMYVJoZytYb2dlbG4wNlJoMFNPTm1RV1RQS0FtNFRselZzMVZRZGxWWllQbVlrNVZ0eWVMZzVUUjRaWWwiLCJtYWMiOiIwZDJiYjY2YjRlNjhhZDlhZGRmZTE3NjZmMTVjMzg1ZDRlMGYwZjMyZjBjMGVkMWNjOWE0ZGE5YzJkOWU5MTI4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:45:04 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkxDdGNNVXJOUFVXL1RXMDRRcmlHZVE9PSIsInZhbHVlIjoid3VoNTFOdmNhME84OHZCaElEYmpERUEzd0VXYnNTS1JYN1VwOFZ4a3RneWdjMURoa3lyNVQ1enMveTI3RlhWMkhnNGRhS0NDUmZDYWhnN3cvN2hobVgzOHNvYUdOYitwSjllU3g1NldUQTFaNytRUnN3ZXBJTzFtNlA1Q2tsMXAiLCJtYWMiOiIyYWNkYTM1N2FlMTc1ZGFmMDA4MzJhZjkxZGQ1ZjRkMmVmYjZhOGJkNmRmYzdiMTgwZDAzNzQwNDgwY2ZhODA2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:45:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InQ5UDVhaXJRMmhVUTFSR25EMlhmNGc9PSIsInZhbHVlIjoiZWJ6QkFSQStJekNaRmJxTDhGS2M4aUhlUXpFTWdvRnorY2UzbWdjYjgyYnFQbWtCekZGc05HWlFFdnQzWGZLZHlUdVBMYVJoZytYb2dlbG4wNlJoMFNPTm1RV1RQS0FtNFRselZzMVZRZGxWWllQbVlrNVZ0eWVMZzVUUjRaWWwiLCJtYWMiOiIwZDJiYjY2YjRlNjhhZDlhZGRmZTE3NjZmMTVjMzg1ZDRlMGYwZjMyZjBjMGVkMWNjOWE0ZGE5YzJkOWU5MTI4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:45:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893342826\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-613638162 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/radar-data/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613638162\", {\"maxDepth\":0})</script>\n"}}