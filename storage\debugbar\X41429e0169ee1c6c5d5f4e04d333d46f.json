{"__meta": {"id": "X41429e0169ee1c6c5d5f4e04d333d46f", "datetime": "2025-08-18 23:35:29", "utime": 1755531329.664974, "method": "GET", "uri": "/ucd/42", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 9, "messages": [{"message": "[23:35:15] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.737875, "xdebug_link": null, "collector": "log"}, {"message": "[23:35:15] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/ucd/42", "message_html": null, "is_string": false, "label": "debug", "time": **********.86736, "xdebug_link": null, "collector": "log"}, {"message": "[23:35:15] LOG.info: UCDController: Sending request to UCD service {\"project_id\":\"42\",\"user_stories_count\":2,\"system_name\":\"Food Ordering System\"}", "message_html": null, "is_string": false, "label": "info", "time": **********.922087, "xdebug_link": null, "collector": "log"}, {"message": "[23:35:15] LOG.info: UCDController: User Story 1: As a Project Manager, I am able to create UI", "message_html": null, "is_string": false, "label": "info", "time": **********.922219, "xdebug_link": null, "collector": "log"}, {"message": "[23:35:15] LOG.info: UCDController: User Story 2: As a Project Manager, I am able to develop the UC004(Delete NFR)", "message_html": null, "is_string": false, "label": "info", "time": **********.922353, "xdebug_link": null, "collector": "log"}, {"message": "[23:35:29] LOG.warning: Creation of dynamic property Illuminate\\Http\\Client\\Response::$cookies is deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php on line 810", "message_html": null, "is_string": false, "label": "warning", "time": 1755531329.521848, "xdebug_link": null, "collector": "log"}, {"message": "[23:35:29] LOG.warning: Creation of dynamic property Illuminate\\Http\\Client\\Response::$transferStats is deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php on line 812", "message_html": null, "is_string": false, "label": "warning", "time": 1755531329.521908, "xdebug_link": null, "collector": "log"}, {"message": "[23:35:29] LOG.info: UCDController: Received response from UCD service {\"status_code\":200,\"response_body_length\":18277}", "message_html": null, "is_string": false, "label": "info", "time": 1755531329.549306, "xdebug_link": null, "collector": "log"}, {"message": "[23:35:29] LOG.info: UCDController: Successfully received response from UCD service {\"data_uri_length\":17698,\"plantuml_text_length\":255,\"plantuml_url\":\"http:\\/\\/www.plantuml.com\\/plantuml\\/png\\/XOyz2m8n44HhzrSigRGGhh2M0-N0mWyKwoCaQowIIsIp9uZutusT8c8xytZ3G9r5imnzW40dGKd8tdM2rZCPyIa2Q2E9KUqudKk4Qnsr8rO0Bq975mYRb6oxPKliesiFjopKunr6GoQZCw4oJ5e8Zolr5bgwKaWNbAuqYwgQZPSKg62RPZzHy03u6iNfTFvX-q7yKKDDqPRJJm==\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755531329.549557, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.183889, "end": 1755531329.665042, "duration": 14.481153011322021, "duration_str": "14.48s", "measures": [{"label": "Booting", "start": **********.183889, "relative_start": 0, "end": **********.697075, "relative_end": **********.697075, "duration": 0.513185977935791, "duration_str": "513ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.697093, "relative_start": 0.5132040977478027, "end": 1755531329.665046, "relative_end": 4.0531158447265625e-06, "duration": 13.967952966690063, "duration_str": "13.97s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25225264, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "ucd.index (\\resources\\views\\ucd\\index.blade.php)", "param_count": 7, "params": ["title", "project", "dataUri", "plantumlText", "plantumlUrl", "userstories", "errorMsg"], "type": "blade"}]}, "route": {"uri": "GET ucd/{project_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\UCDController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "ucd.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\UCDController.php&line=13\">\\app\\Http\\Controllers\\UCDController.php:13-83</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00236, "accumulated_duration_str": "2.36ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 34.322}, {"sql": "select * from `projects` where `id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\UCDController.php", "line": 16}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "\\app\\Http\\Controllers\\UCDController.php:16", "connection": "sagile", "start_percent": 34.322, "width_percent": 35.169}, {"sql": "select * from `user_stories` where `proj_id` = '42'", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\UCDController.php", "line": 17}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Http\\Controllers\\UCDController.php:17", "connection": "sagile", "start_percent": 69.492, "width_percent": 30.508}]}, "models": {"data": {"App\\UserStory": 2, "App\\Project": 1, "App\\User": 1}, "count": 4}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/ucd/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/ucd/42", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1147694680 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1147694680\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1187950120 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1187950120\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/userstory/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ii80RTRocDRPeFlpK2dWRG9aOUdYeVE9PSIsInZhbHVlIjoic1ZXUVpaWmtTMkJpMWVEQSsxQi9idGordFpEVFNLRFZhUWtkbnBuY2NwRTZKeUg1RWxYZGlZaVFObEFscjQwL3dBZkIzdE53K1NRdWVZU0R0UFRkb1h2SUpsS2lEOUJkVThTdkNtclNHS3h5K0pnNllCWEJjOEw5d3p2VHBnRmciLCJtYWMiOiI1Njg0ZGE3MjBjODE1NWY5MGI0NDkyOTk0ZmYxMTY0YWZmOTVhMDNjMTI3NjdiMDdjOTg1YzYzYzY3ZjNkMTc2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IndqTmhnMGJNMkkra2hRd1g3VlZDZEE9PSIsInZhbHVlIjoibzJzZVh2dTZwdGdDM29CbkRVYkJucStraEVmb0hQbFFIZFNsaUVGQmJyeXN5ZFFadVJrYnAwcFFtMWk1Vk1rWjdxZjRZVjh3MHlHQW40cmRaZjhDQTB3bUNhRGF3b2VRSzJadldpWHNXSDdrRzBxOGJLL1FzRzRoQVZmRnRmSmYiLCJtYWMiOiI0ZmFkZjdlYWE0OWNlYWFhZTM0YjE4NTIxMzhiYWRkMGRhYzZiM2M3MDNjZDUyMzg3NWE2OGRlMzY5YzcyYmU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59924</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"7 characters\">/ucd/42</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"7 characters\">/ucd/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/index.php/ucd/42</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/userstory/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ii80RTRocDRPeFlpK2dWRG9aOUdYeVE9PSIsInZhbHVlIjoic1ZXUVpaWmtTMkJpMWVEQSsxQi9idGordFpEVFNLRFZhUWtkbnBuY2NwRTZKeUg1RWxYZGlZaVFObEFscjQwL3dBZkIzdE53K1NRdWVZU0R0UFRkb1h2SUpsS2lEOUJkVThTdkNtclNHS3h5K0pnNllCWEJjOEw5d3p2VHBnRmciLCJtYWMiOiI1Njg0ZGE3MjBjODE1NWY5MGI0NDkyOTk0ZmYxMTY0YWZmOTVhMDNjMTI3NjdiMDdjOTg1YzYzYzY3ZjNkMTc2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IndqTmhnMGJNMkkra2hRd1g3VlZDZEE9PSIsInZhbHVlIjoibzJzZVh2dTZwdGdDM29CbkRVYkJucStraEVmb0hQbFFIZFNsaUVGQmJyeXN5ZFFadVJrYnAwcFFtMWk1Vk1rWjdxZjRZVjh3MHlHQW40cmRaZjhDQTB3bUNhRGF3b2VRSzJadldpWHNXSDdrRzBxOGJLL1FzRzRoQVZmRnRmSmYiLCJtYWMiOiI0ZmFkZjdlYWE0OWNlYWFhZTM0YjE4NTIxMzhiYWRkMGRhYzZiM2M3MDNjZDUyMzg3NWE2OGRlMzY5YzcyYmU4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.1839</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-948458727 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:35:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlZVd2w3NEJvYUlVT1kzMWxoaWc3Y2c9PSIsInZhbHVlIjoiK0hmU0dKdkhHb3dnTGZSak5paHR5MkhSQzFoRHRyd2NkVHFUcnhBdzBCdUovVmYzOUtCSldNZyt2RzZ2Z3ZybEJzUUF6VlNSRkFlMUVUTitaZTN2amtPSnpZc0ROc09uUHRkTnJSOFlRWVdiWm0wY3Q0NzVmQ0NoY2xRMUlmQ24iLCJtYWMiOiIxMzM4YTA2MDAwM2RiNjM0MTQ3ZTQxNWRjOWY3YmNhM2M5ZjdkNWY3MjVkMGMyNDhjNDVlMGVjOWNmYzRlN2VjIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:35:29 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlB4azNlY00vYUVNMjI0ajNnYzVRVUE9PSIsInZhbHVlIjoibWJFbWpsK0RKTVhPaWNUN3lHRGozcGoxSjYzUldTS1J0WHpjMTdqYlJwOStjREI1OXJOTHN0aXQ3OVo3L0dzeVBJN2phODlDYkpsb3dDRjFIN0dEV1Vlb2pKOHVkdENKVW5xSm1ZWGpvY1NWOXZLdVd4dk9TS21sTGt4bXhMKzMiLCJtYWMiOiJkMTA2OTNmNzFlZTY4MTZhODU5ZGIzZmI2MzUyNDA1YjgwNzE0Yjk4NGJmODIzNTYyYTY2MmE1NWQyZWE3YzIzIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:35:29 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlZVd2w3NEJvYUlVT1kzMWxoaWc3Y2c9PSIsInZhbHVlIjoiK0hmU0dKdkhHb3dnTGZSak5paHR5MkhSQzFoRHRyd2NkVHFUcnhBdzBCdUovVmYzOUtCSldNZyt2RzZ2Z3ZybEJzUUF6VlNSRkFlMUVUTitaZTN2amtPSnpZc0ROc09uUHRkTnJSOFlRWVdiWm0wY3Q0NzVmQ0NoY2xRMUlmQ24iLCJtYWMiOiIxMzM4YTA2MDAwM2RiNjM0MTQ3ZTQxNWRjOWY3YmNhM2M5ZjdkNWY3MjVkMGMyNDhjNDVlMGVjOWNmYzRlN2VjIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:35:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlB4azNlY00vYUVNMjI0ajNnYzVRVUE9PSIsInZhbHVlIjoibWJFbWpsK0RKTVhPaWNUN3lHRGozcGoxSjYzUldTS1J0WHpjMTdqYlJwOStjREI1OXJOTHN0aXQ3OVo3L0dzeVBJN2phODlDYkpsb3dDRjFIN0dEV1Vlb2pKOHVkdENKVW5xSm1ZWGpvY1NWOXZLdVd4dk9TS21sTGt4bXhMKzMiLCJtYWMiOiJkMTA2OTNmNzFlZTY4MTZhODU5ZGIzZmI2MzUyNDA1YjgwNzE0Yjk4NGJmODIzNTYyYTY2MmE1NWQyZWE3YzIzIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:35:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-948458727\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-558672876 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://127.0.0.1:8000/ucd/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558672876\", {\"maxDepth\":0})</script>\n"}}