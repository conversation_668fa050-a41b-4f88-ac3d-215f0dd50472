<?php $__env->startSection('navbar'); ?>
<ul style="display: flex; justify-content: space-between; align-items: center; list-style-type: none; margin: 0; padding: 0;">      

<?php if(auth()->check()): ?>
    <li class="aside a <?php echo e(Request::is('profeature*') || Request::is('projects*') || Request::is('sprint*') || Request::is('userstory*') ? 'active' : ''); ?>"><a href="<?php echo e(route('profeature.index')); ?>">Project List</a></li>
    
    <li class="aside a <?php echo e(Request::is('team*') ? 'active' : ''); ?>"><a href="<?php echo e(route('team.index')); ?>">Team</a></li>
    
    <li class="aside a <?php echo e(Request::is('project-assignments*') ? 'active' : ''); ?>"><a href="<?php echo e(route('project-assignments.index')); ?>">Project Assignments</a></li>
    
    <li class="aside a <?php echo e(Request::is('calendar*') ? 'active' : ''); ?>"><a href="<?php echo e(route('calendar.index')); ?>">Calendar</a></li>
    
    <li class="aside a <?php echo e(Request::is('status*') ? 'active' : ''); ?>"><a href="<?php echo e(route('status.index')); ?>">Status</a></li>
    
    <!-- <li class="aside a <?php echo e(Request::is('perfeature*') ? 'active' : ''); ?>"><a href="<?php echo e(route('perfeature.index')); ?>">Performance Feature</a></li>
    
    <li class="aside a <?php echo e(Request::is('secfeature*') ? 'active' : ''); ?>"><a href="<?php echo e(route('secfeature.index')); ?>">Security Feature</a></li> -->
    <li class="aside a <?php echo e(Request::is('nfr*') ? 'active' : ''); ?>"><a href="<?php echo e(route('nfr.index')); ?>">NFR</a></li>

    <li class="aside a <?php echo e(Request::is('tvt*') ? 'active' : ''); ?>"><a href="<?php echo e(route('tvt.index')); ?>">TVT</a></li>
    
    <li class="aside a <?php echo e(Request::is('cig*') ? 'active' : ''); ?>"><a href="<?php echo e(route('cig.index')); ?>">CIG</a></li>
    
    <li class="aside a <?php echo e(Request::is('codestand*') ? 'active' : ''); ?>"><a href="<?php echo e(route('codestand.index')); ?>">Coding Standard</a></li>
    
    <?php if(auth()->user()->role === 'admin'): ?>
        <li class="aside a <?php echo e(Request::is('role*') ? 'active' : ''); ?>"><a href="<?php echo e(route('role.index')); ?>">Role</a></li>
    <?php endif; ?>
    
    <li class="aside a" style="margin-left: auto; color: white;"><span><?php echo e(auth()->user()->name); ?></span></li>
    <li class="aside a"><a href="<?php echo e(url('/logout')); ?>">Logout</a></li>
<?php endif; ?>

</ul>
<?php $__env->stopSection(); ?>
<?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/inc/navbar.blade.php ENDPATH**/ ?>