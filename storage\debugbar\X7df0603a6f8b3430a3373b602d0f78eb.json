{"__meta": {"id": "X7df0603a6f8b3430a3373b602d0f78eb", "datetime": "2025-08-19 10:56:42", "utime": 1755572202.510303, "method": "GET", "uri": "/cig", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[10:56:42] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572202.261284, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:42] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/cig", "message_html": null, "is_string": false, "label": "debug", "time": 1755572202.3689, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572201.691777, "end": 1755572202.510335, "duration": 0.8185579776763916, "duration_str": "819ms", "measures": [{"label": "Booting", "start": 1755572201.691777, "relative_start": 0, "end": 1755572202.225568, "relative_end": 1755572202.225568, "duration": 0.5337910652160645, "duration_str": "534ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572202.225582, "relative_start": 0.5338048934936523, "end": 1755572202.510338, "relative_end": 3.0994415283203125e-06, "duration": 0.2847561836242676, "duration_str": "285ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25131880, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 18, "templates": [{"name": "cig.index (\\resources\\views\\cig\\index.blade.php)", "param_count": 2, "params": ["title", "pros"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 6, "params": ["__env", "app", "menuData", "errors", "title", "pros"], "type": "blade"}, {"name": "inc.dashboard (\\resources\\views\\inc\\dashboard.blade.php)", "param_count": 6, "params": ["__env", "app", "menuData", "errors", "title", "pros"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 6, "params": ["__env", "app", "menuData", "errors", "title", "pros"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 6, "params": ["__env", "app", "menuData", "errors", "title", "pros"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 6, "params": ["__env", "app", "menuData", "errors", "title", "pros"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "pros", "__empty_1", "__currentLoopData", "pro", "loop"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "pros", "__empty_1", "__currentLoopData", "pro", "loop"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "pros", "__empty_1", "__currentLoopData", "pro", "loop"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "pros", "__empty_1", "__currentLoopData", "pro", "loop"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "pros", "__empty_1", "__currentLoopData", "pro", "loop"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "title", "pros", "__empty_1", "__currentLoopData", "pro", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "title", "pros", "__empty_1", "__currentLoopData", "pro", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "title", "pros", "__empty_1", "__currentLoopData", "pro", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "title", "pros", "__empty_1", "__currentLoopData", "pro", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "title", "pros", "__empty_1", "__currentLoopData", "pro", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "title", "pros", "__empty_1", "__currentLoopData", "pro", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "title", "pros", "__empty_1", "__currentLoopData", "pro", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET cig", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "cig.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=16\">\\app\\Http\\Controllers\\CIGController.php:16-23</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0022299999999999998, "accumulated_duration_str": "2.23ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 33.632}, {"sql": "select `team_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\TeamMapping.php", "line": 74}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 19}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\app\\TeamMapping.php:74", "connection": "sagile", "start_percent": 33.632, "width_percent": 31.39}, {"sql": "select * from `projects` where `team_name` in ('iv<PERSON>\\'s team', 'iv<PERSON>\\'s team')", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team", "ivlyn&#039;s team"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\TeamMapping.php", "line": 75}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 19}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\TeamMapping.php:75", "connection": "sagile", "start_percent": 65.022, "width_percent": 34.978}]}, "models": {"data": {"App\\Project": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/cig\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/cig", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2024276500 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2024276500\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-501354051 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-501354051\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-35873377 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/tvt/show/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Inl3TGhHbmVkRWNCMnVLVGtZY1FIcmc9PSIsInZhbHVlIjoiQ3JQRkZvR1BLZDVPRkc3bldEenQxSmlMQ2tqR0tmam85eFp5Nm5FNHlhYXpPTW55SHcwQzFmTnVyejdGR3ZhcXVCaE1Md2Naa3I1Vm0xYS9MMXBseUwxRHZidmsyRWptZmxIZUh1dkg1S0o0LzFuSzRXNlZrSjhQKzFVb0pYaWsiLCJtYWMiOiJhMjQyZDdlZmE0NTFhMzFiODQ5YzY0MjQ0NjEwMGU1MDA3ODdmZjM5ZDQ4ZTM1ZjY2YWI0MDE2NTM2NjUzYmQzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im5vajZZd2tYbEZYczBsMWRmZ2RuRnc9PSIsInZhbHVlIjoiOGxVVEZMQWU2UnAyaWhXSmJBZUR3RldmWU1pQmtkUVQ3SWY1Vlc4UGd2aWNZaVFqaGQ2b2Y5aE5BVUg1c09wOGQ1S1MwVDYvdGpBVVVPNmI4U0s5TnBWZWxUelpORElrejNMUHFpWkVSbHVhVEo5UHlmMDVGejZRWmUzM3ZzV04iLCJtYWMiOiIyZjY5NmY0NWEzZGU5Y2RjYmNhMmViYmNjY2IwZmZlNWRiYzk5NjUzOGZlMDlmOGE5MTQ2N2FkMmYzYWUwOWRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-35873377\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-682879093 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57764</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"4 characters\">/cig</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"4 characters\">/cig</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"14 characters\">/index.php/cig</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/tvt/show/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Inl3TGhHbmVkRWNCMnVLVGtZY1FIcmc9PSIsInZhbHVlIjoiQ3JQRkZvR1BLZDVPRkc3bldEenQxSmlMQ2tqR0tmam85eFp5Nm5FNHlhYXpPTW55SHcwQzFmTnVyejdGR3ZhcXVCaE1Md2Naa3I1Vm0xYS9MMXBseUwxRHZidmsyRWptZmxIZUh1dkg1S0o0LzFuSzRXNlZrSjhQKzFVb0pYaWsiLCJtYWMiOiJhMjQyZDdlZmE0NTFhMzFiODQ5YzY0MjQ0NjEwMGU1MDA3ODdmZjM5ZDQ4ZTM1ZjY2YWI0MDE2NTM2NjUzYmQzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im5vajZZd2tYbEZYczBsMWRmZ2RuRnc9PSIsInZhbHVlIjoiOGxVVEZMQWU2UnAyaWhXSmJBZUR3RldmWU1pQmtkUVQ3SWY1Vlc4UGd2aWNZaVFqaGQ2b2Y5aE5BVUg1c09wOGQ1S1MwVDYvdGpBVVVPNmI4U0s5TnBWZWxUelpORElrejNMUHFpWkVSbHVhVEo5UHlmMDVGejZRWmUzM3ZzV04iLCJtYWMiOiIyZjY5NmY0NWEzZGU5Y2RjYmNhMmViYmNjY2IwZmZlNWRiYzk5NjUzOGZlMDlmOGE5MTQ2N2FkMmYzYWUwOWRlIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572201.6918</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572201</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-682879093\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-553599839 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-553599839\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-810008460 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:56:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkNkZXNTVkw5aHFHSkFNakk3RXlsdlE9PSIsInZhbHVlIjoiNzdYbS8rMGt6MDg1UzVsZUNKcEgvdzlUZWN3VlhVZU5jOWJhZWJRSFlzREZvMm0vR3VDT1B6RnF0U2krZDZXZWhacFJLZ3JOZ3VBQUZCR25ieG02SVJnSnR6V0dRSTJWT3A0eDlrUzB2d01TaEV5ZU9jRlNJWFBzMjFrdHlyRUQiLCJtYWMiOiIzYzgwMTQwNDgwNmFhMmQ2Y2JlOTljODhmNzM5OGYzZDU2ZTI1N2FlMDQ0OThiMDVmMGMyYWEzZWNkYTYzNWQyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:42 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InFOQkRQcUtUeDJTTXB1ay8wOGtUamc9PSIsInZhbHVlIjoiQXRjWmQ1Z3ZrNFR0bTJpRUVaWmdrL0dNN1h2UlozM2xpcXZWVlNDQi9DcVZmcGM2M0J2OS9iNE1DRDdVZjZkNmxMZmdVQ05vTE1VK0VsaGN1NDlxZFZlWlNpMGhzblVKeE90OGI5aEVSSUQvRGlranFkOWcvL0lEcnJYZWdFTUwiLCJtYWMiOiJjOWNlMzBmZGI0YzhkZGVmM2NlMWMzYzFjN2VhMjY0YzcxMzk5YmM1NWQ5ZWFiNDg3OGY0MjkxYjNiNzUwMTgyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:42 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkNkZXNTVkw5aHFHSkFNakk3RXlsdlE9PSIsInZhbHVlIjoiNzdYbS8rMGt6MDg1UzVsZUNKcEgvdzlUZWN3VlhVZU5jOWJhZWJRSFlzREZvMm0vR3VDT1B6RnF0U2krZDZXZWhacFJLZ3JOZ3VBQUZCR25ieG02SVJnSnR6V0dRSTJWT3A0eDlrUzB2d01TaEV5ZU9jRlNJWFBzMjFrdHlyRUQiLCJtYWMiOiIzYzgwMTQwNDgwNmFhMmQ2Y2JlOTljODhmNzM5OGYzZDU2ZTI1N2FlMDQ0OThiMDVmMGMyYWEzZWNkYTYzNWQyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InFOQkRQcUtUeDJTTXB1ay8wOGtUamc9PSIsInZhbHVlIjoiQXRjWmQ1Z3ZrNFR0bTJpRUVaWmdrL0dNN1h2UlozM2xpcXZWVlNDQi9DcVZmcGM2M0J2OS9iNE1DRDdVZjZkNmxMZmdVQ05vTE1VK0VsaGN1NDlxZFZlWlNpMGhzblVKeE90OGI5aEVSSUQvRGlranFkOWcvL0lEcnJYZWdFTUwiLCJtYWMiOiJjOWNlMzBmZGI0YzhkZGVmM2NlMWMzYzFjN2VhMjY0YzcxMzk5YmM1NWQ5ZWFiNDg3OGY0MjkxYjNiNzUwMTgyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-810008460\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2087248712 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://127.0.0.1:8000/cig</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2087248712\", {\"maxDepth\":0})</script>\n"}}