{"__meta": {"id": "Xc5bfb78f9e2e571679b6d0fcccacdfed", "datetime": "2025-08-19 14:29:08", "utime": **********.811439, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[14:29:08] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.654743, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.336625, "end": **********.811468, "duration": 0.47484278678894043, "duration_str": "475ms", "measures": [{"label": "Booting", "start": **********.336625, "relative_start": 0, "end": **********.634663, "relative_end": **********.634663, "duration": 0.2980380058288574, "duration_str": "298ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.634674, "relative_start": 0.2980489730834961, "end": **********.81147, "relative_end": 2.1457672119140625e-06, "duration": 0.17679595947265625, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24057536, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@login", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php&line=32\">\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php:32-60</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00051, "accumulated_duration_str": "510μs", "statements": [{"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 133}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 377}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 86}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 46}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:133", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "oQ29VBJTIpLMeee53Sv9n38tceXGvNa30bInwWHf", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "31", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1857101089 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zmcrsOarapUf2kXnM8i7IWpWkXVKxRtUYsNsuvGw</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">pwd123456</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1857101089\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2122107163 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">99</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ijh5SlV5MDNSa2R2S3M3ODEraUZBZEE9PSIsInZhbHVlIjoiOVpmNko2WFhhalNqY01wRVZYVDM2ZUFNZ1Zma0tZKzl2aWRCR0JhVGx6NERRb0tJL3JPdVlHbzVCSVROdHk4Vm92MmRuaVB5bnNHQjBEWlVWb09RK1AyRW0xNlg5bHg3ZVNQVHhaYWh0TUZEb2RBWkFubUVPbUFSbXRiZW5NYlkiLCJtYWMiOiIwMzIzYWQwODI1ODVkNDc4OTQyYTcwOTk3NmI3MTIxZjFkNjNmYWQwZjk1Y2JjMmQ3MjJhMWNjZjVhYmFmMDI3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImV0RXdWMGdnTER4bmp3VzJPbnNzZHc9PSIsInZhbHVlIjoiS1Q4VVJUaDVvNlVLQ1FuNUFUNzRya3g1ZzZxSFlIZnRySXIzMng1T0h3dFBLbTU1d0QwY1ErZlIrR1B3bStxamFCY1FmWjNLVVc5TWhyWHZJVVRNWmRERnUwMDlsNDN4em9vbjZOZmVVdU5aclZvbFV0MS9XVEQxL29NUkhSUzQiLCJtYWMiOiIzM2E4Y2Y3OWY5MDM0OGFiYjM5MjU4MDAzODRkNjA3ZmM4NTgzNGIyYWFjODY2YzkwMjFmODlkZTljZGVhNWEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2122107163\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1938805060 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">50154</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/login</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">99</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">99</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ijh5SlV5MDNSa2R2S3M3ODEraUZBZEE9PSIsInZhbHVlIjoiOVpmNko2WFhhalNqY01wRVZYVDM2ZUFNZ1Zma0tZKzl2aWRCR0JhVGx6NERRb0tJL3JPdVlHbzVCSVROdHk4Vm92MmRuaVB5bnNHQjBEWlVWb09RK1AyRW0xNlg5bHg3ZVNQVHhaYWh0TUZEb2RBWkFubUVPbUFSbXRiZW5NYlkiLCJtYWMiOiIwMzIzYWQwODI1ODVkNDc4OTQyYTcwOTk3NmI3MTIxZjFkNjNmYWQwZjk1Y2JjMmQ3MjJhMWNjZjVhYmFmMDI3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImV0RXdWMGdnTER4bmp3VzJPbnNzZHc9PSIsInZhbHVlIjoiS1Q4VVJUaDVvNlVLQ1FuNUFUNzRya3g1ZzZxSFlIZnRySXIzMng1T0h3dFBLbTU1d0QwY1ErZlIrR1B3bStxamFCY1FmWjNLVVc5TWhyWHZJVVRNWmRERnUwMDlsNDN4em9vbjZOZmVVdU5aclZvbFV0MS9XVEQxL29NUkhSUzQiLCJtYWMiOiIzM2E4Y2Y3OWY5MDM0OGFiYjM5MjU4MDAzODRkNjA3ZmM4NTgzNGIyYWFjODY2YzkwMjFmODlkZTljZGVhNWEzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.3366</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1938805060\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-116966802 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zmcrsOarapUf2kXnM8i7IWpWkXVKxRtUYsNsuvGw</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tzIpX6GkoMrv0mp6S5lnJzTKCEYCfGa8s2i47b2b</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-116966802\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-847192829 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:29:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImgySnVMM3N0dEhZTVFHKzJTajFMR2c9PSIsInZhbHVlIjoiamFkb01qOFFlTHdyazhMYkt2b0R1WEZaOVh4cXNPb2s1UnpkTFhWQkY0WmRSeHVBc25SR2VPZ2J2ejUvNGsxUFFjNFJ2SFhRS1VRRlVkaEZPelpIeXlhY3hyNzM0RnhBZEhuVHZWMlhhQ1ZmemJwbnVSNnByYlBBWHU0YjQyQ0wiLCJtYWMiOiI5MTUzYTA0ZjEzYmY0MGJjZTY0NjY3NzBkZDU1YTEzZmNhMTE4MWY4N2NkNGE0MDc0YWEwNzc1NzY4Y2JmNzgwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:29:08 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkVqQjdXRkRxcmFiWkZNdE9hSHgxV1E9PSIsInZhbHVlIjoiSkJXaExSMDQ2cEFJOUZnWi9QQVYxaUZsWTB6THh0QWVTcHZVRkt1dzlBQlJ4Q2VhdjVRTmpvV01IeGtOaVo3Y2JaY0ZtQ3ZvZzNZRnlwMzdtMS9zUlhrWng5QkdXM1NvL3VVQ2NFQm1WMjNiTGV6VUNSY3pxZGdUaDlodzVwSUkiLCJtYWMiOiJlNmU2YmY1Y2Q1ZDU0OTBmMGE2ODJiNzE2NWE5OWM0OWQ0Njc3NTgwZmIyNmZkMDU2NzQ3NTdiZmNiMjY3OTIwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:29:08 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImgySnVMM3N0dEhZTVFHKzJTajFMR2c9PSIsInZhbHVlIjoiamFkb01qOFFlTHdyazhMYkt2b0R1WEZaOVh4cXNPb2s1UnpkTFhWQkY0WmRSeHVBc25SR2VPZ2J2ejUvNGsxUFFjNFJ2SFhRS1VRRlVkaEZPelpIeXlhY3hyNzM0RnhBZEhuVHZWMlhhQ1ZmemJwbnVSNnByYlBBWHU0YjQyQ0wiLCJtYWMiOiI5MTUzYTA0ZjEzYmY0MGJjZTY0NjY3NzBkZDU1YTEzZmNhMTE4MWY4N2NkNGE0MDc0YWEwNzc1NzY4Y2JmNzgwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:29:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkVqQjdXRkRxcmFiWkZNdE9hSHgxV1E9PSIsInZhbHVlIjoiSkJXaExSMDQ2cEFJOUZnWi9QQVYxaUZsWTB6THh0QWVTcHZVRkt1dzlBQlJ4Q2VhdjVRTmpvV01IeGtOaVo3Y2JaY0ZtQ3ZvZzNZRnlwMzdtMS9zUlhrWng5QkdXM1NvL3VVQ2NFQm1WMjNiTGV6VUNSY3pxZGdUaDlodzVwSUkiLCJtYWMiOiJlNmU2YmY1Y2Q1ZDU0OTBmMGE2ODJiNzE2NWE5OWM0OWQ0Njc3NTgwZmIyNmZkMDU2NzQ3NTdiZmNiMjY3OTIwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:29:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-847192829\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1529061506 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oQ29VBJTIpLMeee53Sv9n38tceXGvNa30bInwWHf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1529061506\", {\"maxDepth\":0})</script>\n"}}