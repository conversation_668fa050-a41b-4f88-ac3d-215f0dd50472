{"__meta": {"id": "X5024507bc1ea105e92843f370b010b7e", "datetime": "2025-08-19 13:38:18", "utime": 1755581898.973076, "method": "GET", "uri": "/task/57/create", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[13:38:18] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755581898.840331, "xdebug_link": null, "collector": "log"}, {"message": "[13:38:18] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/task/57/create", "message_html": null, "is_string": false, "label": "debug", "time": 1755581898.900001, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755581898.532198, "end": 1755581898.973096, "duration": 0.44089794158935547, "duration_str": "441ms", "measures": [{"label": "Booting", "start": 1755581898.532198, "relative_start": 0, "end": 1755581898.821071, "relative_end": 1755581898.821071, "duration": 0.28887295722961426, "duration_str": "289ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755581898.821083, "relative_start": 0.28888511657714844, "end": 1755581898.973098, "relative_end": 2.1457672119140625e-06, "duration": 0.15201497077941895, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23992752, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "tasks.create (\\resources\\views\\tasks\\create.blade.php)", "param_count": 4, "params": ["title", "statuses", "teamlist", "userstory_id"], "type": "blade"}, {"name": "inc.character-counter (\\resources\\views\\inc\\character-counter.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "statuses", "teamlist", "userstory_id", "inputId", "counterId", "max<PERSON><PERSON><PERSON>"], "type": "blade"}, {"name": "inc.character-counter (\\resources\\views\\inc\\character-counter.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "statuses", "teamlist", "userstory_id", "inputId", "counterId", "max<PERSON><PERSON><PERSON>"], "type": "blade"}]}, "route": {"uri": "GET task/{userstory}/create", "middleware": "web", "controller": "App\\Http\\Controllers\\TaskController@create", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "tasks.create", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TaskController.php&line=212\">\\app\\Http\\Controllers\\TaskController.php:212-236</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00229, "accumulated_duration_str": "2.29ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 21.834}, {"sql": "select * from `user_stories` where `u_id` = '57' limit 1", "type": "query", "params": [], "bindings": ["57"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 214}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:214", "connection": "sagile", "start_percent": 21.834, "width_percent": 20.087}, {"sql": "select * from `projects` where `id` = '43' limit 1", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 217}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:217", "connection": "sagile", "start_percent": 41.921, "width_percent": 17.904}, {"sql": "select `username` from `teammappings` where `project_id` = 43", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 222}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:222", "connection": "sagile", "start_percent": 59.825, "width_percent": 20.087}, {"sql": "select * from `statuses` where `project_id` = 43", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 229}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:229", "connection": "sagile", "start_percent": 79.913, "width_percent": 20.087}]}, "models": {"data": {"App\\Status": 4, "App\\TeamMapping": 2, "App\\Project": 1, "App\\UserStory": 1, "App\\User": 1}, "count": 9}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/task/57/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/task/57/create", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-851021262 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-851021262\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1078486982 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1078486982\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1970092329 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/task/57</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ikt6Y3AxcjNtYUZvek92aFBtMlhXdkE9PSIsInZhbHVlIjoiREFtU2xjNmM3N0JYMXZsaGZxT2tLT0UreTQzYm1jK29uS3hkK1RTMyswU0w2Y0dRZlFBQjVJb1c3WXc3RlVJTE0ybnFyVkNiaS9JY3g5akx4ZjFKSFppMVFxYlFvWFFpeXJwUjFlRWhRWWRzQVhDdUJiWE5VUDM2RFBTdWFhTWwiLCJtYWMiOiJlM2VjYmEwZjVhYjg4MmUxZTUzMjU5Y2E0NjdiZjAyMTMxZjU3N2I3ZmYzZjAzZmY3ODkzYjAxM2U4MzYxOGJkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImllNUxUNVRQRWNRc0dlS3hBRGovbGc9PSIsInZhbHVlIjoiVlZEdHg1REZUZDhnYjg3MVdMU0JmaTZXOW4wTm5XdG9qakVyMVJKK1pGTE1WQWtDdS9PN0lLanpEdVZzZEFtRGRzakZkOFA3U20rSlZacGdZT0d2cnlLaVJnQ053Smd2THhNRXEzVXZ5RGJZVmVnaDVjYXgvZEs5ZFlvL2dQVDMiLCJtYWMiOiI4MTVjNWE5YzJmZWIxNzY1MzZhZjNkOTFmOWZkOWRlNjkwODMyNDYzYjEwOTczZTViYTQ1NWVkNWI0MDhmNTIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1970092329\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-724652413 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59897</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/task/57/create</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/task/57/create</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/index.php/task/57/create</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/task/57</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ikt6Y3AxcjNtYUZvek92aFBtMlhXdkE9PSIsInZhbHVlIjoiREFtU2xjNmM3N0JYMXZsaGZxT2tLT0UreTQzYm1jK29uS3hkK1RTMyswU0w2Y0dRZlFBQjVJb1c3WXc3RlVJTE0ybnFyVkNiaS9JY3g5akx4ZjFKSFppMVFxYlFvWFFpeXJwUjFlRWhRWWRzQVhDdUJiWE5VUDM2RFBTdWFhTWwiLCJtYWMiOiJlM2VjYmEwZjVhYjg4MmUxZTUzMjU5Y2E0NjdiZjAyMTMxZjU3N2I3ZmYzZjAzZmY3ODkzYjAxM2U4MzYxOGJkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImllNUxUNVRQRWNRc0dlS3hBRGovbGc9PSIsInZhbHVlIjoiVlZEdHg1REZUZDhnYjg3MVdMU0JmaTZXOW4wTm5XdG9qakVyMVJKK1pGTE1WQWtDdS9PN0lLanpEdVZzZEFtRGRzakZkOFA3U20rSlZacGdZT0d2cnlLaVJnQ053Smd2THhNRXEzVXZ5RGJZVmVnaDVjYXgvZEs5ZFlvL2dQVDMiLCJtYWMiOiI4MTVjNWE5YzJmZWIxNzY1MzZhZjNkOTFmOWZkOWRlNjkwODMyNDYzYjEwOTczZTViYTQ1NWVkNWI0MDhmNTIxIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755581898.5322</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755581898</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-724652413\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-254280981 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-254280981\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1918155041 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:38:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImpuSXFMUmRwZXN3UkhKQTM3ZTVZUFE9PSIsInZhbHVlIjoidmNMa1h1Q2lKYU9Gc3N6RGh6dlhiUmRiWllENTlJdEx4MldLa3hYenNBb1JTaW1SYUlZanM0dEp6WWVnODVKd0xvZkxobGpMTGpPenRtTVFYL3lSS3YwNFFXNERTYWplbDhHS2FYamtqYjM5eS8wWFZCb21MS0xKdFBSQ3h0V0oiLCJtYWMiOiJmNGNmMTUxYmVhYWQwY2U5ZjgxZWZhMTBmMzNjZTE4MDA3ZDk0YmVjNDE1MTU5NTVjM2JkNjAzZjM4MzYzNTcxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:38:18 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkZOL3pQK2ZIN2MyME5xWmpPd3c4NFE9PSIsInZhbHVlIjoiVFBKVnRmV1ZrcW5IZXN6aHpSN3dIZjJ4WHNGMUhRTUlpTG5GVGk2eXBackxBMi8xeUxLNUZKdldnU0NPaHB6MGx2cENXZ3hZRlc5eDl5cUxXTlNodVdobm9UU2dSMjlnb1ZTOTlHYU1sNlpXSnRvY3VXTHNDMHBZSTdGQW5MMzQiLCJtYWMiOiIzN2M5ZWE4OTEzODU3ZTVkNWE0ZmZiNDVlYjRhNzljNzliNTYyYTBjZjBkNzFhZGNiYzE2ZGYxMmQzNjUwY2U4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:38:18 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImpuSXFMUmRwZXN3UkhKQTM3ZTVZUFE9PSIsInZhbHVlIjoidmNMa1h1Q2lKYU9Gc3N6RGh6dlhiUmRiWllENTlJdEx4MldLa3hYenNBb1JTaW1SYUlZanM0dEp6WWVnODVKd0xvZkxobGpMTGpPenRtTVFYL3lSS3YwNFFXNERTYWplbDhHS2FYamtqYjM5eS8wWFZCb21MS0xKdFBSQ3h0V0oiLCJtYWMiOiJmNGNmMTUxYmVhYWQwY2U5ZjgxZWZhMTBmMzNjZTE4MDA3ZDk0YmVjNDE1MTU5NTVjM2JkNjAzZjM4MzYzNTcxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:38:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkZOL3pQK2ZIN2MyME5xWmpPd3c4NFE9PSIsInZhbHVlIjoiVFBKVnRmV1ZrcW5IZXN6aHpSN3dIZjJ4WHNGMUhRTUlpTG5GVGk2eXBackxBMi8xeUxLNUZKdldnU0NPaHB6MGx2cENXZ3hZRlc5eDl5cUxXTlNodVdobm9UU2dSMjlnb1ZTOTlHYU1sNlpXSnRvY3VXTHNDMHBZSTdGQW5MMzQiLCJtYWMiOiIzN2M5ZWE4OTEzODU3ZTVkNWE0ZmZiNDVlYjRhNzljNzliNTYyYTBjZjBkNzFhZGNiYzE2ZGYxMmQzNjUwY2U4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:38:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1918155041\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-675946289 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/task/57/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-675946289\", {\"maxDepth\":0})</script>\n"}}