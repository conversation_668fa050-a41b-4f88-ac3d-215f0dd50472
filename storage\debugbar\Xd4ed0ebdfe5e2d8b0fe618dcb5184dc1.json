{"__meta": {"id": "Xd4ed0ebdfe5e2d8b0fe618dcb5184dc1", "datetime": "2025-08-19 10:38:45", "utime": 1755571125.941179, "method": "GET", "uri": "/backlogTest/42", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 54, "messages": [{"message": "[10:38:45] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755571125.623599, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/backlogTest/42", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.697903, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.info: BacklogController@index started {\"project_id\":\"42\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755571125.698053, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.info: Project found {\"project\":{\"id\":42,\"team_name\":\"<PERSON><PERSON><PERSON>'s team\",\"proj_name\":\"Food Ordering System\",\"proj_desc\":\"A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.\",\"start_date\":\"2025-08-18\",\"end_date\":\"2026-01-25\",\"shareable_slug\":null,\"created_at\":\"2025-08-18T15:02:05.000000Z\",\"updated_at\":\"2025-08-18T15:02:05.000000Z\"}}", "message_html": null, "is_string": false, "label": "info", "time": 1755571125.714271, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.info: Explicit active sprint check completed {\"proj_name\":\"Food Ordering System\",\"active_sprint_found\":true,\"active_sprint_data\":{\"sprint_id\":35,\"sprint_name\":\"Sprint 2\",\"sprint_desc\":\"2\",\"start_sprint\":\"2025-08-19\",\"end_sprint\":\"2025-09-02\",\"active_sprint\":1,\"proj_name\":\"Food Ordering System\",\"users_name\":\"ivlyn\",\"created_at\":\"2025-08-19T02:36:31.000000Z\",\"updated_at\":\"2025-08-19T02:36:31.000000Z\"}}", "message_html": null, "is_string": false, "label": "info", "time": 1755571125.732425, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.info: Active sprint ID determined {\"active_sprint_id\":35}", "message_html": null, "is_string": false, "label": "info", "time": 1755571125.732558, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.info: Processing user stories with active sprint {\"active_sprint_id\":35}", "message_html": null, "is_string": false, "label": "info", "time": 1755571125.732644, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.info: All non-done user stories retrieved {\"total_user_stories\":4,\"user_story_ids\":[45,46,47,48],\"done_status_id\":208}", "message_html": null, "is_string": false, "label": "info", "time": 1755571125.768709, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Processing user story {\"user_story_id\":45,\"user_story_sprint_id\":\"35\",\"active_sprint_id\":35}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.768894, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: User story is in active sprint - checking tasks {\"user_story_id\":45}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.769043, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Tasks not in sprint check completed {\"user_story_id\":45,\"has_tasks_not_in_sprint\":false}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.788847, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Processing user story {\"user_story_id\":46,\"user_story_sprint_id\":\"35\",\"active_sprint_id\":35}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.788929, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: User story is in active sprint - checking tasks {\"user_story_id\":46}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.788995, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Tasks not in sprint check completed {\"user_story_id\":46,\"has_tasks_not_in_sprint\":false}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.808497, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Processing user story {\"user_story_id\":47,\"user_story_sprint_id\":\"35\",\"active_sprint_id\":35}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.808609, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: User story is in active sprint - checking tasks {\"user_story_id\":47}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.808706, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Tasks not in sprint check completed {\"user_story_id\":47,\"has_tasks_not_in_sprint\":true}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.828809, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: User story has tasks not in active sprint - adding to collection {\"user_story_id\":47}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.828937, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Processing user story {\"user_story_id\":48,\"user_story_sprint_id\":\"35\",\"active_sprint_id\":35}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.829052, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: User story is in active sprint - checking tasks {\"user_story_id\":48}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.829149, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Tasks not in sprint check completed {\"user_story_id\":48,\"has_tasks_not_in_sprint\":true}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.848211, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: User story has tasks not in active sprint - adding to collection {\"user_story_id\":48}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.848316, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.info: User stories filtering completed with active sprint {\"filtered_user_stories_count\":2,\"filtered_user_story_ids\":[47,48]}", "message_html": null, "is_string": false, "label": "info", "time": 1755571125.84845, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.info: Starting task retrieval for user stories", "message_html": null, "is_string": false, "label": "info", "time": 1755571125.848519, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Processing tasks for user story {\"user_story_id\":47}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.865424, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Tasks filtered for active sprint and done status {\"user_story_id\":47,\"active_sprint_id\":35,\"done_task_status_id\":208,\"tasks_count\":1,\"task_ids\":[105]}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.883354, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Processing tasks for user story {\"user_story_id\":48}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.883474, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Tasks filtered for active sprint and done status {\"user_story_id\":48,\"active_sprint_id\":35,\"done_task_status_id\":208,\"tasks_count\":1,\"task_ids\":[106]}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.900919, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.info: Task retrieval completed {\"total_user_stories_with_tasks\":2,\"total_tasks_retrieved\":2}", "message_html": null, "is_string": false, "label": "info", "time": 1755571125.901025, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.info: BacklogController@index completed successfully {\"project_id\":\"42\",\"active_sprint_id\":35,\"user_stories_count\":2,\"tasks_by_user_story_count\":2,\"note\":\"Filtered out user stories with Done status and tasks with done status\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755571125.901111, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Gate check for permission: addToSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.910491, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.911899, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.911983, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Gate check for permission: addToSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.917273, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.918316, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.91839, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Gate check for permission: addToSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.918849, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.920132, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.920226, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Gate check for permission: addToSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.920794, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.921864, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.921937, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Gate check for permission: addUserStory_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.922332, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.92336, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.923419, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Gate check for permission: beginSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.92382, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.924824, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.924897, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Gate check for permission: addToSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.925323, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.926383, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.926445, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Gate check for permission: endSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.926741, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.927577, "xdebug_link": null, "collector": "log"}, {"message": "[10:38:45] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571125.927623, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755571125.259062, "end": 1755571125.941278, "duration": 0.6822159290313721, "duration_str": "682ms", "measures": [{"label": "Booting", "start": 1755571125.259062, "relative_start": 0, "end": 1755571125.597709, "relative_end": 1755571125.597709, "duration": 0.33864688873291016, "duration_str": "339ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755571125.597727, "relative_start": 0.3386650085449219, "end": 1755571125.941281, "relative_end": 3.0994415283203125e-06, "duration": 0.3435540199279785, "duration_str": "344ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24160672, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "backlogTest.index (\\resources\\views\\backlogTest\\index.blade.php)", "param_count": 4, "params": ["project", "userStories", "tasksByUserStory", "activeSprint"], "type": "blade"}]}, "route": {"uri": "GET backlogTest/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\BacklogController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "backlogTest.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\BacklogController.php&line=19\">\\app\\Http\\Controllers\\BacklogController.php:19-239</a>"}, "queries": {"nb_statements": 12, "nb_failed_statements": 0, "accumulated_duration": 0.00636, "accumulated_duration_str": "6.36ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 7.862}, {"sql": "select * from `projects` where `projects`.`id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 24}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:24", "connection": "sagile", "start_percent": 7.862, "width_percent": 7.862}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` = 1 limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 30}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:30", "connection": "sagile", "start_percent": 15.723, "width_percent": 8.019}, {"sql": "select * from `statuses` where `project_id` = '42' and `title` = 'Done' limit 1", "type": "query", "params": [], "bindings": ["42", "Done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 75}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:75", "connection": "sagile", "start_percent": 23.742, "width_percent": 8.491}, {"sql": "select * from `user_stories` where `proj_id` = '42' and `status_id` != 208", "type": "query", "params": [], "bindings": ["42", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 82}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:82", "connection": "sagile", "start_percent": 32.233, "width_percent": 7.862}, {"sql": "select exists(select * from `tasks` where `userstory_id` = 45 and (`sprint_id` != 35 or `sprint_id` is null)) as `exists`", "type": "query", "params": [], "bindings": ["45", "35"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 117}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:117", "connection": "sagile", "start_percent": 40.094, "width_percent": 8.019}, {"sql": "select exists(select * from `tasks` where `userstory_id` = 46 and (`sprint_id` != 35 or `sprint_id` is null)) as `exists`", "type": "query", "params": [], "bindings": ["46", "35"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 117}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:117", "connection": "sagile", "start_percent": 48.113, "width_percent": 7.39}, {"sql": "select exists(select * from `tasks` where `userstory_id` = 47 and (`sprint_id` != 35 or `sprint_id` is null)) as `exists`", "type": "query", "params": [], "bindings": ["47", "35"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 117}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:117", "connection": "sagile", "start_percent": 55.503, "width_percent": 7.547}, {"sql": "select exists(select * from `tasks` where `userstory_id` = 48 and (`sprint_id` != 35 or `sprint_id` is null)) as `exists`", "type": "query", "params": [], "bindings": ["48", "35"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 117}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:117", "connection": "sagile", "start_percent": 63.05, "width_percent": 8.491}, {"sql": "select * from `statuses` where `project_id` = '42' and `slug` = 'done' limit 1", "type": "query", "params": [], "bindings": ["42", "done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 173}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:173", "connection": "sagile", "start_percent": 71.541, "width_percent": 9.906}, {"sql": "select * from `tasks` where `userstory_id` = 47 and (`sprint_id` != 35 or `sprint_id` is null or `sprint_id` = 0) and `status_id` != 208", "type": "query", "params": [], "bindings": ["47", "35", "0", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 191}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:191", "connection": "sagile", "start_percent": 81.447, "width_percent": 9.277}, {"sql": "select * from `tasks` where `userstory_id` = 48 and (`sprint_id` != 35 or `sprint_id` is null or `sprint_id` = 0) and `status_id` != 208", "type": "query", "params": [], "bindings": ["48", "35", "0", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 191}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:191", "connection": "sagile", "start_percent": 90.723, "width_percent": 9.277}]}, "models": {"data": {"App\\Task": 2, "App\\UserStory": 4, "App\\Status": 2, "App\\Sprint": 1, "App\\Project": 1, "App\\User": 1}, "count": 11}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 8, "messages": [{"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571125.91609, "xdebug_link": null}, {"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1567128253 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1567128253\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571125.918582, "xdebug_link": null}, {"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1066186112 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1066186112\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571125.920479, "xdebug_link": null}, {"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1877192493 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1877192493\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571125.922117, "xdebug_link": null}, {"message": "[\n  ability => addUserStory_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1245462816 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">addUserStory_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1245462816\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571125.923581, "xdebug_link": null}, {"message": "[\n  ability => beginSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-165183225 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">beginSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-165183225\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571125.925074, "xdebug_link": null}, {"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-823117708 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-823117708\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571125.926599, "xdebug_link": null}, {"message": "[\n  ability => endSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2088666416 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">endSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2088666416\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571125.927749, "xdebug_link": null}]}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backlogTest/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755570218\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/backlogTest/42", "status_code": "<pre class=sf-dump id=sf-dump-1437478429 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1437478429\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-780750532 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-780750532\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-101334113 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-101334113\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-48950873 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlVMUkh0VFkxRmJhZW9iK3N2RDIyYXc9PSIsInZhbHVlIjoiVE13QXJNYVI0WCs4UWs4SVlnTWJKV3diWGdSU2pqeVhmOGFiMmpUVi9JSmFkR3d5aFhJbEhMaUNvNDIzYUNKNXdWVHd4MkpJbjZiSmpyK0FnTVdPNmdsRVRabkJhTE4zTllVUFdDRERBc0VBQ0FERmxPdWdxQWFWZTR3dTVablMiLCJtYWMiOiIwODdiZGQ0ZGNkODk5NTNjZWQ3YjJhMzUxYTA1MzE0ODJkNDYwZTViZjM1ZDk1ZDM2Y2U0NGMyMGZiYTU5OTFlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlVxTncrTitsVHhla1g2YXI4UTlmU1E9PSIsInZhbHVlIjoiVFloNGlTRlNFWE1FSnRNYUppMkFJOXpSS3N3OWtFMHl6VWVzTUJwUzJNQURTMzRud0gwRnhkdmh2a2FHMHNrbGx6TjRZZ3BXTmlMNm1aQzdOS001V3hLTjlyclFjdlB4dEtrRTJtUU5Zc3pMOGpOcjhKYmN5dmFnVUJlRk1IcmUiLCJtYWMiOiI2MzE0ZWRjMzYxZGQ3NzNhNTI1MWI4YjlhYTNiZGQzNzcxYzAxNGMyZDlhNDliOWQzNDhkMjk4NTJkZTJkMzM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-48950873\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-693346379 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52479</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/index.php/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlVMUkh0VFkxRmJhZW9iK3N2RDIyYXc9PSIsInZhbHVlIjoiVE13QXJNYVI0WCs4UWs4SVlnTWJKV3diWGdSU2pqeVhmOGFiMmpUVi9JSmFkR3d5aFhJbEhMaUNvNDIzYUNKNXdWVHd4MkpJbjZiSmpyK0FnTVdPNmdsRVRabkJhTE4zTllVUFdDRERBc0VBQ0FERmxPdWdxQWFWZTR3dTVablMiLCJtYWMiOiIwODdiZGQ0ZGNkODk5NTNjZWQ3YjJhMzUxYTA1MzE0ODJkNDYwZTViZjM1ZDk1ZDM2Y2U0NGMyMGZiYTU5OTFlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlVxTncrTitsVHhla1g2YXI4UTlmU1E9PSIsInZhbHVlIjoiVFloNGlTRlNFWE1FSnRNYUppMkFJOXpSS3N3OWtFMHl6VWVzTUJwUzJNQURTMzRud0gwRnhkdmh2a2FHMHNrbGx6TjRZZ3BXTmlMNm1aQzdOS001V3hLTjlyclFjdlB4dEtrRTJtUU5Zc3pMOGpOcjhKYmN5dmFnVUJlRk1IcmUiLCJtYWMiOiI2MzE0ZWRjMzYxZGQ3NzNhNTI1MWI4YjlhYTNiZGQzNzcxYzAxNGMyZDlhNDliOWQzNDhkMjk4NTJkZTJkMzM5IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755571125.2591</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755571125</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-693346379\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-764197095 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764197095\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-511180393 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:38:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InMzdlZaclRINkkzaWVZd1hJTUNUMWc9PSIsInZhbHVlIjoiL01XOXVNeENNblBsa29aNWFpUHJCQnpKaFo1UEIvTEtXRUNydDRJTU03bFFGalZMcDl4UjBtNGIxMEZWZGxtMW9ZOHpwWkNZYVJtNnZ3YVExbUdRZlVkOXgrRkJlUHgzSkV2ak43Q1FQVndEYlJLQ00yc2FKWGs2RlBJcXJONjEiLCJtYWMiOiIwMDRmMjU1NjMxN2MwYTQ5ZmExNmVkMzdiNmU0NjZkODAzNmMzZjQ4NDcyZDU0MDk5ZTMyZTRlNWNiYmE3YmRkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:38:45 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImtqM0s3V0l0NFhNYTBRb1hrZklIaGc9PSIsInZhbHVlIjoiMHJxd0xUdTc0OXZydDVpNlVzSFVna2dxdHU1c1JSelhUL05CZFFLL0kvdVZCSHBZaENtck50TEFUVzdwTTZDaGVTa0FmTEpxcDA0VjVjZ0hJZTFzOWY2ZktzM3RmSTVITEtrYjJOakVOMFRtMm0wSi9BejZJZ1Q3bHJuWU5BOGIiLCJtYWMiOiI0ZGM5MzliNThhZGE3MWQwNTM3YjA2OWRkMzhmZWJiNDczNmZjZGZhNTI4MzA3MThiODIwMjA0MTE3NDRhMjhlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:38:45 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InMzdlZaclRINkkzaWVZd1hJTUNUMWc9PSIsInZhbHVlIjoiL01XOXVNeENNblBsa29aNWFpUHJCQnpKaFo1UEIvTEtXRUNydDRJTU03bFFGalZMcDl4UjBtNGIxMEZWZGxtMW9ZOHpwWkNZYVJtNnZ3YVExbUdRZlVkOXgrRkJlUHgzSkV2ak43Q1FQVndEYlJLQ00yc2FKWGs2RlBJcXJONjEiLCJtYWMiOiIwMDRmMjU1NjMxN2MwYTQ5ZmExNmVkMzdiNmU0NjZkODAzNmMzZjQ4NDcyZDU0MDk5ZTMyZTRlNWNiYmE3YmRkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:38:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImtqM0s3V0l0NFhNYTBRb1hrZklIaGc9PSIsInZhbHVlIjoiMHJxd0xUdTc0OXZydDVpNlVzSFVna2dxdHU1c1JSelhUL05CZFFLL0kvdVZCSHBZaENtck50TEFUVzdwTTZDaGVTa0FmTEpxcDA0VjVjZ0hJZTFzOWY2ZktzM3RmSTVITEtrYjJOakVOMFRtMm0wSi9BejZJZ1Q3bHJuWU5BOGIiLCJtYWMiOiI0ZGM5MzliNThhZGE3MWQwNTM3YjA2OWRkMzhmZWJiNDczNmZjZGZhNTI4MzA3MThiODIwMjA0MTE3NDRhMjhlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:38:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-511180393\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-579716327 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755570218</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-579716327\", {\"maxDepth\":0})</script>\n"}}