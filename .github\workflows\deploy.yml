name: Deploy <PERSON><PERSON> App to DigitalOcean

on:
  push:
    branches:
      - <PERSON><PERSON><PERSON>_SAgile  # change to 'dev' or any branch you prefer

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout your Laravel project
      - name: Checkout repository
        uses: actions/checkout@v3

      # Step 2: Set up PHP environment
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'  # change to match your Laravel version
          extensions: mbstring, bcmath, xml, curl, mysql

      # Step 3: Install Composer dependencies
      - name: Install Composer dependencies
        run: composer install --no-progress --prefer-dist --optimize-autoloader

      # Step 4: <PERSON><PERSON> config and routes
      - name: <PERSON><PERSON> config cache
        run: |
          php artisan config:cache
          php artisan route:cache
          php artisan view:cache

      # Step 5: Install doctl (DigitalOcean CLI)
      - name: Install doctl
        run: sudo snap install doctl

      # Step 6: Authenticate with DigitalOcean
      - name: Authenticate with DigitalOcean
        run: doctl auth init -t ${{ secrets.DO_TOKEN }}

      # Step 7: Deploy to DigitalOcean App Platform
      - name: Deploy <PERSON>vel App
        run: |
          doctl apps update 5b9807b7-7caf-48e7-b1b1-0fee021436bf?i=4986cd --spec .do/app.yaml
