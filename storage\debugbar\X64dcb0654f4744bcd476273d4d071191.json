{"__meta": {"id": "X64dcb0654f4744bcd476273d4d071191", "datetime": "2025-08-19 13:34:06", "utime": 1755581646.992989, "method": "GET", "uri": "/userstory/43", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 5, "messages": [{"message": "[13:34:06] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755581646.864515, "xdebug_link": null, "collector": "log"}, {"message": "[13:34:06] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/userstory/43", "message_html": null, "is_string": false, "label": "debug", "time": 1755581646.924465, "xdebug_link": null, "collector": "log"}, {"message": "[13:34:06] LOG.debug: Gate check for permission: add_userstory on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755581646.977991, "xdebug_link": null, "collector": "log"}, {"message": "[13:34:06] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581646.978974, "xdebug_link": null, "collector": "log"}, {"message": "[13:34:06] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581646.979026, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755581646.583649, "end": 1755581646.993013, "duration": 0.4093639850616455, "duration_str": "409ms", "measures": [{"label": "Booting", "start": 1755581646.583649, "relative_start": 0, "end": 1755581646.845226, "relative_end": 1755581646.845226, "duration": 0.26157712936401367, "duration_str": "262ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755581646.845239, "relative_start": 0.26159000396728516, "end": 1755581646.993015, "relative_end": 2.1457672119140625e-06, "duration": 0.14777612686157227, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23918784, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "userstory.index (\\resources\\views\\userstory\\index.blade.php)", "param_count": 3, "params": ["userstories", "project_id", "statuses"], "type": "blade"}]}, "route": {"uri": "GET userstory/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\UserStoryController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "userstory.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\UserStoryController.php&line=52\">\\app\\Http\\Controllers\\UserStoryController.php:52-69</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.0023699999999999997, "accumulated_duration_str": "2.37ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00061, "duration_str": "610μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 25.738}, {"sql": "select * from `projects` where `id` = '43' limit 1", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 55}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:55", "connection": "sagile", "start_percent": 25.738, "width_percent": 18.987}, {"sql": "select * from `user_stories` where `proj_id` = '43'", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 59}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:59", "connection": "sagile", "start_percent": 44.726, "width_percent": 17.3}, {"sql": "select * from `statuses` where `project_id` = '43'", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 62}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:62", "connection": "sagile", "start_percent": 62.025, "width_percent": 19.409}, {"sql": "select * from `projects` where `projects`.`id` = '43' limit 1", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "f1cf5c59d1c0fd8ae7e96081883a6947d41f1029", "line": 3}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 24, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "view::f1cf5c59d1c0fd8ae7e96081883a6947d41f1029:3", "connection": "sagile", "start_percent": 81.435, "width_percent": 18.565}]}, "models": {"data": {"App\\Status": 4, "App\\Project": 2, "App\\User": 1}, "count": 7}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => add_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1450415020 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">add_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1450415020\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755581646.982103, "xdebug_link": null}]}, "session": {"_token": "dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/userstory/43\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755580949\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/userstory/43", "status_code": "<pre class=sf-dump id=sf-dump-1135153503 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1135153503\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-684648972 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-684648972\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1780664364 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1780664364\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1533024252 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InBwOXpmV2o3YlB2WkF5Z1paczdkTHc9PSIsInZhbHVlIjoiWW93UFV0VjdXYXpFenlBdGRxK2dXOWtWak50QkhhdHp5MlhiLytoNHFSczRyYmErSVRmbDlSR1RUbG1MU2pjbTUxdEZKd2lLQW5UcWFlOWNrRkdpRHN3allING1SMnFESVdNb0txV0RPSHozcnVXM3RxeUtGQTBjSjhKQnZBRWgiLCJtYWMiOiJkMmI1YzgzNzk1OGM2NjdlMzI2YTVmMjhkNmI2MjMwNTA1OWViODFmNmVlZjllZDNjODExOGY5YTc5ZGMwY2M3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Imx1ZVV2SHNSalA3ai9ULzFPSWFxalE9PSIsInZhbHVlIjoiNXVvV1p1TE0vTXE2dlJ3ZlFoaTRSL3dodmpGck5BRHBxVWFTNko0N3RoWk5ISnMweXBxZ2UvcjltRWhNbzJvL1NMTzdMQVlKSGRpcDFhSC9iUW1xTWc0cHR1eFFlRVJyOUUzMVVFS2JVU205aml3a0NLcWs0Z2xGMktnV2RwRCsiLCJtYWMiOiI2ZDhkMGFmMzNkMTU4ODRmN2NmZmYwYzM1NjA4Mzc0NjFiMmE0YjBhMjU3ZWJkZmM4ZDliM2U5ODdmZmZkZTFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533024252\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-709984034 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">62675</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"13 characters\">/userstory/43</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"13 characters\">/userstory/43</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/index.php/userstory/43</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/43</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InBwOXpmV2o3YlB2WkF5Z1paczdkTHc9PSIsInZhbHVlIjoiWW93UFV0VjdXYXpFenlBdGRxK2dXOWtWak50QkhhdHp5MlhiLytoNHFSczRyYmErSVRmbDlSR1RUbG1MU2pjbTUxdEZKd2lLQW5UcWFlOWNrRkdpRHN3allING1SMnFESVdNb0txV0RPSHozcnVXM3RxeUtGQTBjSjhKQnZBRWgiLCJtYWMiOiJkMmI1YzgzNzk1OGM2NjdlMzI2YTVmMjhkNmI2MjMwNTA1OWViODFmNmVlZjllZDNjODExOGY5YTc5ZGMwY2M3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Imx1ZVV2SHNSalA3ai9ULzFPSWFxalE9PSIsInZhbHVlIjoiNXVvV1p1TE0vTXE2dlJ3ZlFoaTRSL3dodmpGck5BRHBxVWFTNko0N3RoWk5ISnMweXBxZ2UvcjltRWhNbzJvL1NMTzdMQVlKSGRpcDFhSC9iUW1xTWc0cHR1eFFlRVJyOUUzMVVFS2JVU205aml3a0NLcWs0Z2xGMktnV2RwRCsiLCJtYWMiOiI2ZDhkMGFmMzNkMTU4ODRmN2NmZmYwYzM1NjA4Mzc0NjFiMmE0YjBhMjU3ZWJkZmM4ZDliM2U5ODdmZmZkZTFiIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755581646.5836</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755581646</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-709984034\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1305397620 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GKxRuD042xMQ4k3v5EHCZLsF1sfAXXnsz4rkTCog</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305397620\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-187125769 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:34:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlFQR0Z0WkFPTzN4Y3FzU2d2WkxVa2c9PSIsInZhbHVlIjoiaHZ6ZnB3aStoU3krWnVnRWhKRWt6b21LMEdMc0dCMURmVk4rVFRPWGplOEZsbFc0SzZlVlBZUzZuMFdyaFVobzVPazZ1Y3hTdmlXWGE4VkVyT05IN0RRb2dDbFNFRW80NUlmMms1dFNQdnRVWGtjdHVxdEZWemdEdVJOTjc0MWciLCJtYWMiOiIwNzUwMWIxNWYzMmNjYmQ5YWZiZjBjZjhiNDk0M2NlMjFjNmRhYTJmN2Y1OGJkMzliMGE2MmY4Nzk3YzdlYWQwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:34:06 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkRReDh3TFc3VWJEamxYRUZaRkhjTFE9PSIsInZhbHVlIjoid1owUGZMZGFDRnJNV3MyeDFtRTN6ZVc2d1BveG1vdVlpNHNHOGJNQWhoTDFsUjBZV1U0MUUwVEpINjd3blArTGlDeWY2VzBiaEZrZXY0SnV3aXNvV1czRkFNa3lQYkFKeUNTMG92ZkcvVzdwbytLNDQvd2dkRHVva1A3V044VFMiLCJtYWMiOiJlYjY5MDJlOWVmNzMzZGRhNDdmNGExMGRjOTRjNDdmZTNkMWQwM2FiOTFiMDg5OTk4MDE2YjNjMGUwNmYxNzI3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:34:06 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlFQR0Z0WkFPTzN4Y3FzU2d2WkxVa2c9PSIsInZhbHVlIjoiaHZ6ZnB3aStoU3krWnVnRWhKRWt6b21LMEdMc0dCMURmVk4rVFRPWGplOEZsbFc0SzZlVlBZUzZuMFdyaFVobzVPazZ1Y3hTdmlXWGE4VkVyT05IN0RRb2dDbFNFRW80NUlmMms1dFNQdnRVWGtjdHVxdEZWemdEdVJOTjc0MWciLCJtYWMiOiIwNzUwMWIxNWYzMmNjYmQ5YWZiZjBjZjhiNDk0M2NlMjFjNmRhYTJmN2Y1OGJkMzliMGE2MmY4Nzk3YzdlYWQwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:34:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkRReDh3TFc3VWJEamxYRUZaRkhjTFE9PSIsInZhbHVlIjoid1owUGZMZGFDRnJNV3MyeDFtRTN6ZVc2d1BveG1vdVlpNHNHOGJNQWhoTDFsUjBZV1U0MUUwVEpINjd3blArTGlDeWY2VzBiaEZrZXY0SnV3aXNvV1czRkFNa3lQYkFKeUNTMG92ZkcvVzdwbytLNDQvd2dkRHVva1A3V044VFMiLCJtYWMiOiJlYjY5MDJlOWVmNzMzZGRhNDdmNGExMGRjOTRjNDdmZTNkMWQwM2FiOTFiMDg5OTk4MDE2YjNjMGUwNmYxNzI3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:34:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-187125769\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1147995373 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/userstory/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755580949</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1147995373\", {\"maxDepth\":0})</script>\n"}}