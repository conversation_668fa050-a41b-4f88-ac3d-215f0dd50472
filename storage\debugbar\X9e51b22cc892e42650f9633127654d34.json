{"__meta": {"id": "X9e51b22cc892e42650f9633127654d34", "datetime": "2025-08-18 23:52:27", "utime": 1755532347.500198, "method": "GET", "uri": "/userstory/42", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 29, "messages": [{"message": "[23:52:27] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755532347.110571, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/userstory/42", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.307909, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Gate check for permission: editStatus_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.454628, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team i<PERSON><PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.456375, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.456474, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Gate check for permission: view_task on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.463936, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team i<PERSON><PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.465461, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.465681, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Gate check for permission: edit_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.467742, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team i<PERSON><PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.469404, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.469536, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Gate check for permission: delete_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.47012, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team i<PERSON><PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.471317, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.471413, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Gate check for permission: editStatus_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.472021, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team i<PERSON><PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.474877, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.475004, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Gate check for permission: view_task on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.475685, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team i<PERSON><PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.476812, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.476892, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Gate check for permission: edit_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.477448, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team i<PERSON><PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.478789, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.478904, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Gate check for permission: delete_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.47954, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team i<PERSON><PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.481482, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.481565, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Gate check for permission: add_userstory on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.48209, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team i<PERSON><PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.48311, "xdebug_link": null, "collector": "log"}, {"message": "[23:52:27] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755532347.483201, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755532343.795211, "end": 1755532347.500264, "duration": 3.7050528526306152, "duration_str": "3.71s", "measures": [{"label": "Booting", "start": 1755532343.795211, "relative_start": 0, "end": 1755532346.994828, "relative_end": 1755532346.994828, "duration": 3.1996169090270996, "duration_str": "3.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755532346.994852, "relative_start": 3.199640989303589, "end": 1755532347.500268, "relative_end": 4.0531158447265625e-06, "duration": 0.5054159164428711, "duration_str": "505ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23959072, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "userstory.index (\\resources\\views\\userstory\\index.blade.php)", "param_count": 3, "params": ["userstories", "project_id", "statuses"], "type": "blade"}]}, "route": {"uri": "GET userstory/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\UserStoryController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "userstory.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\UserStoryController.php&line=52\">\\app\\Http\\Controllers\\UserStoryController.php:52-69</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00358, "accumulated_duration_str": "3.58ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 17.877}, {"sql": "select * from `projects` where `id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 55}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:55", "connection": "sagile", "start_percent": 17.877, "width_percent": 17.318}, {"sql": "select * from `user_stories` where `proj_id` = '42'", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 59}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:59", "connection": "sagile", "start_percent": 35.196, "width_percent": 19.274}, {"sql": "select * from `statuses` where `project_id` = '42'", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 62}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:62", "connection": "sagile", "start_percent": 54.469, "width_percent": 24.302}, {"sql": "select * from `projects` where `projects`.`id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "f1cf5c59d1c0fd8ae7e96081883a6947d41f1029", "line": 3}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 24, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "view::f1cf5c59d1c0fd8ae7e96081883a6947d41f1029:3", "connection": "sagile", "start_percent": 78.771, "width_percent": 21.229}]}, "models": {"data": {"App\\Status": 4, "App\\UserStory": 2, "App\\Project": 2, "App\\User": 1}, "count": 9}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 9, "messages": [{"message": "[\n  ability => editStatus_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1051568231 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">editStatus_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1051568231\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755532347.46205, "xdebug_link": null}, {"message": "[\n  ability => view_task,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1375577608 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">view_task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375577608\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755532347.466048, "xdebug_link": null}, {"message": "[\n  ability => edit_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-48846566 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">edit_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-48846566\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755532347.469814, "xdebug_link": null}, {"message": "[\n  ability => delete_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1019191849 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1019191849\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755532347.471653, "xdebug_link": null}, {"message": "[\n  ability => editStatus_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2088841513 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">editStatus_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2088841513\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755532347.475259, "xdebug_link": null}, {"message": "[\n  ability => view_task,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1867832366 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">view_task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1867832366\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755532347.477109, "xdebug_link": null}, {"message": "[\n  ability => edit_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1542525237 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">edit_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1542525237\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755532347.47923, "xdebug_link": null}, {"message": "[\n  ability => delete_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1217635972 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">delete_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1217635972\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755532347.481788, "xdebug_link": null}, {"message": "[\n  ability => add_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1694701097 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">add_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1694701097\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755532347.483433, "xdebug_link": null}]}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/userstory/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755527110\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/userstory/42", "status_code": "<pre class=sf-dump id=sf-dump-1399304246 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1399304246\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-347128943 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-347128943\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-168864186 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-168864186\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-818320045 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IngrVnZqWHFadkJ4VWlOTHJlWTg1ZXc9PSIsInZhbHVlIjoidGVxQnFSSVR2Z2JVMzd4TFJ0RU1mcDBudnN5T3MzZ0lBenU1Nmc3bnp6SDFRMUNtQ3ZPM29JNEhDbGZZeUVOU3R5RDNvTXY0MkI4dEdzYUI3OWVFRzJ0ZnJoZE4yOGUwc0NkTjFvQnNidllodzVnZE4vWGd6ME1ESlY2WmplUXEiLCJtYWMiOiI5MDBmMzE5YmVkMTQyMzI4NWE3ZmUzYjMwYzRhNGRiOWIwMzkxZWE3OWFkNDIwNWE2MjY0ZWRjODlkOTE2MmIxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Iit5UGV0NlNQQlBYek4rZDE0eWpWQnc9PSIsInZhbHVlIjoiL0x5Y2grVWNES0tCTEs1RyszdFE1WXMxbWpsaWFxaTl3eWY2aTJ2Y0UxcUNCTEhpODlCOU04cXhGdGJ1Z3VQRThpRVN4L043ZWtZU0ZWOEpYdDRUaC84OUgyaXh4enQyMURQV3VBVVdoNE1uRjZzaTJTNWQwWUNwZXJ5NVdmaEEiLCJtYWMiOiJmMThjNGQ5YWIwZWEwYmEzMTk5NjJiMjg5OTFhNGRjNGYzMjE4MDRjZjRmYzc0ZmNiZmVjZDNhMTEyNGRiNGViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-818320045\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-591038086 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51017</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"13 characters\">/userstory/42</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"13 characters\">/userstory/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/index.php/userstory/42</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IngrVnZqWHFadkJ4VWlOTHJlWTg1ZXc9PSIsInZhbHVlIjoidGVxQnFSSVR2Z2JVMzd4TFJ0RU1mcDBudnN5T3MzZ0lBenU1Nmc3bnp6SDFRMUNtQ3ZPM29JNEhDbGZZeUVOU3R5RDNvTXY0MkI4dEdzYUI3OWVFRzJ0ZnJoZE4yOGUwc0NkTjFvQnNidllodzVnZE4vWGd6ME1ESlY2WmplUXEiLCJtYWMiOiI5MDBmMzE5YmVkMTQyMzI4NWE3ZmUzYjMwYzRhNGRiOWIwMzkxZWE3OWFkNDIwNWE2MjY0ZWRjODlkOTE2MmIxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Iit5UGV0NlNQQlBYek4rZDE0eWpWQnc9PSIsInZhbHVlIjoiL0x5Y2grVWNES0tCTEs1RyszdFE1WXMxbWpsaWFxaTl3eWY2aTJ2Y0UxcUNCTEhpODlCOU04cXhGdGJ1Z3VQRThpRVN4L043ZWtZU0ZWOEpYdDRUaC84OUgyaXh4enQyMURQV3VBVVdoNE1uRjZzaTJTNWQwWUNwZXJ5NVdmaEEiLCJtYWMiOiJmMThjNGQ5YWIwZWEwYmEzMTk5NjJiMjg5OTFhNGRjNGYzMjE4MDRjZjRmYzc0ZmNiZmVjZDNhMTEyNGRiNGViIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755532343.7952</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755532343</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-591038086\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-852624133 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-852624133\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:52:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlEwSVNGQlY1cG15S0pzVTVKbW1jNmc9PSIsInZhbHVlIjoiVDUwY2hxYThQOXRXZmZGZFRMbXMvSXZyZ1NHSHhsMEt2RjZoNW44SDZDeFljUFZRMDhHYUwwN2J4WHJ1eUM2aVpaa3dhQWpid20vY0lMbHFFSktVMStCS3RCcTkwZERQZnJ2d3Vhc1JnNitqWjNMMVlLM084QTRZdXR0S0h1WU0iLCJtYWMiOiIzYzRiNGQ5MmJlZTkwZmJjODE4OGNiZGI1MGZkNzA2ZWM5ZDZjNmY4MmRkMWExMDk1MzEyYjMzNmUzMmU0Yjc4IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:52:27 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InlKUU9Ucm96Y1RGcTJDN2JORWoxQWc9PSIsInZhbHVlIjoicW5HWnk4dnRMaU16Qm1uS0JWbUlhVWNoZmVjS2N2dDBVY2ZKSjJYdlRhRVE4K1JrNzhkWkRwaURSeHFYd1hTS2l3VHdNaDJFQmhERFJNUzdPR2I1QVBnRVBuRXF0ZDlwY0kwOTNSNG5GdWdSdDE1YWg0V0hWN09idU1LdHpqdHAiLCJtYWMiOiIwM2Y0ZDQyMWM5MGNlZDZjNzUyOGFlNTYwODBmZDA5ZWM1NTkwMzVkYTQ0MTI4MDRjMGQ3MTMyMWM2ZDQ2Mjg4IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:52:27 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlEwSVNGQlY1cG15S0pzVTVKbW1jNmc9PSIsInZhbHVlIjoiVDUwY2hxYThQOXRXZmZGZFRMbXMvSXZyZ1NHSHhsMEt2RjZoNW44SDZDeFljUFZRMDhHYUwwN2J4WHJ1eUM2aVpaa3dhQWpid20vY0lMbHFFSktVMStCS3RCcTkwZERQZnJ2d3Vhc1JnNitqWjNMMVlLM084QTRZdXR0S0h1WU0iLCJtYWMiOiIzYzRiNGQ5MmJlZTkwZmJjODE4OGNiZGI1MGZkNzA2ZWM5ZDZjNmY4MmRkMWExMDk1MzEyYjMzNmUzMmU0Yjc4IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:52:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InlKUU9Ucm96Y1RGcTJDN2JORWoxQWc9PSIsInZhbHVlIjoicW5HWnk4dnRMaU16Qm1uS0JWbUlhVWNoZmVjS2N2dDBVY2ZKSjJYdlRhRVE4K1JrNzhkWkRwaURSeHFYd1hTS2l3VHdNaDJFQmhERFJNUzdPR2I1QVBnRVBuRXF0ZDlwY0kwOTNSNG5GdWdSdDE1YWg0V0hWN09idU1LdHpqdHAiLCJtYWMiOiIwM2Y0ZDQyMWM5MGNlZDZjNzUyOGFlNTYwODBmZDA5ZWM1NTkwMzVkYTQ0MTI4MDRjMGQ3MTMyMWM2ZDQ2Mjg4IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:52:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/userstory/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755527110</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}