{"__meta": {"id": "Xac639d2c2b2620626be49740ad826fe2", "datetime": "2025-08-19 11:08:38", "utime": 1755572918.512578, "method": "GET", "uri": "/radar-data/42/37", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[11:08:38] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572918.302358, "xdebug_link": null, "collector": "log"}, {"message": "[11:08:38] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/radar-data/42/37", "message_html": null, "is_string": false, "label": "debug", "time": 1755572918.420073, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572917.737955, "end": 1755572918.512611, "duration": 0.774655818939209, "duration_str": "775ms", "measures": [{"label": "Booting", "start": 1755572917.737955, "relative_start": 0, "end": 1755572918.260865, "relative_end": 1755572918.260865, "duration": 0.5229098796844482, "duration_str": "523ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572918.260885, "relative_start": 0.5229299068450928, "end": 1755572918.512614, "relative_end": 3.0994415283203125e-06, "duration": 0.25172901153564453, "duration_str": "252ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23640312, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET radar-data/{proj_id}/{sprint_id?}", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@getRadarData", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=25\">\\app\\Http\\Controllers\\CIGController.php:25-77</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00277, "accumulated_duration_str": "2.77ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 29.242}, {"sql": "select * from `user_stories` where `proj_id` = '42' and `sprint_id` = '37'", "type": "query", "params": [], "bindings": ["42", "37"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 36}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:36", "connection": "sagile", "start_percent": 29.242, "width_percent": 25.993}, {"sql": "select * from `user_story_general_nfr` where `user_story_general_nfr`.`user_story_id` in (50)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:36", "connection": "sagile", "start_percent": 55.235, "width_percent": 23.105}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:36", "connection": "sagile", "start_percent": 78.339, "width_percent": 21.661}]}, "models": {"data": {"App\\GeneralNFR": 1, "App\\UserStoryGeneralNfr": 1, "App\\UserStory": 1, "App\\User": 1}, "count": 4}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/radar-data/42/37\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"path_info": "/radar-data/42/37", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2023189034 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2023189034\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1372456403 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1372456403\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-462336715 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=37</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InRaZi9hdnAvbnU5aERYVC9yYlduNXc9PSIsInZhbHVlIjoiOEVybmh0dHJCY2Rlc1l6ckdlS25BN1BySldNRFFqazB4aG1KTTZZMEZ4dzMxTGRuSGtwY0dDczJVdW1kNzdSc21oSmJjRE9Wc3lDbWtXSjcxOUl5SjVUeHdSbmt6aFZuVFdaNk1JZFBDT0dub1czRkMraU5TWFhyWnl3aktmL2giLCJtYWMiOiI0ZmE3ZDhmMjYwZjFiN2Q5NDI0NTg1NWJmY2RhMDY2YWQzNzExNmQzYWU0YjYzZTk5ZGI2YmRhZTgxNGNlOGM0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InVENFcreEJtRmloQ0hGZWNkRVlyS2c9PSIsInZhbHVlIjoiTjB2SHZNVVhsT1dUeVlLTDB0b1M2ODR3eDg2Qm5LMW9qRXNCeHlPVUNsaktYRWdtcnBxRHI0VWkzTXVWNnVBeVlrOGZuYm9CdHdFNXVFdWVxUEFYWUlEL0V4bFlCL0Z5blFzNDQ1RkpBYndkM3BJRUh0Q3F1MjJ0UmxRVkFMVysiLCJtYWMiOiIyMGQxNDQ5Mzc3NWI5NTQ4Y2U3ODc1YTdkNjRmNzg4MWE0ZTQ3ZGY2NzdhYmQ3Y2IwZjkzMDcyZDJjMDkwYzkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-462336715\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-386567064 data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">61039</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/radar-data/42/37</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/radar-data/42/37</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/index.php/radar-data/42/37</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=37</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InRaZi9hdnAvbnU5aERYVC9yYlduNXc9PSIsInZhbHVlIjoiOEVybmh0dHJCY2Rlc1l6ckdlS25BN1BySldNRFFqazB4aG1KTTZZMEZ4dzMxTGRuSGtwY0dDczJVdW1kNzdSc21oSmJjRE9Wc3lDbWtXSjcxOUl5SjVUeHdSbmt6aFZuVFdaNk1JZFBDT0dub1czRkMraU5TWFhyWnl3aktmL2giLCJtYWMiOiI0ZmE3ZDhmMjYwZjFiN2Q5NDI0NTg1NWJmY2RhMDY2YWQzNzExNmQzYWU0YjYzZTk5ZGI2YmRhZTgxNGNlOGM0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InVENFcreEJtRmloQ0hGZWNkRVlyS2c9PSIsInZhbHVlIjoiTjB2SHZNVVhsT1dUeVlLTDB0b1M2ODR3eDg2Qm5LMW9qRXNCeHlPVUNsaktYRWdtcnBxRHI0VWkzTXVWNnVBeVlrOGZuYm9CdHdFNXVFdWVxUEFYWUlEL0V4bFlCL0Z5blFzNDQ1RkpBYndkM3BJRUh0Q3F1MjJ0UmxRVkFMVysiLCJtYWMiOiIyMGQxNDQ5Mzc3NWI5NTQ4Y2U3ODc1YTdkNjRmNzg4MWE0ZTQ3ZGY2NzdhYmQ3Y2IwZjkzMDcyZDJjMDkwYzkzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572917.738</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572917</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-386567064\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1699073151 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1699073151\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-929608171 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 03:08:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImgyclN4N1dORXh4SURZKzR1TkYzemc9PSIsInZhbHVlIjoicmdYSW1WaENpQWtSd0JqKzRvK1JKWFNET3k4WkNoVnl0NzNCZXRodHZKdmhzeDQwekttWWR1Rm5BMmJlbDBjK1RPcGFlSm9ES1R4d2VHRDEzbWozSERFMXp4TFdETWhoMzFlV2VIb2hmajJFYVZLYkpJNmZIWTRrVUFadTBTd3ciLCJtYWMiOiI5N2Q0OTFjNTcxNTkyOTg2Yjk0ZmFjNDhmOWViZDViZmE3ZWM4Njc5ZjlhY2U0OGUzYjJmOTI2NmJhMjA3YzA1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:38 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlJUK2lDYW5vaDh2SXlHNS85d3Vod3c9PSIsInZhbHVlIjoiTDZVbDh5aUFOLzVmeFJERmVSQTNMMFpvVXMyNitsVzV6YThEcjgwd2pUSWJ1c2krcGUzem9tbDZXYjhkdE5nQ0MxeFVuVWlnczdOUGpDbVU3cDR4Nk9YdTQ5SXh4RWtuVjFQaTJVN3pURjgrSkhLaVNBM25tdTJmVnhtVU1Ha28iLCJtYWMiOiIwOTNhOGUxOWU1MDllNmQyMDk4YWUxNjE4M2MwODhjMGU2MjlhZThjMGM1OGUzOGY4OWZjZDg4OWU0NWY5NmU4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:38 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImgyclN4N1dORXh4SURZKzR1TkYzemc9PSIsInZhbHVlIjoicmdYSW1WaENpQWtSd0JqKzRvK1JKWFNET3k4WkNoVnl0NzNCZXRodHZKdmhzeDQwekttWWR1Rm5BMmJlbDBjK1RPcGFlSm9ES1R4d2VHRDEzbWozSERFMXp4TFdETWhoMzFlV2VIb2hmajJFYVZLYkpJNmZIWTRrVUFadTBTd3ciLCJtYWMiOiI5N2Q0OTFjNTcxNTkyOTg2Yjk0ZmFjNDhmOWViZDViZmE3ZWM4Njc5ZjlhY2U0OGUzYjJmOTI2NmJhMjA3YzA1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlJUK2lDYW5vaDh2SXlHNS85d3Vod3c9PSIsInZhbHVlIjoiTDZVbDh5aUFOLzVmeFJERmVSQTNMMFpvVXMyNitsVzV6YThEcjgwd2pUSWJ1c2krcGUzem9tbDZXYjhkdE5nQ0MxeFVuVWlnczdOUGpDbVU3cDR4Nk9YdTQ5SXh4RWtuVjFQaTJVN3pURjgrSkhLaVNBM25tdTJmVnhtVU1Ha28iLCJtYWMiOiIwOTNhOGUxOWU1MDllNmQyMDk4YWUxNjE4M2MwODhjMGU2MjlhZThjMGM1OGUzOGY4OWZjZDg4OWU0NWY5NmU4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-929608171\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-463626998 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/radar-data/42/37</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-463626998\", {\"maxDepth\":0})</script>\n"}}