{"__meta": {"id": "X334d2f6702064febe7894af54ce7d729", "datetime": "2025-08-19 14:07:18", "utime": 1755583638.235605, "method": "GET", "uri": "/role/45", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 23, "messages": [{"message": "[14:07:18] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755583638.094089, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/role/45", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.154874, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: Gate check for permission: add_roles on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.213387, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.214371, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: Using team role map for team Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.214424, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: Gate check for permission: edit_roles on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.218744, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.219513, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: Using team role map for team Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.219562, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: Gate check for permission: delete_roles on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.219889, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.220609, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: Using team role map for team Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.220655, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: Gate check for permission: edit_roles on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.220911, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.221673, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: Using team role map for team Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.221717, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: Gate check for permission: delete_roles on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.221984, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.222697, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: Using team role map for team Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.222741, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: Gate check for permission: updateUserRole_roles on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.222987, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.223678, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: Using team role map for team Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.223721, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: Gate check for permission: updateUserRole_roles on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.223987, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.224667, "xdebug_link": null, "collector": "log"}, {"message": "[14:07:18] LOG.debug: Using team role map for team Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583638.22471, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755583637.808502, "end": 1755583638.235648, "duration": 0.42714595794677734, "duration_str": "427ms", "measures": [{"label": "Booting", "start": 1755583637.808502, "relative_start": 0, "end": 1755583638.074591, "relative_end": 1755583638.074591, "duration": 0.26608896255493164, "duration_str": "266ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755583638.074604, "relative_start": 0.2661020755767822, "end": 1755583638.23565, "relative_end": 2.1457672119140625e-06, "duration": 0.16104602813720703, "duration_str": "161ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23958032, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "role.index (\\resources\\views\\role\\index.blade.php)", "param_count": 4, "params": ["roles", "pro", "teamMembers", "title"], "type": "blade"}]}, "route": {"uri": "GET role/{project_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\RoleController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "role.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\RoleController.php&line=20\">\\app\\Http\\Controllers\\RoleController.php:20-51</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00226, "accumulated_duration_str": "2.26ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 19.027}, {"sql": "select `team_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\RoleController.php", "line": 27}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Http\\Controllers\\RoleController.php:27", "connection": "sagile", "start_percent": 19.027, "width_percent": 19.469}, {"sql": "select * from `projects` where `team_name` in ('iv<PERSON>\\'s team', 'iv<PERSON>\\'s team', 'Team 888', 'Team 888', 'Team AD', 'Team AD', 'Team AD', 'Team AD')", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team", "ivlyn&#039;s team", "Team 888", "Team 888", "Team AD", "Team AD", "Team AD", "Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\RoleController.php", "line": 28}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Http\\Controllers\\RoleController.php:28", "connection": "sagile", "start_percent": 38.496, "width_percent": 21.239}, {"sql": "select * from `roles` where `project_id` = '45'", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\RoleController.php", "line": 31}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Http\\Controllers\\RoleController.php:31", "connection": "sagile", "start_percent": 59.735, "width_percent": 18.584}, {"sql": "select * from `teammappings` where `team_name` = 'Team AD' and `project_id` = 45 and `invitation_status` = 'accepted'", "type": "query", "params": [], "bindings": ["Team AD", "45", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\RoleController.php", "line": 42}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Http\\Controllers\\RoleController.php:42", "connection": "sagile", "start_percent": 78.319, "width_percent": 21.681}]}, "models": {"data": {"App\\TeamMapping": 2, "App\\Role": 2, "App\\Project": 1, "App\\User": 1}, "count": 6}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 7, "messages": [{"message": "[\n  ability => add_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">add_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583638.217424, "xdebug_link": null}, {"message": "[\n  ability => edit_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-399643525 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">edit_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-399643525\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583638.219701, "xdebug_link": null}, {"message": "[\n  ability => delete_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-258678849 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-258678849\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583638.22077, "xdebug_link": null}, {"message": "[\n  ability => edit_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-727773325 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">edit_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727773325\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583638.221826, "xdebug_link": null}, {"message": "[\n  ability => delete_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-766608624 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-766608624\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583638.222845, "xdebug_link": null}, {"message": "[\n  ability => updateUserRole_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1113010085 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">updateUserRole_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113010085\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583638.223821, "xdebug_link": null}, {"message": "[\n  ability => updateUserRole_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1230490304 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">updateUserRole_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1230490304\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583638.22481, "xdebug_link": null}]}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/role/45\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755581799\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/role/45", "status_code": "<pre class=sf-dump id=sf-dump-1053550006 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1053550006\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-52489155 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-52489155\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1183983700 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1183983700\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-483921833 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkpycFRNeWRMbmtoTVNEOS8waCtuNWc9PSIsInZhbHVlIjoiVkg4dG9oMzdrT1JyRXFncTQ3dG1BRVJOMTJuVWdnWnRGQm9nQ3JPM3Uxa09FVUhEZ1UrdlV5UitxUUhSVzZIQjJSNFYyUHYxRkYwK00xUW8yeGJ0V3BJcWM1anVFUzZ0b2o2WUIrSzI0eXpWczVhR0o2bC9QSitUNExQR2xBSHMiLCJtYWMiOiIzMjQ5M2I3OWRmOWFjYzNjMThkYzJmNWNlN2EzMGYwZmZjOTg2Y2FhNGY0NDkyNzE3MWQ2NTdmMTYxNWRjZmVjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ijl1V1U3cFZTK08rWW8zeHUvUUZnRFE9PSIsInZhbHVlIjoicE1BWXhIUExXOGRNMmM2Unh0YzhiWXo4R1Nhd2lZblBvNXhQRG9kVnFzc1JjeFhxdUJ2ZU9la0FLSnNJQ3RxcnBISU9UL2RNdldoWTFJQVl1ejJMMFFUWndVbnNWQjIyTjVQSEFuaVNSWFZ2QVF6OWFkeVFPRTU5V3MzZy9aaDciLCJtYWMiOiI1MmY5ZDQyOWE5YWUwOWU4NTQyNTRjYTgyNTdhOTZjOTUyY2MxODlkODdmYTZmOGY1OTUxNThiYjZmMTc2ZjFmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-483921833\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1332234066 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54844</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"8 characters\">/role/45</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"8 characters\">/role/45</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"18 characters\">/index.php/role/45</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/45</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkpycFRNeWRMbmtoTVNEOS8waCtuNWc9PSIsInZhbHVlIjoiVkg4dG9oMzdrT1JyRXFncTQ3dG1BRVJOMTJuVWdnWnRGQm9nQ3JPM3Uxa09FVUhEZ1UrdlV5UitxUUhSVzZIQjJSNFYyUHYxRkYwK00xUW8yeGJ0V3BJcWM1anVFUzZ0b2o2WUIrSzI0eXpWczVhR0o2bC9QSitUNExQR2xBSHMiLCJtYWMiOiIzMjQ5M2I3OWRmOWFjYzNjMThkYzJmNWNlN2EzMGYwZmZjOTg2Y2FhNGY0NDkyNzE3MWQ2NTdmMTYxNWRjZmVjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ijl1V1U3cFZTK08rWW8zeHUvUUZnRFE9PSIsInZhbHVlIjoicE1BWXhIUExXOGRNMmM2Unh0YzhiWXo4R1Nhd2lZblBvNXhQRG9kVnFzc1JjeFhxdUJ2ZU9la0FLSnNJQ3RxcnBISU9UL2RNdldoWTFJQVl1ejJMMFFUWndVbnNWQjIyTjVQSEFuaVNSWFZ2QVF6OWFkeVFPRTU5V3MzZy9aaDciLCJtYWMiOiI1MmY5ZDQyOWE5YWUwOWU4NTQyNTRjYTgyNTdhOTZjOTUyY2MxODlkODdmYTZmOGY1OTUxNThiYjZmMTc2ZjFmIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755583637.8085</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755583637</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1332234066\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1714296629 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1714296629\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1915682694 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:07:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjZOK1RsYUdrNmhrT2l0V1pSQ1JPNFE9PSIsInZhbHVlIjoiTGZNdFltbnhCUHoyUU9iVklHNHMzMldUMVhWc25nMlA5eXNuZ1Z6ejJsRkxkT2dkeHpJTzFDTTRpY3FOUFRWYUdOVHFpRldwR1NSUTZiL0c5cU5PcFZlQjlGUWJIQjlPRnlSakMwUDlYbmpNbWtueGxhdE9UYWJKMS8wQkdxRFkiLCJtYWMiOiIyNmQwMDJiZWE1Y2EwOTM3YTlmN2M2YWNmMjk3NjUxNzEyMTc1ZjEzYzYyYWNlOTAxNjRkYzZlODM0NTllZjE1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:07:18 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjVidFAvUGhxVWdsNjA0UlVQUnRkc3c9PSIsInZhbHVlIjoiajkrZElMVzBEUE1URG90SzZHaXFYUEFlOEtzZnF4aWY4VGtqWXhCNnNBK2Fjd0VKbzF3UGVTenhzYmsyalpFTTExeXlKN1lwRStrZnVTOXpLa3g2UjZIcTlCTSszOENDcXBzRklGUlN6TjlnQUp0QnAzOVJhSWFLTGJKRzlvQnAiLCJtYWMiOiI2NDk1YjFkOTRmZjI1OTM0YWZiYzBlMGZkYzkyY2MwNjQwMjA2N2VkNzI5YTA5MzgzMjIxZjEyNGM1NWY3ODFlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:07:18 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjZOK1RsYUdrNmhrT2l0V1pSQ1JPNFE9PSIsInZhbHVlIjoiTGZNdFltbnhCUHoyUU9iVklHNHMzMldUMVhWc25nMlA5eXNuZ1Z6ejJsRkxkT2dkeHpJTzFDTTRpY3FOUFRWYUdOVHFpRldwR1NSUTZiL0c5cU5PcFZlQjlGUWJIQjlPRnlSakMwUDlYbmpNbWtueGxhdE9UYWJKMS8wQkdxRFkiLCJtYWMiOiIyNmQwMDJiZWE1Y2EwOTM3YTlmN2M2YWNmMjk3NjUxNzEyMTc1ZjEzYzYyYWNlOTAxNjRkYzZlODM0NTllZjE1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:07:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjVidFAvUGhxVWdsNjA0UlVQUnRkc3c9PSIsInZhbHVlIjoiajkrZElMVzBEUE1URG90SzZHaXFYUEFlOEtzZnF4aWY4VGtqWXhCNnNBK2Fjd0VKbzF3UGVTenhzYmsyalpFTTExeXlKN1lwRStrZnVTOXpLa3g2UjZIcTlCTSszOENDcXBzRklGUlN6TjlnQUp0QnAzOVJhSWFLTGJKRzlvQnAiLCJtYWMiOiI2NDk1YjFkOTRmZjI1OTM0YWZiYzBlMGZkYzkyY2MwNjQwMjA2N2VkNzI5YTA5MzgzMjIxZjEyNGM1NWY3ODFlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:07:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1915682694\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1336604819 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/role/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755581799</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1336604819\", {\"maxDepth\":0})</script>\n"}}