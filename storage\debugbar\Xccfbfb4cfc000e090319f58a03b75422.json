{"__meta": {"id": "Xccfbfb4cfc000e090319f58a03b75422", "datetime": "2025-08-19 14:08:44", "utime": 1755583724.927423, "method": "GET", "uri": "/projects/45", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 95, "messages": [{"message": "[14:08:44] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755583724.732661, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: LoadUserPermissions: Extracted project ID: 45 from URL: http://127.0.0.1:8000/projects/45", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.822156, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: LoadUserPermissions: Found project 45 with team Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.834374, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: LoadUserPermissions: Looking for team role map for team: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.834427, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: LoadUserPermissions: Available teams in role map: [\"Team AD\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.834472, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: LoadUserPermissions: Found team data structure: multiple_projects", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.834512, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: LoadUserPermissions: Set permissions for user <PERSON><PERSON><PERSON> in team Team AD for project 45: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.834585, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_kanban on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.869777, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.870736, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.870856, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.870922, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_burndown on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.875029, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.875746, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.875801, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.875849, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_backlog on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.876065, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.876762, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.876811, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.876857, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_userstory on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.877059, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.877764, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.87787, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.877927, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_forum on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.878122, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.878832, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.878881, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.87893, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_bugtracking on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.879122, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.879805, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.879851, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.879901, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_roles on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.880091, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.880774, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.88082, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.880869, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_status on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.881141, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.882041, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.882096, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.882149, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_kanban on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.882365, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.883113, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.883162, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.88321, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_details on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.883416, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.884145, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.884194, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.884244, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_kanban on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.884442, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.885136, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.885246, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.885302, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_burndown on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.885548, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.886254, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.886302, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.886348, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_backlog on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.886583, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.887285, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.887333, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.887383, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_userstory on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.887606, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.8883, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.888347, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.888397, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_forum on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.888612, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.889311, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.889363, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.889412, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_bugtracking on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.889637, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.890333, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.890381, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.890442, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_roles on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.890659, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.891356, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.891424, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.891473, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_status on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.891693, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.892409, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.89246, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.892513, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_kanban on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.892738, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.893499, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.893546, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.893595, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: view_details on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.893808, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.894515, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.894563, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.894609, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: edit_details on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.894847, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.89557, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.895617, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.895667, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Gate check for permission: delete_details on project: 45 (SAgile) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.895888, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team AD: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.896629, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Using cached permissions for team Team AD: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.89668, "xdebug_link": null, "collector": "log"}, {"message": "[14:08:44] LOG.debug: Cached permission check result: granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755583724.896728, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755583724.245491, "end": 1755583724.92754, "duration": 0.682049036026001, "duration_str": "682ms", "measures": [{"label": "Booting", "start": 1755583724.245491, "relative_start": 0, "end": 1755583724.698138, "relative_end": 1755583724.698138, "duration": 0.45264697074890137, "duration_str": "453ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755583724.69815, "relative_start": 0.45265889167785645, "end": 1755583724.927541, "relative_end": 9.5367431640625e-07, "duration": 0.22939109802246094, "duration_str": "229ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24197408, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 10, "templates": [{"name": "project.details (\\resources\\views\\project\\details.blade.php)", "param_count": 1, "params": ["project"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 5, "params": ["__env", "app", "menuData", "errors", "project"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 5, "params": ["__env", "app", "menuData", "errors", "project"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET projects/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\ProductFeatureController@details", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "projects.details", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProductFeatureController.php&line=133\">\\app\\Http\\Controllers\\ProductFeatureController.php:133-153</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0015300000000000001, "accumulated_duration_str": "1.53ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 28.758}, {"sql": "select `id`, `team_name` from `projects` where `id` = '45' limit 1", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 167}, {"index": 14, "namespace": "middleware", "name": "load.permissions", "line": 33}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 16, "namespace": "middleware", "name": "bindings", "line": 50}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "middleware::load.permissions:167", "connection": "sagile", "start_percent": 28.758, "width_percent": 24.837}, {"sql": "select `team_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 140}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:140", "connection": "sagile", "start_percent": 53.595, "width_percent": 22.222}, {"sql": "select * from `projects` where `id` = '45' and `team_name` in ('iv<PERSON>\\'s team', 'iv<PERSON>\\'s team', 'Team 888', 'Team 888', 'Team AD', 'Team AD', 'Team AD', 'Team AD') limit 1", "type": "query", "params": [], "bindings": ["45", "ivlyn&#039;s team", "ivlyn&#039;s team", "Team 888", "Team 888", "Team AD", "Team AD", "Team AD", "Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 146}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:146", "connection": "sagile", "start_percent": 75.817, "width_percent": 24.183}]}, "models": {"data": {"App\\Project": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 22, "messages": [{"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.873738, "xdebug_link": null}, {"message": "[\n  ability => view_burndown,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-356626804 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_burndown</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-356626804\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.875946, "xdebug_link": null}, {"message": "[\n  ability => view_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1411551104 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1411551104\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.876947, "xdebug_link": null}, {"message": "[\n  ability => view_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-661898139 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-661898139\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.878014, "xdebug_link": null}, {"message": "[\n  ability => view_forum,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1466349376 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_forum</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1466349376\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.879015, "xdebug_link": null}, {"message": "[\n  ability => view_bugtracking,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1026203326 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_bugtracking</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1026203326\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.879984, "xdebug_link": null}, {"message": "[\n  ability => view_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-654402060 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-654402060\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.880999, "xdebug_link": null}, {"message": "[\n  ability => view_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-122701155 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-122701155\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.882247, "xdebug_link": null}, {"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1418122111 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1418122111\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.883304, "xdebug_link": null}, {"message": "[\n  ability => view_details,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1129839014 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1129839014\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.884332, "xdebug_link": null}, {"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1998263114 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1998263114\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.88539, "xdebug_link": null}, {"message": "[\n  ability => view_burndown,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-838953512 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_burndown</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-838953512\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.886434, "xdebug_link": null}, {"message": "[\n  ability => view_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1159811687 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1159811687\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.887468, "xdebug_link": null}, {"message": "[\n  ability => view_userstory,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1937004563 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1937004563\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.888478, "xdebug_link": null}, {"message": "[\n  ability => view_forum,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-975442038 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_forum</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-975442038\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.889499, "xdebug_link": null}, {"message": "[\n  ability => view_bugtracking,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-782995221 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_bugtracking</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-782995221\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.890525, "xdebug_link": null}, {"message": "[\n  ability => view_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1449676512 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1449676512\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.891558, "xdebug_link": null}, {"message": "[\n  ability => view_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-716457718 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-716457718\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.892603, "xdebug_link": null}, {"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1617724734 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1617724734\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.893675, "xdebug_link": null}, {"message": "[\n  ability => view_details,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1072127202 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1072127202\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.894691, "xdebug_link": null}, {"message": "[\n  ability => edit_details,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-580169511 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">edit_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-580169511\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.89575, "xdebug_link": null}, {"message": "[\n  ability => delete_details,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-556827235 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">delete_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-556827235\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583724.896811, "xdebug_link": null}]}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/projects/45\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755581799\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/projects/45", "status_code": "<pre class=sf-dump id=sf-dump-2103011351 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2103011351\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-632244374 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-632244374\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-591266752 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-591266752\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1188692902 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkdyV0VWVTkrU2o3OUJsOFI2enJhSXc9PSIsInZhbHVlIjoib2Q1NzFNUXpDSEdDdC9uaXpTaDUxWGNHMGF6R3JSam1Nd1o1UVphbHc5SXd0bzV3TU9zb0ZsYWJRbndSUlFMaTArcXo2dC9SQXJHelR3VzZvb3N1UXNwTU00NnM3cmZMRjF0Q3pPYmtlTktMUzV2MVJIV2grSjltUkJJMEhtK0giLCJtYWMiOiJhZWU0NDY5ODc5OWFiZGVmZDFmOTRhMDljM2VmM2ZkYTQxYTBiZDI1MzhhNTM0YWEyYzIwYzljODYxMzZiZGZkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjE3aXQ3RXlSbm1QU2dPa2JMYXVDVWc9PSIsInZhbHVlIjoiUEt3MDQ2a0hMRmJ5Y1ZFWncvbjROZWtxdEFweTRsZ29PZHF2Vk1IZk5yYXIrcHdTeU5jbVpaaU5meklFMmZPdXNKMjhvLzd2M3RpelN3V2pEMElnamJreUJDa1ZLYmJUUGphTUR5K0FFTFVVYU4wS3Y4QVZJL001NkNXb01GNDkiLCJtYWMiOiJmOWIwZmZkODcxMDAxMDQyZWIyNmZhMTJiMzlkMzI3YzIxMDhhYTQyYzBkMzc2ZWExYWI0NmRkMGE2YmU1NDc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1188692902\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-280979230 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59967</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/projects/45</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/projects/45</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/index.php/projects/45</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkdyV0VWVTkrU2o3OUJsOFI2enJhSXc9PSIsInZhbHVlIjoib2Q1NzFNUXpDSEdDdC9uaXpTaDUxWGNHMGF6R3JSam1Nd1o1UVphbHc5SXd0bzV3TU9zb0ZsYWJRbndSUlFMaTArcXo2dC9SQXJHelR3VzZvb3N1UXNwTU00NnM3cmZMRjF0Q3pPYmtlTktMUzV2MVJIV2grSjltUkJJMEhtK0giLCJtYWMiOiJhZWU0NDY5ODc5OWFiZGVmZDFmOTRhMDljM2VmM2ZkYTQxYTBiZDI1MzhhNTM0YWEyYzIwYzljODYxMzZiZGZkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjE3aXQ3RXlSbm1QU2dPa2JMYXVDVWc9PSIsInZhbHVlIjoiUEt3MDQ2a0hMRmJ5Y1ZFWncvbjROZWtxdEFweTRsZ29PZHF2Vk1IZk5yYXIrcHdTeU5jbVpaaU5meklFMmZPdXNKMjhvLzd2M3RpelN3V2pEMElnamJreUJDa1ZLYmJUUGphTUR5K0FFTFVVYU4wS3Y4QVZJL001NkNXb01GNDkiLCJtYWMiOiJmOWIwZmZkODcxMDAxMDQyZWIyNmZhMTJiMzlkMzI3YzIxMDhhYTQyYzBkMzc2ZWExYWI0NmRkMGE2YmU1NDc4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755583724.2455</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755583724</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-280979230\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-35937033 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-35937033\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-105911872 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:08:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjNrZlFFanZENzFKSlBHTmhOWU1FZnc9PSIsInZhbHVlIjoiaHlxcXlCeFl1SWlVSEpBeWVIS1Nnemt0OHVJc2VpbW1GRHhnSDVYQXBDUkZwdjZhYUxaeWNaN2EzaUxoaGl5ZEtieWc0UGFTU2pxUVArcll2aUk3N3IycmlmMkkwVFovdkVyUUQ3bkVGLy9GemVKcGEvSlU1V3dGN2ZsVDZFWFIiLCJtYWMiOiI3MWZjYTk3ODAxNzVkMDkyYmEzNDQ4ZDIyMzUzNjkxZTMzYzFlYmJlMzU0ZjM1M2FiYTM0NzE5MzE0MTRmMjc3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:08:44 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im1UNTVlbTFUTE51c09YZEU5YWtNbkE9PSIsInZhbHVlIjoiK1dhLzlSQjF3bjJ2WkZXN0pkQW5YU29Md0N3czFSaGJVaEhDN3hNdGFIL0JKcTNPc2J6blZHUTJmRW1BTnU3akJnWU9GbkdFTjE1Mkcrd1BmaUdBOVBsaFJ6dTVSQlJWNDFjbUwrWmxsdGw3cXc2aWhMZjJ6Wms2NVZGU1BIdjkiLCJtYWMiOiIzMDY5MzYzZjQ5YWMwOGQwZDUyMGZlNTljZTFmMmU1ZGEwYzgxNmYxMzgxOTA2OTExZGQyMzg2MDJiZmQxZjIyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:08:44 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjNrZlFFanZENzFKSlBHTmhOWU1FZnc9PSIsInZhbHVlIjoiaHlxcXlCeFl1SWlVSEpBeWVIS1Nnemt0OHVJc2VpbW1GRHhnSDVYQXBDUkZwdjZhYUxaeWNaN2EzaUxoaGl5ZEtieWc0UGFTU2pxUVArcll2aUk3N3IycmlmMkkwVFovdkVyUUQ3bkVGLy9GemVKcGEvSlU1V3dGN2ZsVDZFWFIiLCJtYWMiOiI3MWZjYTk3ODAxNzVkMDkyYmEzNDQ4ZDIyMzUzNjkxZTMzYzFlYmJlMzU0ZjM1M2FiYTM0NzE5MzE0MTRmMjc3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:08:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im1UNTVlbTFUTE51c09YZEU5YWtNbkE9PSIsInZhbHVlIjoiK1dhLzlSQjF3bjJ2WkZXN0pkQW5YU29Md0N3czFSaGJVaEhDN3hNdGFIL0JKcTNPc2J6blZHUTJmRW1BTnU3akJnWU9GbkdFTjE1Mkcrd1BmaUdBOVBsaFJ6dTVSQlJWNDFjbUwrWmxsdGw3cXc2aWhMZjJ6Wms2NVZGU1BIdjkiLCJtYWMiOiIzMDY5MzYzZjQ5YWMwOGQwZDUyMGZlNTljZTFmMmU1ZGEwYzgxNmYxMzgxOTA2OTExZGQyMzg2MDJiZmQxZjIyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:08:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105911872\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1410098137 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755581799</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1410098137\", {\"maxDepth\":0})</script>\n"}}