<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class UserStoryGeneralNfr extends Model
{
    use HasFactory;
    public $timestamps = false;
    // Define table if the table name is not the plural form of the model name
    protected $table = 'user_story_general_nfr';  // Adjust if necessary

    // Define fillable columns
    protected $fillable = [
        'user_story_id',
        'general_nfr_id',
        'specific_nfr_id'
    ];

    public function generalNfr()
    {
        return $this->belongsTo(GeneralNFR::class, 'general_nfr_id', 'general_nfr_id');
    }
    public function userStory()
    {
        return $this->belongsTo(UserStory::class, 'user_story_id', 'u_id');
    }

    public function specificNfr()
    {
        return $this->belongsTo(specificNfr::class, 'specific_nfr_id', 'nfr_id');
    }

    // public static function getNFRByUserStoryId($userStoryId)
    // {
    //     $nfrs = self::where('user_story_id', $userStoryId)->get();

    //     return [
    //         'general' => $nfrs->pluck('general_nfr_id')->unique(),
    //         'specific' => $nfrs->pluck('specific_nfr_id'),
    //     ];
    // }

    public static function getLinkedUserStoriesByNFR(
    $nfrId,
    $type = 'general', // 'general' or 'specific'
    $projectFilter = null,
    $sprintFilter = null,
    //$projectIds = []
    ) {
        $query = DB::table('user_story_general_nfr')
            ->join('user_stories', 'user_story_general_nfr.user_story_id', '=', 'user_stories.u_id')
            ->join('projects', 'user_stories.proj_id', '=', 'projects.id')
            ->leftJoin('sprint', function($join) {
                $join->on('user_stories.sprint_id', '=', 'sprint.sprint_id')
                     ->where('user_stories.sprint_id', '!=', '0');
            });

        // Choose the correct NFR column
        if ($type === 'general') {
            $query->where('user_story_general_nfr.general_nfr_id', $nfrId);
        } elseif ($type === 'specific') {
            $query->where('user_story_general_nfr.specific_nfr_id', $nfrId);
        }

        // Apply project and sprint filters
        $query->when($projectFilter, function ($query) use ($projectFilter) {
            return $query->where('user_stories.proj_id', $projectFilter);
        });

        $query->when($sprintFilter, function ($query) use ($sprintFilter) {
            return $query->where('user_stories.sprint_id', $sprintFilter);
        });

    return $query->distinct('user_story_general_nfr.user_story_id')
        ->select('user_stories.user_story', 'user_stories.u_id', 'projects.proj_name',
                 DB::raw('COALESCE(sprint.sprint_name, "Backlog") as sprint_name'))
        ->paginate(8);
    }
    public static function updateNFRUserStoryLink($user_story_id, $selected_nfrs)
    {
        // Delete existing mappings
        self::where('user_story_id', $user_story_id)->delete();

        // Reinsert updated mappings
        foreach ($selected_nfrs as $general_nfr_id => $specific_nfr_ids_array) {
            foreach ($specific_nfr_ids_array as $specific_nfr_id) {
                self::create([
                    'user_story_id' => $user_story_id,
                    'general_nfr_id' => $general_nfr_id,
                    'specific_nfr_id' => $specific_nfr_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    public static function getNFRByUserStoryId($userStoryId, $generalNfrId = null, $withDetails = false, $perPage = 5)
    {
        $query = self::where('user_story_id', $userStoryId);

        if ($generalNfrId !== null) {
            $query->where('general_nfr_id', $generalNfrId);
        }

        if ($withDetails) {
            $paginated = $query->paginate($perPage);
            return [
                'paginated' => $paginated,
                'formatted' => self::formatList($paginated),
            ];
        } else {
            $nfrs = $query->get();
            return [
                'general' => $nfrs->pluck('general_nfr_id')->unique(),
                'specific' => $nfrs->pluck('specific_nfr_id'),
            ];
        }
    }

    public static function formatList($nfrRecords)
    {
        $formatted = [];

        foreach ($nfrRecords as $nfr) {
            $general = GeneralNFR::find($nfr->general_nfr_id);
            $specific = SpecificNFR::find($nfr->specific_nfr_id);

            $formatted[] = [
                'general' => $general->general_nfr ?? 'Unknown',
                'specific' => $specific->specific_nfr ?? 'Unknown',
            ];
        }

        return $formatted;
    }

    public static function filterByProject($query, $projId)
{
    return $query->whereHas('userStory', function ($q) use ($projId) {
        $q->where('proj_id', $projId);
    });
}

    public static function filterBySprint($query, $sprintName)
    {
        return $query->whereHas('userStory.sprint', function ($q) use ($sprintName) {
            $q->where('sprint_name', $sprintName);
        });
    }

    public static function filterByGeneralNFR($query, $generalNfrId)
    {
        return $query->where('general_nfr_id', $generalNfrId);
    }

    public static function filterBySpecificNFR($query, $specificNfrId)
    {
        return $query->where('specific_nfr_id', $specificNfrId);
    }
    public static function getFilteredTVT($projId, $filters = [], $perPage = 8)
    {
        $query = self::with(['userStory.sprint', 'generalNfr', 'specificNfr']);
        $query = self::filterByProject($query, $projId);

        if (!empty($filters['sprint'])) {
            $query = self::filterBySprint($query, $filters['sprint']);
        }

        if (!empty($filters['general_nfr'])) {
            $query = self::filterByGeneralNFR($query, $filters['general_nfr']);
        }

        if (!empty($filters['nfr'])) {
            $query = self::filterBySpecificNFR($query, $filters['nfr']);
        }

        return $query->paginate($perPage);
    }
}
