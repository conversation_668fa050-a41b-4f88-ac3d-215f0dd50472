{"__meta": {"id": "X228aaad75e82e18e3e7f9cac2d3bf52b", "datetime": "2025-08-19 13:32:20", "utime": 1755581540.821473, "method": "GET", "uri": "/role/43", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 23, "messages": [{"message": "[13:32:20] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755581540.567147, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/role/43", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.627079, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: Gate check for permission: add_roles on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.797092, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.79807, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.798118, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: Gate check for permission: edit_roles on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.802584, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.80344, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.80349, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: Gate check for permission: delete_roles on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.80386, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.804658, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.804705, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: Gate check for permission: edit_roles on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.804971, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.805705, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.805749, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: Gate check for permission: delete_roles on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.806015, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.806736, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.806779, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: Gate check for permission: updateUserRole_roles on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.807032, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.807765, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.807808, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: Gate check for permission: updateUserRole_roles on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.80808, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.808815, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:20] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755581540.808864, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755581540.22772, "end": 1755581540.821518, "duration": 0.5937979221343994, "duration_str": "594ms", "measures": [{"label": "Booting", "start": 1755581540.22772, "relative_start": 0, "end": 1755581540.546905, "relative_end": 1755581540.546905, "duration": 0.3191850185394287, "duration_str": "319ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755581540.546919, "relative_start": 0.3191990852355957, "end": 1755581540.82152, "relative_end": 2.1457672119140625e-06, "duration": 0.2746009826660156, "duration_str": "275ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24094968, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "role.index (\\resources\\views\\role\\index.blade.php)", "param_count": 4, "params": ["roles", "pro", "teamMembers", "title"], "type": "blade"}]}, "route": {"uri": "GET role/{project_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\RoleController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "role.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\RoleController.php&line=20\">\\app\\Http\\Controllers\\RoleController.php:20-51</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00227, "accumulated_duration_str": "2.27ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 19.824}, {"sql": "select `team_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\RoleController.php", "line": 27}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\RoleController.php:27", "connection": "sagile", "start_percent": 19.824, "width_percent": 20.264}, {"sql": "select * from `projects` where `team_name` in ('iv<PERSON>\\'s team', 'iv<PERSON>\\'s team', 'Team 888', 'Team 888')", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team", "ivlyn&#039;s team", "Team 888", "Team 888"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\RoleController.php", "line": 28}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Http\\Controllers\\RoleController.php:28", "connection": "sagile", "start_percent": 40.088, "width_percent": 18.502}, {"sql": "select * from `roles` where `project_id` = '43'", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\RoleController.php", "line": 31}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Http\\Controllers\\RoleController.php:31", "connection": "sagile", "start_percent": 58.59, "width_percent": 17.621}, {"sql": "select * from `teammappings` where `team_name` = 'Team 888' and `project_id` = 43 and `invitation_status` = 'accepted'", "type": "query", "params": [], "bindings": ["Team 888", "43", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\RoleController.php", "line": 42}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Http\\Controllers\\RoleController.php:42", "connection": "sagile", "start_percent": 76.211, "width_percent": 23.789}]}, "models": {"data": {"App\\TeamMapping": 2, "App\\Role": 2, "App\\Project": 1, "App\\User": 1}, "count": 6}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 7, "messages": [{"message": "[\n  ability => add_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">add_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755581540.801234, "xdebug_link": null}, {"message": "[\n  ability => edit_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1400308121 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">edit_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400308121\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755581540.803653, "xdebug_link": null}, {"message": "[\n  ability => delete_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-934504132 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-934504132\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755581540.804823, "xdebug_link": null}, {"message": "[\n  ability => edit_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1889548259 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">edit_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1889548259\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755581540.805858, "xdebug_link": null}, {"message": "[\n  ability => delete_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2021250833 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2021250833\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755581540.806883, "xdebug_link": null}, {"message": "[\n  ability => updateUserRole_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-329593563 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">updateUserRole_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-329593563\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755581540.807909, "xdebug_link": null}, {"message": "[\n  ability => updateUserRole_roles,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-251163847 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">updateUserRole_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-251163847\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755581540.808967, "xdebug_link": null}]}, "session": {"_token": "dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/role/43\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755580949\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/role/43", "status_code": "<pre class=sf-dump id=sf-dump-500125257 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-500125257\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-542135830 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-542135830\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1655428474 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1655428474\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1408675767 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjhMaHpFMHV1R2pqWndRTUx0ZUh1NUE9PSIsInZhbHVlIjoiWnJUclZ0WG1CWEM4SmVCMENmWkcwbVczd2pnblh2RSs4U1NzaW5wNDZWcnRCWWVhRU5vMU5vUUFWZDR3bVZ5V0d6L3k3NGV4Vm1aWUxvbmNSZ3ZGVklpNnF3Um45OVhZNVVrOWVCR2xOTUtvYVpIRDdlTW1KaWwweDZ5aUdCcnQiLCJtYWMiOiJmNzE1YjlmYWUyZjdkY2IyMWNiNDFjZjA0ODc1M2RiOWI4MmUyMjY3NmJhMzRkOTlmYzY3ZjM1ZGUwNmU2ZDdiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlNNd2NuVUtIT1lxYUN6emJuZENTUHc9PSIsInZhbHVlIjoic0lvendsczFMdjFDL2F1cFUxa3ZEYThqdGMrSXgvWGdSemo4Q21BNnBzbURXQWZNWnkyeWJ1b0ducUJvTFNLRzVFMUdmS0pFZDQybWVUaWY3c1hVK2RUTVRKTzQ0UENNdnlLNHQ2Y2cwbVg5MjRNUllWcnpUNEc2bnAzd290VWoiLCJtYWMiOiI2Y2QyZDUyMDk5NTNhZTQwOWI3NDc3ZDQxMTliNDI3MjcyODQ4OWE0YjVkNDNmYWQzNGFjOWFlZTQxMDA2MzI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1408675767\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1562994381 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58200</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"8 characters\">/role/43</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"8 characters\">/role/43</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"18 characters\">/index.php/role/43</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/43</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjhMaHpFMHV1R2pqWndRTUx0ZUh1NUE9PSIsInZhbHVlIjoiWnJUclZ0WG1CWEM4SmVCMENmWkcwbVczd2pnblh2RSs4U1NzaW5wNDZWcnRCWWVhRU5vMU5vUUFWZDR3bVZ5V0d6L3k3NGV4Vm1aWUxvbmNSZ3ZGVklpNnF3Um45OVhZNVVrOWVCR2xOTUtvYVpIRDdlTW1KaWwweDZ5aUdCcnQiLCJtYWMiOiJmNzE1YjlmYWUyZjdkY2IyMWNiNDFjZjA0ODc1M2RiOWI4MmUyMjY3NmJhMzRkOTlmYzY3ZjM1ZGUwNmU2ZDdiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlNNd2NuVUtIT1lxYUN6emJuZENTUHc9PSIsInZhbHVlIjoic0lvendsczFMdjFDL2F1cFUxa3ZEYThqdGMrSXgvWGdSemo4Q21BNnBzbURXQWZNWnkyeWJ1b0ducUJvTFNLRzVFMUdmS0pFZDQybWVUaWY3c1hVK2RUTVRKTzQ0UENNdnlLNHQ2Y2cwbVg5MjRNUllWcnpUNEc2bnAzd290VWoiLCJtYWMiOiI2Y2QyZDUyMDk5NTNhZTQwOWI3NDc3ZDQxMTliNDI3MjcyODQ4OWE0YjVkNDNmYWQzNGFjOWFlZTQxMDA2MzI0IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755581540.2277</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755581540</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1562994381\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1139467791 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GKxRuD042xMQ4k3v5EHCZLsF1sfAXXnsz4rkTCog</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1139467791\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:32:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InFiR0w5WmppMkR3RHUyWE9KNnRmWkE9PSIsInZhbHVlIjoia0tSKzRFZTBLRTkrSldlT0dVdk1jU3ZvSS9hVG1vMDVqVFl4b2M2cFRMREhTVXpaSzlkZFcrUjFJdnFvd2M0ZFdNcGNFcGg3dkhOVVhaMElCZ3FRWGR4Zmc5blhNNzlKQVk3elN2Z3pzNnA4akpBbkNqcHhGc0FGc2YxQlpLaUMiLCJtYWMiOiJkOTg1MWJmYmM4NGJlYTIyODM2ODM1NDMxNzk3N2U3OGVmOTM4MTAxMWUxMDAzOWM5ZmQ5ZjI0OGFmZTQ2MTUyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:32:20 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im9kTlFWRWRvOVcyMDFDMDNCMjNxdkE9PSIsInZhbHVlIjoiWjRabEFGVHBBbFp2dHdYMFNjU2JvaXh5ak5LcWN2WldoRnZVbHJMb2ttMDNsYXg3SHRnTStJZlhhUWlhVGhtM1oxTzUvZHpMbEp0RlJZcHBodUtnc05UblpqTEUzeXQxNkp0cVdrKzNCRlhza0x1bmlFazlqdFBxVHVIY2hnTm8iLCJtYWMiOiJlYmNhNDIwOTMyMzdhNzRkNTNlMTViMjk0ZGM1YThiN2U3ODZjYzY0ZDA4YTdiM2EyM2RkMjBmOGY0YWUzYzhjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:32:20 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InFiR0w5WmppMkR3RHUyWE9KNnRmWkE9PSIsInZhbHVlIjoia0tSKzRFZTBLRTkrSldlT0dVdk1jU3ZvSS9hVG1vMDVqVFl4b2M2cFRMREhTVXpaSzlkZFcrUjFJdnFvd2M0ZFdNcGNFcGg3dkhOVVhaMElCZ3FRWGR4Zmc5blhNNzlKQVk3elN2Z3pzNnA4akpBbkNqcHhGc0FGc2YxQlpLaUMiLCJtYWMiOiJkOTg1MWJmYmM4NGJlYTIyODM2ODM1NDMxNzk3N2U3OGVmOTM4MTAxMWUxMDAzOWM5ZmQ5ZjI0OGFmZTQ2MTUyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:32:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im9kTlFWRWRvOVcyMDFDMDNCMjNxdkE9PSIsInZhbHVlIjoiWjRabEFGVHBBbFp2dHdYMFNjU2JvaXh5ak5LcWN2WldoRnZVbHJMb2ttMDNsYXg3SHRnTStJZlhhUWlhVGhtM1oxTzUvZHpMbEp0RlJZcHBodUtnc05UblpqTEUzeXQxNkp0cVdrKzNCRlhza0x1bmlFazlqdFBxVHVIY2hnTm8iLCJtYWMiOiJlYmNhNDIwOTMyMzdhNzRkNTNlMTViMjk0ZGM1YThiN2U3ODZjYzY0ZDA4YTdiM2EyM2RkMjBmOGY0YWUzYzhjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:32:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/role/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755580949</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}