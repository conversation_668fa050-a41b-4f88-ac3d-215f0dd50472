<?php

namespace App\Http\Controllers;
use App\Priority;
use App\UserStory;
use App\Status;
use App\SecurityFeature;
use App\PerformanceFeature;
use App\Project;
use App\Role;
use App\Mapping;
use App\Team;
use App\TeamMapping;
use App\Sprint;
use App\Task;
use App\Http\Controllers\Auth;
use App\Http\Controllers\NFRController;
use App\GeneralNFR;
use App\SpecificNFR;
use App\UserStoryGeneralNfr;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

class UserStoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    


    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public static function getID()
    {
        $parameters = \Request::segment(3);
        return $parameters;
    }

    public static function getID2()
    {
        $parameters = \Request::segment(2);
        return $parameters;
    }

    public function index($project_id)
    {
        // Get the project from the sprint
        $project = Project::where('id', $project_id)->firstOrFail();
        $project_name = $project->proj_name;
        
        // Get the user stories associated with this sprint
        $userstories = UserStory::where('proj_id', $project_id)->get();
        
        // Get all status options for user stories
        $statuses = Status::where('project_id', $project_id)->get();
        
        return view('userstory.index', [
            'userstories' => $userstories,
            'project_id' => $project_id,
            'statuses' => $statuses,
        ]);
    }

    /**
     * Update the status of a user story without page redirect.
     *  
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function updateStatus(Request $request, $id)
    {
        // Find the user story by ID
        $userstory = UserStory::findOrFail($id);
        
        // Validate the request
        $request->validate([
            'status_id' => 'required|exists:statuses,id',
        ]);
        
        // Update the status
        $userstory->status_id = $request->status_id;
        $userstory->save();
        
        // Return JSON response for AJAX request
        return response()->json([
            'success' => true,
            'message' => 'Status updated successfully'
        ]);
    }
    public function create($proj_id)
    {
        // Get team members for this specific project
        $userTeams = TeamMapping::where('project_id', $proj_id)
            ->select('username')
            ->get()
            ->map(function ($member) {
                return ['username' => $member->username];
            })
            ->toArray();
        
        // Get roles directly from roles table for this project
        $roles = Role::where('project_id', $proj_id)
            ->pluck('role_name')
            ->toArray();

        // Get statuses, security and performance features
        $statuses = Status::where('project_id', $proj_id)->get();
        $secfeatures = SecurityFeature::select('secfeature_name')->get();
        $perfeatures = PerformanceFeature::select('perfeature_name')->get();

        // Retrieve all general NFRs and specific NFRs
        $generalNFRs = GeneralNFR::all();
        $generalNFRNames = [];
        $generalNFRIds = [];
        $specificNFRs = [];
        $specificNFRIds = [];
        foreach ($generalNFRs as $generalNFR) {
            $generalNFRNames[] = $generalNFR->general_nfr;
            $generalNFRIds[] = $generalNFR->general_nfr_id;
            $specificNFRs[$generalNFR->general_nfr_id] = SpecificNFR::where('general_nfr_id', $generalNFR->general_nfr_id)->pluck('specific_nfr')->toArray();
            $specificNFRIds[$generalNFR->general_nfr_id] = SpecificNFR::where('general_nfr_id', $generalNFR->general_nfr_id)->pluck('nfr_id')->toArray();


        //$statuses = Status::where('project_id', $project->id)->get();

        $nfrData = SpecificNFR::getNFRData();
        $generalNFRData = GeneralNFR::getAllGeneralNFRNameAndId();

        return view('userstory.create', array_merge([
        'title' => 'Create User Story',
        'statuses' => $statuses,
        'proj_id' => $proj_id,
        'teamlist' => $userTeams,
        'roles' => $roles,
        'generalNFRNames' => $generalNFRData['names'],
        'generalNFRIds' => $generalNFRData['ids'],
        ], $nfrData));
    }
    }
    
    public function store(Request $request)
    {
        // Validation
        $validation = $request->validate([
            'user_story' => 'required|unique:user_stories,user_story,NULL,id,sprint_id,' . $request->input('sprint_id'),
            'means' => 'required',
            'status_id' => 'required',
            'role' => 'required',
        ], [
            'user_story.required' => '*The User Story Name is required',
            'user_story.unique' => '*There is already an existing User Story in the sprint with the same name',
            'means.required' => '*The means is required',
            'role.required' => '*The role is required',
            'status_id.required' => '*The Status is required',
        ]);

        // Validate sprint exists if not backlog (sprint_id != 0)
        if ($request->sprint_id != '0' && $request->sprint_id != 0) {
            $sprint = Sprint::where('sprint_id', $request->sprint_id)->first();
            if (!$sprint) {
                return back()->withErrors(['sprint_id' => 'The selected sprint does not exist.'])->withInput();
            }
        }

        // Create new UserStory instance
        $userstory = new UserStory;
        $userstory->user_story = $request->user_story;
        $userstory->means = $request->means;
        $userstory->status_id = $request->status_id;
        $userstory->sprint_id = $request->sprint_id;
        $userstory->prio_story = 0;
        $userstory->proj_id = $request->proj_id;
        // Store features and usernames as JSON
        $userstory->secfeature_id = json_encode($request->secfeature_id);
        $userstory->perfeature_id = json_encode($request->perfeature_id);
        $userstory->user_names = json_encode($request->user_names);
    
        // Gather selected NFR IDs into arrays
        $general_nfr_ids = [];
        $specific_nfr_ids = [];
    
        if ($request->has('selected_nfrs')) {
            foreach ($request->input('selected_nfrs') as $general_nfr_id => $specific_nfr_ids_array) {
                $general_nfr_ids[] = $general_nfr_id; // Store general NFR ID
                foreach ($specific_nfr_ids_array as $specific_nfr_id) {
                    $specific_nfr_ids[$general_nfr_id][] = $specific_nfr_id;
                }
            }
        }
    
        // Store NFR IDs as JSON
        $userstory->general_nfr_id = json_encode($general_nfr_ids);
        $userstory->specific_nfr_id = json_encode($specific_nfr_ids);
    
        // Save the user story
        $userstory->save();
        $user_story_id = $userstory->u_id;

        // Insert related records into user_story_general_nfr table
    if (count($general_nfr_ids) > 0 && count($specific_nfr_ids) > 0) {
        foreach ($general_nfr_ids as $general_nfr_id) {
            foreach ($specific_nfr_ids[$general_nfr_id] as $specific_nfr_id) {
                UserStoryGeneralNfr::create([
                    'user_story_id' => $user_story_id,
                    'general_nfr_id' => $general_nfr_id,
                    'specific_nfr_id' => $specific_nfr_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }
    
        return redirect()->route('userstory.index', ['proj_id' => $request->proj_id])
            ->with('success', 'User Story has successfully been created!');
    }
    
    
    /**
     * Display the specified resource.
     *
     * @param  \App\UserStory  $userStory
     * @return \Illuminate\Httfp\Response
    */
        
    public function show(UserStory $userStory)
    {
       //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\UserStory  $userStory
     * @return \Illuminate\Http\Response
     */

     public function edit(UserStory $userstory)
     {
         $project = Project::findOrFail($userstory->proj_id);
     
         // Get statuses and team details
         $statuses = Status::where('project_id', $project->id)->get();
         $teamlist = TeamMapping::where('project_id', $userstory->proj_id)
                        ->select('username')
                        ->get()
                        ->map(function ($member) {
                            return ['username' => $member->username];
                        });
     
         $secfeatures = SecurityFeature::select('secfeature_name')->get();
         $perfeatures = PerformanceFeature::select('perfeature_name')->get();
         // Get NFRs
//          $generalNFRs = NFRmodel::all();
//          $generalNFRNames = [];
//          $generalNFRIds = [];
//          $specificNFRs = [];
//          $specificNFRIds = [];
//          foreach ($generalNFRs as $generalNFR) {
//              $generalNFRNames[] = $generalNFR->general_nfr;
//              $generalNFRIds[] = $generalNFR->general_nfr_id;
//              $specificNFRs[$generalNFR->general_nfr_id] = NFR::where('general_nfr_id', $generalNFR->general_nfr_id)->pluck('specific_nfr')->toArray();
//              $specificNFRIds[$generalNFR->general_nfr_id] = NFR::where('general_nfr_id', $generalNFR->general_nfr_id)->pluck('nfr_id')->toArray();
//          }
     
        // Get the proj_name from the project
        //$team_name = $project->team_name;
        // $secfeature = new SecurityFeature;
        // $perfeature = new PerformanceFeature;
        // $secfeatures = $secfeature->select('secfeature_name')->get();
        // $perfeatures = $perfeature->select('perfeature_name')->get();
        // Get NFRs
        // Get general and specific NFRs
        $nfrData = SpecificNFR::getNFRData();
        $generalNFRData = GeneralNFR::getAllGeneralNFRNameAndId();
     
        // Fetch the user story's associated NFRs
        $selectedNFRs = UserStoryGeneralNfr::getNFRByUserStoryId($userstory->u_id);

        return view('userstory.edit', array_merge([
        'title' => 'Edit User Story',
        'userstory' => $userstory,
        'statuses' => $statuses,
        'teamlist' => $teamlist,
        'generalNFRNames' => $generalNFRData['names'],
        'generalNFRIds' => $generalNFRData['ids'],
        'selectedGeneralNFRIds' => $selectedNFRs['general'],
        'selectedSpecificNFRIds' => $selectedNFRs['specific'],
        ], $nfrData));
     }
     


    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\UserStory  $userStory
     * @return \Illuminate\Http\Response
     */
    
     public function update(Request $request, UserStory $userstory)
{
    // Validate the request
    $validation = $request->validate([
        'status_id' => 'required',
    ], [
        'status_id.required' => '*The Status is required',
    ]);

    // Update the UserStory attributes
    $userstory->status_id = $request->status_id;
    $userstory->perfeature_id = json_encode($request->perfeature_id);
    $userstory->secfeature_id = json_encode($request->secfeature_id);
    $userstory->user_names = json_encode($request->user_names);

    // Save changes to the UserStory
    $userstory->save();

    // Delete all existing entries in user_story_general_nfr for the given user_story_id
     // ! Start of Overhaul
    UserStoryGeneralNfr::where('user_story_id', $userstory->u_id)->delete();

    // Reinsert new entries for NFRs if provided
    $general_nfr_ids = [];
    $specific_nfr_ids = [];

    if ($request->has('selected_nfrs')) {
        foreach ($request->input('selected_nfrs') as $general_nfr_id => $specific_nfr_ids_array) {
            $general_nfr_ids[] = $general_nfr_id;
            foreach ($specific_nfr_ids_array as $specific_nfr_id) {
                $specific_nfr_ids[] = $specific_nfr_id;

                // Insert into user_story_general_nfr table
                UserStoryGeneralNfr::create([
                    'user_story_id' => $userstory->u_id,
                    'general_nfr_id' => $general_nfr_id,
                    'specific_nfr_id' => $specific_nfr_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    // Fetch the necessary data for userstory.index route
    $project_id = $userstory->proj_id;
    $userstories = UserStory::where('proj_id', $project_id)->get();
    $statuses = Status::where('project_id', $project_id)->get();

    // Redirect back to userstory.index with all required parameters
    return redirect()->route('userstory.index', ['proj_id' => $userstory->proj_id])
        ->with('userstories', $userstories)
        ->with('statuses', $statuses)
        ->with('project_id', $project_id)
        ->with('success', 'User Story has successfully been updated!');
       // ! end of overhaul
  
    // ! Start of main ivlyn
//     UserStoryGeneralNfr::updateNFRUserStoryLink($userstory->u_id, $request->input('selected_nfrs'));
    
//     // Redirect back with success message
//     return redirect()->route('profeature.index3', ['sprint_id' => $userstory->sprint_id])
//         ->with('success', 'User Story - ' . $userstory->user_story . ' has successfully been updated!');
       
       // ! End of Main

}
public function viewDetails(Request $request, UserStory $userstory)
{
    $sprint = Sprint::getSprintById($userstory->sprint_id);
    $project = Project::getProjectByName($sprint->proj_name);

    // Fetch available general NFRs for the dropdown filter
    $generalNFRs = GeneralNFR::getGeneralNFRList();

    // Handle AJAX request for filtering NFRs by General NFR ID
    if ($request->ajax() && $request->has('general_nfr_id')) {
        $generalNfrId = $request->general_nfr_id === 'all' ? null : $request->general_nfr_id;

        $nfrData = UserStoryGeneralNfr::getNFRByUserStoryId(
            $userstory->u_id,
            $generalNfrId,
            true,
            5
        );

        return response()->json([
            'nfrDetails' => $nfrData['formatted'],
            'pagination' => (string) $nfrData['paginated']->links('pagination::bootstrap-4')
        ]);
    }

    // Load default paginated NFR details for initial page load
    $nfrData = UserStoryGeneralNfr::getNFRByUserStoryId(
        $userstory->u_id,
        null,
        true,
        5
    );

    return view('userstory.viewDetails', [
        'title' => 'User Story Details',
        'userstory' => $userstory,
        'sprint' => $sprint,
        'project' => $project,
        'generalNFRs' => $generalNFRs,
        'nfrDetails' => $nfrData['formatted'],
        'pagination' => $nfrData['paginated']->links('pagination::bootstrap-4')
    ]);
}

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\UserStory  $userStory
     * @return \Illuminate\Http\Response
     */
    public function destroy(UserStory $userstory)
{
    // Determine if the user story is associated with a sprint
    $sprint_id = $userstory->sprint_id;

    // Get tasks related to the user story
    $tasks = Task::where('userstory_id', $userstory->u_id)->get();

    // Delete tasks related to the user story
    $tasks->each->delete();

    // Delete the user story
    $userstory->delete();

    // Prepare the redirect based on whether the user story is in a sprint or backlog
    if ($sprint_id) {
        $userstories = UserStory::where('sprint_id', $sprint_id)->get();
        $sprint = Sprint::find($sprint_id);
        $statuses = Status::all();

        return redirect()->route('profeature.index3', ['sprint_id' => $sprint_id])
            ->with('userstories', $userstories)
            ->with('statuses', $statuses)
            ->with('success', 'User Story has successfully been deleted!');
    } else {
        // Handle backlog condition
        $userstories = UserStory::where('proj_id', $userstory->proj_id)
            ->where('sprint_id', "=", 0)
            ->get();

        $project = Project::find($userstory->proj_id);

        // Get the user's team mapping
        $user = \Auth::user();
        $teammapping = TeamMapping::where('username', $user->username)
            ->pluck('team_name')
            ->toArray();

        // Get projects associated with the user's team
        $projects = Project::whereIn('team_name', $teammapping)->get();

        return redirect()->route('userstory.index', ['proj_id' => $project->id])
            ->with('project', $project)
            ->with('userstories', $userstories)
            ->with('pros', $projects)
            ->with('title', 'Backlog for ' . $project->proj_name)
            ->with('success', 'Userstory has successfully been deleted!');
    }
}


    //Backlog in Project Page -- START
    public function createBacklog($proj_id)
    {
        $user = \Auth::user();
        $project = Project::where('id', $proj_id)->first();
        $team = Team::where('team_name', $project->team_name)->first();
        $roles = TeamMapping::where('team_name', $team->team_name)
                            ->where('project_id', $proj_id)
                            ->distinct('role_name')
                            ->pluck('role_name');

        $secfeature = new SecurityFeature;
        $perfeature = new PerformanceFeature;
        $secfeatures = $secfeature->select('secfeature_name')->get();
        $perfeatures = $perfeature->select('perfeature_name')->get();
        
        // Retrieve all teams associated with the project
        $teams = Project::where('id', $proj_id)->get();
        $userTeams = [];
        foreach ($teams as $team) {
            $teamMembers = TeamMapping::where('team_name', $team->team_name)
                                      ->where('project_id', $proj_id)
                                      ->get();
            foreach ($teamMembers as $member) {
                $userTeams[] = [
                    'username' => $member->username,
                ];
            }
        }
        
        // Get statuses for the project
        $statuses = Status::where('project_id', $proj_id)->get();
        
        // Retrieve all general NFRs and specific NFRs
        $generalNFRs = GeneralNFR::all();
        $generalNFRNames = [];
        $generalNFRIds = [];
        $specificNFRs = [];
        $specificNFRIds = [];
        foreach ($generalNFRs as $generalNFR) {
            $generalNFRNames[] = $generalNFR->general_nfr;
            $generalNFRIds[] = $generalNFR->general_nfr_id;
            $specificNFRs[$generalNFR->general_nfr_id] = SpecificNFR::where('general_nfr_id', $generalNFR->general_nfr_id)->pluck('specific_nfr')->toArray();
            $specificNFRIds[$generalNFR->general_nfr_id] = SpecificNFR::where('general_nfr_id', $generalNFR->general_nfr_id)->pluck('nfr_id')->toArray();
        }
        
        return view('backlog.create', [
            'title' => 'Create Backlog for ' . $project->proj_name,
            'proj_id' => $proj_id,
            'roles' => $roles,
            'secfeatures' => $secfeature->all(),
            'perfeatures' => $perfeature->all(),
            'teamlist' => $userTeams,
            'statuses' => $statuses,
            'generalNFRNames' => $generalNFRNames,
            'generalNFRIds' => $generalNFRIds,
            'specificNFRs' => $specificNFRs,
            'specificNFRIds' => $specificNFRIds
        ]);
    }

    public function storeBacklog(Request $request)
    {
        //Get the current project involved
        $project = Project::where('id', $request->proj_id)->first();

        //Validate the request parameters
        $validation = $request->validate([
            'user_story' => 'required|unique:user_stories,user_story,NULL,id,sprint_id,'.$request->input('sprint_id'), 
            'means' => 'required',
            'role' => 'required',
        ], [
            'user_story.required' => '*The User Story Name is required',
            'user_story.unique' => '*There is already an existing User Story in the sprint with the same name',
            'means.required' => '*The means is required',
            'role.required' => '*The role is required',
        ]);

        //Assign request values to new Userstory 
        $userstory = new UserStory;
        $userstory->user_story = $request->user_story;

        // Set status - use provided status_id if available, otherwise default to 'Backlog'
        if ($request->has('status_id') && $request->status_id) {
            $userstory->status_id = $request->status_id;
        } else {
            $status = Status::where('project_id', $request->proj_id)
                          ->where('title', 'Backlog')
                          ->first();
            $userstory->status_id = $status ? $status->id : null;
        }
        
        $userstory->means = $request->means;
        $userstory->prio_story = 0;
        $userstory->sprint_id = 0;
        
        // Store features, assigned users, and NFRs as JSON
        $userstory->perfeature_id = json_encode($request->perfeature_id);
        $userstory->secfeature_id = json_encode($request->secfeature_id);
        $userstory->user_names = json_encode($request->user_names);
        $userstory->proj_id = $project->id;
        
        // Gather selected NFR IDs into arrays
        $general_nfr_ids = [];
        $specific_nfr_ids = [];
    
        if ($request->has('selected_nfrs')) {
            foreach ($request->input('selected_nfrs') as $general_nfr_id => $specific_nfr_ids_array) {
                $general_nfr_ids[] = $general_nfr_id; // Store general NFR ID
                foreach ($specific_nfr_ids_array as $specific_nfr_id) {
                    $specific_nfr_ids[$general_nfr_id][] = $specific_nfr_id;
                }
            }
        }
    
        // Store NFR IDs as JSON
        $userstory->general_nfr_id = json_encode($general_nfr_ids);
        $userstory->specific_nfr_id = json_encode($specific_nfr_ids);
        
        $userstory->save();
        $user_story_id = $userstory->u_id;
        
        // Insert related records into user_story_general_nfr table
        if (count($general_nfr_ids) > 0 && count($specific_nfr_ids) > 0) {
            foreach ($general_nfr_ids as $general_nfr_id) {
                foreach ($specific_nfr_ids[$general_nfr_id] as $specific_nfr_id) {
                    UserStoryGeneralNfr::create([
                        'user_story_id' => $user_story_id,
                        'general_nfr_id' => $general_nfr_id,
                        'specific_nfr_id' => $specific_nfr_id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }

        //redirect to backlog index page
        return redirect()->route('backlogTest.index', ['proj_id' => $project->id])
            ->with('success', 'Backlog item has successfully been created!');
    }

    public function editBacklog(UserStory $userstory, $id =[])
    { 
        $user = \Auth::user();
        
        $project = Project::where('id', $userstory->proj_id)->first();
        
        $secfeature = new SecurityFeature;
        $perfeature = new PerformanceFeature;
        $secfeatures = $secfeature->select('secfeature_name')->get();
        $perfeatures = $perfeature->select('perfeature_name')->get();

        return view('backlog.edit',['secfeatures'=>$secfeature->all(), 'perfeatures'=>$perfeature->all()])
            ->with('title', 'Edit Backlog - "' . $userstory->user_story . '" in '. $project->proj_name)    
            ->with('userstory', $userstory);
    }

    public function updateBacklog(Request $request, UserStory $userstory)
    {
        //Validate the request parameters
        // $validation = $request->validate([
        //     // 'means' => 'required',
        //     'title' => 'required',
        // ], [
        //     // 'means.required' => '*The Description is required',
        //     'title.required' => '*The Status is required',
        // ]);

        //Assign request values to current Userstory 
        //user_story name and sprint ID not included because does not change
        // $userstory->means = $request->means;
        // $userstory->title = $request->title;

        $str_perfeatures = json_encode($request->perfeature_id);
        $str_secfeatures = json_encode($request->secfeature_id);

        $userstory->perfeature_id = $str_perfeatures;
        $userstory->secfeature_id = $str_secfeatures;
        
        $userstory->save();

        //Get current project
        $project = Project::where('id', $userstory->proj_id)->first();

        //redirect to backlog index page
        $userstories = \App\UserStory::where('proj_id', $project->id)
            ->whereNull('sprint_id')
            ->get();

        //Get the project where user's team name(s) is the same with project's team name
        $user = \Auth::user();
        $teammapping = \App\TeamMapping::where('username', '=', $user->username)->pluck('team_name')->toArray(); // use pluck() to retrieve an array of team names
        $pro = \App\Project::whereIn('team_name', $teammapping)->get(); // use whereIn() to retrieve the projects that have a team_name value in the array

        return redirect()->route('backlog.index', ['proj_id' => $project->id])
            ->with('project', $project)
            ->with('userstories', $userstories)
            ->with('pros', $pro)
            ->with('title', 'Backlog for ' . $project->proj_name)
            ->with('success', 'Backlog - ' . $userstory->user_story . ' has successfully been updated!');

    }
    //END BACKLOG FOR PROJECT 

    public function getUserStoriesBySprint($sprintId)
{
    // Retrieve user stories for the given sprint ID
    $userStories = UserStory::where('sprint_id', $sprintId)->pluck('user_story', 'id');

    // Return as JSON response
    return response()->json($userStories);
}

    //BACKLOG FOR USERSTORY
    public function backlog($sprint_id)
    {
        //Get the project where user's team name(s) is the same with project's team name
        $user = \Auth::user();
        $teammapping = \App\TeamMapping::where('username', '=', $user->username)->pluck('team_name')->toArray(); // use pluck() to retrieve an array of team names
        $pro = \App\Project::whereIn('team_name', $teammapping)->get(); // use whereIn() to retrieve the projects that have a team_name value in the array

        //Get current project 
        $sprint = Sprint::where('sprint_id', $sprint_id)->first();
        $project = Project::where('proj_name', $sprint->proj_name)->first();
        
        $userstory = \App\UserStory::where('proj_id', $project->id)
            ->where('sprint_id','=', 0)
            ->get();

        return view('userstory.backlog',['userstories'=>$userstory,])
            ->with('project', $project)
            ->with('sprint_id', $sprint_id)
            ->with('pros', $pro)
            ->with('title', 'Assign Backlog from ' . $project->proj_name. ' to ' . $sprint->sprint_name);
    }


    public function assignUserstory($sprint_id, UserStory $userstory)
    {
        try {
            // Use the new assignToSprint method for validation
            $userstory->assignToSprint($sprint_id);

            $userstories = UserStory::where('sprint_id', $sprint_id)->get();
            $sprint = Sprint::where('sprint_id', $sprint_id)->first();

            $statuses = Status::all();

            return redirect()->route('profeature.index3', ['sprint_id' => $sprint_id])
                ->with('userstories', $userstories)
                ->with('statuses', $statuses)
                ->with('title', 'User Story for ' . $sprint->sprint_name)
                ->with('success', 'User Story has successfully been assigned from Backlog!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to assign user story: ' . $e->getMessage());
        }
    }

    //END BACKLOG FOR USERSTORY

    public static function calculateProjectProgress($projectId)
{
    // Get all user stories for the project
    $allUserStories = UserStory::where('proj_id', $projectId)->get();
    $totalStories = $allUserStories->count();

    if ($totalStories === 0) {
        return 0;
    }

    // Get the "Done" status ID for this project
    $doneStatus = Status::where('project_id', $projectId)
        ->where('title', 'Done')
        ->first();

    if (!$doneStatus) {
        return 0;
    }

    // Count completed stories (those with "Done" status)
    $completedStories = $allUserStories->where('status_id', $doneStatus->id)->count();

    // Calculate progress percentage
    return round(($completedStories / $totalStories) * 100);
}
}


