<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create User Story</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- FontAwesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: none;
            border-radius: 0.5rem;
            overflow: hidden;
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            padding: 1rem;
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        .form-check-input {
            cursor: pointer;
        }
        
        .form-check {
            margin-bottom: 0.5rem;
        }
        
        .text-danger {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
        
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Create New User Story</h4>
                        <a href="<?php echo e(route('userstory.index', ['proj_id' => $proj_id])); ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i> Back to User Stories
                        </a>
                    </div>
                    <div class="card-body">
                        <form action="<?php echo e(route('userstory.store')); ?>" method="post" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="proj_id" value="<?php echo e($proj_id); ?>">
                            <input type="hidden" name="sprint_id" value="0">

                            <div class="mb-3">
                                <label for="role" class="form-label">Role : As a</label>
                                <select name="role" id="role" class="form-select" onchange="updateUserStory()">
                                    <option value="" selected disabled>Select</option>
                                    <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($role); ?>"> <?php echo e($role); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['role'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-3">
                                <label for="means" class="form-label">Means : I am able to</label>
                                <input type="text" name="means" id="means" class="form-control" onchange="updateUserStory()" maxlength="100">
                                <?php echo $__env->make('inc.character-counter', 
                                [ 'inputId' => 'means', 
                                        'counterId' => 'means_char_count', 
                                        'maxLength' => 100], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                <?php $__errorArgs = ['means'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-3">
                                <label for="ends" class="form-label">Ends : so that I can (optional)</label>
                                <input type="text" name="ends" id="ends" class="form-control" onchange="updateUserStory()" maxlength="100">
                                <?php echo $__env->make('inc.character-counter', 
                                [ 'inputId' => 'ends', 
                                        'counterId' => 'ends_char_count', 
                                        'maxLength' => 100], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>

                            <div class="mb-3">
                                <label for="user_story" class="form-label">User Story</label>
                                <input type="text" name="user_story" id="user_story" class="form-control" maxlength="255">
                                <?php echo $__env->make('inc.character-counter', 
                                [ 'inputId' => 'user_story', 
                                        'counterId' => 'user_story_char_count', 
                                        'maxLength' => 255], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                <?php $__errorArgs = ['user_story'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-3">
                                <label for="status_id" class="form-label">Status</label>
                                <select name="status_id" id="status_id" class="form-select">
                                    <option value="" selected disabled>Select</option>
                                    <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($status->id); ?>"><?php echo e($status->title); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['status_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-3">
                                <label for="user_names" class="form-label">Assigned to</label>
                                <select name="user_names[]" class="form-select" multiple>
                                    <?php $__currentLoopData = $teamlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $teammember): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($teammember['username']); ?>"><?php echo e($teammember['username']); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            <?php if(isset($generalNFRIds)): ?>
                            <div class="mb-3">
                                <h4>General NFRs and Their Specific Requirements</h4>
                                <div class="row">
                                    <?php $__currentLoopData = $generalNFRIds; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $generalNFR): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-6 mb-3">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h5 class="mb-0"><?php echo e($generalNFRNames[$index]); ?></h5>
                                                </div>
                                                <div class="card-body">
                                                    <?php if(isset($specificNFRs[$generalNFR]) && isset($specificNFRIds[$generalNFR])): ?>
                                                        <?php $__currentLoopData = $specificNFRs[$generalNFR]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $specificIndex => $specificNFR): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="form-check">
                                                                <input type="checkbox" 
                                                                    class="form-check-input" 
                                                                    name="selected_nfrs[<?php echo e($generalNFR); ?>][]" 
                                                                    value="<?php echo e($specificNFRIds[$generalNFR][$specificIndex]); ?>"
                                                                    id="nfr_<?php echo e($generalNFR); ?>_<?php echo e($specificIndex); ?>">
                                                                <label class="form-check-label" for="nfr_<?php echo e($generalNFR); ?>_<?php echo e($specificIndex); ?>">
                                                                    <?php echo e($specificNFR); ?>

                                                                </label>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php else: ?>
                                                        <p class="text-muted">No specific requirements available.</p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                            <?php endif; ?>

                            <div class="mt-4 d-flex justify-content-between">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i> Create User Story
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function updateUserStory() {
            var role = document.getElementById("role").value;
            var means = document.getElementById("means").value;
            var ends = document.getElementById("ends").value;
            var userStory = "As a " + role + ", I am able to " + means;
            if (ends.trim() !== "") {
                userStory += " so that I can " + ends;
            }
            document.getElementById("user_story").value = userStory;
        }

        // Function to notify parent iframe about height changes (for iframe compatibility)
        function notifyParentAboutHeight() {
            const height = Math.max(
                document.body.scrollHeight,
                document.documentElement.scrollHeight,
                document.body.offsetHeight,
                document.documentElement.offsetHeight,
                document.body.clientHeight,
                document.documentElement.clientHeight
            );
            
            // Add small buffer to avoid scrollbars
            const heightWithBuffer = height + 30;
            
            // Send message to parent
            if (window.parent !== window) {
                window.parent.postMessage({
                    type: 'resize',
                    height: heightWithBuffer,
                    iframeId: 'userstory-iframe'
                }, '*');
            }
        }
        
        // Set up event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Initial height notification
            notifyParentAboutHeight();
            
            // Set up mutation observer to detect content changes
            const observer = new MutationObserver(function() {
                notifyParentAboutHeight();
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                characterData: true
            });
            
            // Additional triggers for height recalculation
            window.addEventListener('load', notifyParentAboutHeight);
            window.addEventListener('resize', notifyParentAboutHeight);
        });
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/userstory/create.blade.php ENDPATH**/ ?>