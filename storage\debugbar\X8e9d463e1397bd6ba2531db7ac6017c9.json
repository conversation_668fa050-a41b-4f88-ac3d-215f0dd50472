{"__meta": {"id": "X8e9d463e1397bd6ba2531db7ac6017c9", "datetime": "2025-08-19 14:20:56", "utime": 1755584456.851511, "method": "GET", "uri": "/role/45", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 15, "messages": [{"message": "[14:20:56] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584456.672779, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:56] LOG.debug: Gate check for permission: add_roles on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584456.827725, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:56] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584456.828712, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:56] LOG.debug: Gate check for permission: edit_roles on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584456.833461, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:56] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584456.834287, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:56] LOG.debug: Gate check for permission: delete_roles on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584456.834619, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:56] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584456.835375, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:56] LOG.debug: Gate check for permission: edit_roles on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584456.83562, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:56] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584456.836348, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:56] LOG.debug: Gate check for permission: delete_roles on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584456.836598, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:56] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584456.83732, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:56] LOG.debug: Gate check for permission: updateUserRole_roles on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584456.837558, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:56] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584456.838423, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:56] LOG.debug: Gate check for permission: updateUserRole_roles on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584456.838695, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:56] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584456.839441, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584456.319841, "end": 1755584456.851542, "duration": 0.5317010879516602, "duration_str": "532ms", "measures": [{"label": "Booting", "start": 1755584456.319841, "relative_start": 0, "end": 1755584456.647509, "relative_end": 1755584456.647509, "duration": 0.3276681900024414, "duration_str": "328ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584456.647529, "relative_start": 0.32768797874450684, "end": 1755584456.851544, "relative_end": 1.9073486328125e-06, "duration": 0.20401501655578613, "duration_str": "204ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23943920, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "role.index (\\resources\\views\\role\\index.blade.php)", "param_count": 4, "params": ["roles", "pro", "teamMembers", "title"], "type": "blade"}]}, "route": {"uri": "GET role/{project_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\RoleController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "role.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\RoleController.php&line=20\">\\app\\Http\\Controllers\\RoleController.php:20-51</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00269, "accumulated_duration_str": "2.69ms", "statements": [{"sql": "select * from `users` where `id` = 31 limit 1", "type": "query", "params": [], "bindings": ["31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 20.818}, {"sql": "select `team_name` from `teammappings` where `username` = 'tay'", "type": "query", "params": [], "bindings": ["tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\RoleController.php", "line": 27}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\app\\Http\\Controllers\\RoleController.php:27", "connection": "sagile", "start_percent": 20.818, "width_percent": 22.305}, {"sql": "select * from `projects` where `team_name` in ('Team 888', 'Team 888', 'Team AD', 'Team AD')", "type": "query", "params": [], "bindings": ["Team 888", "Team 888", "Team AD", "Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\RoleController.php", "line": 28}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Http\\Controllers\\RoleController.php:28", "connection": "sagile", "start_percent": 43.123, "width_percent": 20.074}, {"sql": "select * from `roles` where `project_id` = '45'", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\RoleController.php", "line": 31}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Http\\Controllers\\RoleController.php:31", "connection": "sagile", "start_percent": 63.197, "width_percent": 19.331}, {"sql": "select * from `teammappings` where `team_name` = 'Team AD' and `project_id` = 45 and `invitation_status` = 'accepted'", "type": "query", "params": [], "bindings": ["Team AD", "45", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\RoleController.php", "line": 42}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\RoleController.php:42", "connection": "sagile", "start_percent": 82.528, "width_percent": 17.472}]}, "models": {"data": {"App\\TeamMapping": 2, "App\\Role": 2, "App\\Project": 1, "App\\User": 1}, "count": 6}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 7, "messages": [{"message": "[\n  ability => add_roles,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">add_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584456.831967, "xdebug_link": null}, {"message": "[\n  ability => edit_roles,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-872635889 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">edit_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-872635889\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584456.834405, "xdebug_link": null}, {"message": "[\n  ability => delete_roles,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-942506358 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-942506358\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584456.835471, "xdebug_link": null}, {"message": "[\n  ability => edit_roles,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1805429439 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">edit_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1805429439\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584456.836439, "xdebug_link": null}, {"message": "[\n  ability => delete_roles,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-567206405 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">delete_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-567206405\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584456.837409, "xdebug_link": null}, {"message": "[\n  ability => updateUserRole_roles,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-720567525 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">updateUserRole_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-720567525\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584456.838513, "xdebug_link": null}, {"message": "[\n  ability => updateUserRole_roles,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-976729502 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">updateUserRole_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-976729502\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584456.839528, "xdebug_link": null}]}, "session": {"_token": "p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/role/45\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "31", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755584266\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/role/45", "status_code": "<pre class=sf-dump id=sf-dump-598057968 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-598057968\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-140257651 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-140257651\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1562660725 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1562660725\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1643314135 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkpFcXlPQnpaVk5XRWFVYnF4VzJUSFE9PSIsInZhbHVlIjoiMWVvSldUZzdlVW54dXNnRTY5aE9kaVlWZWd6SktwT3JIekorNGwwTk9FTlRtTGk3TUFqRXdHNmlRTDRaQkxuUzdJMEg5ZGNpR2VZNWZRSEdsekJpemRtTDJKcVJhUHJtUDVWV2NQNHJEYXRHLzBZMno3YXk4ZUVML0w2MTNVVEwiLCJtYWMiOiI0ODQ4OTM0M2Y1OGQzYjFlNmQyZTlhNTQ0M2IxOTVmNmQyOGIxZmFlNDdkY2E0ZmMzMTIyY2I4YzdiZTMwMWIwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkZDaXp3TEptT3BMOGFJQWFMZmRwWmc9PSIsInZhbHVlIjoiYStFeXRMZ3daRS9ndkhMN1VCS0V2SkluaytsR0lMZitaZlp3SFkvZ3FCMnBxbUtZL0lrQ1pyaEQ2ZkEzNXBVb2IyYS9oWEpsWHozcFJseVR4R3duRE0rQmJhcWhyNGY1Q1VWTzJpTUtoMXl5NWF5Ym5ycHE2VWk0QzJ4bUFPUlciLCJtYWMiOiJiNDE3ZjU2NWViZWY2MGJhNmJhZjgwMTU5MDU2MDZmNDRiNTE2MWIzZGY4YWMxMjQxZjNkOTI2YzY1Nzc0ZjJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1643314135\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-592784129 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51595</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"8 characters\">/role/45</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"8 characters\">/role/45</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"18 characters\">/index.php/role/45</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/45</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkpFcXlPQnpaVk5XRWFVYnF4VzJUSFE9PSIsInZhbHVlIjoiMWVvSldUZzdlVW54dXNnRTY5aE9kaVlWZWd6SktwT3JIekorNGwwTk9FTlRtTGk3TUFqRXdHNmlRTDRaQkxuUzdJMEg5ZGNpR2VZNWZRSEdsekJpemRtTDJKcVJhUHJtUDVWV2NQNHJEYXRHLzBZMno3YXk4ZUVML0w2MTNVVEwiLCJtYWMiOiI0ODQ4OTM0M2Y1OGQzYjFlNmQyZTlhNTQ0M2IxOTVmNmQyOGIxZmFlNDdkY2E0ZmMzMTIyY2I4YzdiZTMwMWIwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkZDaXp3TEptT3BMOGFJQWFMZmRwWmc9PSIsInZhbHVlIjoiYStFeXRMZ3daRS9ndkhMN1VCS0V2SkluaytsR0lMZitaZlp3SFkvZ3FCMnBxbUtZL0lrQ1pyaEQ2ZkEzNXBVb2IyYS9oWEpsWHozcFJseVR4R3duRE0rQmJhcWhyNGY1Q1VWTzJpTUtoMXl5NWF5Ym5ycHE2VWk0QzJ4bUFPUlciLCJtYWMiOiJiNDE3ZjU2NWViZWY2MGJhNmJhZjgwMTU5MDU2MDZmNDRiNTE2MWIzZGY4YWMxMjQxZjNkOTI2YzY1Nzc0ZjJmIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584456.3198</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584456</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-592784129\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-285794239 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CfapZDSBofQwHxJsJhdmHULLiISKd6PUgVWjQqhr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-285794239\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1788029563 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:20:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlRZR2REQ3VmcU1xWFlKUXd5eVp6Rmc9PSIsInZhbHVlIjoiMXovVUhLTDFWR1FjS0MzL28rZ1VqZzM0WDFzWWhKZG5uY2hzMXUrZHdPK2dXUGdFSjV3YmozNTFpTHNtR2YvSTJIUXZyeUhmK1NzbG5rczNLQ2MyUjJoUkRxa00vZzEwVzR6VFhQQ3RFYU5CR2VFdXlxaTFWSWwxRERWa1VJTWMiLCJtYWMiOiJjNWJlMGUzZWFmNTM4OGJhZjZlNmEyODFlMjhhYjhhYzRkM2E5MjAyOWM2NTE5OTFiMzhjODQ0Y2RjM2U0YjgzIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:20:56 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkhrUWhZTDBwMTk1Sm40ck03dXg1cXc9PSIsInZhbHVlIjoicWNhdWFNYkoxd2dHaXcrWW1WendZZ3IwRXk3NTFKd0FieXNrdUpvYndRSTRSTjVVRlRaNFhFZjBHRDNqY1IyRmpkblo0Y3dxU1g0OUlGM2s5WUgvUTlDbFljSjJkVTdGN3FCb1oxeUtmaFBZSUVKbDNsSVc2QW5jdEloYWVTR0IiLCJtYWMiOiI5Y2U5NDliZWFmOWYxOTRmNmVkMzc5MWUzZDcxYmQ5YjNkZDE1NWQxN2Y3NDQ5YThmOTU0OGRjMWE4NmVjY2U2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:20:56 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlRZR2REQ3VmcU1xWFlKUXd5eVp6Rmc9PSIsInZhbHVlIjoiMXovVUhLTDFWR1FjS0MzL28rZ1VqZzM0WDFzWWhKZG5uY2hzMXUrZHdPK2dXUGdFSjV3YmozNTFpTHNtR2YvSTJIUXZyeUhmK1NzbG5rczNLQ2MyUjJoUkRxa00vZzEwVzR6VFhQQ3RFYU5CR2VFdXlxaTFWSWwxRERWa1VJTWMiLCJtYWMiOiJjNWJlMGUzZWFmNTM4OGJhZjZlNmEyODFlMjhhYjhhYzRkM2E5MjAyOWM2NTE5OTFiMzhjODQ0Y2RjM2U0YjgzIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:20:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkhrUWhZTDBwMTk1Sm40ck03dXg1cXc9PSIsInZhbHVlIjoicWNhdWFNYkoxd2dHaXcrWW1WendZZ3IwRXk3NTFKd0FieXNrdUpvYndRSTRSTjVVRlRaNFhFZjBHRDNqY1IyRmpkblo0Y3dxU1g0OUlGM2s5WUgvUTlDbFljSjJkVTdGN3FCb1oxeUtmaFBZSUVKbDNsSVc2QW5jdEloYWVTR0IiLCJtYWMiOiI5Y2U5NDliZWFmOWYxOTRmNmVkMzc5MWUzZDcxYmQ5YjNkZDE1NWQxN2Y3NDQ5YThmOTU0OGRjMWE4NmVjY2U2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:20:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1788029563\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1307656516 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/role/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755584266</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1307656516\", {\"maxDepth\":0})</script>\n"}}