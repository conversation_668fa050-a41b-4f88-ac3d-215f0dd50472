<?php
    // Find the project once to use in permission checks
    $project = App\Project::find($project_id);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Stories</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
            margin: 0;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .table {
            background-color: white;
            border-radius: 0.25rem;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .button-container {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        .success-alert {
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
            border-radius: 0.25rem;
        }
        .table-success {
            background-color: rgba(212, 237, 218, 0.5);
        }
        .table-danger {
            background-color: rgba(248, 215, 218, 0.5);
        }
        .status-form {
            display: flex;
            align-items: center;
        }
        .status-form select {
            flex-grow: 1;
            margin-right: 0.5rem;
        }
        .status-form button {
            flex-shrink: 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <?php if(session('success')): ?>
        <div class="success-alert">
            <?php echo e(session('success')); ?>

        </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">User Stories</h4>
                </div>

                <div class="table-responsive">
                    <table id="userstories" class="table table-bordered">
                        <thead>
                            <tr>
                                <th>User Story</th>
                                <th>Assigned To</th>
                                <th>Status</th>
                                <th>NFR Details</th>
                                <th>Task</th>
                                <th>Edit</th> 
                                <th>Delete</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $userstories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $userstory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <?php
                                    // Check if the user story has NFRs linked
                                    $hasNfr = isset($nfrData[$userstory->u_id]) && count($nfrData[$userstory->u_id]) > 0;
                                ?>
                                <tr class="<?php echo e($hasNfr ? 'table' : 'table'); ?>"> 
                                    <td><?php echo e($userstory->user_story); ?></td>
                                    <td>
                                        <?php
                                            // Decode user_names and sanitize
                                            $userNames = json_decode(trim($userstory->user_names, '"'), true);
                                        ?>

                                        <?php if(is_array($userNames)): ?>
                                            <?php $__currentLoopData = $userNames; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user_show): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                - <?php echo e($user_show); ?><br>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php else: ?>
                                            <p>-</p>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('editStatus_userstory', $project)): ?>
                                        <select name="status_id" class="form-select form-select-sm status-select" data-userstory-id="<?php echo e($userstory->u_id); ?>">
                                            <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($status->id); ?>" <?php echo e($userstory->status_id == $status->id ? 'selected' : ''); ?>>
                                                    <?php echo e($status->title); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <?php else: ?>
                                        <span><?php echo e($statuses->where('id', $userstory->status_id)->first()->title ?? 'Unknown'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($hasNfr): ?>
                                            <a href="<?php echo e(route('userstory.viewDetails', [$userstory->u_id])); ?>" class="btn btn-secondary">View Details</a>
                                        <?php else: ?>
                                            <p>No NFRs assigned</p>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_task', $project)): ?>
                                        <a href="<?php echo e(action('TaskController@index', $userstory['u_id'])); ?>" class="btn btn-primary">View</a>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit_userstory', $project)): ?>
                                        <a href="<?php echo e(route('userstory.edit', [$userstory->u_id])); ?>" class="btn btn-secondary">Edit</a>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete_userstory', $project)): ?>
                                        <a href="<?php echo e(route('userstory.destroy', $userstory)); ?>" class="btn btn-danger" 
                                           onclick="return confirm('Are you sure you want to delete this User Story?');">
                                            Delete
                                        </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7">No user stories added yet</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                <div class="button-container mt-4">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('add_userstory', $project)): ?>
                    <a href="<?php echo e(route('userstory.create', ['proj_id' => $project_id])); ?>" class="btn btn-success">Create User Story</a>
                    <?php endif; ?>
                </div>
                
                <div class="button-container mt-4">
                    <a href="<?php echo e(route('ucd.index', ['project_id' => $project_id])); ?>" class="btn btn-success">UCD</a>
                </div>

            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css"></script>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
          // Only run if user has permission to edit status (the select elements will exist)
          if (document.querySelectorAll('.status-select').length > 0) {
              // Select all status dropdowns
              const statusSelects = document.querySelectorAll('.status-select');
              
              // Add change event listener to each dropdown
              statusSelects.forEach(select => {
                  select.addEventListener('change', function() {
                  const userstoryId = this.getAttribute('data-userstory-id');
                  const statusId = this.value;
                  
                  // Create form data
                  const formData = new FormData();
                  formData.append('status_id', statusId);
                  formData.append('_token', '<?php echo e(csrf_token()); ?>');
                  formData.append('_method', 'PATCH');
                  
                  // Send AJAX request
                  fetch(`<?php echo e(url('userstory')); ?>/${userstoryId}/updateStatus`, {
                      method: 'POST',
                      body: formData
                  })
                  .then(response => {
                      if (!response.ok) {
                          throw new Error('Network response was not ok');
                      }
                      return response.json();
                  })
                  .then(data => {
                      // Show a temporary success message if needed
                      if (data.success) {
                          // Optional: Show a temporary success message
                          const messageContainer = document.createElement('div');
                          messageContainer.className = 'alert alert-success position-fixed top-0 end-0 m-3';
                          messageContainer.textContent = data.message;
                          document.body.appendChild(messageContainer);
                          
                          // Remove the message after 3 seconds
                          setTimeout(() => {
                              messageContainer.remove();
                          }, 3000);
                      }
                  })
                  .catch(error => {
                      console.error('Error updating status:', error);
                  });
              })  
            });
          }
      });
    </script>
</body>
</html><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/userstory/index.blade.php ENDPATH**/ ?>