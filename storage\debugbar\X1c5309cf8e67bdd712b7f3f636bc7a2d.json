{"__meta": {"id": "X1c5309cf8e67bdd712b7f3f636bc7a2d", "datetime": "2025-08-19 10:44:50", "utime": 1755571490.796214, "method": "GET", "uri": "/cig/create/42", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[10:44:50] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755571490.156978, "xdebug_link": null, "collector": "log"}, {"message": "[10:44:50] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/cig/create/42", "message_html": null, "is_string": false, "label": "debug", "time": 1755571490.258623, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755571489.725896, "end": 1755571490.796242, "duration": 1.0703461170196533, "duration_str": "1.07s", "measures": [{"label": "Booting", "start": 1755571489.725896, "relative_start": 0, "end": 1755571490.130905, "relative_end": 1755571490.130905, "duration": 0.40500903129577637, "duration_str": "405ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755571490.130919, "relative_start": 0.40502309799194336, "end": 1755571490.796244, "relative_end": 1.9073486328125e-06, "duration": 0.6653249263763428, "duration_str": "665ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25737272, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 18, "templates": [{"name": "cig.create (\\resources\\views\\cig\\create.blade.php)", "param_count": 7, "params": ["title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\resources\\views\\vendor\\pagination\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 26, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 26, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 26, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "userStory", "general<PERSON>fr", "nfrIndex", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET cig/create/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@create", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "cig.create", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=81\">\\app\\Http\\Controllers\\CIGController.php:81-108</a>"}, "queries": {"nb_statements": 17, "nb_failed_statements": 0, "accumulated_duration": 0.01042, "accumulated_duration_str": "10.42ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 5.566}, {"sql": "select * from `projects` where `projects`.`id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Project.php", "line": 86}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\app\\Project.php:86", "connection": "sagile", "start_percent": 5.566, "width_percent": 6.238}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System'", "type": "query", "params": [], "bindings": ["Food Ordering System"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Sprint.php", "line": 62}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 85}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\app\\Sprint.php:62", "connection": "sagile", "start_percent": 11.804, "width_percent": 6.238}, {"sql": "select * from `user_stories` where `proj_id` = '42'", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 36}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 91}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00061, "duration_str": "610μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:36", "connection": "sagile", "start_percent": 18.042, "width_percent": 5.854}, {"sql": "select * from `user_story_general_nfr` where `user_story_general_nfr`.`user_story_id` in (45, 46, 47, 48)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 36}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 91}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:36", "connection": "sagile", "start_percent": 23.896, "width_percent": 6.814}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 91}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:36", "connection": "sagile", "start_percent": 30.71, "width_percent": 7.678}, {"sql": "select count(*) as aggregate from `user_stories` where `proj_id` = '42' and exists (select * from `user_story_general_nfr` where `user_stories`.`u_id` = `user_story_general_nfr`.`user_story_id`)", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\UserStory.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 94}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\app\\UserStory.php:57", "connection": "sagile", "start_percent": 38.388, "width_percent": 6.046}, {"sql": "select `u_id`, `user_story`, `sprint_id` from `user_stories` where `proj_id` = '42' and exists (select * from `user_story_general_nfr` where `user_stories`.`u_id` = `user_story_general_nfr`.`user_story_id`) order by `u_id` asc limit 5 offset 0", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\UserStory.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 94}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\app\\UserStory.php:57", "connection": "sagile", "start_percent": 44.434, "width_percent": 6.526}, {"sql": "select * from `user_story_general_nfr` where `user_story_general_nfr`.`user_story_id` = 45 and `user_story_general_nfr`.`user_story_id` is not null", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 50.96, "width_percent": 5.47}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 56.43, "width_percent": 5.566}, {"sql": "select * from `nfr` where `nfr`.`nfr_id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 61.996, "width_percent": 6.334}, {"sql": "select * from `user_story_general_nfr` where `user_story_general_nfr`.`user_story_id` = 47 and `user_story_general_nfr`.`user_story_id` is not null", "type": "query", "params": [], "bindings": ["47"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 68.33, "width_percent": 6.718}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 75.048, "width_percent": 4.319}, {"sql": "select * from `nfr` where `nfr`.`nfr_id` in (2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 79.367, "width_percent": 5.758}, {"sql": "select * from `user_story_general_nfr` where `user_story_general_nfr`.`user_story_id` = 48 and `user_story_general_nfr`.`user_story_id` is not null", "type": "query", "params": [], "bindings": ["48"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 85.125, "width_percent": 5.47}, {"sql": "select * from `generalnfr` where `generalnfr`.`general_nfr_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 90.595, "width_percent": 4.798}, {"sql": "select * from `nfr` where `nfr`.`nfr_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 112}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php", "line": 770}, {"index": 26, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 111}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 97}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:112", "connection": "sagile", "start_percent": 95.393, "width_percent": 4.607}]}, "models": {"data": {"App\\SpecificNFR": 6, "App\\GeneralNFR": 6, "App\\UserStoryGeneralNfr": 12, "App\\UserStory": 7, "App\\Sprint": 2, "App\\Project": 1, "App\\User": 1}, "count": 35}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/cig/create/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/cig/create/42", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1023784833 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1023784833\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-652647818 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-652647818\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-803084205 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://127.0.0.1:8000/cig</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImtZUmdseFpDK0FqQ3dyV1pXYzgySUE9PSIsInZhbHVlIjoiTTJoVEs2blZEV0g5Zy9yZ3NZOVFicTRxT2lxMDFRYmhqdnBFV004SHlLdksySjVFMGw3Wkt0YW0xd0xYNi9ySFg2NVQ4QzIzSWlYeEdaSTdUVFFjUHZQZXVzSUpTMU1xUGozVDl2bzJkSG4rYVJQS3JkdHFpSENoODkyZEc4aGgiLCJtYWMiOiI3MDI1ODRhYjEwNTk3NzViZWY5M2I0NWE3NmFhOTQ1OTNhZDk1YzQ5Mjc5YjFiZDlmODk5OWYzZTI5MzBmNDVmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlZ1clg0UEJXWHpMdGFneG9jRXRTMFE9PSIsInZhbHVlIjoiVWJ6QjNmL0lGVnlqdDJnWTN2cDk0NktqMUppSXNxa2Roenp4MGJGS3pOV1l1QzB1NTZLdmNoaXBiRDJhZWF6QnUzSndkVVA2NDRnS2g3MUpRcnJNUTRheEI4Y3dnVG94bEZuQTNnaXdBNFFobzNJeUkwVnFyVXRhRDZGQ2pvbUUiLCJtYWMiOiI1Y2M4ZGI1MTVkNDgxNDBlZGU5YmY3ODg2MzViZmQzZmZjMzBkMDEwZmMxZjgzZWFiYTc4ZDkxNzQ3YTJiM2MyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-803084205\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-574697067 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51905</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"14 characters\">/cig/create/42</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"14 characters\">/cig/create/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/index.php/cig/create/42</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://127.0.0.1:8000/cig</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImtZUmdseFpDK0FqQ3dyV1pXYzgySUE9PSIsInZhbHVlIjoiTTJoVEs2blZEV0g5Zy9yZ3NZOVFicTRxT2lxMDFRYmhqdnBFV004SHlLdksySjVFMGw3Wkt0YW0xd0xYNi9ySFg2NVQ4QzIzSWlYeEdaSTdUVFFjUHZQZXVzSUpTMU1xUGozVDl2bzJkSG4rYVJQS3JkdHFpSENoODkyZEc4aGgiLCJtYWMiOiI3MDI1ODRhYjEwNTk3NzViZWY5M2I0NWE3NmFhOTQ1OTNhZDk1YzQ5Mjc5YjFiZDlmODk5OWYzZTI5MzBmNDVmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlZ1clg0UEJXWHpMdGFneG9jRXRTMFE9PSIsInZhbHVlIjoiVWJ6QjNmL0lGVnlqdDJnWTN2cDk0NktqMUppSXNxa2Roenp4MGJGS3pOV1l1QzB1NTZLdmNoaXBiRDJhZWF6QnUzSndkVVA2NDRnS2g3MUpRcnJNUTRheEI4Y3dnVG94bEZuQTNnaXdBNFFobzNJeUkwVnFyVXRhRDZGQ2pvbUUiLCJtYWMiOiI1Y2M4ZGI1MTVkNDgxNDBlZGU5YmY3ODg2MzViZmQzZmZjMzBkMDEwZmMxZjgzZWFiYTc4ZDkxNzQ3YTJiM2MyIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755571489.7259</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755571489</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574697067\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-842229644 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-842229644\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1625108503 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:44:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IncrRlplRnc1Q1EvdFE0VGtSam1KeXc9PSIsInZhbHVlIjoiVE5YUG4xWWQ5YUFDL0RHeUZHQUk2cHNjQWE4eTNWRmROMDlRY0lqUWpJdCtERis1UTBMc1NKeVRIUXppR0Jqei9UQVE3M1gvemFRT2dBVU9DZmt6U2tlblBxUGI5S1d4VDJxTGI3TGFXMThkaXVBQzg5djhSMzVTWDAvS0ZVa0ciLCJtYWMiOiIwNTFlYzY1NDAwNzA1NWQ2YTAzMmM1ZWZlNWIyNDMxMjcxM2VkYWZjNDdlNmQ2ZWU1Y2IyYjgyMTUyODYxNGQ3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:44:50 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlRNaGRyZS93aFJxaHpwZkg0N3pWMmc9PSIsInZhbHVlIjoiZStwQkR3a1RVaitDMS8vUi9sUVplQmlJbjhqZ0JHVXVUMjltUkV6MWU3WU5OcXRyQzZ2amZaU0Zlc01uQXFOdjlMdnVXOXV4Qnp5YVZjMTU5UVZ6OEdiNDcxci96QWczVTlxcHh4V2FUMEdUNjdNNkdabDVrQTBjeVlBVmRnM0EiLCJtYWMiOiJmZDM5ZGEzZTYwYTA2NzAxNTkzMDJjNTUyNWE3NGQxOTUzYmFmOTk1OWUwZjQ4N2RmY2Y1Y2ZlZmY1ZDVkODJkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:44:50 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IncrRlplRnc1Q1EvdFE0VGtSam1KeXc9PSIsInZhbHVlIjoiVE5YUG4xWWQ5YUFDL0RHeUZHQUk2cHNjQWE4eTNWRmROMDlRY0lqUWpJdCtERis1UTBMc1NKeVRIUXppR0Jqei9UQVE3M1gvemFRT2dBVU9DZmt6U2tlblBxUGI5S1d4VDJxTGI3TGFXMThkaXVBQzg5djhSMzVTWDAvS0ZVa0ciLCJtYWMiOiIwNTFlYzY1NDAwNzA1NWQ2YTAzMmM1ZWZlNWIyNDMxMjcxM2VkYWZjNDdlNmQ2ZWU1Y2IyYjgyMTUyODYxNGQ3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:44:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlRNaGRyZS93aFJxaHpwZkg0N3pWMmc9PSIsInZhbHVlIjoiZStwQkR3a1RVaitDMS8vUi9sUVplQmlJbjhqZ0JHVXVUMjltUkV6MWU3WU5OcXRyQzZ2amZaU0Zlc01uQXFOdjlMdnVXOXV4Qnp5YVZjMTU5UVZ6OEdiNDcxci96QWczVTlxcHh4V2FUMEdUNjdNNkdabDVrQTBjeVlBVmRnM0EiLCJtYWMiOiJmZDM5ZGEzZTYwYTA2NzAxNTkzMDJjNTUyNWE3NGQxOTUzYmFmOTk1OWUwZjQ4N2RmY2Y1Y2ZlZmY1ZDVkODJkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:44:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1625108503\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-946348423 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-946348423\", {\"maxDepth\":0})</script>\n"}}