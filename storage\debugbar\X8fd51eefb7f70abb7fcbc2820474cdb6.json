{"__meta": {"id": "X8fd51eefb7f70abb7fcbc2820474cdb6", "datetime": "2025-08-19 13:17:34", "utime": 1755580654.520482, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[13:17:34] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755580654.281157, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755580654.00546, "end": 1755580654.520506, "duration": 0.5150458812713623, "duration_str": "515ms", "measures": [{"label": "Booting", "start": 1755580654.00546, "relative_start": 0, "end": 1755580654.263203, "relative_end": 1755580654.263203, "duration": 0.25774288177490234, "duration_str": "258ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755580654.263213, "relative_start": 0.2577528953552246, "end": 1755580654.520509, "relative_end": 3.0994415283203125e-06, "duration": 0.257296085357666, "duration_str": "257ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23311888, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "auth.login (\\resources\\views\\auth\\login.blade.php)", "param_count": 0, "params": [], "type": "blade"}, {"name": "inc.page-auth (\\resources\\views\\inc\\page-auth.blade.php)", "param_count": 4, "params": ["__env", "app", "menuData", "errors"], "type": "blade"}, {"name": "layouts.blankLayout (\\resources\\views\\layouts\\blankLayout.blade.php)", "param_count": 4, "params": ["__env", "app", "menuData", "errors"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 4, "params": ["__env", "app", "menuData", "errors"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 5, "params": ["__env", "app", "menuData", "errors", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 5, "params": ["__env", "app", "menuData", "errors", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 5, "params": ["__env", "app", "menuData", "errors", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET login", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php&line=19\">\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php:19-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "BkrRPIr4Izbs1esgnOnLniu1ROlR55WioisLwo42", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1917799827 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1917799827\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-973506754 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-973506754\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1370993446 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1370993446\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-439561613 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlEyOGlaOFdoNXMydityVTJMVjF5dUE9PSIsInZhbHVlIjoiNWQ5a2c4UUkxRWpEdHRQN0g3a0pZQUIvcGZrbml2bEpzZ2JkY0haK2plTHVwaEZqUStOOUZvMnk5cFNXQ25hai9RNmd4L0F0SUd6ZXRiMWJrYk1ibmJLeE1pVWd4R3RtTWNXLzVmUW9XMGJ4T3JDaHlYZHhRTERNTE9RQ213WUsiLCJtYWMiOiJhN2QxMDAyMTg2Nzg3ZjgwYTQwOGYzOTUzNzYxNzRhYjMzYTZlMmIzM2Q1ZDMwZGEzY2U3MjRhNzVjMzBkZjkxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImdYMnhvZWlpTFFHTDladGdaM2lYSnc9PSIsInZhbHVlIjoidTFMKzd2Mld0NXpYZExXaFlCdUdQcnp4b3FBZ004c3FBYkJUUzhtbXhiaG9JeVRvTkQ3cGdEVjdFRTVPL3k1QmFoUlAxcnVnTTZMNjY0RnJYaTVJOEFuT1hKUVJnem0xWlI4WUNtQktFY1U2ZEZ4Kzl1R09aODY0dVpjOCsvZXAiLCJtYWMiOiJkNDJhNWViZTYxMzJmZGQxMDI3OWMxZjEyZGM1Y2JkNDEzZjM1MGUxZTUwYjNmNTQxN2NkNDE5ZTEzMTRjYjQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-439561613\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1768114909 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51124</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/login</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlEyOGlaOFdoNXMydityVTJMVjF5dUE9PSIsInZhbHVlIjoiNWQ5a2c4UUkxRWpEdHRQN0g3a0pZQUIvcGZrbml2bEpzZ2JkY0haK2plTHVwaEZqUStOOUZvMnk5cFNXQ25hai9RNmd4L0F0SUd6ZXRiMWJrYk1ibmJLeE1pVWd4R3RtTWNXLzVmUW9XMGJ4T3JDaHlYZHhRTERNTE9RQ213WUsiLCJtYWMiOiJhN2QxMDAyMTg2Nzg3ZjgwYTQwOGYzOTUzNzYxNzRhYjMzYTZlMmIzM2Q1ZDMwZGEzY2U3MjRhNzVjMzBkZjkxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImdYMnhvZWlpTFFHTDladGdaM2lYSnc9PSIsInZhbHVlIjoidTFMKzd2Mld0NXpYZExXaFlCdUdQcnp4b3FBZ004c3FBYkJUUzhtbXhiaG9JeVRvTkQ3cGdEVjdFRTVPL3k1QmFoUlAxcnVnTTZMNjY0RnJYaTVJOEFuT1hKUVJnem0xWlI4WUNtQktFY1U2ZEZ4Kzl1R09aODY0dVpjOCsvZXAiLCJtYWMiOiJkNDJhNWViZTYxMzJmZGQxMDI3OWMxZjEyZGM1Y2JkNDEzZjM1MGUxZTUwYjNmNTQxN2NkNDE5ZTEzMTRjYjQ0IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755580654.0055</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755580654</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1768114909\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1712705645 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BkrRPIr4Izbs1esgnOnLniu1ROlR55WioisLwo42</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2c53gnhjoEvbgROlM1txupNWlHxc45QrlVzMCSWR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1712705645\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-804760110 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:17:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IktSM0NDSGQ5RmNYNzJKQ2NjRHExeGc9PSIsInZhbHVlIjoia2UzSnFaRGF0WXNCNlZRU0N2ckR5dGZBYnVNU0JENlhIUHRpRWZDYVZzUkc5NGJnZG9STHQyMkllUFo2aHZhc3NDaGVEWDJLTDluQVVqcTFORGVNRWpUUUg1K0VjNnJTY2htMXFDdGNsSk51Wm5jN2ZMZHZHTG5wajlaT0ZCR1ciLCJtYWMiOiJkMzA5MmU5MTcxYmZkYzQ1MjFhZGE5Njc4Y2IwYWMyNjYyY2FkYjE3Y2Q3MmE4ODk2ZDhmMDA3OWVkYzE1ZWM1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:17:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjhVUExzdmtlaUVhSUhKeHhuMUdkaGc9PSIsInZhbHVlIjoiS0xNTWsyWUNOKzVzWlVCZk85dEp5ckVzSFNlcHNLRVh4eVpvWHVuU2xsZElZOUxJUk91eDh4aFM5andCOGV0K3JSVHJjVGU5ckpGd1kvemlFREV6WjQ1U2lzbm9rNjhWTUZYT29pTVVva2xyWXFCMGdTcXI0N0YxTmxReEs4cTkiLCJtYWMiOiIxNzZjYzRlYmIyYTNlMWMwZDI2MzRkMDg2MGE0Nzk5M2Q4YWViNzZkNjliMzYwZmQzYTE3YTgzOTgxMjQ0ZmZiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:17:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IktSM0NDSGQ5RmNYNzJKQ2NjRHExeGc9PSIsInZhbHVlIjoia2UzSnFaRGF0WXNCNlZRU0N2ckR5dGZBYnVNU0JENlhIUHRpRWZDYVZzUkc5NGJnZG9STHQyMkllUFo2aHZhc3NDaGVEWDJLTDluQVVqcTFORGVNRWpUUUg1K0VjNnJTY2htMXFDdGNsSk51Wm5jN2ZMZHZHTG5wajlaT0ZCR1ciLCJtYWMiOiJkMzA5MmU5MTcxYmZkYzQ1MjFhZGE5Njc4Y2IwYWMyNjYyY2FkYjE3Y2Q3MmE4ODk2ZDhmMDA3OWVkYzE1ZWM1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:17:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjhVUExzdmtlaUVhSUhKeHhuMUdkaGc9PSIsInZhbHVlIjoiS0xNTWsyWUNOKzVzWlVCZk85dEp5ckVzSFNlcHNLRVh4eVpvWHVuU2xsZElZOUxJUk91eDh4aFM5andCOGV0K3JSVHJjVGU5ckpGd1kvemlFREV6WjQ1U2lzbm9rNjhWTUZYT29pTVVva2xyWXFCMGdTcXI0N0YxTmxReEs4cTkiLCJtYWMiOiIxNzZjYzRlYmIyYTNlMWMwZDI2MzRkMDg2MGE0Nzk5M2Q4YWViNzZkNjliMzYwZmQzYTE3YTgzOTgxMjQ0ZmZiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:17:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-804760110\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1383563123 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BkrRPIr4Izbs1esgnOnLniu1ROlR55WioisLwo42</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1383563123\", {\"maxDepth\":0})</script>\n"}}