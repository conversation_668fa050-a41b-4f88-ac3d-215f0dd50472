{"__meta": {"id": "X897321997314298a08920373acc642ae", "datetime": "2025-08-19 14:10:45", "utime": 1755583845.886245, "method": "GET", "uri": "/project-assignments/45", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 7, "messages": [{"message": "[14:10:45] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755583845.574425, "xdebug_link": null, "collector": "log"}, {"message": "[14:10:45] LOG.debug: LoadUserPermissions: Extracted project ID: 45 from URL: http://127.0.0.1:8000/project-assignments/45", "message_html": null, "is_string": false, "label": "debug", "time": 1755583845.673015, "xdebug_link": null, "collector": "log"}, {"message": "[14:10:45] LOG.debug: LoadUserPermissions: Found project 45 with team Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583845.685318, "xdebug_link": null, "collector": "log"}, {"message": "[14:10:45] LOG.debug: LoadUserPermissions: Looking for team role map for team: Team AD", "message_html": null, "is_string": false, "label": "debug", "time": 1755583845.685382, "xdebug_link": null, "collector": "log"}, {"message": "[14:10:45] LOG.debug: LoadUserPermissions: Available teams in role map: [\"Team AD\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583845.685429, "xdebug_link": null, "collector": "log"}, {"message": "[14:10:45] LOG.debug: LoadUserPermissions: Found team data structure: multiple_projects", "message_html": null, "is_string": false, "label": "debug", "time": 1755583845.685469, "xdebug_link": null, "collector": "log"}, {"message": "[14:10:45] LOG.debug: LoadUserPermissions: Set permissions for user <PERSON><PERSON><PERSON> in team Team AD for project 45: [\"view_kanban\",\"view_burndown\",\"view_backlog\",\"view_userstory\",\"view_forum\",\"view_bugtracking\",\"view_status\",\"view_details\",\"view_roles\",\"addLane_kanban\",\"addTask_kanban\",\"editLane_kanban\",\"deleteLane_kanban\",\"deleteTask_kanban\",\"addComment_kanban\",\"addUserStory_backlog\",\"beginSprint_backlog\",\"addToSprint_backlog\",\"endSprint_backlog\",\"add_userstory\",\"edit_userstory\",\"delete_userstory\",\"editStatus_userstory\",\"add_task\",\"edit_task\",\"delete_task\",\"viewCalendar_task\",\"viewComments_task\",\"add_roles\",\"edit_roles\",\"delete_roles\",\"add_status\",\"edit_status\",\"delete_status\",\"edit_details\",\"delete_details\",\"view_sprintArchive\",\"viewKanbanArchive_sprintArchive\",\"viewBurndownArchive_sprintArchive\",\"updateTaskStatus_kanban\",\"view_task\",\"share_details\",\"editTask_kanban\",\"updateUserRole_roles\"]", "message_html": null, "is_string": false, "label": "debug", "time": 1755583845.685608, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755583845.099781, "end": 1755583845.88629, "duration": 0.7865090370178223, "duration_str": "787ms", "measures": [{"label": "Booting", "start": 1755583845.099781, "relative_start": 0, "end": 1755583845.543107, "relative_end": 1755583845.543107, "duration": 0.4433259963989258, "duration_str": "443ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755583845.543117, "relative_start": 0.44333600997924805, "end": 1755583845.886293, "relative_end": 2.86102294921875e-06, "duration": 0.34317588806152344, "duration_str": "343ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25513008, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 14, "templates": [{"name": "project-assignments.show (\\resources\\views\\project-assignments\\show.blade.php)", "param_count": 3, "params": ["project", "projectAssignments", "canManageAssignments"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "project", "projectAssignments", "canManageAssignments", "__currentLoopData", "assignment", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET project-assignments/{project}", "middleware": "web", "controller": "App\\Http\\Controllers\\ProjectAssignmentController@show", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "project-assignments.show", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProjectAssignmentController.php&line=146\">\\app\\Http\\Controllers\\ProjectAssignmentController.php:146-159</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.00362, "accumulated_duration_str": "3.62ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 14.088}, {"sql": "select `id`, `team_name` from `projects` where `id` = '45' limit 1", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 167}, {"index": 14, "namespace": "middleware", "name": "load.permissions", "line": 33}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 16, "namespace": "middleware", "name": "bindings", "line": 50}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "middleware::load.permissions:167", "connection": "sagile", "start_percent": 14.088, "width_percent": 11.05}, {"sql": "select * from `projects` where `projects`.`id` = '45' limit 1", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 148}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:148", "connection": "sagile", "start_percent": 25.138, "width_percent": 11.878}, {"sql": "select * from `teammappings` where `project_id` is not null and `project_id` = '45'", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 153}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:153", "connection": "sagile", "start_percent": 37.017, "width_percent": 9.116}, {"sql": "select * from `users` where `users`.`username` in ('ivlyn', 'tay')", "type": "query", "params": [], "bindings": ["ivlyn", "tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 153}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:153", "connection": "sagile", "start_percent": 46.133, "width_percent": 14.088}, {"sql": "select exists(select * from `teammappings` where `project_id` is null and `username` = 'ivlyn' and `team_name` = 'Team AD' and `role_name` = 'Team Manager' and `invitation_status` = 'accepted') as `exists`", "type": "query", "params": [], "bindings": ["ivlyn", "Team AD", "Team Manager", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 294}, {"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 156}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:294", "connection": "sagile", "start_percent": 60.221, "width_percent": 13.536}, {"sql": "select * from `roles` where `project_id` = 45", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Project.php", "line": 81}, {"index": 15, "namespace": "view", "name": "c87405fae7fd5db3ca9ba3400477180b915713eb", "line": 94}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Project.php:81", "connection": "sagile", "start_percent": 73.757, "width_percent": 12.983}, {"sql": "select * from `roles` where `project_id` = 45", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Project.php", "line": 81}, {"index": 15, "namespace": "view", "name": "c87405fae7fd5db3ca9ba3400477180b915713eb", "line": 94}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Project.php:81", "connection": "sagile", "start_percent": 86.74, "width_percent": 13.26}]}, "models": {"data": {"App\\Role": 4, "App\\TeamMapping": 2, "App\\Project": 1, "App\\User": 3}, "count": 10}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/project-assignments/45\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755581799\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/project-assignments/45", "status_code": "<pre class=sf-dump id=sf-dump-1181926544 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1181926544\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1711907721 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1711907721\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1612221456 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1612221456\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1646948314 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"62 characters\">http://127.0.0.1:8000/project-assignments/create?project_id=45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImFjdjJ5SU8vYk5RSFIyMmhhMk9XakE9PSIsInZhbHVlIjoiK2hqZzc2SWhHK1lNSFI5bjR6SUlEQWFXcDZEMC9UMzJMTXBQdzN4T0s4UFR4dnd4blJNc3IwU2YxSngwMTA5YzNzMkpxc3lhQnd5N3RuWm5DQWRrU2VVMWJocUpPbFBIL1Q3QnZNTnIwN05WQVczeUZQb2J0eE1xUUhPaU1maEoiLCJtYWMiOiJhNjY2NjVjMjA2ZTIxMzhlYTA0NWMwNjhmM2YzZmRjZGU5MjIzODczY2M5M2YxYmVlZDlkNTBmNWI5MTE1MGYxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InZiY0tVYW9hWGRMbmtEb1g4OTVNb3c9PSIsInZhbHVlIjoiVCtTRkNtM0p5WUtZT3hkS0VaeC95aTAvSGhsSGg5VHFDcnZzWnVPaGhpSFhVRWRoUGFGY1dqWFpLc05zUmZQL0dubEVBNS9mSXhZemZuWkFiQWVhTWVqSWlBanZVY0RSZlBIWE5hbWZmcEFGTUV1aEFZMUN5NytKbGh2UUxnYjgiLCJtYWMiOiIwYTRiZmFjODBiYzdiY2FmMzExMGVmYzY3YjBkMjM1Y2UzZTNjN2Y1M2JlOWJjNWI2NjFjMDIzMDRiN2E2ZDFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1646948314\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-799967578 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58760</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/project-assignments/45</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/project-assignments/45</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/index.php/project-assignments/45</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"62 characters\">http://127.0.0.1:8000/project-assignments/create?project_id=45</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImFjdjJ5SU8vYk5RSFIyMmhhMk9XakE9PSIsInZhbHVlIjoiK2hqZzc2SWhHK1lNSFI5bjR6SUlEQWFXcDZEMC9UMzJMTXBQdzN4T0s4UFR4dnd4blJNc3IwU2YxSngwMTA5YzNzMkpxc3lhQnd5N3RuWm5DQWRrU2VVMWJocUpPbFBIL1Q3QnZNTnIwN05WQVczeUZQb2J0eE1xUUhPaU1maEoiLCJtYWMiOiJhNjY2NjVjMjA2ZTIxMzhlYTA0NWMwNjhmM2YzZmRjZGU5MjIzODczY2M5M2YxYmVlZDlkNTBmNWI5MTE1MGYxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InZiY0tVYW9hWGRMbmtEb1g4OTVNb3c9PSIsInZhbHVlIjoiVCtTRkNtM0p5WUtZT3hkS0VaeC95aTAvSGhsSGg5VHFDcnZzWnVPaGhpSFhVRWRoUGFGY1dqWFpLc05zUmZQL0dubEVBNS9mSXhZemZuWkFiQWVhTWVqSWlBanZVY0RSZlBIWE5hbWZmcEFGTUV1aEFZMUN5NytKbGh2UUxnYjgiLCJtYWMiOiIwYTRiZmFjODBiYzdiY2FmMzExMGVmYzY3YjBkMjM1Y2UzZTNjN2Y1M2JlOWJjNWI2NjFjMDIzMDRiN2E2ZDFlIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755583845.0998</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755583845</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-799967578\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-580905892 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-580905892\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1083342288 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:10:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Iml0YVdxNm8vbTh0R3Q4NG9iN2RnVWc9PSIsInZhbHVlIjoiVmxPT25RK1Vhdm9RMXp0eDBMc3VMNS9VRkZ0bkJ6VnRSRXk3clcyMlJrWUpBblJQVVczK3BLSVJOdDJTMWFFRklUZmlZcjJJM2JVZWRHNExGamdoZlRKUzU5dDBLSG9wc2g0OEZEbzczYUpXdUUvN2NIV202c0dNYjI3NDlvUDIiLCJtYWMiOiI0YjUzZjU1Y2YxZWU3ZTc4NzgzMTQyOGI4YTE3MDI2YTMxMTkxODY2YmZhMWQ5MjdiMTA2N2ExNDM4NzczYTRhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:10:45 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InlEa1U3SkNwUHZ1SzNqWmd1Q3gzUFE9PSIsInZhbHVlIjoiZmVzR2xReVM1OW1na2haNk8raDU0c1BUUjY4OURmbDZJem5mTzVhYndEMUZURFp1bFEvVHVQb0xrRytwb0FPYitCSVJucU9jd2dqb05XamYzN293Z3hhZ1hwRm1CWERPQXphODBrMkFqUWl3SXYrNjA3SStxbi9CRmkreDZZZ0giLCJtYWMiOiIyNDc5OWUyYWRhMjc4MjIwNDVmNDBlNWMzZDQ0MzFjNGIzNTdhM2JiZGEyNTk5Y2M0OWZhOTA4ODJmMTcyZjA3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:10:45 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Iml0YVdxNm8vbTh0R3Q4NG9iN2RnVWc9PSIsInZhbHVlIjoiVmxPT25RK1Vhdm9RMXp0eDBMc3VMNS9VRkZ0bkJ6VnRSRXk3clcyMlJrWUpBblJQVVczK3BLSVJOdDJTMWFFRklUZmlZcjJJM2JVZWRHNExGamdoZlRKUzU5dDBLSG9wc2g0OEZEbzczYUpXdUUvN2NIV202c0dNYjI3NDlvUDIiLCJtYWMiOiI0YjUzZjU1Y2YxZWU3ZTc4NzgzMTQyOGI4YTE3MDI2YTMxMTkxODY2YmZhMWQ5MjdiMTA2N2ExNDM4NzczYTRhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:10:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InlEa1U3SkNwUHZ1SzNqWmd1Q3gzUFE9PSIsInZhbHVlIjoiZmVzR2xReVM1OW1na2haNk8raDU0c1BUUjY4OURmbDZJem5mTzVhYndEMUZURFp1bFEvVHVQb0xrRytwb0FPYitCSVJucU9jd2dqb05XamYzN293Z3hhZ1hwRm1CWERPQXphODBrMkFqUWl3SXYrNjA3SStxbi9CRmkreDZZZ0giLCJtYWMiOiIyNDc5OWUyYWRhMjc4MjIwNDVmNDBlNWMzZDQ0MzFjNGIzNTdhM2JiZGEyNTk5Y2M0OWZhOTA4ODJmMTcyZjA3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:10:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083342288\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-199237914 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/project-assignments/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755581799</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-199237914\", {\"maxDepth\":0})</script>\n"}}