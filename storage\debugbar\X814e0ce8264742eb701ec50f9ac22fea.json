{"__meta": {"id": "X814e0ce8264742eb701ec50f9ac22fea", "datetime": "2025-08-19 13:58:53", "utime": 1755583133.6297, "method": "GET", "uri": "/backlogTest/43", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 39, "messages": [{"message": "[13:58:53] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755583133.453531, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/backlogTest/43", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.51276, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.info: BacklogController@index started {\"project_id\":\"43\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755583133.512874, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.info: Project found {\"project\":{\"id\":43,\"team_name\":\"Team 888\",\"proj_name\":\"Food Ordering System\",\"proj_desc\":\"this is a web-based food oerdering system\",\"start_date\":\"2025-08-19\",\"end_date\":\"2025-12-31\",\"shareable_slug\":null,\"created_at\":\"2025-08-19T05:31:41.000000Z\",\"updated_at\":\"2025-08-19T05:31:41.000000Z\"}}", "message_html": null, "is_string": false, "label": "info", "time": 1755583133.524901, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.info: Explicit active sprint check completed {\"proj_name\":\"Food Ordering System\",\"active_sprint_found\":true,\"active_sprint_data\":{\"sprint_id\":46,\"sprint_name\":\"Sprint 2\",\"sprint_desc\":\"2\",\"start_sprint\":\"2025-07-15\",\"end_sprint\":\"2025-07-31\",\"active_sprint\":1,\"proj_name\":\"Food Ordering System\",\"users_name\":\"ivlyn\",\"created_at\":\"2025-08-19T05:55:00.000000Z\",\"updated_at\":\"2025-08-19T05:55:00.000000Z\"}}", "message_html": null, "is_string": false, "label": "info", "time": 1755583133.537679, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.info: Active sprint ID determined {\"active_sprint_id\":46}", "message_html": null, "is_string": false, "label": "info", "time": 1755583133.53783, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.info: Processing user stories with active sprint {\"active_sprint_id\":46}", "message_html": null, "is_string": false, "label": "info", "time": 1755583133.537888, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.info: All non-done user stories retrieved {\"total_user_stories\":1,\"user_story_ids\":[58],\"done_status_id\":212}", "message_html": null, "is_string": false, "label": "info", "time": 1755583133.562891, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Processing user story {\"user_story_id\":58,\"user_story_sprint_id\":\"46\",\"active_sprint_id\":46}", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.562979, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: User story is in active sprint - checking tasks {\"user_story_id\":58}", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.563039, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Tasks not in sprint check completed {\"user_story_id\":58,\"has_tasks_not_in_sprint\":true}", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.577403, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: User story has tasks not in active sprint - adding to collection {\"user_story_id\":58}", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.577477, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.info: User stories filtering completed with active sprint {\"filtered_user_stories_count\":1,\"filtered_user_story_ids\":[58]}", "message_html": null, "is_string": false, "label": "info", "time": 1755583133.577558, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.info: Starting task retrieval for user stories", "message_html": null, "is_string": false, "label": "info", "time": 1755583133.577601, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Processing tasks for user story {\"user_story_id\":58}", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.590017, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Tasks filtered for active sprint and done status {\"user_story_id\":58,\"active_sprint_id\":46,\"done_task_status_id\":212,\"tasks_count\":2,\"task_ids\":[121,122]}", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.602404, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.info: Task retrieval completed {\"total_user_stories_with_tasks\":1,\"total_tasks_retrieved\":2}", "message_html": null, "is_string": false, "label": "info", "time": 1755583133.602473, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.info: BacklogController@index completed successfully {\"project_id\":\"43\",\"active_sprint_id\":46,\"user_stories_count\":1,\"tasks_by_user_story_count\":1,\"note\":\"Filtered out user stories with Done status and tasks with done status\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755583133.602525, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Gate check for permission: addToSprint_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.609247, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.610247, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.610298, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Gate check for permission: addToSprint_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.614029, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.6148, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.614848, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Gate check for permission: addToSprint_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.61514, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.615863, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.615909, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Gate check for permission: addUserStory_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.616162, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.616878, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.616923, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Gate check for permission: beginSprint_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.617181, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.617897, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.617941, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Gate check for permission: addToSprint_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.618204, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.618959, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.619003, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Gate check for permission: endSprint_backlog on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.619213, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.619912, "xdebug_link": null, "collector": "log"}, {"message": "[13:58:53] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583133.619955, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755583133.17513, "end": 1755583133.629755, "duration": 0.45462512969970703, "duration_str": "455ms", "measures": [{"label": "Booting", "start": 1755583133.17513, "relative_start": 0, "end": 1755583133.434675, "relative_end": 1755583133.434675, "duration": 0.25954508781433105, "duration_str": "260ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755583133.434689, "relative_start": 0.25955915451049805, "end": 1755583133.629757, "relative_end": 1.9073486328125e-06, "duration": 0.1950678825378418, "duration_str": "195ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24129640, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "backlogTest.index (\\resources\\views\\backlogTest\\index.blade.php)", "param_count": 4, "params": ["project", "userStories", "tasksByUserStory", "activeSprint"], "type": "blade"}]}, "route": {"uri": "GET backlogTest/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\BacklogController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "backlogTest.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\BacklogController.php&line=19\">\\app\\Http\\Controllers\\BacklogController.php:19-239</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.00367, "accumulated_duration_str": "3.67ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 12.534}, {"sql": "select * from `projects` where `projects`.`id` = '43' limit 1", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 24}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:24", "connection": "sagile", "start_percent": 12.534, "width_percent": 10.899}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` = 1 limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 30}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:30", "connection": "sagile", "start_percent": 23.433, "width_percent": 11.717}, {"sql": "select * from `statuses` where `project_id` = '43' and `title` = 'Done' limit 1", "type": "query", "params": [], "bindings": ["43", "Done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 75}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:75", "connection": "sagile", "start_percent": 35.15, "width_percent": 13.079}, {"sql": "select * from `user_stories` where `proj_id` = '43' and `status_id` != 212", "type": "query", "params": [], "bindings": ["43", "212"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 82}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:82", "connection": "sagile", "start_percent": 48.229, "width_percent": 14.714}, {"sql": "select exists(select * from `tasks` where `userstory_id` = 58 and (`sprint_id` != 46 or `sprint_id` is null)) as `exists`", "type": "query", "params": [], "bindings": ["58", "46"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 117}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:117", "connection": "sagile", "start_percent": 62.943, "width_percent": 10.354}, {"sql": "select * from `statuses` where `project_id` = '43' and `slug` = 'done' limit 1", "type": "query", "params": [], "bindings": ["43", "done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 173}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:173", "connection": "sagile", "start_percent": 73.297, "width_percent": 13.896}, {"sql": "select * from `tasks` where `userstory_id` = 58 and (`sprint_id` != 46 or `sprint_id` is null or `sprint_id` = 0) and `status_id` != 212", "type": "query", "params": [], "bindings": ["58", "46", "0", "212"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 191}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:191", "connection": "sagile", "start_percent": 87.193, "width_percent": 12.807}]}, "models": {"data": {"App\\Task": 2, "App\\UserStory": 1, "App\\Status": 2, "App\\Sprint": 1, "App\\Project": 1, "App\\User": 1}, "count": 8}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 7, "messages": [{"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583133.613169, "xdebug_link": null}, {"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-614290687 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614290687\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583133.614973, "xdebug_link": null}, {"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1581789059 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1581789059\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583133.616018, "xdebug_link": null}, {"message": "[\n  ability => addUserStory_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1888980240 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">addUserStory_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1888980240\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583133.617028, "xdebug_link": null}, {"message": "[\n  ability => beginSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-147705354 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">beginSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-147705354\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583133.618045, "xdebug_link": null}, {"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1947808528 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1947808528\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583133.619105, "xdebug_link": null}, {"message": "[\n  ability => endSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1075892717 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">endSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1075892717\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583133.620091, "xdebug_link": null}]}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backlogTest/43\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755581799\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/backlogTest/43", "status_code": "<pre class=sf-dump id=sf-dump-1974881378 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1974881378\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-209619043 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-209619043\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1500271547 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1500271547\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1647600157 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImR1NUVsRjZaMjcraFZzdmdPTDN4QlE9PSIsInZhbHVlIjoicjU5eTQ2VVhnUGUwT1hIQTRNVWorQVpONnBPWDlWSUhmaFlWTHVpRFJOWndZZUlVZi9LSUlsRmxTSDhOV3pCVml2NWV3THQ1WFN4RitzVVlGSUlORi9ET1krb3lOd0w0aXFsM05ER1N4OUtTM01qV3l6UEtOV3lyL1FLWTJ3dG0iLCJtYWMiOiJkN2IxZjczYmM2MWI1ZWY2YzRhYmU5YzA1OTkxZGRjYWQ1ODJhYzQ1N2QxMmFmZWJiOTVjNzJiMGRiM2JhNDczIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik9sSHEyTG12ei9wSmFsNmJQVCtmOWc9PSIsInZhbHVlIjoiYUUvY01QZjRsRFhCMmd2djlUUC9ja1pCVUhGRkU0eDdCSERCOFpCaUY1VlFRMGJRbkJWOTB0MUVzSG5VVnpaZDFoVDRuTmljMkk1N0pYWnJxbnRnclRacFROM1Nwc2VtMnZhcGpPUjRLZm9IMFFHRkRsZEtUd3JTMFkrL3NMR3QiLCJtYWMiOiJlOGEyYmNlZTQ4ZmYxYTA3ZjdlOWY1Y2Q5M2E5ZmZlZDgyYzA0NjY1NTJlOTExOTg3NTcxMGJhMjZhNzZiZDljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1647600157\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-548067265 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52555</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/43</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/43</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/index.php/backlogTest/43</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/43</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImR1NUVsRjZaMjcraFZzdmdPTDN4QlE9PSIsInZhbHVlIjoicjU5eTQ2VVhnUGUwT1hIQTRNVWorQVpONnBPWDlWSUhmaFlWTHVpRFJOWndZZUlVZi9LSUlsRmxTSDhOV3pCVml2NWV3THQ1WFN4RitzVVlGSUlORi9ET1krb3lOd0w0aXFsM05ER1N4OUtTM01qV3l6UEtOV3lyL1FLWTJ3dG0iLCJtYWMiOiJkN2IxZjczYmM2MWI1ZWY2YzRhYmU5YzA1OTkxZGRjYWQ1ODJhYzQ1N2QxMmFmZWJiOTVjNzJiMGRiM2JhNDczIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik9sSHEyTG12ei9wSmFsNmJQVCtmOWc9PSIsInZhbHVlIjoiYUUvY01QZjRsRFhCMmd2djlUUC9ja1pCVUhGRkU0eDdCSERCOFpCaUY1VlFRMGJRbkJWOTB0MUVzSG5VVnpaZDFoVDRuTmljMkk1N0pYWnJxbnRnclRacFROM1Nwc2VtMnZhcGpPUjRLZm9IMFFHRkRsZEtUd3JTMFkrL3NMR3QiLCJtYWMiOiJlOGEyYmNlZTQ4ZmYxYTA3ZjdlOWY1Y2Q5M2E5ZmZlZDgyYzA0NjY1NTJlOTExOTg3NTcxMGJhMjZhNzZiZDljIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755583133.1751</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755583133</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-548067265\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1607850515 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1607850515\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-147853468 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:58:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImRkd3B0ekFuamdTaVp1eTVxdkVDelE9PSIsInZhbHVlIjoia2tXUHhsTE1Ub0JBbzQ3T01Kb0ZEeXJuTCs5UjRndGUzdjVUM3oxRWRIdW5vbjN5eGE2K1JYQnBpUVdSdzJvQmYrZUdNRHdIS1NBNElEZ2xacHNkNWJjOTRCRy9meWdVdUFuQlEzYzQ5b0ZGMklQUGtUV3pabjVrVHgzeVFQT3UiLCJtYWMiOiI3MDE4NDgzMTk4ZTBkYjc1NzhkNTg5ZDk2NTlkMzhjNjUyODlkMzNkZjczMDdjNTBkODU4ZDQxMWZmNWM2ZjY3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:58:53 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjAvcVIvdmlUOXZ5NjR2aHBwUmtkc3c9PSIsInZhbHVlIjoicnBvUU54MEdSeVZMZ3NCZU5ZUi9WVTd2cGtVSTJtd2FiZmY1OEZuTVZOdGtpZHdLaitBd0d0ZzNwMzB5a2VQRUVZRkZPc2RGb3ZhcGVYN2F2MG1jOWhad3NrWTEyNEZaU0J3NzJRNjlZMUhhZHFSRXJmUHpYUHFqS1VLNjdRU1giLCJtYWMiOiJjMzY3ZmQ0YzJiMDljZTY5MWM3NmIzMjhhY2FhNmU2M2Y5ODJmMjgzN2M5NDM3NGI3MjU0NzRkYWYxZTA2ZThlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:58:53 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImRkd3B0ekFuamdTaVp1eTVxdkVDelE9PSIsInZhbHVlIjoia2tXUHhsTE1Ub0JBbzQ3T01Kb0ZEeXJuTCs5UjRndGUzdjVUM3oxRWRIdW5vbjN5eGE2K1JYQnBpUVdSdzJvQmYrZUdNRHdIS1NBNElEZ2xacHNkNWJjOTRCRy9meWdVdUFuQlEzYzQ5b0ZGMklQUGtUV3pabjVrVHgzeVFQT3UiLCJtYWMiOiI3MDE4NDgzMTk4ZTBkYjc1NzhkNTg5ZDk2NTlkMzhjNjUyODlkMzNkZjczMDdjNTBkODU4ZDQxMWZmNWM2ZjY3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:58:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjAvcVIvdmlUOXZ5NjR2aHBwUmtkc3c9PSIsInZhbHVlIjoicnBvUU54MEdSeVZMZ3NCZU5ZUi9WVTd2cGtVSTJtd2FiZmY1OEZuTVZOdGtpZHdLaitBd0d0ZzNwMzB5a2VQRUVZRkZPc2RGb3ZhcGVYN2F2MG1jOWhad3NrWTEyNEZaU0J3NzJRNjlZMUhhZHFSRXJmUHpYUHFqS1VLNjdRU1giLCJtYWMiOiJjMzY3ZmQ0YzJiMDljZTY5MWM3NmIzMjhhY2FhNmU2M2Y5ODJmMjgzN2M5NDM3NGI3MjU0NzRkYWYxZTA2ZThlIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:58:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-147853468\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1566767431 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755581799</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566767431\", {\"maxDepth\":0})</script>\n"}}