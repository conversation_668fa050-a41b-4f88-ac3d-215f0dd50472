1755570818a:1:{s:12:"<PERSON><PERSON><PERSON>'s team";a:2:{s:17:"multiple_projects";b:1;s:8:"projects";a:1:{i:42;a:4:{s:9:"role_name";s:15:"Project Manager";s:7:"role_id";i:31;s:10:"project_id";i:42;s:11:"permissions";a:44:{i:0;s:11:"view_kanban";i:1;s:13:"view_burndown";i:2;s:12:"view_backlog";i:3;s:14:"view_userstory";i:4;s:10:"view_forum";i:5;s:16:"view_bugtracking";i:6;s:11:"view_status";i:7;s:12:"view_details";i:8;s:10:"view_roles";i:9;s:14:"addLane_kanban";i:10;s:14:"addTask_kanban";i:11;s:15:"editLane_kanban";i:12;s:17:"deleteLane_kanban";i:13;s:17:"deleteTask_kanban";i:14;s:17:"addComment_kanban";i:15;s:20:"addUserStory_backlog";i:16;s:19:"beginSprint_backlog";i:17;s:19:"addToSprint_backlog";i:18;s:17:"endSprint_backlog";i:19;s:13:"add_userstory";i:20;s:14:"edit_userstory";i:21;s:16:"delete_userstory";i:22;s:20:"editStatus_userstory";i:23;s:8:"add_task";i:24;s:9:"edit_task";i:25;s:11:"delete_task";i:26;s:17:"viewCalendar_task";i:27;s:17:"viewComments_task";i:28;s:9:"add_roles";i:29;s:10:"edit_roles";i:30;s:12:"delete_roles";i:31;s:10:"add_status";i:32;s:11:"edit_status";i:33;s:13:"delete_status";i:34;s:12:"edit_details";i:35;s:14:"delete_details";i:36;s:18:"view_sprintArchive";i:37;s:31:"viewKanbanArchive_sprintArchive";i:38;s:33:"viewBurndownArchive_sprintArchive";i:39;s:23:"updateTaskStatus_kanban";i:40;s:9:"view_task";i:41;s:13:"share_details";i:42;s:15:"editTask_kanban";i:43;s:20:"updateUserRole_roles";}}}}}