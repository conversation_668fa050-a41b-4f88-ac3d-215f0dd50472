{"__meta": {"id": "X00159c8f77d8e40e64e5590f180556b6", "datetime": "2025-08-19 14:18:17", "utime": 1755584297.52939, "method": "GET", "uri": "/45/burn-down-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[14:18:17] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584297.413344, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584297.073627, "end": 1755584297.52941, "duration": 0.4557828903198242, "duration_str": "456ms", "measures": [{"label": "Booting", "start": 1755584297.073627, "relative_start": 0, "end": 1755584297.39396, "relative_end": 1755584297.39396, "duration": 0.32033300399780273, "duration_str": "320ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584297.393971, "relative_start": 0.3203439712524414, "end": 1755584297.529412, "relative_end": 2.1457672119140625e-06, "duration": 0.13544106483459473, "duration_str": "135ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23786160, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "burndown.index (\\resources\\views\\burndown\\index.blade.php)", "param_count": 2, "params": ["project", "hasActiveSprint"], "type": "blade"}]}, "route": {"uri": "GET {proj_id}/burn-down-chart", "middleware": "web", "controller": "App\\Http\\Controllers\\BurnDownChartController@getBurndownData", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "burnDown.getBurndownData", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\BurndownChartController.php&line=13\">\\app\\Http\\Controllers\\BurndownChartController.php:13-111</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0015999999999999999, "accumulated_duration_str": "1.6ms", "statements": [{"sql": "select * from `users` where `id` = 31 limit 1", "type": "query", "params": [], "bindings": ["31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 26.875}, {"sql": "select * from `projects` where `id` = '45' limit 1", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\BurndownChartController.php", "line": 15}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Http\\Controllers\\BurndownChartController.php:15", "connection": "sagile", "start_percent": 26.875, "width_percent": 27.5}, {"sql": "select * from `sprint` where `proj_name` = 'SAgile' and `active_sprint` = 1 limit 1", "type": "query", "params": [], "bindings": ["SAgile", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BurndownChartController.php", "line": 22}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Http\\Controllers\\BurndownChartController.php:22", "connection": "sagile", "start_percent": 54.375, "width_percent": 22.5}, {"sql": "select * from `sprint` where `proj_name` = 'SAgile' and `active_sprint` is null and `start_sprint` <= '2025-08-19 14:18:17' and `end_sprint` >= '2025-08-19 14:18:17' limit 1", "type": "query", "params": [], "bindings": ["SAgile", "2025-08-19 14:18:17", "2025-08-19 14:18:17"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BurndownChartController.php", "line": 31}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Http\\Controllers\\BurndownChartController.php:31", "connection": "sagile", "start_percent": 76.875, "width_percent": 23.125}]}, "models": {"data": {"App\\Project": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/45/burn-down-chart\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "31", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/45/burn-down-chart", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1955671722 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1955671722\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-61363790 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-61363790\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-232803326 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IktiWVBxRkFWUXlhS2hCRkxQRTdsTkE9PSIsInZhbHVlIjoickc2a2wrOUo4Qk5nZ1hmNisrUkVoWVhJUVJxMDlMZTV1QmtlNXg1Ty9CWlVITUlzdVNtOUFsVkduMDdOaXh0M1VtRUQyOHlrYVBONm1ML1hNZmFRZUM3OUdIL1ZKMktqdm1ydkswRjBQRFZjbU94Ny9PbHd6dDNSTndNSzBvdW0iLCJtYWMiOiI0YTIwMmRlYTZhZjg0ZmViYWQxZGVkMWUwNDdiNjUzN2VjZWIwZDJkZDZmMTQ2Yjg0YThkNzE4ZTExZjRiYjE3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IllDUEJMc3I5ZVVIcTBST0JrM3I1UXc9PSIsInZhbHVlIjoicHl3dDBBZTRpMWNkUGZNNkx1Um54U3dpZHRvZWNIbGRMOUMxTHNuQUpESlh3RUdaNnBTeW1CN0oyK1JoYlNhTDFlRmppWUFXODlXejZsTThOdjY5QWZRMXVOR2IrM0x4TjFFUWFqZzZ5R2VXLzhyTlBGUUhDQjRjcXlhRW1zUmoiLCJtYWMiOiI1Y2UzMjRkMTJlZjExNDlkZTBiYjQ3OGRjMTNlMDg1ZWZhZGI1YjdjM2JlOTQ4MGUzOTcwMTgzYzNjYWM5ZjQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-232803326\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-212541130 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">62005</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/45/burn-down-chart</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/45/burn-down-chart</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/index.php/45/burn-down-chart</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/45</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IktiWVBxRkFWUXlhS2hCRkxQRTdsTkE9PSIsInZhbHVlIjoickc2a2wrOUo4Qk5nZ1hmNisrUkVoWVhJUVJxMDlMZTV1QmtlNXg1Ty9CWlVITUlzdVNtOUFsVkduMDdOaXh0M1VtRUQyOHlrYVBONm1ML1hNZmFRZUM3OUdIL1ZKMktqdm1ydkswRjBQRFZjbU94Ny9PbHd6dDNSTndNSzBvdW0iLCJtYWMiOiI0YTIwMmRlYTZhZjg0ZmViYWQxZGVkMWUwNDdiNjUzN2VjZWIwZDJkZDZmMTQ2Yjg0YThkNzE4ZTExZjRiYjE3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IllDUEJMc3I5ZVVIcTBST0JrM3I1UXc9PSIsInZhbHVlIjoicHl3dDBBZTRpMWNkUGZNNkx1Um54U3dpZHRvZWNIbGRMOUMxTHNuQUpESlh3RUdaNnBTeW1CN0oyK1JoYlNhTDFlRmppWUFXODlXejZsTThOdjY5QWZRMXVOR2IrM0x4TjFFUWFqZzZ5R2VXLzhyTlBGUUhDQjRjcXlhRW1zUmoiLCJtYWMiOiI1Y2UzMjRkMTJlZjExNDlkZTBiYjQ3OGRjMTNlMDg1ZWZhZGI1YjdjM2JlOTQ4MGUzOTcwMTgzYzNjYWM5ZjQyIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584297.0736</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584297</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-212541130\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1448453936 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CfapZDSBofQwHxJsJhdmHULLiISKd6PUgVWjQqhr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1448453936\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1918191603 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:18:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlB2NXNnOEh1SFBOL0JEQWJaa3YvVlE9PSIsInZhbHVlIjoiLzQwMlMrTm5BWGlFczZUYVI3NXErNXdWckJyMm14V05PUWZPTDBSNVpvMmM2dWFYdzh6TGVNK3IyZ3FzZUdDczcvb3Jwc003Ujl4cXdNTjJjM1lieCtwbU5MT2lMdlJBZXFDMnF2TGp6VUxmaVJ4dk1SRzBmSnZVUUJYUFYzY1giLCJtYWMiOiJkZjIyYzhmYmRmMDE2ZDUwN2IzNjcwMGM2NDA0Njk0NDgxZDdiZDAyYTEzMTBkYmJjMDkwY2U0ZjQzMGY1YWViIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:18:17 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlcxdXVQeHhGNGlGeTduQUhPL2J1dmc9PSIsInZhbHVlIjoiWGVFUXUvVzg0dGxSRUNlOG9iZTFpSkV3NmZpb3N6emdQQWh1bmZIS1VsTkthb0QxVmszaHI5RUFEY2hYNTBja3kycDY0MjBvcHNMaWZ4SjJhWkcrQkk4QTZ5NDdOV29DUXdBOUxrNGpud2doNFRub1hNdnpoVTlsZks4WGRIZGEiLCJtYWMiOiI1MGQ1ZTcxZjJjMjI4MDAxZWRmOTM3MzEzZDgzZDEwNWVmMmNiNzE0YjE2YWNkMzUyZjU5MzE5ZDUwZGNlOTI0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:18:17 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlB2NXNnOEh1SFBOL0JEQWJaa3YvVlE9PSIsInZhbHVlIjoiLzQwMlMrTm5BWGlFczZUYVI3NXErNXdWckJyMm14V05PUWZPTDBSNVpvMmM2dWFYdzh6TGVNK3IyZ3FzZUdDczcvb3Jwc003Ujl4cXdNTjJjM1lieCtwbU5MT2lMdlJBZXFDMnF2TGp6VUxmaVJ4dk1SRzBmSnZVUUJYUFYzY1giLCJtYWMiOiJkZjIyYzhmYmRmMDE2ZDUwN2IzNjcwMGM2NDA0Njk0NDgxZDdiZDAyYTEzMTBkYmJjMDkwY2U0ZjQzMGY1YWViIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:18:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlcxdXVQeHhGNGlGeTduQUhPL2J1dmc9PSIsInZhbHVlIjoiWGVFUXUvVzg0dGxSRUNlOG9iZTFpSkV3NmZpb3N6emdQQWh1bmZIS1VsTkthb0QxVmszaHI5RUFEY2hYNTBja3kycDY0MjBvcHNMaWZ4SjJhWkcrQkk4QTZ5NDdOV29DUXdBOUxrNGpud2doNFRub1hNdnpoVTlsZks4WGRIZGEiLCJtYWMiOiI1MGQ1ZTcxZjJjMjI4MDAxZWRmOTM3MzEzZDgzZDEwNWVmMmNiNzE0YjE2YWNkMzUyZjU5MzE5ZDUwZGNlOTI0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:18:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1918191603\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-457523017 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/45/burn-down-chart</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-457523017\", {\"maxDepth\":0})</script>\n"}}