{"__meta": {"id": "X4d0a16cb0fb3ac41bc2da01f448cde79", "datetime": "2025-08-18 23:43:26", "utime": 1755531806.693748, "method": "POST", "uri": "/upload-radar-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:43:26] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755531806.621405, "xdebug_link": null, "collector": "log"}, {"message": "[23:43:26] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/upload-radar-chart", "message_html": null, "is_string": false, "label": "debug", "time": 1755531806.683749, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755531806.324378, "end": 1755531806.69377, "duration": 0.36939191818237305, "duration_str": "369ms", "measures": [{"label": "Booting", "start": 1755531806.324378, "relative_start": 0, "end": 1755531806.60033, "relative_end": 1755531806.60033, "duration": 0.2759521007537842, "duration_str": "276ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755531806.600343, "relative_start": 0.27596497535705566, "end": 1755531806.693772, "relative_end": 2.1457672119140625e-06, "duration": 0.0934290885925293, "duration_str": "93.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23504512, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST upload-radar-chart", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@uploadRadarChart", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=141\">\\app\\Http\\Controllers\\CIGController.php:141-151</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00046, "accumulated_duration_str": "460μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/radar-data/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]"}, "request": {"path_info": "/upload-radar-chart", "status_code": "<pre class=sf-dump id=sf-dump-872038902 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-872038902\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1316793303 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1316793303\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1950142055 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"74022 characters\">data:image/png;base64,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</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1950142055\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-955348386 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">74034</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjV6ZlVPUnAwNCt5OVJLSm9LOHVHMVE9PSIsInZhbHVlIjoiZU1FaFY2ZzRiamt4S3AvMDlsMmo2Q1ViNFFpVkJmWFRzSmFpQS9hNzVOMzR3ckNFVUpJNFFSdW9rdUYxVW56Y1c2WkZwNmRqMFlWZUU1R0lvSDIyQkxvV3hJNksvaDg3eEt4b1VIeVgzTnk2K3ZKNnBqbEVETFI0UkxFbElkelIiLCJtYWMiOiJjOWYxMTM2ODAwNzM5MzdhYmYwNzYwMDVmMzZlMGIzYmM5NGExZjk2NDI4MDI4ZDhiNTQ4ZTI3ZThkMjYxNGY5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjBJUHNRanQyWUFZMVhjSDhITEx5S2c9PSIsInZhbHVlIjoicEU2RGx2OGIxQjQ5elczN2ZpcEY4MVMrN3hPc1NRSjlhVURrYURUWDE5OHErcWpBaDF3VkZkV2QvekpheWdPRldzMVBvcVNOd3RWZ3VUejgreUNGUUZDSi9PZENERndDQjFUQ1R6S3FmK09uaEo5UDh3Z3luL1djZXBFNldMRFgiLCJtYWMiOiJkMzJmYjA0ZDgxNTAzMDY1ZmY1ZGM2OTAyZjgyODI3NTU2MGIxN2FiN2ZmOWQ4NGRlNjg4YTg3MjhiNmFlOTJhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-955348386\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-2012788451 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56864</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/index.php/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">74034</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">74034</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjV6ZlVPUnAwNCt5OVJLSm9LOHVHMVE9PSIsInZhbHVlIjoiZU1FaFY2ZzRiamt4S3AvMDlsMmo2Q1ViNFFpVkJmWFRzSmFpQS9hNzVOMzR3ckNFVUpJNFFSdW9rdUYxVW56Y1c2WkZwNmRqMFlWZUU1R0lvSDIyQkxvV3hJNksvaDg3eEt4b1VIeVgzTnk2K3ZKNnBqbEVETFI0UkxFbElkelIiLCJtYWMiOiJjOWYxMTM2ODAwNzM5MzdhYmYwNzYwMDVmMzZlMGIzYmM5NGExZjk2NDI4MDI4ZDhiNTQ4ZTI3ZThkMjYxNGY5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjBJUHNRanQyWUFZMVhjSDhITEx5S2c9PSIsInZhbHVlIjoicEU2RGx2OGIxQjQ5elczN2ZpcEY4MVMrN3hPc1NRSjlhVURrYURUWDE5OHErcWpBaDF3VkZkV2QvekpheWdPRldzMVBvcVNOd3RWZ3VUejgreUNGUUZDSi9PZENERndDQjFUQ1R6S3FmK09uaEo5UDh3Z3luL1djZXBFNldMRFgiLCJtYWMiOiJkMzJmYjA0ZDgxNTAzMDY1ZmY1ZGM2OTAyZjgyODI3NTU2MGIxN2FiN2ZmOWQ4NGRlNjg4YTg3MjhiNmFlOTJhIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755531806.3244</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755531806</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2012788451\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2097245409 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2097245409\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-636538683 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:43:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjAvR0pkRndJUVFxaEM2Ri8rd2FsaWc9PSIsInZhbHVlIjoiOXo3eUhLNGdDNHphNjg0eFh0OW5tRkkvb3ltbkR5elBMRzV4d2lDOEFWS0cwUE1oQld5cy92MDhNMWVyVEhZbDgwYTg0NmMzeDVuVVdIcDFxektFbUNUbENkTGNyS2NHajhDaDEwWkF3NTlJR1l3Vnd1V0wvMTMrUGx4RGd2QmEiLCJtYWMiOiI5M2M3ZWQ1ZTljNzhmNmZiNzI5MDNjNjI4ZTdjNzQ2MDJiMzA0YTk3NTUzMWU2MDU3OWY5ODczYjBkNWVlN2FkIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:26 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im9Nd1BCTmF1K3BFd0JQUmNXeGM2OGc9PSIsInZhbHVlIjoibUwrNDdaVzF5Zlk3bUJoL09BUGQzbWx1cUhMQXBFU3JiSFN3UFdMWmR3bFh2cEdyTllMeHFrSW5oMmpHNzd6dzJMVEV4ZnR4ZmQ2Qkg2R3NJbUM0TW54eWRRd2g4dDlHRWVmbWtDOG40M01PZVZQYnA5eXFvMWNORld1N0F0cEYiLCJtYWMiOiI0MzczOTc2N2ZlYTAzOTk0OGYyNTlmNGEyYjE3Y2QxOGQzNTZjNmJmNDg3NmY5MTQ2MzU1MjM2MDZiZTQwMDhkIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:26 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjAvR0pkRndJUVFxaEM2Ri8rd2FsaWc9PSIsInZhbHVlIjoiOXo3eUhLNGdDNHphNjg0eFh0OW5tRkkvb3ltbkR5elBMRzV4d2lDOEFWS0cwUE1oQld5cy92MDhNMWVyVEhZbDgwYTg0NmMzeDVuVVdIcDFxektFbUNUbENkTGNyS2NHajhDaDEwWkF3NTlJR1l3Vnd1V0wvMTMrUGx4RGd2QmEiLCJtYWMiOiI5M2M3ZWQ1ZTljNzhmNmZiNzI5MDNjNjI4ZTdjNzQ2MDJiMzA0YTk3NTUzMWU2MDU3OWY5ODczYjBkNWVlN2FkIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im9Nd1BCTmF1K3BFd0JQUmNXeGM2OGc9PSIsInZhbHVlIjoibUwrNDdaVzF5Zlk3bUJoL09BUGQzbWx1cUhMQXBFU3JiSFN3UFdMWmR3bFh2cEdyTllMeHFrSW5oMmpHNzd6dzJMVEV4ZnR4ZmQ2Qkg2R3NJbUM0TW54eWRRd2g4dDlHRWVmbWtDOG40M01PZVZQYnA5eXFvMWNORld1N0F0cEYiLCJtYWMiOiI0MzczOTc2N2ZlYTAzOTk0OGYyNTlmNGEyYjE3Y2QxOGQzNTZjNmJmNDg3NmY5MTQ2MzU1MjM2MDZiZTQwMDhkIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-636538683\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1047828195 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/radar-data/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1047828195\", {\"maxDepth\":0})</script>\n"}}