{"__meta": {"id": "X5d8d863cf442ec5b27268e214973a6a1", "datetime": "2025-08-19 10:41:49", "utime": 1755571309.561778, "method": "PATCH", "uri": "/userstory/45/updateStatus", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[10:41:49] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755571309.389128, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:49] LOG.debug: LoadUserPermissions: Extracted project ID: 45 from URL: http://127.0.0.1:8000/userstory/45/updateStatus", "message_html": null, "is_string": false, "label": "debug", "time": 1755571309.467611, "xdebug_link": null, "collector": "log"}, {"message": "[10:41:49] LOG.debug: LoadUserPermissions: No project found for ID: 45", "message_html": null, "is_string": false, "label": "debug", "time": 1755571309.485419, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755571309.040399, "end": 1755571309.561805, "duration": 0.5214059352874756, "duration_str": "521ms", "measures": [{"label": "Booting", "start": 1755571309.040399, "relative_start": 0, "end": 1755571309.364371, "relative_end": 1755571309.364371, "duration": 0.3239719867706299, "duration_str": "324ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755571309.364385, "relative_start": 0.3239858150482178, "end": 1755571309.561808, "relative_end": 3.0994415283203125e-06, "duration": 0.19742321968078613, "duration_str": "197ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24297680, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PATCH userstory/{id}/updateStatus", "middleware": "web", "controller": "App\\Http\\Controllers\\UserStoryController@updateStatus", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "userstory.updateStatus", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\UserStoryController.php&line=78\">\\app\\Http\\Controllers\\UserStoryController.php:78-97</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.005, "accumulated_duration_str": "5ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 11}, {"sql": "select `id`, `team_name` from `projects` where `id` = '45' limit 1", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 167}, {"index": 14, "namespace": "middleware", "name": "load.permissions", "line": 33}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 16, "namespace": "middleware", "name": "bindings", "line": 50}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "middleware::load.permissions:167", "connection": "sagile", "start_percent": 11, "width_percent": 13}, {"sql": "select * from `user_stories` where `user_stories`.`u_id` = '45' limit 1", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 81}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:81", "connection": "sagile", "start_percent": 24, "width_percent": 11.2}, {"sql": "select count(*) as aggregate from `statuses` where `id` = '208'", "type": "query", "params": [], "bindings": ["208"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "sagile", "start_percent": 35.2, "width_percent": 9.4}, {"sql": "update `user_stories` set `status_id` = '208', `user_stories`.`updated_at` = '2025-08-19 10:41:49' where `u_id` = 45", "type": "query", "params": [], "bindings": ["208", "2025-08-19 10:41:49", "45"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\UserStoryController.php", "line": 90}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00277, "duration_str": "2.77ms", "stmt_id": "\\app\\Http\\Controllers\\UserStoryController.php:90", "connection": "sagile", "start_percent": 44.6, "width_percent": 55.4}]}, "models": {"data": {"App\\UserStory": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/userstory/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"path_info": "/userstory/45/updateStatus", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1337904965 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1337904965\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1960024389 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>status_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">208</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"5 characters\">PATCH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1960024389\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-668281623 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">375</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryymZdmN6WR7iG14MB</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/userstory/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IllUenkzaHdQV1pSbTlXRWRSb3NNVXc9PSIsInZhbHVlIjoiQ1ZzTnpsWU1ETVRWdGlKcjRzTEYzaXdtbWV0N3YvQ2QxUEllekFmb3pzTmI3ZG5EZ21aQ2s2NitpRkpmVUZibGpXbFVWL2JYd2NZd3FnQy91NVJzUDByMnZiNGgyMnlJZ0VSa3ZMYVJZUUIySzFqZ2U0bTZGNERjTXZMQ0RUOWoiLCJtYWMiOiIyNDU2NjlhYTY0YmZjNzkzZjUxYWFhNTQ1NjcwNzE1MmMxNWUyNjMzMzY3Y2Q2MmNhNmI0ZmEzZjhjNjBiMTRlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjdBRkt6NldQSjNpeHdqZ1M5MVpnWHc9PSIsInZhbHVlIjoic3ZHYXFDVUZ3LzgzeW9TOGgxeUo3emJicDBtQzErZ0NKcVl6MS9mVkZBSzRvQ25nN1JlZnZLRExjcUE5a25ZdWpBeGdwRC9OUmJob2gxaG5aODlhQ3ZWOWtoZC9ZOWxlb2c1T1k4OWExa1JaM0F6Tyt6b21jUEczeTRSZmtBZDIiLCJtYWMiOiIyNjc0MDA1NjlhNTYyMDg0ZmNmOTgxNjlmMmIzMGU3NDdjMTU3Zjg5YmMwODg4NmEzNzg0ZDJkZTgxNjhkN2U2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-668281623\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-575007700 data-indent-pad=\"  \"><span class=sf-dump-note>array:34</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56320</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/userstory/45/updateStatus</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/userstory/45/updateStatus</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/index.php/userstory/45/updateStatus</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">375</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">375</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryymZdmN6WR7iG14MB</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryymZdmN6WR7iG14MB</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/userstory/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IllUenkzaHdQV1pSbTlXRWRSb3NNVXc9PSIsInZhbHVlIjoiQ1ZzTnpsWU1ETVRWdGlKcjRzTEYzaXdtbWV0N3YvQ2QxUEllekFmb3pzTmI3ZG5EZ21aQ2s2NitpRkpmVUZibGpXbFVWL2JYd2NZd3FnQy91NVJzUDByMnZiNGgyMnlJZ0VSa3ZMYVJZUUIySzFqZ2U0bTZGNERjTXZMQ0RUOWoiLCJtYWMiOiIyNDU2NjlhYTY0YmZjNzkzZjUxYWFhNTQ1NjcwNzE1MmMxNWUyNjMzMzY3Y2Q2MmNhNmI0ZmEzZjhjNjBiMTRlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjdBRkt6NldQSjNpeHdqZ1M5MVpnWHc9PSIsInZhbHVlIjoic3ZHYXFDVUZ3LzgzeW9TOGgxeUo3emJicDBtQzErZ0NKcVl6MS9mVkZBSzRvQ25nN1JlZnZLRExjcUE5a25ZdWpBeGdwRC9OUmJob2gxaG5aODlhQ3ZWOWtoZC9ZOWxlb2c1T1k4OWExa1JaM0F6Tyt6b21jUEczeTRSZmtBZDIiLCJtYWMiOiIyNjc0MDA1NjlhNTYyMDg0ZmNmOTgxNjlmMmIzMGU3NDdjMTU3Zjg5YmMwODg4NmEzNzg0ZDJkZTgxNjhkN2U2IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755571309.0404</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755571309</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-575007700\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1435747708 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1435747708\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1475530427 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:41:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InBFU0RZc1hCOGtRazBLbGZGdkluOFE9PSIsInZhbHVlIjoidFY4Yk1EYVBxcFZvMzRyQ1d3azJJTHZDNDNBYXVYS3JvWDVpcVRXaXNET2tPQUxOWGVLMFliSzJYUThCd3VIdEREOGJabEFTNmNGamtGcVB3dmNqMXVmdXhoQi92M0tjenp3bDFaVmpZTWJpNTJITGg2R2I0dDYyQWc5Z2pHa3UiLCJtYWMiOiJkNzc1ZjE1YzhmNTM4NjMwNzllNmI3OWVjOWI4YmI2YTgxZWIwM2QzZjRmNTc5NzEyMzYwY2Q0NzRlNDI1ZWNkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:41:49 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkViZWRRUWJxdEMxYVFwdVRvcXVEMUE9PSIsInZhbHVlIjoiSTZnSE91OUlsNUlKVG5FRWRELzdobURZdmdKRGt4dEF5c1dzMkJpcVV1Tll1OElWdWtaQ3dxaE9KZ2x4V3F0bFMwRGwzUDNXVWdCNXlqS2FPR3ViY2Q0dU8yVzhIcEloUTA3Z2dqZ3JRdm9DV1hpVkJNZGxydXpsVWtCeStwTDAiLCJtYWMiOiJiNTY1ZTAzZmIyN2Y5MGY0NzYxOTFjNTNmYmI1Y2RhNWQ3OGUxYTkxZjY5YzJmZTUyYTJmNjk0YTUxZDg4ZTQxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:41:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InBFU0RZc1hCOGtRazBLbGZGdkluOFE9PSIsInZhbHVlIjoidFY4Yk1EYVBxcFZvMzRyQ1d3azJJTHZDNDNBYXVYS3JvWDVpcVRXaXNET2tPQUxOWGVLMFliSzJYUThCd3VIdEREOGJabEFTNmNGamtGcVB3dmNqMXVmdXhoQi92M0tjenp3bDFaVmpZTWJpNTJITGg2R2I0dDYyQWc5Z2pHa3UiLCJtYWMiOiJkNzc1ZjE1YzhmNTM4NjMwNzllNmI3OWVjOWI4YmI2YTgxZWIwM2QzZjRmNTc5NzEyMzYwY2Q0NzRlNDI1ZWNkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:41:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkViZWRRUWJxdEMxYVFwdVRvcXVEMUE9PSIsInZhbHVlIjoiSTZnSE91OUlsNUlKVG5FRWRELzdobURZdmdKRGt4dEF5c1dzMkJpcVV1Tll1OElWdWtaQ3dxaE9KZ2x4V3F0bFMwRGwzUDNXVWdCNXlqS2FPR3ViY2Q0dU8yVzhIcEloUTA3Z2dqZ3JRdm9DV1hpVkJNZGxydXpsVWtCeStwTDAiLCJtYWMiOiJiNTY1ZTAzZmIyN2Y5MGY0NzYxOTFjNTNmYmI1Y2RhNWQ3OGUxYTkxZjY5YzJmZTUyYTJmNjk0YTUxZDg4ZTQxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:41:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1475530427\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2083296197 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/userstory/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2083296197\", {\"maxDepth\":0})</script>\n"}}