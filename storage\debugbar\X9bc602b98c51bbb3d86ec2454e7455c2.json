{"__meta": {"id": "X9bc602b98c51bbb3d86ec2454e7455c2", "datetime": "2025-08-19 13:23:17", "utime": 1755580997.372512, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[13:23:17] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755580997.243209, "xdebug_link": null, "collector": "log"}, {"message": "[13:23:17] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000", "message_html": null, "is_string": false, "label": "debug", "time": 1755580997.339843, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755580996.739146, "end": 1755580997.372535, "duration": 0.6333889961242676, "duration_str": "633ms", "measures": [{"label": "Booting", "start": 1755580996.739146, "relative_start": 0, "end": 1755580997.209417, "relative_end": 1755580997.209417, "duration": 0.47027111053466797, "duration_str": "470ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755580997.209432, "relative_start": 0.47028589248657227, "end": 1755580997.372538, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "welcome (\\resources\\views\\welcome.blade.php)", "param_count": 0, "params": [], "type": "blade"}]}, "route": {"uri": "GET /", "middleware": "web", "uses": "Closure() {#1346\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#345 …}\n  file: \"C:\\xampp\\htdocs\\SAgilePMT_UTM\\routes\\web.php\"\n  line: \"97 to 99\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\routes\\web.php&line=97\">\\routes\\web.php:97-99</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00053, "accumulated_duration_str": "530μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "load.permissions", "line": 22}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1506973087 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1506973087\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1357773180 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkpIY3NvL1RJZ2JMd3MzVGdoYTlueUE9PSIsInZhbHVlIjoiSElZSm1CS1VwS1lMZ1VBS0lMQkIzSmtpMVpNMmNzYjBmQlNJbEJZQ1k0QTliOG9nS2MxQmlqWWFwTWEzUDRCSUMvWklVaXpwZURZZTNVYXJjY0djcy83bXZqbzdpM2FDQWNzRzRTTU1oZGV3bDF0eDUxdjZCTXdiVlpLamRYTXkiLCJtYWMiOiI1Y2VhYTI3ZTAxM2QzMTBlOWViOGExNTUxMmIzNjIzNDVkNDFkOTI4YjQ5MGNjNWM4YjhjMjc1NjIyNmU1YzBiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImcrQytSNzE3NHFrSnplL2VFZC9TUEE9PSIsInZhbHVlIjoiSm5vME9XejhZaXJ6ejNKa3B0dzhjekxUb1FTcS9wNUVOd1h4YzdDdjVEVkZZcjZpbjlRSTRsa2hMenhoZGNQNWpzUUE2K09XQWRZQ1UxUGZMYUhSanZqQkdJelFQdnhDU053RkM1VEZEMU0yMkp4cS82clIxbUgrMlF1WTFRVXQiLCJtYWMiOiJhMzY1YmVmY2JkNzQ5OTFjNDhiMzE4NmEwYmUyMmJmZmM3MzQ0ZjA4NGMyMTAwYTc2MTQyOGIyZTU1YTZjMmU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1357773180\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-2016896229 data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">64124</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str>/</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkpIY3NvL1RJZ2JMd3MzVGdoYTlueUE9PSIsInZhbHVlIjoiSElZSm1CS1VwS1lMZ1VBS0lMQkIzSmtpMVpNMmNzYjBmQlNJbEJZQ1k0QTliOG9nS2MxQmlqWWFwTWEzUDRCSUMvWklVaXpwZURZZTNVYXJjY0djcy83bXZqbzdpM2FDQWNzRzRTTU1oZGV3bDF0eDUxdjZCTXdiVlpLamRYTXkiLCJtYWMiOiI1Y2VhYTI3ZTAxM2QzMTBlOWViOGExNTUxMmIzNjIzNDVkNDFkOTI4YjQ5MGNjNWM4YjhjMjc1NjIyNmU1YzBiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImcrQytSNzE3NHFrSnplL2VFZC9TUEE9PSIsInZhbHVlIjoiSm5vME9XejhZaXJ6ejNKa3B0dzhjekxUb1FTcS9wNUVOd1h4YzdDdjVEVkZZcjZpbjlRSTRsa2hMenhoZGNQNWpzUUE2K09XQWRZQ1UxUGZMYUhSanZqQkdJelFQdnhDU053RkM1VEZEMU0yMkp4cS82clIxbUgrMlF1WTFRVXQiLCJtYWMiOiJhMzY1YmVmY2JkNzQ5OTFjNDhiMzE4NmEwYmUyMmJmZmM3MzQ0ZjA4NGMyMTAwYTc2MTQyOGIyZTU1YTZjMmU1IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755580996.7391</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755580996</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2016896229\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1246096549 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GKxRuD042xMQ4k3v5EHCZLsF1sfAXXnsz4rkTCog</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1246096549\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-644711309 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:23:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkdXU1BkcU9FYXhPVnZhT0lyYStkcGc9PSIsInZhbHVlIjoiL2tQRkVKZmVqRDBIUHNMQnBFWkl6a1BlTVBvV1RISDE3dlRzcit4aDNLT29TOXMwdDVTUmFOVGluWUlpQXJWTitZQ3pLMWxGY2ZQVDlLTjc2QTZKR3JHd1V3KzJ3dnBORjAwWkUrL0FzcjZUVXVsc01HcUl2QjBrMGlzNmhBWVYiLCJtYWMiOiI5N2I4OGEzZjE4NGQyNjg3ODY5OWQ4NjZlYWFiZjk2YzAxNDQzOTkxMzM3ZGQzZjBkMDhhNTA0YzdhZWYyNTllIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:23:17 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlN5MUFuUi9hVUNVb2N0WGxhaGd5QVE9PSIsInZhbHVlIjoiZ01aNWJPWSs1ZGV6K1VwVWdlQkp6YnhyUUNxUWdWVEtRdERkQzdvdG1KZzIvdjdZcHZhWmxWZVhPbmUvaW50TWNpNS9DOVFjcm5CRFZmQi9SZEozVktVeTFiTzNwNEl5WmRVa3dKQW9qTjFhVjBSTkM3TzU2TXVUaUZHd1dzTWIiLCJtYWMiOiJhYWM1YTU5MGYzNDQyZDAyOWM2MTY3N2U5Zjg1MzAxNzFlN2E3YTk2MmI1M2JjM2FkNDRiM2Y5NDI3MGRmMzY5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:23:17 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkdXU1BkcU9FYXhPVnZhT0lyYStkcGc9PSIsInZhbHVlIjoiL2tQRkVKZmVqRDBIUHNMQnBFWkl6a1BlTVBvV1RISDE3dlRzcit4aDNLT29TOXMwdDVTUmFOVGluWUlpQXJWTitZQ3pLMWxGY2ZQVDlLTjc2QTZKR3JHd1V3KzJ3dnBORjAwWkUrL0FzcjZUVXVsc01HcUl2QjBrMGlzNmhBWVYiLCJtYWMiOiI5N2I4OGEzZjE4NGQyNjg3ODY5OWQ4NjZlYWFiZjk2YzAxNDQzOTkxMzM3ZGQzZjBkMDhhNTA0YzdhZWYyNTllIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:23:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlN5MUFuUi9hVUNVb2N0WGxhaGd5QVE9PSIsInZhbHVlIjoiZ01aNWJPWSs1ZGV6K1VwVWdlQkp6YnhyUUNxUWdWVEtRdERkQzdvdG1KZzIvdjdZcHZhWmxWZVhPbmUvaW50TWNpNS9DOVFjcm5CRFZmQi9SZEozVktVeTFiTzNwNEl5WmRVa3dKQW9qTjFhVjBSTkM3TzU2TXVUaUZHd1dzTWIiLCJtYWMiOiJhYWM1YTU5MGYzNDQyZDAyOWM2MTY3N2U5Zjg1MzAxNzFlN2E3YTk2MmI1M2JjM2FkNDRiM2Y5NDI3MGRmMzY5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:23:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-644711309\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-303314254 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-303314254\", {\"maxDepth\":0})</script>\n"}}