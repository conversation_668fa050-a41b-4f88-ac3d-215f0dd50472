{"__meta": {"id": "Xe871303deda3884cf3a39d055d3756ef", "datetime": "2025-08-18 23:55:20", "utime": 1755532520.215989, "method": "GET", "uri": "/nfr/general/2/details?project_filter=&sprint_filter=34", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:55:20] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755532520.029286, "xdebug_link": null, "collector": "log"}, {"message": "[23:55:20] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/nfr/general/2/details", "message_html": null, "is_string": false, "label": "debug", "time": 1755532520.09534, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755532519.734138, "end": 1755532520.216011, "duration": 0.4818730354309082, "duration_str": "482ms", "measures": [{"label": "Booting", "start": 1755532519.734138, "relative_start": 0, "end": 1755532520.009797, "relative_end": 1755532520.009797, "duration": 0.27565908432006836, "duration_str": "276ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755532520.009808, "relative_start": 0.27567005157470703, "end": 1755532520.216013, "relative_end": 1.9073486328125e-06, "duration": 0.20620489120483398, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25352432, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 19, "templates": [{"name": "nfr.viewGeneral (\\resources\\views\\nfr\\viewGeneral.blade.php)", "param_count": 7, "params": ["generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.dashboard (\\resources\\views\\inc\\dashboard.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\resources\\views\\vendor\\pagination\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "generalNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET nfr/general/{general_nfr_id}/details", "middleware": "web", "controller": "App\\Http\\Controllers\\GeneralNFRController@viewGeneral", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "nfr.viewGeneral", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\GeneralNFRController.php&line=47\">\\app\\Http\\Controllers\\GeneralNFRController.php:47-93</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00274, "accumulated_duration_str": "2.74ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 16.058}, {"sql": "select * from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\GeneralNFRController.php", "line": 52}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\app\\Http\\Controllers\\GeneralNFRController.php:52", "connection": "sagile", "start_percent": 16.058, "width_percent": 23.358}, {"sql": "select * from `projects`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\GeneralNFRController.php", "line": 62}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Http\\Controllers\\GeneralNFRController.php:62", "connection": "sagile", "start_percent": 39.416, "width_percent": 13.139}, {"sql": "select * from `generalnfr` where `general_nfr_id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\GeneralNFR.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\GeneralNFRController.php", "line": 69}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\GeneralNFR.php:36", "connection": "sagile", "start_percent": 52.555, "width_percent": 12.774}, {"sql": "select count(distinct `user_story_general_nfr`.`user_story_id`) as aggregate from `user_story_general_nfr` inner join `user_stories` on `user_story_general_nfr`.`user_story_id` = `user_stories`.`u_id` inner join `projects` on `user_stories`.`proj_id` = `projects`.`id` inner join `sprint` on `user_stories`.`sprint_id` = `sprint`.`sprint_id` where `user_story_general_nfr`.`general_nfr_id` = '2' and `user_stories`.`sprint_id` = '34'", "type": "query", "params": [], "bindings": ["2", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\UserStoryGeneralNfr.php", "line": 77}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\GeneralNFRController.php", "line": 80}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\app\\UserStoryGeneralNfr.php:77", "connection": "sagile", "start_percent": 65.328, "width_percent": 23.358}, {"sql": "select * from `sprint`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Sprint.php", "line": 76}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\GeneralNFRController.php", "line": 88}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Sprint.php:76", "connection": "sagile", "start_percent": 88.686, "width_percent": 11.314}]}, "models": {"data": {"App\\Sprint": 1, "App\\GeneralNFR": 1, "App\\Project": 1, "App\\TeamMapping": 2, "App\\User": 1}, "count": 6}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/nfr/general/2/details?project_filter=&sprint_filter=34\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/nfr/general/2/details", "status_code": "<pre class=sf-dump id=sf-dump-1781589687 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1781589687\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-114881289 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>project_filter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sprint_filter</span>\" => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-114881289\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1861164864 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>project_filter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sprint_filter</span>\" => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1861164864\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1484678250 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/nfr/general/2/details</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlI5ZDl0UG9Sd2dQcUNCNTd5VUlzdlE9PSIsInZhbHVlIjoiMU8wVUMyd0hwUVpnazJTVVhZdGtiMzUrUDJzNUpDc2g1akh4d0ZFckRobE4yQzVKUjNoZzhiNk1YdnJZeFYrTzZzcUNxL0taUWdTdVFqckZ0Z3NTOENDNDZrMzhZZnVsQjJSRVhQUEl1R2JMVU0yN0Qvam1jSFZqQ1lneFBoWUgiLCJtYWMiOiI1NDU5ZGM1MjBjMTMyMGZkMDViZTdlOGFkOGMxOWYzZTU5ZWU2OWViYjQ5OTQ1MmM2NmY3ZGNhMTRiYWU4ZWM3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlFJaFVjREVKNWlMemREdHNRVTVJUEE9PSIsInZhbHVlIjoiOVNJN3JvclpaZk5rcitrYXRVMXpOdHVUbyt2ellTNjFiL2VKTTVPWVFiWGhLS1dvcXNqNXBsMmZ1b3ViODlmQ1gwRExVWDZSQ1ozZldmUHdBTHkvZ3Z5S2Q3bW03NlJDMWxrL3M4SU53Wk1mM2Vwakk0Qk80b0xUL042ZjBsSDEiLCJtYWMiOiJiZjJkNDdiMzczNmM4N2Y2ZWY2ZWEyMjQwYmFhMWY5YjE3YzczNDNkM2M0ZGNkNzA1MGQ4ODcxOTdmNDE3NTA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1484678250\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1342377166 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52508</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"55 characters\">/nfr/general/2/details?project_filter=&amp;sprint_filter=34</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/nfr/general/2/details</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/index.php/nfr/general/2/details</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"32 characters\">project_filter=&amp;sprint_filter=34</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/nfr/general/2/details</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlI5ZDl0UG9Sd2dQcUNCNTd5VUlzdlE9PSIsInZhbHVlIjoiMU8wVUMyd0hwUVpnazJTVVhZdGtiMzUrUDJzNUpDc2g1akh4d0ZFckRobE4yQzVKUjNoZzhiNk1YdnJZeFYrTzZzcUNxL0taUWdTdVFqckZ0Z3NTOENDNDZrMzhZZnVsQjJSRVhQUEl1R2JMVU0yN0Qvam1jSFZqQ1lneFBoWUgiLCJtYWMiOiI1NDU5ZGM1MjBjMTMyMGZkMDViZTdlOGFkOGMxOWYzZTU5ZWU2OWViYjQ5OTQ1MmM2NmY3ZGNhMTRiYWU4ZWM3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlFJaFVjREVKNWlMemREdHNRVTVJUEE9PSIsInZhbHVlIjoiOVNJN3JvclpaZk5rcitrYXRVMXpOdHVUbyt2ellTNjFiL2VKTTVPWVFiWGhLS1dvcXNqNXBsMmZ1b3ViODlmQ1gwRExVWDZSQ1ozZldmUHdBTHkvZ3Z5S2Q3bW03NlJDMWxrL3M4SU53Wk1mM2Vwakk0Qk80b0xUL042ZjBsSDEiLCJtYWMiOiJiZjJkNDdiMzczNmM4N2Y2ZWY2ZWEyMjQwYmFhMWY5YjE3YzczNDNkM2M0ZGNkNzA1MGQ4ODcxOTdmNDE3NTA0IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755532519.7341</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755532519</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1342377166\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-176438712 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-176438712\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-221841818 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:55:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkxJSThWNWpuVHJUUkFlc2dlSCtER1E9PSIsInZhbHVlIjoiTjVHSXVnZDlHeHBFcUhHRnRZNjRWYTljRzRGZy9tcDdTZENqeWRiSFBua1VhK0Q3M2ZHcWhBSVVNRXZJQXVydGRFVlNhQ2lyNkRlc3JVbWFrSVAyR0J5ZWZSOEEwem5LSWNoQ2M5VzcyT29RTVJIQS9sejkxQUJvV3pxcm9xdi8iLCJtYWMiOiI3YzAyNjQwYjY3NzA4N2VkZjMyOTQwZGU0YzgwZWUzZGIzNTVmNzlkOWNlYzFmMTZkNWZlOWIzYTRjM2QzOGIxIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:55:20 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im1NS0oyZnk3ZTd2djNHWFdBelJubkE9PSIsInZhbHVlIjoieTFkZW5PYjFhWk85OUNENHF5Qi9QWi9nTEFFdnVRbkEzaFYwU3dyS0xwd2JuM0RTUWZRM1ZYcTFYUzBwYkRxTDVqY3dtSm4zQlNVL1JVRzRZZUFxVkJzaTdiMTlsVkZreitZTm9JZDF5cjdFNDB4S2VnQnRxQjVKUDFEYVVZZTciLCJtYWMiOiJkOGUyYWQ0MDA2MDRlNDZiYzg0Mzc2NmFmMjA0ZGRiNzVkYzlmYmM2MTlhMDU0NDViZjUzZGY1M2FiMTZmZTE3IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:55:20 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkxJSThWNWpuVHJUUkFlc2dlSCtER1E9PSIsInZhbHVlIjoiTjVHSXVnZDlHeHBFcUhHRnRZNjRWYTljRzRGZy9tcDdTZENqeWRiSFBua1VhK0Q3M2ZHcWhBSVVNRXZJQXVydGRFVlNhQ2lyNkRlc3JVbWFrSVAyR0J5ZWZSOEEwem5LSWNoQ2M5VzcyT29RTVJIQS9sejkxQUJvV3pxcm9xdi8iLCJtYWMiOiI3YzAyNjQwYjY3NzA4N2VkZjMyOTQwZGU0YzgwZWUzZGIzNTVmNzlkOWNlYzFmMTZkNWZlOWIzYTRjM2QzOGIxIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:55:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im1NS0oyZnk3ZTd2djNHWFdBelJubkE9PSIsInZhbHVlIjoieTFkZW5PYjFhWk85OUNENHF5Qi9QWi9nTEFFdnVRbkEzaFYwU3dyS0xwd2JuM0RTUWZRM1ZYcTFYUzBwYkRxTDVqY3dtSm4zQlNVL1JVRzRZZUFxVkJzaTdiMTlsVkZreitZTm9JZDF5cjdFNDB4S2VnQnRxQjVKUDFEYVVZZTciLCJtYWMiOiJkOGUyYWQ0MDA2MDRlNDZiYzg0Mzc2NmFmMjA0ZGRiNzVkYzlmYmM2MTlhMDU0NDViZjUzZGY1M2FiMTZmZTE3IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:55:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-221841818\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1519446477 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://127.0.0.1:8000/nfr/general/2/details?project_filter=&amp;sprint_filter=34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1519446477\", {\"maxDepth\":0})</script>\n"}}