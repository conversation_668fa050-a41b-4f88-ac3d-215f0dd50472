{"__meta": {"id": "X4a5274545111bf73a945e224a0c3ccff", "datetime": "2025-08-19 13:53:46", "utime": 1755582826.530243, "method": "GET", "uri": "/task/58/calendarTask", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[13:53:46] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755582826.376452, "xdebug_link": null, "collector": "log"}, {"message": "[13:53:46] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/task/58/calendarTask", "message_html": null, "is_string": false, "label": "debug", "time": 1755582826.436745, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755582826.089355, "end": 1755582826.530263, "duration": 0.44090795516967773, "duration_str": "441ms", "measures": [{"label": "Booting", "start": 1755582826.089355, "relative_start": 0, "end": 1755582826.3577, "relative_end": 1755582826.3577, "duration": 0.2683451175689697, "duration_str": "268ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755582826.357711, "relative_start": 0.2683560848236084, "end": 1755582826.530265, "relative_end": 2.1457672119140625e-06, "duration": 0.17255401611328125, "duration_str": "173ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23927536, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "tasks.calendarTask (\\resources\\views\\tasks\\calendarTask.blade.php)", "param_count": 0, "params": [], "type": "blade"}]}, "route": {"uri": "GET task/{userstory_id}/calendarTask", "middleware": "web", "controller": "App\\Http\\Controllers\\TaskController@indexCalendar", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "tasks.calendarTask", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TaskController.php&line=52\">\\app\\Http\\Controllers\\TaskController.php:52-69</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00209, "accumulated_duration_str": "2.09ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 22.967}, {"sql": "select `team_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 56}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:56", "connection": "sagile", "start_percent": 22.967, "width_percent": 18.182}, {"sql": "select * from `projects` where `team_name` in ('iv<PERSON>\\'s team', 'iv<PERSON>\\'s team', 'Team 888', 'Team 888', 'Team AD', 'Team AD')", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team", "ivlyn&#039;s team", "Team 888", "Team 888", "Team AD", "Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 57}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:57", "connection": "sagile", "start_percent": 41.148, "width_percent": 19.617}, {"sql": "select * from `tasks` where `userstory_id` = '58'", "type": "query", "params": [], "bindings": ["58"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 60}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:60", "connection": "sagile", "start_percent": 60.766, "width_percent": 21.531}, {"sql": "select * from `user_stories` where `u_id` = '58' limit 1", "type": "query", "params": [], "bindings": ["58"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 62}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:62", "connection": "sagile", "start_percent": 82.297, "width_percent": 17.703}]}, "models": {"data": {"App\\UserStory": 1, "App\\Task": 1, "App\\Project": 2, "App\\User": 1}, "count": 5}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/task/58/calendarTask\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/task/58/calendarTask", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-508112362 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-508112362\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-692313610 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-692313610\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1136522947 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/task/58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Img4eFVCV3ByV3VZRmVmNzU4RXFxbVE9PSIsInZhbHVlIjoibkhaWDE4cEUzYkdkSytrRjMvZ25tbkFMQmV1Zm1GRGZkYU1ubWZIeWQxUmJ5cUp0R0hCcGJ0S2kvNmt0bDgrWnBRbjdjSTZiSjR6ZERrWEp5YWJJbEtBYWJEUE9aUUZ1MjVxKzY0RVdzOGN1MGc5T09ncTVjYTNVaUE2RW1aeXUiLCJtYWMiOiJkNDNkZmNmNzBjM2M1NTM3OWVkZDE0ZjI1ZThmMjcyNGExMzkyYTg0ZDRiMjM3OWIxOWUwNzlmOWNhZTNjYTZhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImJKSldjdWpmYzFyNmU3OHp1cVMzUXc9PSIsInZhbHVlIjoieE1WZ3BTVU0rWHZiRWpPdzZkZDY0dlJqVVcrZWtKemduVFNwa0hrMHVlNG0wVXN3U1JUQ0J0V1RYb0hZTWdObUhReHFmU2hjejFzY2FiVDFmTk5CR2xEUzdUZktRQTQxL1NsRUIyYVJkdUE2UjZFZUIyN0JCemM0UldWbW5QbkYiLCJtYWMiOiJjZGQ1NTg5M2ZhY2U2NGU0MTFlOTNlNDA0NGY0MzYzMWUyYjJkMjcxOWU1NmQyNTZlYTUwZTNlNzJhNjMwNTlmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1136522947\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1985400389 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">63234</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/task/58/calendarTask</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/task/58/calendarTask</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/index.php/task/58/calendarTask</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/task/58</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Img4eFVCV3ByV3VZRmVmNzU4RXFxbVE9PSIsInZhbHVlIjoibkhaWDE4cEUzYkdkSytrRjMvZ25tbkFMQmV1Zm1GRGZkYU1ubWZIeWQxUmJ5cUp0R0hCcGJ0S2kvNmt0bDgrWnBRbjdjSTZiSjR6ZERrWEp5YWJJbEtBYWJEUE9aUUZ1MjVxKzY0RVdzOGN1MGc5T09ncTVjYTNVaUE2RW1aeXUiLCJtYWMiOiJkNDNkZmNmNzBjM2M1NTM3OWVkZDE0ZjI1ZThmMjcyNGExMzkyYTg0ZDRiMjM3OWIxOWUwNzlmOWNhZTNjYTZhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImJKSldjdWpmYzFyNmU3OHp1cVMzUXc9PSIsInZhbHVlIjoieE1WZ3BTVU0rWHZiRWpPdzZkZDY0dlJqVVcrZWtKemduVFNwa0hrMHVlNG0wVXN3U1JUQ0J0V1RYb0hZTWdObUhReHFmU2hjejFzY2FiVDFmTk5CR2xEUzdUZktRQTQxL1NsRUIyYVJkdUE2UjZFZUIyN0JCemM0UldWbW5QbkYiLCJtYWMiOiJjZGQ1NTg5M2ZhY2U2NGU0MTFlOTNlNDA0NGY0MzYzMWUyYjJkMjcxOWU1NmQyNTZlYTUwZTNlNzJhNjMwNTlmIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755582826.0894</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755582826</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985400389\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-777262053 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-777262053\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1039543720 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:53:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IklkSXhQSFpiMk45QTRKTnNqZXVUZ0E9PSIsInZhbHVlIjoiemZRK0hxNDF0Q3R1ODFXNHZ6U0JxVmFhOFNMVy81TjZybmwzYjZCaFJsVXVOWW1NV1dzOVp6bzN3cktzY09LanlXaVdRa0w0Qk90ek9FQVcrRko2RnBLOGVRWGlhNUJSc20xdWExdmNrYkpOMTNvWlRIbnF6VEk2UTRmVWtRSTMiLCJtYWMiOiIyY2EzMGFlZDYzMmI0OWM1ZTE3YjMzYWNkNWIyMzBiYjQ0MzVkZDZkMzZhOGZlMDUyN2EwOThlOTJiZDg0NjFjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:53:46 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InNSMG9uSXBkK1BuTEk4VGY1T1ZGS0E9PSIsInZhbHVlIjoiYTlLNnJLcmRyUTJuZkJ4b0N3Um1nTHlPQm9OZFBzWWFpOHc1RFJFSjZvZDNma21LVFNxbGxUSVd0VlNjWTZaektucjcrUTZmRXZLTWxzTXU2ZVZXWFRUTWYrWEZoM1BkeVBXODE1OGRuUzVQMy85MitvVE50TjVsK3ZrZ3draXIiLCJtYWMiOiI5ZTRlNjhiM2U2ZGFjOWI1Mjc3ODg1YzU4NTMzZGJkY2VlZGE1YmQ5NTliZGNjOTA4NDcyZjA4ODgwMjRiMTI4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:53:46 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IklkSXhQSFpiMk45QTRKTnNqZXVUZ0E9PSIsInZhbHVlIjoiemZRK0hxNDF0Q3R1ODFXNHZ6U0JxVmFhOFNMVy81TjZybmwzYjZCaFJsVXVOWW1NV1dzOVp6bzN3cktzY09LanlXaVdRa0w0Qk90ek9FQVcrRko2RnBLOGVRWGlhNUJSc20xdWExdmNrYkpOMTNvWlRIbnF6VEk2UTRmVWtRSTMiLCJtYWMiOiIyY2EzMGFlZDYzMmI0OWM1ZTE3YjMzYWNkNWIyMzBiYjQ0MzVkZDZkMzZhOGZlMDUyN2EwOThlOTJiZDg0NjFjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:53:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InNSMG9uSXBkK1BuTEk4VGY1T1ZGS0E9PSIsInZhbHVlIjoiYTlLNnJLcmRyUTJuZkJ4b0N3Um1nTHlPQm9OZFBzWWFpOHc1RFJFSjZvZDNma21LVFNxbGxUSVd0VlNjWTZaektucjcrUTZmRXZLTWxzTXU2ZVZXWFRUTWYrWEZoM1BkeVBXODE1OGRuUzVQMy85MitvVE50TjVsK3ZrZ3draXIiLCJtYWMiOiI5ZTRlNjhiM2U2ZGFjOWI1Mjc3ODg1YzU4NTMzZGJkY2VlZGE1YmQ5NTliZGNjOTA4NDcyZjA4ODgwMjRiMTI4IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:53:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1039543720\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-790991133 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/task/58/calendarTask</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-790991133\", {\"maxDepth\":0})</script>\n"}}