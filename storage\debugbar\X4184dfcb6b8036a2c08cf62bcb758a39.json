{"__meta": {"id": "X4184dfcb6b8036a2c08cf62bcb758a39", "datetime": "2025-08-19 13:52:23", "utime": 1755582743.067673, "method": "GET", "uri": "/status/43", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 56, "messages": [{"message": "[13:52:22] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755582742.918477, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:22] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/status/43", "message_html": null, "is_string": false, "label": "debug", "time": 1755582742.976809, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: add_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.032939, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.03393, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.033981, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.038149, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.038977, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.039026, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.03947, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.040293, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.040339, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.04068, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.041402, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.041448, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: delete_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.041726, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.042422, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.042467, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.042715, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.043403, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.043447, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.043659, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.04435, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.044395, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: delete_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.044637, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.04533, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.045427, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.045702, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.04667, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.046717, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.046987, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.047715, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.047759, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: delete_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.048028, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.048784, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.048827, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.049071, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.049772, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.049815, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: edit_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.050026, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.05071, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.050752, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: delete_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.050997, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.051759, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.05181, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: delete_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.052051, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.052843, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.052888, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: delete_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.05361, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.054427, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.054566, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: delete_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.054907, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.055636, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.055681, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Gate check for permission: delete_status on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.055985, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.056692, "xdebug_link": null, "collector": "log"}, {"message": "[13:52:23] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755582743.056735, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755582742.628952, "end": 1755582743.067749, "duration": 0.4387969970703125, "duration_str": "439ms", "measures": [{"label": "Booting", "start": 1755582742.628952, "relative_start": 0, "end": 1755582742.89855, "relative_end": 1755582742.89855, "duration": 0.26959800720214844, "duration_str": "270ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755582742.898561, "relative_start": 0.2696089744567871, "end": 1755582743.067751, "relative_end": 1.9073486328125e-06, "duration": 0.1691899299621582, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24015264, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "status.project (\\resources\\views\\status\\project.blade.php)", "param_count": 4, "params": ["statuses", "title", "pros", "project"], "type": "blade"}]}, "route": {"uri": "GET status/{proj_ID}", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\StatusController@indexProjectStatus", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "status.project", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\StatusController.php&line=33\">\\app\\Http\\Controllers\\StatusController.php:33-50</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00216, "accumulated_duration_str": "2.16ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 19.444}, {"sql": "select `team_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 37}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:37", "connection": "sagile", "start_percent": 19.444, "width_percent": 18.056}, {"sql": "select * from `projects` where `team_name` in ('iv<PERSON>\\'s team', 'iv<PERSON>\\'s team', 'Team 888', 'Team 888', 'Team AD', 'Team AD')", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team", "ivlyn&#039;s team", "Team 888", "Team 888", "Team AD", "Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 38}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:38", "connection": "sagile", "start_percent": 37.5, "width_percent": 21.759}, {"sql": "select * from `projects` where `id` = '43' limit 1", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 40}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:40", "connection": "sagile", "start_percent": 59.259, "width_percent": 17.593}, {"sql": "select * from `statuses` where `project_id` = '43' order by `order` asc", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\StatusController.php", "line": 43}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Http\\Controllers\\StatusController.php:43", "connection": "sagile", "start_percent": 76.852, "width_percent": 23.148}]}, "models": {"data": {"App\\Status": 4, "App\\Project": 3, "App\\User": 1}, "count": 8}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 18, "messages": [{"message": "[\n  ability => add_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">add_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.036859, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-496678852 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-496678852\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.039269, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1948932492 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1948932492\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.040464, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-41002438 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-41002438\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.041558, "xdebug_link": null}, {"message": "[\n  ability => delete_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-290988276 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-290988276\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.042574, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1009451442 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1009451442\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.04355, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2125083619 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2125083619\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.044496, "xdebug_link": null}, {"message": "[\n  ability => delete_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1687776993 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1687776993\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.045564, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-824578288 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-824578288\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.046864, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-839160332 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-839160332\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.047868, "xdebug_link": null}, {"message": "[\n  ability => delete_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-538499407 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-538499407\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.048931, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-252301037 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-252301037\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.049918, "xdebug_link": null}, {"message": "[\n  ability => edit_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-370520832 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-370520832\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.050854, "xdebug_link": null}, {"message": "[\n  ability => delete_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-217811574 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-217811574\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.051918, "xdebug_link": null}, {"message": "[\n  ability => delete_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-586987557 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-586987557\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.052996, "xdebug_link": null}, {"message": "[\n  ability => delete_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-806682745 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-806682745\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.054693, "xdebug_link": null}, {"message": "[\n  ability => delete_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2107181102 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2107181102\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.055796, "xdebug_link": null}, {"message": "[\n  ability => delete_status,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-597792671 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-597792671\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755582743.056845, "xdebug_link": null}]}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/status/43\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755581799\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/status/43", "status_code": "<pre class=sf-dump id=sf-dump-1383457591 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1383457591\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-490871090 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-490871090\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1597667692 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1597667692\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-132037360 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlJjT1puOWF2Z2QyajVjYWhLUXgvV1E9PSIsInZhbHVlIjoiTkxUSVRkaUFPbnVjQ0hiZ0JaVmUyREdHd01VRFlkMytraGx5UzlLMDdDcEllWDFKT0lLb1J4VW9ZMkRMZEpyaVJXL1BmZ3A5dGNtYW9PRXplMjRKamlmV2JZaVBZdW8yaTZrNG43UmMzSVJMSnZwRjg3RGYwcWI4aTRFb0tRa2QiLCJtYWMiOiJhOGUzN2IyNTFlNjY3YjViZmZmZTUwYzhiOTFhNGJkY2FhYmYxYTA4NjE2MjgzYWQ2OWM3NThlNGFmNmVjZjM5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlhyY0tFcXFJOE5CSm9waXFCU3puZEE9PSIsInZhbHVlIjoiRTRmRDJqQUVOUzVBN2phUU1xblQ2OHBNNFdlemdBcGg4a0puWm5KYitkRDhPQjRjT2pvMEtwNStKaWQvbFA4WDBGQTdoT0JPTS9ZRW1qQ2VjcmpHbHlpU1B6cWgzazlBSlBnOWFaN2M1N2dld29OUzZnbHV0dWZ3NXJoYjkzUnMiLCJtYWMiOiI0YzZhMjNhOWEyYzVkYmIxODdlOGM0MTE4YmQ1YzlmZDExYWNiMGFlYWYzZTdmZDI5NmFhMjM0NGJhNTNlM2ViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132037360\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1464649357 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">50892</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/status/43</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/status/43</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/index.php/status/43</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/43</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlJjT1puOWF2Z2QyajVjYWhLUXgvV1E9PSIsInZhbHVlIjoiTkxUSVRkaUFPbnVjQ0hiZ0JaVmUyREdHd01VRFlkMytraGx5UzlLMDdDcEllWDFKT0lLb1J4VW9ZMkRMZEpyaVJXL1BmZ3A5dGNtYW9PRXplMjRKamlmV2JZaVBZdW8yaTZrNG43UmMzSVJMSnZwRjg3RGYwcWI4aTRFb0tRa2QiLCJtYWMiOiJhOGUzN2IyNTFlNjY3YjViZmZmZTUwYzhiOTFhNGJkY2FhYmYxYTA4NjE2MjgzYWQ2OWM3NThlNGFmNmVjZjM5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlhyY0tFcXFJOE5CSm9waXFCU3puZEE9PSIsInZhbHVlIjoiRTRmRDJqQUVOUzVBN2phUU1xblQ2OHBNNFdlemdBcGg4a0puWm5KYitkRDhPQjRjT2pvMEtwNStKaWQvbFA4WDBGQTdoT0JPTS9ZRW1qQ2VjcmpHbHlpU1B6cWgzazlBSlBnOWFaN2M1N2dld29OUzZnbHV0dWZ3NXJoYjkzUnMiLCJtYWMiOiI0YzZhMjNhOWEyYzVkYmIxODdlOGM0MTE4YmQ1YzlmZDExYWNiMGFlYWYzZTdmZDI5NmFhMjM0NGJhNTNlM2ViIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755582742.629</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755582742</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1464649357\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1730910918 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730910918\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-92530207 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:52:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkhZNzhpTEdDVG50OC9kVjZtODRTN3c9PSIsInZhbHVlIjoiK3VyK3UyQUxUTU8zdGRPVk9KSDNVOWhDdUdtUGpnMHNQT0hzYVJ5QU5BV1owRVNUVjg3ZEhudVVZVXdVbGRBTFdNVVJSUGU3WWt0bDU4Tlg3OGR2M2M5SW5KamxGSFYwYUU4Mzd0ZldxWE9va2Y4UWRNVkpBSEZXL2xmQ0NLVFAiLCJtYWMiOiI5MDJmNDM0NzRiZWRiNDk1N2Q1ZDVlZDg0NjQ5ZmFhZTdhNjI1ZTc5Mjc3YTYwZmNiZDFjYTJhM2Y3ZjIyN2ZhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:52:23 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Ikgzc3dWUVFqRFBiT3NPQ2JPVUQvTUE9PSIsInZhbHVlIjoibVZ4WjFXT0s4bFIwMHN5VzViQjMxM0YwbFpsVUF3VXdNandrRVNBWjU2VDMxWTZITWh6bmpFY09qUHdYYm5vTUVCdDUwMm1hYWl4aWR4cHIxSmg1eFNRZkU5LzIzWnRCRzlVRS9QbEU5S1lzQStNUzJsbE1TWGRWNkhCMGpJcWEiLCJtYWMiOiJkNGRiZWI3YmQ0NTg5Y2FiNTQ5NzQ5YWI5MGY2ZWViNmI0ZmZlOTc2OTJjNDE4ZTBmNzRmNTZlNmM0OGM0M2IxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:52:23 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkhZNzhpTEdDVG50OC9kVjZtODRTN3c9PSIsInZhbHVlIjoiK3VyK3UyQUxUTU8zdGRPVk9KSDNVOWhDdUdtUGpnMHNQT0hzYVJ5QU5BV1owRVNUVjg3ZEhudVVZVXdVbGRBTFdNVVJSUGU3WWt0bDU4Tlg3OGR2M2M5SW5KamxGSFYwYUU4Mzd0ZldxWE9va2Y4UWRNVkpBSEZXL2xmQ0NLVFAiLCJtYWMiOiI5MDJmNDM0NzRiZWRiNDk1N2Q1ZDVlZDg0NjQ5ZmFhZTdhNjI1ZTc5Mjc3YTYwZmNiZDFjYTJhM2Y3ZjIyN2ZhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:52:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Ikgzc3dWUVFqRFBiT3NPQ2JPVUQvTUE9PSIsInZhbHVlIjoibVZ4WjFXT0s4bFIwMHN5VzViQjMxM0YwbFpsVUF3VXdNandrRVNBWjU2VDMxWTZITWh6bmpFY09qUHdYYm5vTUVCdDUwMm1hYWl4aWR4cHIxSmg1eFNRZkU5LzIzWnRCRzlVRS9QbEU5S1lzQStNUzJsbE1TWGRWNkhCMGpJcWEiLCJtYWMiOiJkNGRiZWI3YmQ0NTg5Y2FiNTQ5NzQ5YWI5MGY2ZWViNmI0ZmZlOTc2OTJjNDE4ZTBmNzRmNTZlNmM0OGM0M2IxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:52:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-92530207\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-537928079 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/status/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755581799</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-537928079\", {\"maxDepth\":0})</script>\n"}}