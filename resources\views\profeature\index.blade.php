@extends('layouts.app2')

@include('inc.style')
@include('inc.success')
@include('inc.dashboard')
@include('inc.navbar')

@section('content')
    @include('inc.title')

    <link rel="stylesheet" href="{{ asset('bootstrap.min.css') }}">

    <br>

    <!-- Check if projects exist -->
    @if ($pros->isEmpty())
        <div class="alert alert-warning">
            <h3>There are no projects yet.</h3>
        </div>
    @else
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Title</th>
                    <th>Description</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                    <th>Team</th>
                    <th>Backlog</th>
                    <th>Sprint</th>
                    <th>Forum</th>
                    <th>Bug Tracking</th>
                    <th>Edit</th>
                    <th>Delete</th>
                </tr>
            </thead>
            <tbody>
                <!-- Loop through existing projects -->
                @foreach($pros as $project)
                    <tr>
                        <td>{{ $project->proj_name }}</td>
                        <td>{{ $project->proj_desc }}</td>
                        <td>{{ date('d F Y', strtotime($project->start_date)) }}</td>
                        <td>{{ date('d F Y', strtotime($project->end_date)) }}</td>
                        <td><a href="{{ route('team.index') }}">{{ $project->team_name }}</a></td>
                        
                        <td>
                            <a class="btn btn-primary" href="{{ route('backlog.index', $project->id) }}">View</a>
                        </td>
                        <td>
                            <a class="btn btn-primary" href="{{ action('ProductFeatureController@index2', $project->proj_name) }}">View</a>
                        </td>
                        <td>
                            <a class="btn btn-primary" href="{{ route('forum.index', ['projectId' => $project->id]) }}">View</a>
                        </td>
                        <td>
                            <a class="btn btn-primary" href="{{ route('bugtrack.index', ['projectId' => $project->id]) }}">View</a>
                        </td>

                        <td>
                            <a class="btn btn-secondary" href="{{ route('project.edit', $project->id) }}">Edit</a>
                        </td>
                        <td>
                            <form action="{{ route('project.destroy', $project->id) }}" method="GET" style="display:inline;">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this project?');">Delete</button>
                            </form>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @endif

    <br>
    <!-- Button to create a new project -->
    <a href="{{ route('project.create') }}" class="btn btn-success">Add Project</a>

<h3 class="text-danger mt-4"> Warning </h3>
<p class="text-danger mt-4">DO NOT CREATE THE PROJECT FOR ANYONE ELSE AND ONLY PICK THE ONE THAT YOU HAVE BEEN ASSIGNED!
</p>
<h6> Please create the project only for you and your assigned team as you have logged into the system. Otherwise, the
    table cannot be updated normally.</h6>
<p>For example, if your name is Abu and your assigned team is Team1, you can create project but be sure to click on your
    team, Team1.</p>
@endsection