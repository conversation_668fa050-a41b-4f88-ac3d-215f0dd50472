{"__meta": {"id": "X2b10985f37c9505c466828a1629dbb73", "datetime": "2025-08-19 10:56:48", "utime": 1755572208.018648, "method": "POST", "uri": "/upload-radar-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[10:56:47] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572207.891079, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:48] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/upload-radar-chart", "message_html": null, "is_string": false, "label": "debug", "time": 1755572208.003296, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572207.276735, "end": 1755572208.018675, "duration": 0.7419400215148926, "duration_str": "742ms", "measures": [{"label": "Booting", "start": 1755572207.276735, "relative_start": 0, "end": 1755572207.853688, "relative_end": 1755572207.853688, "duration": 0.5769529342651367, "duration_str": "577ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572207.853702, "relative_start": 0.5769670009613037, "end": 1755572208.018678, "relative_end": 2.86102294921875e-06, "duration": 0.16497588157653809, "duration_str": "165ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23475816, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST upload-radar-chart", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@uploadRadarChart", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=141\">\\app\\Http\\Controllers\\CIGController.php:141-151</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.0008900000000000001, "accumulated_duration_str": "890μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0008900000000000001, "duration_str": "890μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/radar-data/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"path_info": "/upload-radar-chart", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1452832071 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"63914 characters\">data:image/png;base64,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</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1452832071\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1283724605 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">63926</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InFnZEZKMTZSWUtRQWIzbXZFMFY5alE9PSIsInZhbHVlIjoid0JYdG9xL1VCeE9HRU5ac3YzVnc1dVpvZXo2VEpHcFFCazg4NjJhVndDVVgzYWNIeGsxVWZWeUlEaVZTdHBNbDNhQTE5TU9tL0tsN2JxZ3pLdEJBZ21FTWIxc3JqbGhiYTBEZHphQTY3bE9xMzBHb201b1RkczBiMzBqTzM5U1AiLCJtYWMiOiIyNWE5YzYxOTNmYWIzNTZmNDQwNWM3ZDk3MmM5ZTM1YWMyNTAzOTFlNzRjNGJmZjY5OGE4ZDJkZTNlMTAyY2JmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik0vVkJDbHFCM2RHOVNzSDBpc3dTT3c9PSIsInZhbHVlIjoiNUE1b1ljTFl3ZnVHQy9sUTN4TkRNNEQxYkZYaGdhOEpndTNxdE5uRkxjZUZLblhBbkJBNmVZZVR0VVRZL3VxdXZ6WERmNFhEYXRCc040ZjBLSzE1Wi9jR3ZMb0tMNXJ0SmdlN09XdUhsSUN1MmFULzZXWUM2NS9WenpSNFkwRzciLCJtYWMiOiJjNDk5YzgwY2MyNjQ0NmRmMjE1ODljYTA3NWRmOGU2YTJiZDU1OGZkMzE0MTYwYmVlM2Q4ZDYxMGUxN2ZjM2JjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1283724605\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-580716133 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57548</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/index.php/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">63926</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">63926</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InFnZEZKMTZSWUtRQWIzbXZFMFY5alE9PSIsInZhbHVlIjoid0JYdG9xL1VCeE9HRU5ac3YzVnc1dVpvZXo2VEpHcFFCazg4NjJhVndDVVgzYWNIeGsxVWZWeUlEaVZTdHBNbDNhQTE5TU9tL0tsN2JxZ3pLdEJBZ21FTWIxc3JqbGhiYTBEZHphQTY3bE9xMzBHb201b1RkczBiMzBqTzM5U1AiLCJtYWMiOiIyNWE5YzYxOTNmYWIzNTZmNDQwNWM3ZDk3MmM5ZTM1YWMyNTAzOTFlNzRjNGJmZjY5OGE4ZDJkZTNlMTAyY2JmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik0vVkJDbHFCM2RHOVNzSDBpc3dTT3c9PSIsInZhbHVlIjoiNUE1b1ljTFl3ZnVHQy9sUTN4TkRNNEQxYkZYaGdhOEpndTNxdE5uRkxjZUZLblhBbkJBNmVZZVR0VVRZL3VxdXZ6WERmNFhEYXRCc040ZjBLSzE1Wi9jR3ZMb0tMNXJ0SmdlN09XdUhsSUN1MmFULzZXWUM2NS9WenpSNFkwRzciLCJtYWMiOiJjNDk5YzgwY2MyNjQ0NmRmMjE1ODljYTA3NWRmOGU2YTJiZDU1OGZkMzE0MTYwYmVlM2Q4ZDYxMGUxN2ZjM2JjIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572207.2767</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572207</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-580716133\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-727912532 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727912532\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1010222382 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:56:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im0vYVdBR25NdzdsT0x5a2NxNGhWb2c9PSIsInZhbHVlIjoiSXRzMHUzZ0dwcnZub25wcG5GSXI0VUgrbnZTdjhWMTBPR1NwWllsbEQwa1NkT2FuSWVrR0lUUFFRRm9EeEZacnFkYjFuNEJ5aW9ETzE5OUs3Z3E4TmVBS0tkbWlQNUdBaXVhRG5OVVJYUXg2anFzRlpRTkQ4bnJiZ3pZVFdyNDYiLCJtYWMiOiI1MDY0Y2YyYWQyZGQ1Y2QwZGE0N2RlOTc5YTNjYjk3OWU2NGFhYmJjNzg1Mzg4NDY3NjAzMWFkMTI1ZThjZDhiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:48 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImppN1RJaWFtbHhNRzJEdG9Ib3l3VlE9PSIsInZhbHVlIjoicXNlYUZDQzdsU1ZTMGhyTks4ZjdFVXBEZ3FWbFNEQWxzSXlRUnpRdno0UUxabUoxc3FscnBqV05Pb0d4QWRkVG9DVUQ3bnAyQUdsbVBmaU1SUEplV1Exeko1TVBseXZYeDRxTHJxQW5uNHhia3NBNFZvTnFJRW1uSHZUYk1RcnAiLCJtYWMiOiI1ODNiMzI3OWM4M2UzMzhlN2JmODhjODk4MzAxMzE5ZDVjYWM2ZjA5YzQwMWU3MDc1Y2FlOTNmMWE5ZWRiZDMxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:48 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im0vYVdBR25NdzdsT0x5a2NxNGhWb2c9PSIsInZhbHVlIjoiSXRzMHUzZ0dwcnZub25wcG5GSXI0VUgrbnZTdjhWMTBPR1NwWllsbEQwa1NkT2FuSWVrR0lUUFFRRm9EeEZacnFkYjFuNEJ5aW9ETzE5OUs3Z3E4TmVBS0tkbWlQNUdBaXVhRG5OVVJYUXg2anFzRlpRTkQ4bnJiZ3pZVFdyNDYiLCJtYWMiOiI1MDY0Y2YyYWQyZGQ1Y2QwZGE0N2RlOTc5YTNjYjk3OWU2NGFhYmJjNzg1Mzg4NDY3NjAzMWFkMTI1ZThjZDhiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImppN1RJaWFtbHhNRzJEdG9Ib3l3VlE9PSIsInZhbHVlIjoicXNlYUZDQzdsU1ZTMGhyTks4ZjdFVXBEZ3FWbFNEQWxzSXlRUnpRdno0UUxabUoxc3FscnBqV05Pb0d4QWRkVG9DVUQ3bnAyQUdsbVBmaU1SUEplV1Exeko1TVBseXZYeDRxTHJxQW5uNHhia3NBNFZvTnFJRW1uSHZUYk1RcnAiLCJtYWMiOiI1ODNiMzI3OWM4M2UzMzhlN2JmODhjODk4MzAxMzE5ZDVjYWM2ZjA5YzQwMWU3MDc1Y2FlOTNmMWE5ZWRiZDMxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010222382\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/radar-data/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}