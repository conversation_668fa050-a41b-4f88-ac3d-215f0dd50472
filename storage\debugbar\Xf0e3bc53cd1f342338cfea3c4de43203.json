{"__meta": {"id": "Xf0e3bc53cd1f342338cfea3c4de43203", "datetime": "2025-08-18 23:01:11", "utime": 1755529271.481571, "method": "GET", "uri": "/teammapping/ivlyn%27s%20team", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:01:10] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755529270.870627, "xdebug_link": null, "collector": "log"}, {"message": "[23:01:11] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/teammapping/ivlyn%27s%20team", "message_html": null, "is_string": false, "label": "debug", "time": 1755529271.009318, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755529270.177221, "end": 1755529271.481607, "duration": 1.3043859004974365, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": 1755529270.177221, "relative_start": 0, "end": 1755529270.825506, "relative_end": 1755529270.825506, "duration": 0.648284912109375, "duration_str": "648ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755529270.825527, "relative_start": 0.6483058929443359, "end": 1755529271.481611, "relative_end": 4.0531158447265625e-06, "duration": 0.6560840606689453, "duration_str": "656ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25472416, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 15, "templates": [{"name": "teammapping.index (\\resources\\views\\teammapping\\index.blade.php)", "param_count": 3, "params": ["teammappings", "teams", "title"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET teammapping/{team_name}", "middleware": "web", "controller": "App\\Http\\Controllers\\TeamMappingController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "teammapping.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TeamMappingController.php&line=59\">\\app\\Http\\Controllers\\TeamMappingController.php:59-76</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00336, "accumulated_duration_str": "3.36ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 28.869}, {"sql": "select * from `teams` where `team_name` = 'ivlyn\\'s team' limit 1", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 64}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:64", "connection": "sagile", "start_percent": 28.869, "width_percent": 24.405}, {"sql": "select * from `teammappings` where `project_id` is null and `team_name` = 'ivlyn\\'s team'", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 70}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:70", "connection": "sagile", "start_percent": 53.274, "width_percent": 24.702}, {"sql": "select exists(select * from `teammappings` where `team_name` = 'ivlyn\\'s team' and `username` = 'ivlyn' and `role_name` = 'Team Manager' and `invitation_status` = 'accepted') as `exists`", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team", "ivlyn", "Team Manager", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": "view", "name": "4dd4546e8c5ba9283dc8de130df2adb90e812ffc", "line": 74}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 15, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.00074, "duration_str": "740μs", "stmt_id": "view::4dd4546e8c5ba9283dc8de130df2adb90e812ffc:74", "connection": "sagile", "start_percent": 77.976, "width_percent": 22.024}]}, "models": {"data": {"App\\TeamMapping": 1, "App\\Team": 1, "App\\User": 1}, "count": 3}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/teammapping/ivlyn%27s%20team\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755527110\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/teammapping/ivlyn%27s%20team", "status_code": "<pre class=sf-dump id=sf-dump-986221219 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-986221219\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1892704313 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1892704313\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1817722158 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1817722158\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1485971034 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/team</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImtmcFZCeXlIV0xmMWQvVXh5c24va1E9PSIsInZhbHVlIjoieHJ0MUR5R2JidmVDMHdVSE5WcjBEMlI1b0g5cnU3VG56ejFlMy9oRnZlZ0dGZlFBcmJUdTZ5Y0tQMXUzRk1wK1lhTW5tVE91U2wxME9BakJhMDN1b2JJVERYNFlBdXJjNXpLYXBhQktnNWhhRmc3aVVFMDNWNGdSdzF2RHp2bE4iLCJtYWMiOiI4Mzk1ZmY2NmZlNDUwZTBlNzU0NTFhNjNiMzljNDBjOTA0ZTE1NDYwMDdlZjMxZjRmNzZmMDI4NDc4YzFhODZjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Imo3ZzNwdUNRSDlweVpSUTNPOWlTSnc9PSIsInZhbHVlIjoiQU5TYTdDMU83Z2VWS2FjRmdJM2ZFN3M0RjlEVGVjNUhSRXZ3WVZFNE9rVmk5dGpOZzNnb2ZpencyanoxVGNoc1IrbExmMzZJY2F0TUxJZU05cjI0aGdGQ3Nrb2xFSTNKOWI3VldRSjZJSXo1U0pxTkR6RjYxNGcwamhyeE9PVGsiLCJtYWMiOiI2MzA2MDJmNDFmMDZiNmUzMjg0YjMwYWMxNTgxODYwMTJjMzIwNmFkOTNkODVjNDlmNTljN2RhZGY1MTkwN2FlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1485971034\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1265859883 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60825</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/teammapping/ivlyn%27s%20team</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/teammapping/ivlyn&#039;s team</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"35 characters\">/index.php/teammapping/ivlyn&#039;s team</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/team</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImtmcFZCeXlIV0xmMWQvVXh5c24va1E9PSIsInZhbHVlIjoieHJ0MUR5R2JidmVDMHdVSE5WcjBEMlI1b0g5cnU3VG56ejFlMy9oRnZlZ0dGZlFBcmJUdTZ5Y0tQMXUzRk1wK1lhTW5tVE91U2wxME9BakJhMDN1b2JJVERYNFlBdXJjNXpLYXBhQktnNWhhRmc3aVVFMDNWNGdSdzF2RHp2bE4iLCJtYWMiOiI4Mzk1ZmY2NmZlNDUwZTBlNzU0NTFhNjNiMzljNDBjOTA0ZTE1NDYwMDdlZjMxZjRmNzZmMDI4NDc4YzFhODZjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Imo3ZzNwdUNRSDlweVpSUTNPOWlTSnc9PSIsInZhbHVlIjoiQU5TYTdDMU83Z2VWS2FjRmdJM2ZFN3M0RjlEVGVjNUhSRXZ3WVZFNE9rVmk5dGpOZzNnb2ZpencyanoxVGNoc1IrbExmMzZJY2F0TUxJZU05cjI0aGdGQ3Nrb2xFSTNKOWI3VldRSjZJSXo1U0pxTkR6RjYxNGcwamhyeE9PVGsiLCJtYWMiOiI2MzA2MDJmNDFmMDZiNmUzMjg0YjMwYWMxNTgxODYwMTJjMzIwNmFkOTNkODVjNDlmNTljN2RhZGY1MTkwN2FlIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755529270.1772</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755529270</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1265859883\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1546153794 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1546153794\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1640346192 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:01:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImowWjQrTThvenJqbFVQdnNHYVpDS3c9PSIsInZhbHVlIjoiZlRRWU9QNlhRSlJpbTNrWm04M2tONjBKNkJWR3FjUmwxT054VHhFN1NnVFBmSmRja2FraEQxNDJveUZsVzZBOEZqQlFLSzhqelNieGo3dFEzK0RFYlI3TkRIREdUWGVqblhRdjdpZHg2NVR2U2lhOEhqN2UrM0t6VXc3ckRocGYiLCJtYWMiOiI4ZjEwMDlmODA1YWUyNjlmZDA5OTUwYzQ4OWJiYjhlMDJhM2UxYTUyNWFlZDYwM2MzNDNlN2ZlNjdiNWI4ZjM5IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:01:11 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlFodUVjcnVuU25QcVVjb2dxT0RKSVE9PSIsInZhbHVlIjoiYTg2eVlPNGJEU1lRamhBaTVnK2l4dnhqRUlsMkx1R1NlcjYwN3VPNE1VYjNJUDVDYUQzTmdsTTQ1WE41NDdkcXVGbjk1dXpHVmd3aXNRZlRzbnFyRFczNVAwWm9GRy9sdWwzNHd2U3FybnhtK3ljWU10TzAxNzlSZzVxYmcwOTciLCJtYWMiOiJjYTU3Y2QyMmRmZGIzMDJhYTFkZGQzZmU2NTkzYzA1OGEwNTI2OTM2OTY3YzQzODNhNjUwMmY2OTA0NTZhZGUzIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:01:11 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImowWjQrTThvenJqbFVQdnNHYVpDS3c9PSIsInZhbHVlIjoiZlRRWU9QNlhRSlJpbTNrWm04M2tONjBKNkJWR3FjUmwxT054VHhFN1NnVFBmSmRja2FraEQxNDJveUZsVzZBOEZqQlFLSzhqelNieGo3dFEzK0RFYlI3TkRIREdUWGVqblhRdjdpZHg2NVR2U2lhOEhqN2UrM0t6VXc3ckRocGYiLCJtYWMiOiI4ZjEwMDlmODA1YWUyNjlmZDA5OTUwYzQ4OWJiYjhlMDJhM2UxYTUyNWFlZDYwM2MzNDNlN2ZlNjdiNWI4ZjM5IiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:01:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlFodUVjcnVuU25QcVVjb2dxT0RKSVE9PSIsInZhbHVlIjoiYTg2eVlPNGJEU1lRamhBaTVnK2l4dnhqRUlsMkx1R1NlcjYwN3VPNE1VYjNJUDVDYUQzTmdsTTQ1WE41NDdkcXVGbjk1dXpHVmd3aXNRZlRzbnFyRFczNVAwWm9GRy9sdWwzNHd2U3FybnhtK3ljWU10TzAxNzlSZzVxYmcwOTciLCJtYWMiOiJjYTU3Y2QyMmRmZGIzMDJhYTFkZGQzZmU2NTkzYzA1OGEwNTI2OTM2OTY3YzQzODNhNjUwMmY2OTA0NTZhZGUzIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:01:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1640346192\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1646374229 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"50 characters\">http://127.0.0.1:8000/teammapping/ivlyn%27s%20team</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755527110</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1646374229\", {\"maxDepth\":0})</script>\n"}}