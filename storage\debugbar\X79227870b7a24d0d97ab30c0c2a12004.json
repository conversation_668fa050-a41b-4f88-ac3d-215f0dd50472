{"__meta": {"id": "X79227870b7a24d0d97ab30c0c2a12004", "datetime": "2025-08-19 14:06:55", "utime": 1755583615.286862, "method": "GET", "uri": "/projects", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[14:06:55] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755583615.151246, "xdebug_link": null, "collector": "log"}, {"message": "[14:06:55] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/projects", "message_html": null, "is_string": false, "label": "debug", "time": 1755583615.211926, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755583614.833946, "end": 1755583615.286884, "duration": 0.4529380798339844, "duration_str": "453ms", "measures": [{"label": "Booting", "start": 1755583614.833946, "relative_start": 0, "end": 1755583615.131699, "relative_end": 1755583615.131699, "duration": 0.29775309562683105, "duration_str": "298ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755583615.13171, "relative_start": 0.2977640628814697, "end": 1755583615.286886, "relative_end": 1.9073486328125e-06, "duration": 0.15517592430114746, "duration_str": "155ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23833920, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 9, "templates": [{"name": "project.newIndex (\\resources\\views\\project\\newIndex.blade.php)", "param_count": 2, "params": ["projects", "pendingInvitations"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "projects", "pendingInvitations", "__empty_1", "__currentLoopData", "project", "loop"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "projects", "pendingInvitations", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "projects", "pendingInvitations", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "projects", "pendingInvitations", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 18, "params": ["__env", "app", "menuData", "errors", "projects", "pendingInvitations", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "projects", "pendingInvitations", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "projects", "pendingInvitations", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 19, "params": ["__env", "app", "menuData", "errors", "projects", "pendingInvitations", "__empty_1", "__currentLoopData", "project", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET projects", "middleware": "web", "controller": "App\\Http\\Controllers\\ProductFeatureController@indexReloaded", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "project.newIndex", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProductFeatureController.php&line=70\">\\app\\Http\\Controllers\\ProductFeatureController.php:70-96</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00182, "accumulated_duration_str": "1.82ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 26.374}, {"sql": "select `project_id` from `teammappings` where `username` = 'ivlyn' and `invitation_status` = 'accepted' and `project_id` is not null", "type": "query", "params": [], "bindings": ["ivlyn", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 79}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:79", "connection": "sagile", "start_percent": 26.374, "width_percent": 25.275}, {"sql": "select * from `teammappings` where `username` = 'ivlyn' and `invitation_status` = 'pending' and `project_id` is not null", "type": "query", "params": [], "bindings": ["ivlyn", "pending"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 86}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:86", "connection": "sagile", "start_percent": 51.648, "width_percent": 22.527}, {"sql": "select * from `projects` where `id` in (42, 43, 44, 45, 45)", "type": "query", "params": [], "bindings": ["42", "43", "44", "45", "45"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 89}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:89", "connection": "sagile", "start_percent": 74.176, "width_percent": 25.824}]}, "models": {"data": {"App\\Project": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/projects\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/projects", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-49877975 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-49877975\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-175525471 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-175525471\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-648764899 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/teams/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjJrQVd2Z0E0OTlBbGpzUERwd1ZlNXc9PSIsInZhbHVlIjoiYVpTTWo2N25MdUJoTzAxVVgwRWtFOTE4RjZ5REtjNkNwa2F5YlRCR0hqMkpYWEQxdVpqZk9KOG5LVGE4Qm5iUy9YNUpjYzJGSlh4aVlOWkJTUkZzLy9ZMEVKeGJWczVPVCtuM2ZQNFkrRlU5cEVlTGlJTTlBVWhZV2pER1paR04iLCJtYWMiOiI0NTBjZDI2MjVjNmIzYzllY2U5NWUzZTZhNTI0MzY2Y2IyY2FiMDk5NWE4NjIzNTk3YzNjMTY5OWZkYTIxODYwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkkvcDk2TVMraDlJNlRZdldaR2VBM3c9PSIsInZhbHVlIjoieXlNWE5CRnN2QmlZOUc0Z0dvUi8wQnpGOFJZZktpOHYyZDVacUEweDlWTTNXYzQ4eGw5TmdGUkpLUXRxUXNkbWY4QnZrRGU1YldSTXpaaUpTSXZWKzBzWHV4SkJtb3Q5TG9sUm51WG1yQWk5UXB2OG9yaENQUDN0MnE1YmZORVEiLCJtYWMiOiJmYjlmY2UxMTBmNDI3Njk1NDUyMDlhYmY5Yzg3NDg3NGFiZmE1MzhiNDRlNGE4NTljZDIyMDYxNGZhZWNjYjE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-648764899\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-739703095 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">63934</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/projects</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/projects</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/index.php/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/teams/create</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjJrQVd2Z0E0OTlBbGpzUERwd1ZlNXc9PSIsInZhbHVlIjoiYVpTTWo2N25MdUJoTzAxVVgwRWtFOTE4RjZ5REtjNkNwa2F5YlRCR0hqMkpYWEQxdVpqZk9KOG5LVGE4Qm5iUy9YNUpjYzJGSlh4aVlOWkJTUkZzLy9ZMEVKeGJWczVPVCtuM2ZQNFkrRlU5cEVlTGlJTTlBVWhZV2pER1paR04iLCJtYWMiOiI0NTBjZDI2MjVjNmIzYzllY2U5NWUzZTZhNTI0MzY2Y2IyY2FiMDk5NWE4NjIzNTk3YzNjMTY5OWZkYTIxODYwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkkvcDk2TVMraDlJNlRZdldaR2VBM3c9PSIsInZhbHVlIjoieXlNWE5CRnN2QmlZOUc0Z0dvUi8wQnpGOFJZZktpOHYyZDVacUEweDlWTTNXYzQ4eGw5TmdGUkpLUXRxUXNkbWY4QnZrRGU1YldSTXpaaUpTSXZWKzBzWHV4SkJtb3Q5TG9sUm51WG1yQWk5UXB2OG9yaENQUDN0MnE1YmZORVEiLCJtYWMiOiJmYjlmY2UxMTBmNDI3Njk1NDUyMDlhYmY5Yzg3NDg3NGFiZmE1MzhiNDRlNGE4NTljZDIyMDYxNGZhZWNjYjE2IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755583614.8339</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755583614</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739703095\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-136195478 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-136195478\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-547780711 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:06:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ijk3M2ZIZ0ZHektYSFdBNUdOM3c5MkE9PSIsInZhbHVlIjoibmhXL1A1MEw3SUw3SCtBWU52UDhuRXlXeXQ2OXBndlZHdjg4V2Q5U09rMjFJa3JSUGVKcGgxeGZRODViRk9LUEhZZjAzajJhL1ZIWkJvSHFBK1hDKy9RK29xbVE0S0xWTytSeVgxZ0w0Mjh4YUZGOWF0RlJ3YWsvNFJ5WktqRWgiLCJtYWMiOiJlYzJlZTNhMDEzYjFhNGJhNTRlMDhkOTJiYTg0Njg2ZDA0NWU2YmMwNTY2OTQ1N2M4ZTg0NjQ0NDNjMDc2MzMxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:06:55 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImZlWkNCUVJVR2hlcXhUaS9ONXF3VUE9PSIsInZhbHVlIjoiNzZIazBXVTFVSUNEWVZQc3NLYlFucXd5NlVWcXg3NXpzWmhBd3ZwWVBsNDJSV1A2MUpqSkJqNVZ2blluSDBlaXY3akRwdGZFcEoyNXNHQ0VINHlrQ3FPRGZBYlJucXhFVXRFUW5iQ2FkUGlLcnhSY2drTVl1d3hPS2dxZm1sSHYiLCJtYWMiOiI5NGYwNWQ1MzE3YWM0YjVhNzhjZTRmZGFkOTU5N2YxNmJjY2JjNjM4NDhkOTQ2OWVhYzA3NzVmZmQ1NjYyMWY0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:06:55 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ijk3M2ZIZ0ZHektYSFdBNUdOM3c5MkE9PSIsInZhbHVlIjoibmhXL1A1MEw3SUw3SCtBWU52UDhuRXlXeXQ2OXBndlZHdjg4V2Q5U09rMjFJa3JSUGVKcGgxeGZRODViRk9LUEhZZjAzajJhL1ZIWkJvSHFBK1hDKy9RK29xbVE0S0xWTytSeVgxZ0w0Mjh4YUZGOWF0RlJ3YWsvNFJ5WktqRWgiLCJtYWMiOiJlYzJlZTNhMDEzYjFhNGJhNTRlMDhkOTJiYTg0Njg2ZDA0NWU2YmMwNTY2OTQ1N2M4ZTg0NjQ0NDNjMDc2MzMxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:06:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImZlWkNCUVJVR2hlcXhUaS9ONXF3VUE9PSIsInZhbHVlIjoiNzZIazBXVTFVSUNEWVZQc3NLYlFucXd5NlVWcXg3NXpzWmhBd3ZwWVBsNDJSV1A2MUpqSkJqNVZ2blluSDBlaXY3akRwdGZFcEoyNXNHQ0VINHlrQ3FPRGZBYlJucXhFVXRFUW5iQ2FkUGlLcnhSY2drTVl1d3hPS2dxZm1sSHYiLCJtYWMiOiI5NGYwNWQ1MzE3YWM0YjVhNzhjZTRmZGFkOTU5N2YxNmJjY2JjNjM4NDhkOTQ2OWVhYzA3NzVmZmQ1NjYyMWY0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:06:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-547780711\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-824305939 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-824305939\", {\"maxDepth\":0})</script>\n"}}