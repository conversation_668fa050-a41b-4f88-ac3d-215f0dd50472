{"__meta": {"id": "X84717e29913f106635851ddc4bd864bb", "datetime": "2025-08-19 10:40:23", "utime": 1755571223.199618, "method": "GET", "uri": "/nfr/general/2/specific/3/details", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[10:40:22] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755571222.602018, "xdebug_link": null, "collector": "log"}, {"message": "[10:40:22] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/nfr/general/2/specific/3/details", "message_html": null, "is_string": false, "label": "debug", "time": 1755571222.720358, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755571222.239882, "end": 1755571223.199646, "duration": 0.9597640037536621, "duration_str": "960ms", "measures": [{"label": "Booting", "start": 1755571222.239882, "relative_start": 0, "end": 1755571222.576787, "relative_end": 1755571222.576787, "duration": 0.33690500259399414, "duration_str": "337ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755571222.576805, "relative_start": 0.33692312240600586, "end": 1755571223.199649, "relative_end": 3.0994415283203125e-06, "duration": 0.6228439807891846, "duration_str": "623ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25457248, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 19, "templates": [{"name": "nfr.viewSpecific (\\resources\\views\\nfr\\viewSpecific.blade.php)", "param_count": 7, "params": ["specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.dashboard (\\resources\\views\\inc\\dashboard.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\resources\\views\\vendor\\pagination\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "specificNFR", "pro", "user", "linkedUserStories", "sprints", "title", "pros", "__currentLoopData", "project", "loop", "sprint", "story", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET nfr/general/{general_nfr_id}/specific/{nfr_id}/details", "middleware": "web", "controller": "App\\Http\\Controllers\\SpecificNFRController@viewSpecific", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "nfr.viewSpecific", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\SpecificNFRController.php&line=22\">\\app\\Http\\Controllers\\SpecificNFRController.php:22-65</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.00406, "accumulated_duration_str": "4.06ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 12.808}, {"sql": "select * from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 28}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Http\\Controllers\\SpecificNFRController.php:28", "connection": "sagile", "start_percent": 12.808, "width_percent": 12.808}, {"sql": "select * from `projects`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 38}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Http\\Controllers\\SpecificNFRController.php:38", "connection": "sagile", "start_percent": 25.616, "width_percent": 11.084}, {"sql": "select * from `nfr` where `nfr_id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\SpecificNFR.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 44}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\SpecificNFR.php:55", "connection": "sagile", "start_percent": 36.7, "width_percent": 12.562}, {"sql": "select * from `sprint`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Sprint.php", "line": 76}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Sprint.php:76", "connection": "sagile", "start_percent": 49.261, "width_percent": 11.084}, {"sql": "select count(distinct `user_story_general_nfr`.`user_story_id`) as aggregate from `user_story_general_nfr` inner join `user_stories` on `user_story_general_nfr`.`user_story_id` = `user_stories`.`u_id` inner join `projects` on `user_stories`.`proj_id` = `projects`.`id` left join `sprint` on `user_stories`.`sprint_id` = `sprint`.`sprint_id` and `user_stories`.`sprint_id` != '0' where `user_story_general_nfr`.`specific_nfr_id` = '3'", "type": "query", "params": [], "bindings": ["0", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\UserStoryGeneralNfr.php", "line": 81}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 55}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\app\\UserStoryGeneralNfr.php:81", "connection": "sagile", "start_percent": 60.345, "width_percent": 21.675}, {"sql": "select distinct `user_stories`.`user_story`, `user_stories`.`u_id`, `projects`.`proj_name`, COALESCE(sprint.sprint_name, \"Backlog\") as sprint_name from `user_story_general_nfr` inner join `user_stories` on `user_story_general_nfr`.`user_story_id` = `user_stories`.`u_id` inner join `projects` on `user_stories`.`proj_id` = `projects`.`id` left join `sprint` on `user_stories`.`sprint_id` = `sprint`.`sprint_id` and `user_stories`.`sprint_id` != '0' where `user_story_general_nfr`.`specific_nfr_id` = '3' limit 8 offset 0", "type": "query", "params": [], "bindings": ["0", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\UserStoryGeneralNfr.php", "line": 81}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\SpecificNFRController.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\app\\UserStoryGeneralNfr.php:81", "connection": "sagile", "start_percent": 82.02, "width_percent": 17.98}]}, "models": {"data": {"App\\Sprint": 2, "App\\SpecificNFR": 1, "App\\Project": 1, "App\\TeamMapping": 2, "App\\User": 1}, "count": 7}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/nfr/general/2/specific/3/details\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/nfr/general/2/specific/3/details", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-474778789 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-474778789\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1513400501 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1513400501\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-145303462 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/nfr/general/2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImZPcnBhZFdhRi81NG94OWFualFrWFE9PSIsInZhbHVlIjoiK2grOEdXK3RYeEFlWUVERnhMR08rNlhLVk5wcjFLUUJTY052WEZWaDFXQ2krYmZzTzBjZFZvam5iNjRXQ0s2WTlOVkJNSnU3RXFmY1M1YVozaGdvYUQ1VUZPREJiZDc4ZWhjSEt4NTNyVzRkTmJIN0s4Z25lL3V2TTJnakt4UHUiLCJtYWMiOiI0MjM4NDM0Y2U3NGQwN2Y5NjJjYTQ4NzUwZTBhYjdjZjk2ZTBiMDE0NjBjODgxMzVkNGUzODMzNTRhMjk1MzM4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImthY01vNXkzLzBOdVhrWHIyYzJIeWc9PSIsInZhbHVlIjoiVk1kOWZvT1FtOUJoVFpwamZPNUEzUVg1MVdzRFRvTVNnLzhpNFBxcWRhM2FLdzNLRklja01vMDFXVGNjdGlxY1Y0bm45NFlYaURHVUhrc3BZTHYrenVQbXpMYWF0L3orTW42dEVUSndNeHJtNG8rOHBuVjNpRC9aQU5CZmxwNE0iLCJtYWMiOiIyNjM4OTMzMzA4ZGZlNWM0ZjFiM2I0OWVmMmU0YTE5YTE4OWFlMzg1NzVjNzc5ZTY5ZDVjNzA3YjNhODQyODVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-145303462\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1816302592 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">61979</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/nfr/general/2/specific/3/details</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/nfr/general/2/specific/3/details</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"43 characters\">/index.php/nfr/general/2/specific/3/details</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/nfr/general/2</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImZPcnBhZFdhRi81NG94OWFualFrWFE9PSIsInZhbHVlIjoiK2grOEdXK3RYeEFlWUVERnhMR08rNlhLVk5wcjFLUUJTY052WEZWaDFXQ2krYmZzTzBjZFZvam5iNjRXQ0s2WTlOVkJNSnU3RXFmY1M1YVozaGdvYUQ1VUZPREJiZDc4ZWhjSEt4NTNyVzRkTmJIN0s4Z25lL3V2TTJnakt4UHUiLCJtYWMiOiI0MjM4NDM0Y2U3NGQwN2Y5NjJjYTQ4NzUwZTBhYjdjZjk2ZTBiMDE0NjBjODgxMzVkNGUzODMzNTRhMjk1MzM4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImthY01vNXkzLzBOdVhrWHIyYzJIeWc9PSIsInZhbHVlIjoiVk1kOWZvT1FtOUJoVFpwamZPNUEzUVg1MVdzRFRvTVNnLzhpNFBxcWRhM2FLdzNLRklja01vMDFXVGNjdGlxY1Y0bm45NFlYaURHVUhrc3BZTHYrenVQbXpMYWF0L3orTW42dEVUSndNeHJtNG8rOHBuVjNpRC9aQU5CZmxwNE0iLCJtYWMiOiIyNjM4OTMzMzA4ZGZlNWM0ZjFiM2I0OWVmMmU0YTE5YTE4OWFlMzg1NzVjNzc5ZTY5ZDVjNzA3YjNhODQyODVhIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755571222.2399</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755571222</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1816302592\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-900161190 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-900161190\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1812026199 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:40:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InRLekhxejlpVmt4RHU3MlZ6a2M3b2c9PSIsInZhbHVlIjoiOTFrMW51QU1NVXpmVVB0SFB2RlF6ZXBXdFRIb25pTDNrbERNd2NVOTN0ZnNodCt5TndMdzFKeTcrM2xoZW5BemJxbXl0R1JvQ3J3Wk5lcUhwaS9LR1VEeGV5RkFsMjQwRG5lT0EydHU2bTBxY0RHWXl0Njd6a0U1WkNIbml1T0giLCJtYWMiOiJmZTdlZjNlZjYxMDM1Njg2ODVkMGUxMzk5YTE1NTA0MDBmODZkYTJjM2U3MmEyZDdhMzY2MDRmMDg2ODcyYjRmIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:40:23 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Ill4WXFCWTVLODkzNGRleWF0c1JuUnc9PSIsInZhbHVlIjoiVkk3Qy9YanMwZ01SUG9Zc0Q4WUZIdGw5Sll6SmUxQjNpTWZZVnlnMkh1UFRjQi9nZmFTazlJcUV2a0djRTludGJjZTFjRVhKZGtYUW9nM292NmFDcWdHT0hrRnVwUVRXS2ZSRXNTZGZpYURIOEJIc2luNDRvbXRmQUtMTko5VGQiLCJtYWMiOiI3NGI5MWRhYzU1MzQzYmIyYWE5NzJlMGIyNGY4OWFlOTA0M2NmOThmOTdiMmYwMTBjNWM5OWZjMWU1ODE2MDc1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:40:23 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InRLekhxejlpVmt4RHU3MlZ6a2M3b2c9PSIsInZhbHVlIjoiOTFrMW51QU1NVXpmVVB0SFB2RlF6ZXBXdFRIb25pTDNrbERNd2NVOTN0ZnNodCt5TndMdzFKeTcrM2xoZW5BemJxbXl0R1JvQ3J3Wk5lcUhwaS9LR1VEeGV5RkFsMjQwRG5lT0EydHU2bTBxY0RHWXl0Njd6a0U1WkNIbml1T0giLCJtYWMiOiJmZTdlZjNlZjYxMDM1Njg2ODVkMGUxMzk5YTE1NTA0MDBmODZkYTJjM2U3MmEyZDdhMzY2MDRmMDg2ODcyYjRmIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:40:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Ill4WXFCWTVLODkzNGRleWF0c1JuUnc9PSIsInZhbHVlIjoiVkk3Qy9YanMwZ01SUG9Zc0Q4WUZIdGw5Sll6SmUxQjNpTWZZVnlnMkh1UFRjQi9nZmFTazlJcUV2a0djRTludGJjZTFjRVhKZGtYUW9nM292NmFDcWdHT0hrRnVwUVRXS2ZSRXNTZGZpYURIOEJIc2luNDRvbXRmQUtMTko5VGQiLCJtYWMiOiI3NGI5MWRhYzU1MzQzYmIyYWE5NzJlMGIyNGY4OWFlOTA0M2NmOThmOTdiMmYwMTBjNWM5OWZjMWU1ODE2MDc1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:40:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1812026199\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1307255277 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"54 characters\">http://127.0.0.1:8000/nfr/general/2/specific/3/details</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1307255277\", {\"maxDepth\":0})</script>\n"}}