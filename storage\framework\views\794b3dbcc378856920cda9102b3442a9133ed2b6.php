<script>
// Comments functionality JavaScript
document.addEventListener('DOMContentLoaded', function () {
    // Prevent click event propagation for filter and sort elements
    document.querySelectorAll('.filter-created-by-select, .sort-comments-form').forEach(element => {
        element.addEventListener('click', function (e) {
            e.stopPropagation();
        });
    });

    // Handle filtering comments by creator
    document.querySelectorAll('.filter-created-by-select').forEach(select => {
        select.addEventListener('change', function (e) {
            e.stopPropagation();

            const taskId = this.getAttribute('data-task-id'); // Get the task ID
            const filterBy = this.value; // Get the selected value
            const commentsContainer = document.getElementById(`comments-container-${taskId}`);

            if (!commentsContainer) {
                console.error(`Comments container not found for task ID: ${taskId}`);
                return;
            }

            // Get all comment wrappers inside the task-comments container
            const comments = Array.from(commentsContainer.querySelectorAll('.comment-wrapper'));

            let visibleCount = 0;
            const sortByDate = new URLSearchParams(window.location.search).get('sort_date') || 'desc';

            // Sort comments by date
            comments.sort((a, b) => {
                const dateA = new Date(a.getAttribute('data-created-date'));
                const dateB = new Date(b.getAttribute('data-created-date'));
                return sortByDate === 'desc' ? dateB - dateA : dateA - dateB;
            });

            comments.forEach((comment, index) => {
                const createdBy = comment.getAttribute('filter-created-by'); // Get the created_by attribute for each comment

                // Show or hide comments based on the filter
                if (filterBy === 'all' || createdBy === filterBy) {
                    comment.style.display = visibleCount < 2 ? 'block' : 'none'; // Show first 2, hide the rest
                    if (visibleCount >= 2) {
                        comment.classList.add('hidden-comment');
                    } else {
                        comment.classList.remove('hidden-comment');
                    }
                    visibleCount++;
                } else {
                    comment.style.display = 'none';
                    comment.classList.remove('hidden-comment');
                }
            });

            // Show or hide "Show More" and "Show Less" buttons based on visible comments
            const showMoreBtn = commentsContainer.querySelector('.show-more-btn');
            const showLessBtn = commentsContainer.querySelector('.show-less-btn');

            if (visibleCount <= 2) {
                // Hide buttons if there are 2 or fewer visible comments
                if (showMoreBtn) showMoreBtn.style.display = 'none';
                if (showLessBtn) showLessBtn.style.display = 'none';
            } else {
                // Show "Show More" button if there are hidden comments
                if (showMoreBtn) showMoreBtn.style.display = 'inline-block';
                if (showLessBtn) showLessBtn.style.display = 'none';
            }
        });
    });

    // Handle Show More and Show Less buttons
    document.querySelectorAll('.show-more-btn').forEach(showMoreBtn => {
        const commentsContainer = showMoreBtn.closest('.task-comments'); // Get the parent comments container
        const showLessBtn = commentsContainer.querySelector('.show-less-btn');

        showMoreBtn.addEventListener('click', (event) => {
            event.stopPropagation();

            // Show all comments in the current container
            commentsContainer.querySelectorAll('.hidden-comment').forEach(wrapper => {
                wrapper.style.display = 'block';
            });

            // Toggle button visibility
            showMoreBtn.style.display = 'none';
            showLessBtn.style.display = 'inline-block';
        });

        showLessBtn.addEventListener('click', (event) => {
            event.stopPropagation();

            // Hide all comments beyond the first 2 in the current container
            let visibleCount = 0;
            commentsContainer.querySelectorAll('.comment-wrapper').forEach(wrapper => {
                visibleCount++;
                if (visibleCount > 2) {
                    wrapper.style.display = 'none';
                }
            });

            // Toggle button visibility
            showLessBtn.style.display = 'none';
            showMoreBtn.style.display = 'inline-block';
        });
    });

    // Handle sorting by date using AJAX
    document.querySelectorAll('.sort-comments-form').forEach(form => {
        form.querySelector('select').addEventListener('change', function (e) {
            e.preventDefault(); // Prevent the default form submission
            
            // Get the selected sort date value
            const sortByDate = this.value;

            // Prepare the form data
            const formData = new FormData(form);
            formData.append('sort_date', sortByDate);

            // Send the AJAX request
            fetch(form.action, {
                method: 'GET',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                // Update the page content with the new sorted data
                document.getElementById('comments-container').innerHTML = data; // Replace with your container's ID
            })
            .catch(error => {
                console.error('Error during AJAX request:', error);
            });
        });
    });

    // Add Comment Button Functionality
    document.querySelectorAll('.add-comment-btn').forEach(button => {
        button.addEventListener('click', function (event) {
            event.stopPropagation();

            const taskId = this.getAttribute('data-task-id');
            const modal = document.getElementById('createCommentModal');
            modal.style.display = 'flex';

            const saveCreateButton = document.getElementById('saveCreateCommentBtn');
            saveCreateButton.onclick = function () {
                const comment = document.getElementById('createCommentInput').value.trim();

                if (comment) {
                    fetch(`/tasks/${taskId}/createKanbanComment`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({ comment: comment })
                    })
                    .then(response => {
                        console.log('Fetch response:', response);
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            console.log('Comment created successfully.');
                            modal.style.display = 'none'; // Close modal
                            if (data.success) {
                                location.reload(); // Auto refresh the page
                            } else {
                                alert('Error creating comment: ' + data.message);
                            }
                        } else {
                            alert('Error creating comment: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Fetch error:', error);
                    });
                } else {
                    console.log('No comment provided. Closing modal.');
                    modal.style.display = 'none'; // Close modal
                }
            };
        });
    });
    
    // Edit Comment Button Functionality
    document.querySelectorAll('.edit-comment-btn').forEach(button => {
        button.addEventListener('click', function (event) {
            event.stopPropagation();

            const commentId = this.getAttribute('data-comment-id');
            const commentElement = this.previousElementSibling;

            // Extract the raw comment text without the (edited) indicator
            let rawComment = commentElement.textContent.trim();
            const editedIndicatorIndex = rawComment.indexOf('(edited)');
            if (editedIndicatorIndex !== -1) {
                rawComment = rawComment.slice(0, editedIndicatorIndex).trim();
            }

            // Show the modal
            const modal = document.getElementById('editCommentModal');
            const inputField = document.getElementById('editCommentInput');
            modal.style.display = 'flex';
            inputField.value = rawComment;

            // Save button functionality
            const saveButton = document.getElementById('saveCommentBtn');
            saveButton.onclick = function () {
                const newComment = inputField.value.trim();

                if (newComment && newComment !== rawComment) {
                    fetch(`/tasks/${commentId}/updateKanbanComment`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({ comment: newComment })
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            // Update the comment text
                            commentElement.textContent = newComment;

                            // Add the (edited) indicator if it was updated
                            let editedIndicator = commentElement.querySelector('.edited-indicator');
                            if (!editedIndicator) {
                                editedIndicator = document.createElement('span');
                                editedIndicator.classList.add('edited-indicator');
                                editedIndicator.style.color = 'gray';
                                editedIndicator.style.fontSize = '12px';
                                editedIndicator.textContent = ' (edited)';
                                commentElement.appendChild(editedIndicator); // Append directly to the comment element
                            }
                            modal.style.display = 'none'; // Close the modal
                            setTimeout(() => {
                                alert('Comment updated successfully!');
                            }, 100);
                        } else {
                            alert('Error updating comment: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
                } else {
                    modal.style.display = 'none'; // Close modal if no changes
                }
            };
        });
    });
    
    // Delete Comment Button Functionality
    document.querySelectorAll('.delete-comment-btn').forEach(button => {
        button.addEventListener('click', function (e) {
            e.stopPropagation();
            const confirmDelete = confirm('Are you sure you want to delete this comment?');
            if (!confirmDelete) {
                // Stop the deletion and prevent any further actions
                e.preventDefault(); // Prevent the default action (deletion)
                return false; // Explicitly stop further propagation
            }
            // If confirmed, allow the deletion to proceed (the default action will be executed)
        });
    });
    
    // Close modal functionality
    document.querySelectorAll('.close-modal').forEach(closeButton => {
        closeButton.addEventListener('click', function () {
            const modal = this.closest('.modal'); // Find the closest modal container
            if (modal) {
                modal.style.display = 'none'; // Hide the modal
            }
        });
    });
});
</script><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/inc/kanban/comments-script-js.blade.php ENDPATH**/ ?>