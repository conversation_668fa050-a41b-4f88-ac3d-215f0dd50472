

<?php
$isMenu = false;
?>

<?php
/* Display elements */
$contentNavbar = false;
$containerNav = ($containerNav ?? 'container-fluid'); // Changed to full width
$isNavbar = ($isNavbar ?? true);
$isMenu = ($isMenu ?? true);
$isFlex = ($isFlex ?? false);
$isFooter = ($isFooter ?? true);

/* HTML Classes */
$navbarDetached = 'navbar-detached';

/* Content classes */
$container = ($container ?? 'container-fluid'); // Changed to full width

?>

<?php $__env->startSection('layoutContent'); ?>
<div class="layout-wrapper layout-content-navbar <?php echo e($isMenu ? '' : 'layout-without-menu'); ?>">
  <div class="layout-container">

    <!-- Layout page -->
    <div class="layout-page">

      <!-- Content wrapper -->
      <div class="content-wrapper">

        <!-- Content -->
        <?php if($isFlex): ?>
        <div class="<?php echo e($container); ?> d-flex align-items-stretch flex-grow-1 p-0">
        <?php else: ?>
        <div class="<?php echo e($container); ?> flex-grow-1 container-p-y px-0"> 
        <?php endif; ?>

          <?php echo $__env->yieldContent('content'); ?>

        </div>
        <!-- / Content -->

        <div class="content-backdrop fade"></div>
      </div>
      <!--/ Content wrapper -->
    </div>
    <!-- / Layout page -->

  </div>

  <?php if($isMenu): ?>
  <!-- Overlay -->
  <div class="layout-overlay layout-menu-toggle"></div>
  <?php endif; ?>

  <!-- Drag Target Area To SlideIn Menu On Small Screens -->
  <div class="drag-target"></div>
</div>
<!-- / Layout wrapper -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.commonMaster', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/layouts/detailsPartialLoad.blade.php ENDPATH**/ ?>