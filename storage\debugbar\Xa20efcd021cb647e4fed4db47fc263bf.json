{"__meta": {"id": "Xa20efcd021cb647e4fed4db47fc263bf", "datetime": "2025-08-19 11:06:56", "utime": 1755572816.572494, "method": "GET", "uri": "/42/kanbanBoard", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 5, "messages": [{"message": "[11:06:56] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572816.227914, "xdebug_link": null, "collector": "log"}, {"message": "[11:06:56] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/42/kanbanBoard", "message_html": null, "is_string": false, "label": "debug", "time": 1755572816.41249, "xdebug_link": null, "collector": "log"}, {"message": "[11:06:56] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755572816.509882, "xdebug_link": null, "collector": "log"}, {"message": "[11:06:56] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572816.512192, "xdebug_link": null, "collector": "log"}, {"message": "[11:06:56] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572816.512335, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572815.052535, "end": 1755572816.57254, "duration": 1.5200049877166748, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": 1755572815.052535, "relative_start": 0, "end": 1755572816.156577, "relative_end": 1755572816.156577, "duration": 1.1040420532226562, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572816.156601, "relative_start": 1.1040658950805664, "end": 1755572816.572547, "relative_end": 6.9141387939453125e-06, "duration": 0.41594600677490234, "duration_str": "416ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24035680, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "kanban.index (\\resources\\views\\kanban\\index.blade.php)", "param_count": 2, "params": ["project", "hasActiveSprint"], "type": "blade"}, {"name": "inc.kanbanStyle (\\resources\\views\\inc\\kanbanStyle.blade.php)", "param_count": 6, "params": ["__env", "app", "menuData", "errors", "project", "hasActiveSprint"], "type": "blade"}, {"name": "inc.kanban.kanban-script-js (\\resources\\views\\inc\\kanban\\kanban-script-js.blade.php)", "param_count": 6, "params": ["__env", "app", "menuData", "errors", "project", "hasActiveSprint"], "type": "blade"}, {"name": "inc.kanban.comments-script-js (\\resources\\views\\inc\\kanban\\comments-script-js.blade.php)", "param_count": 6, "params": ["__env", "app", "menuData", "errors", "project", "hasActiveSprint"], "type": "blade"}]}, "route": {"uri": "GET {proj_id}/kanbanBoard", "middleware": "web", "controller": "App\\Http\\Controllers\\TaskController@kanbanIndex", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "sprint.kanbanPage", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TaskController.php&line=86\">\\app\\Http\\Controllers\\TaskController.php:86-176</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00331, "accumulated_duration_str": "3.31ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 24.169}, {"sql": "select * from `projects` where `id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 88}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:88", "connection": "sagile", "start_percent": 24.169, "width_percent": 32.024}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` = 1 limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 95}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:95", "connection": "sagile", "start_percent": 56.193, "width_percent": 23.565}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` is null and `start_sprint` <= '2025-08-19 11:06:56' and `end_sprint` >= '2025-08-19 11:06:56' limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System", "2025-08-19 11:06:56", "2025-08-19 11:06:56"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 104}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:104", "connection": "sagile", "start_percent": 79.758, "width_percent": 20.242}]}, "models": {"data": {"App\\Project": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755572816.522343, "xdebug_link": null}]}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/42/kanbanBoard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755570218\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/42/kanbanBoard", "status_code": "<pre class=sf-dump id=sf-dump-787019439 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-787019439\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1137712821 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1137712821\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1243143102 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1243143102\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1684452128 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkJJUUpsdWwzOERCcEZMaklvYk5xN3c9PSIsInZhbHVlIjoidVhrZVdvZUQ2SjVydGRFYVZDTnRlOVlaanlQeUNIRU5Pek52SUl4TmxmUlh5RVZPTDQveWVzQXBlMmdocXpjN1k3KzFZL25kNlJlNTQxenhNdnZjRmVqU1BwRnY3MC9kRkxjd1VHOXhTZTdtcnRBb2d6Rk1qWlRseXcvdkM0a3AiLCJtYWMiOiJjOTZmOTZlYjRhYTE1OTIyN2Q0NGNlMzljMzIwNDM1YzcyMzM4ODJhMDdkZTI0ZjA3ODY2YTdjOWRjYzZkOGJiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlNWczJoMXRnZEpHZEs1UG1XL0lob0E9PSIsInZhbHVlIjoiMFo1SkZYZk5XM1lnby9ldE82MWNvWHJ3c0NTbm9PeEExa2dMMUszMHpFd0RZdi8rc2FIbkdFa3Q3WWZ1Zlo5UEdWMDVkQkFDRC9NSU4vd0tEbmlyTExIZWRQdml1KzB3djFwUnBxV2ErRWNENGk4aXpNcVlxV1hXa21za0F3YXciLCJtYWMiOiJhNzc3NjJkNjY2YWFhMzA2M2Q2ZmJkODdiOTA0YzIxMTMzMjc4ZTYxZGM0OGJjYTBkZmM0ODc5NTIyOTJkNTQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1684452128\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-549428330 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">49450</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/42/kanbanBoard</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/42/kanbanBoard</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/index.php/42/kanbanBoard</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkJJUUpsdWwzOERCcEZMaklvYk5xN3c9PSIsInZhbHVlIjoidVhrZVdvZUQ2SjVydGRFYVZDTnRlOVlaanlQeUNIRU5Pek52SUl4TmxmUlh5RVZPTDQveWVzQXBlMmdocXpjN1k3KzFZL25kNlJlNTQxenhNdnZjRmVqU1BwRnY3MC9kRkxjd1VHOXhTZTdtcnRBb2d6Rk1qWlRseXcvdkM0a3AiLCJtYWMiOiJjOTZmOTZlYjRhYTE1OTIyN2Q0NGNlMzljMzIwNDM1YzcyMzM4ODJhMDdkZTI0ZjA3ODY2YTdjOWRjYzZkOGJiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlNWczJoMXRnZEpHZEs1UG1XL0lob0E9PSIsInZhbHVlIjoiMFo1SkZYZk5XM1lnby9ldE82MWNvWHJ3c0NTbm9PeEExa2dMMUszMHpFd0RZdi8rc2FIbkdFa3Q3WWZ1Zlo5UEdWMDVkQkFDRC9NSU4vd0tEbmlyTExIZWRQdml1KzB3djFwUnBxV2ErRWNENGk4aXpNcVlxV1hXa21za0F3YXciLCJtYWMiOiJhNzc3NjJkNjY2YWFhMzA2M2Q2ZmJkODdiOTA0YzIxMTMzMjc4ZTYxZGM0OGJjYTBkZmM0ODc5NTIyOTJkNTQ3IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572815.0525</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572815</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-549428330\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1932912637 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932912637\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1203823894 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 03:06:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImJIV1cyVXE4TEd6S1JWOFo1SWxNM1E9PSIsInZhbHVlIjoiT1dMYlRZWk5JWlg5WVVPK2wvbWJ1VFJiRktpOGtFeFpvQ29MVnZLQ3ArUXpPNG9rTEd4c0FjZ2dVNWtUcUIxR2hhMDQzSStERDAycW91T0poYXRZdjRPUE00NEdNb2RIUjdJL0pBak1qQ1huU3BDcWk2NlkvYXVQbGpFR05zLzAiLCJtYWMiOiI4NjQ0YjBlMmNmNzk0ODExNWU0MDViYWZiYjM1Yzk2NGQ2MDNkZDYyYTQ4OGVlMjc2MTU2MWIzNDk4ZTQxOGQ3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:06:56 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InhKVDVIdFNCODR1MU1qSVR4UXVJZ1E9PSIsInZhbHVlIjoiS0FuYzdMMlhVSEZIZVJoVDd5TmFnbW4xbUJKMWVQNTYwL1NHaGkxOXFUTUs3L2w4WjlJclhKQ2diMno4NjNWeEYwMVh1ZEFLbUJMT1pWc29JeVBQUmQyem40ZVFaSkpRa1F4TDB5c01RRXpjS2pEdTQ2eWpKSnh4ZjBIRVBBWSsiLCJtYWMiOiIwYWRlZmJkZjg3M2RlMmQ5MTcyM2ExZGJhNTI1MTE2ZWFiZDBkODdhMTAxMTI4NDg3NzY1MjM5ZGE1NTI2YzFmIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:06:56 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImJIV1cyVXE4TEd6S1JWOFo1SWxNM1E9PSIsInZhbHVlIjoiT1dMYlRZWk5JWlg5WVVPK2wvbWJ1VFJiRktpOGtFeFpvQ29MVnZLQ3ArUXpPNG9rTEd4c0FjZ2dVNWtUcUIxR2hhMDQzSStERDAycW91T0poYXRZdjRPUE00NEdNb2RIUjdJL0pBak1qQ1huU3BDcWk2NlkvYXVQbGpFR05zLzAiLCJtYWMiOiI4NjQ0YjBlMmNmNzk0ODExNWU0MDViYWZiYjM1Yzk2NGQ2MDNkZDYyYTQ4OGVlMjc2MTU2MWIzNDk4ZTQxOGQ3IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:06:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InhKVDVIdFNCODR1MU1qSVR4UXVJZ1E9PSIsInZhbHVlIjoiS0FuYzdMMlhVSEZIZVJoVDd5TmFnbW4xbUJKMWVQNTYwL1NHaGkxOXFUTUs3L2w4WjlJclhKQ2diMno4NjNWeEYwMVh1ZEFLbUJMT1pWc29JeVBQUmQyem40ZVFaSkpRa1F4TDB5c01RRXpjS2pEdTQ2eWpKSnh4ZjBIRVBBWSsiLCJtYWMiOiIwYWRlZmJkZjg3M2RlMmQ5MTcyM2ExZGJhNTI1MTE2ZWFiZDBkODdhMTAxMTI4NDg3NzY1MjM5ZGE1NTI2YzFmIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:06:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1203823894\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/42/kanbanBoard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755570218</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}