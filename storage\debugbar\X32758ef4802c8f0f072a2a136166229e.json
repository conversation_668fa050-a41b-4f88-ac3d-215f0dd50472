{"__meta": {"id": "X32758ef4802c8f0f072a2a136166229e", "datetime": "2025-08-19 14:25:56", "utime": 1755584756.233812, "method": "POST", "uri": "/teammappings", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 6, "messages": [{"message": "[14:25:52] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584752.898203, "xdebug_link": null, "collector": "log"}, {"message": "[14:25:52] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/teammappings", "message_html": null, "is_string": false, "label": "debug", "time": 1755584752.96131, "xdebug_link": null, "collector": "log"}, {"message": "[14:25:53] LOG.warning: Since league/commonmark 2.2.0: Calling \"convertToHtml()\" on a League\\CommonMark\\MarkdownConverter class is deprecated, use \"convert()\" instead. in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\symfony\\deprecation-contracts\\function.php on line 25", "message_html": null, "is_string": false, "label": "warning", "time": 1755584753.106214, "xdebug_link": null, "collector": "log"}, {"message": "[14:25:53] LOG.warning: Since league/commonmark 2.2.0: Calling \"convertToHtml()\" on a League\\CommonMark\\MarkdownConverter class is deprecated, use \"convert()\" instead. in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\symfony\\deprecation-contracts\\function.php on line 25", "message_html": null, "is_string": false, "label": "warning", "time": 1755584753.166739, "xdebug_link": null, "collector": "log"}, {"message": "[14:25:53] LOG.warning: Callables of the form [\"Swift_Message\", \"Swift_Mime_SimpleMessage::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\Message.php on line 46", "message_html": null, "is_string": false, "label": "warning", "time": 1755584753.231087, "xdebug_link": null, "collector": "log"}, {"message": "[14:25:53] LOG.warning: Callables of the form [\"Swift_MimePart\", \"Swift_Mime_MimePart::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\MimePart.php on line 30", "message_html": null, "is_string": false, "label": "warning", "time": 1755584753.265506, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584752.582047, "end": 1755584756.233837, "duration": 3.651789903640747, "duration_str": "3.65s", "measures": [{"label": "Booting", "start": 1755584752.582047, "relative_start": 0, "end": 1755584752.877722, "relative_end": 1755584752.877722, "duration": 0.29567503929138184, "duration_str": "296ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584752.877733, "relative_start": 0.2956860065460205, "end": 1755584756.233839, "relative_end": 2.1457672119140625e-06, "duration": 3.3561060428619385, "duration_str": "3.36s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 29524216, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 30, "templates": [{"name": "mail.team-invitation (\\resources\\views\\mail\\team-invitation.blade.php)", "param_count": 13, "params": ["teamName", "<PERSON><PERSON><PERSON>", "userEmail", "invitationToken", "connection", "queue", "chainConnection", "chainQueue", "chainCatchCallbacks", "delay", "afterCommit", "middleware", "chained"], "type": "blade"}, {"name": "mail::button (\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\resources\\views\\html\\button.blade.php)", "param_count": 4, "params": ["url", "color", "slot", "__laravel_slots"], "type": "blade"}, {"name": "mail::button (\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\resources\\views\\html\\button.blade.php)", "param_count": 4, "params": ["url", "color", "slot", "__laravel_slots"], "type": "blade"}, {"name": "mail::message (\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\resources\\views\\html\\message.blade.php)", "param_count": 2, "params": ["slot", "__laravel_slots"], "type": "blade"}, {"name": "mail::header (\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\resources\\views\\html\\header.blade.php)", "param_count": 3, "params": ["url", "slot", "__laravel_slots"], "type": "blade"}, {"name": "mail::footer (\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\resources\\views\\html\\footer.blade.php)", "param_count": 2, "params": ["slot", "__laravel_slots"], "type": "blade"}, {"name": "mail::layout (\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\resources\\views\\html\\layout.blade.php)", "param_count": 4, "params": ["slot", "header", "footer", "__laravel_slots"], "type": "blade"}, {"name": "mail::themes.default (\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\resources\\views\\html\\themes\\default.css)", "param_count": 13, "params": ["teamName", "<PERSON><PERSON><PERSON>", "userEmail", "invitationToken", "connection", "queue", "chainConnection", "chainQueue", "chainCatchCallbacks", "delay", "afterCommit", "middleware", "chained"], "type": "css"}, {"name": "mail.team-invitation (\\resources\\views\\mail\\team-invitation.blade.php)", "param_count": 13, "params": ["teamName", "<PERSON><PERSON><PERSON>", "userEmail", "invitationToken", "connection", "queue", "chainConnection", "chainQueue", "chainCatchCallbacks", "delay", "afterCommit", "middleware", "chained"], "type": "blade"}, {"name": "mail::button (\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\resources\\views\\text\\button.blade.php)", "param_count": 4, "params": ["url", "color", "slot", "__laravel_slots"], "type": "blade"}, {"name": "mail::button (\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\resources\\views\\text\\button.blade.php)", "param_count": 4, "params": ["url", "color", "slot", "__laravel_slots"], "type": "blade"}, {"name": "mail::message (\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\resources\\views\\text\\message.blade.php)", "param_count": 2, "params": ["slot", "__laravel_slots"], "type": "blade"}, {"name": "mail::header (\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\resources\\views\\text\\header.blade.php)", "param_count": 3, "params": ["url", "slot", "__laravel_slots"], "type": "blade"}, {"name": "mail::footer (\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\resources\\views\\text\\footer.blade.php)", "param_count": 2, "params": ["slot", "__laravel_slots"], "type": "blade"}, {"name": "mail::layout (\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\resources\\views\\text\\layout.blade.php)", "param_count": 4, "params": ["slot", "header", "footer", "__laravel_slots"], "type": "blade"}, {"name": "teammapping.index (\\resources\\views\\teammapping\\index.blade.php)", "param_count": 4, "params": ["teammappings", "teams", "success", "title"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 8, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "success", "title"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 8, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "success", "title"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "success", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "success", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "success", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "success", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "success", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "success", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "success", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "success", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 24, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "success", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "success", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "success", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "teammappings", "teams", "success", "title", "currentUser", "canManageTeam", "teamRoles", "__empty_1", "__currentLoopData", "teammapping", "loop", "role", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "POST teammappings", "middleware": "web", "controller": "App\\Http\\Controllers\\TeamMappingController@store", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "teammappings.store", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TeamMappingController.php&line=105\">\\app\\Http\\Controllers\\TeamMappingController.php:105-166</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.011170000000000001, "accumulated_duration_str": "11.17ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 3.85}, {"sql": "select exists(select * from `teammappings` where `team_name` = 'Team 888' and `username` = 'ivlyn' and `role_name` = 'Team Manager' and `invitation_status` = 'accepted') as `exists`", "type": "query", "params": [], "bindings": ["Team 888", "ivlyn", "Team Manager", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 35}, {"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 56}, {"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 108}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:35", "connection": "sagile", "start_percent": 3.85, "width_percent": 4.029}, {"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 610}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 114}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:610", "connection": "sagile", "start_percent": 7.878, "width_percent": 4.208}, {"sql": "select * from `teammappings` where `team_name` = 'Team 888' and `username` = 'tay' limit 1", "type": "query", "params": [], "bindings": ["Team 888", "tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 615}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 114}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:615", "connection": "sagile", "start_percent": 12.086, "width_percent": 4.297}, {"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 627}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 114}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:627", "connection": "sagile", "start_percent": 16.383, "width_percent": 3.85}, {"sql": "insert into `teammappings` (`username`, `role_name`, `team_name`, `invitation_status`, `invitation_token`, `updated_at`, `created_at`) values ('tay', 'Team Member', 'Team 888', 'pending', 'IezUuDXNA33qCKCyTepQHEJbUwtfIGzJ', '2025-08-19 14:25:53', '2025-08-19 14:25:53')", "type": "query", "params": [], "bindings": ["tay", "Team Member", "Team 888", "pending", "IezUuDXNA33qCKCyTepQHEJbUwtfIGzJ", "2025-08-19 14:25:53", "2025-08-19 14:25:53"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 637}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 114}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00743, "duration_str": "7.43ms", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:637", "connection": "sagile", "start_percent": 20.233, "width_percent": 66.517}, {"sql": "select * from `teams` where `team_name` = 'Team 888' limit 1", "type": "query", "params": [], "bindings": ["Team 888"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 684}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 114}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:684", "connection": "sagile", "start_percent": 86.75, "width_percent": 4.566}, {"sql": "select * from `teammappings` where `team_name` = 'Team 888' and `project_id` is null", "type": "query", "params": [], "bindings": ["Team 888"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 687}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TeamMappingController.php", "line": 114}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Http\\Controllers\\TeamMappingController.php:687", "connection": "sagile", "start_percent": 91.316, "width_percent": 4.655}, {"sql": "select exists(select * from `teammappings` where `team_name` = 'Team 888' and `username` = 'ivlyn' and `role_name` = 'Team Manager' and `invitation_status` = 'accepted') as `exists`", "type": "query", "params": [], "bindings": ["Team 888", "ivlyn", "Team Manager", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": "view", "name": "4dd4546e8c5ba9283dc8de130df2adb90e812ffc", "line": 74}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 15, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "view::4dd4546e8c5ba9283dc8de130df2adb90e812ffc:74", "connection": "sagile", "start_percent": 95.971, "width_percent": 4.029}]}, "models": {"data": {"App\\TeamMapping": 2, "App\\Team": 1, "App\\User": 3}, "count": 6}, "swiftmailer_mails": {"count": 1, "mails": [{"to": "<<EMAIL>>", "subject": "Invitation to Join Team Team 888", "headers": "Message-ID: <6a47577b7d483dc9883270dddea4de25@127.0.0.1>\r\nDate: <PERSON><PERSON>, 19 Aug 2025 14:25:53 +0800\r\nSubject: Invitation to Join Team Team 888\r\nFrom: <PERSON><PERSON> <<EMAIL>>\r\nTo: <EMAIL>\r\nMIME-Version: 1.0\r\nContent-Type: multipart/alternative;\r\n boundary=\"_=_swift_1755584753_2a51baf6a8b216cc6a273478afde2d57_=_\"\r\n", "body": null, "html": null}]}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A7qC5dKg3kSvtr41icWUi6S70mrKKeUAGVsmye8B", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/teammappings/Team%20888/create?teams=39\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755584713\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/teammappings", "status_code": "<pre class=sf-dump id=sf-dump-1932933497 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1932933497\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1152084941 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1152084941\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-443737254 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A7qC5dKg3kSvtr41icWUi6S70mrKKeUAGVsmye8B</span>\"\n  \"<span class=sf-dump-key>team_name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Team 888</span>\"\n  \"<span class=sf-dump-key>emails</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[&quot;<EMAIL>&quot;]</span>\"\n  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Team Member</span>\"\n  \"<span class=sf-dump-key>action</span>\" => \"<span class=sf-dump-str title=\"16 characters\">send_invitations</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-443737254\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-598363144 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">153</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"61 characters\">http://127.0.0.1:8000/teammappings/Team%20888/create?teams=39</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ikc0ZVdqMmk0dXhQK1FPZmhiYWViUWc9PSIsInZhbHVlIjoiMUpxei9OM2RiSFIvWk8yM1VjdlQ2bTZOdUppdUF6MTRXaWZPSkQvN0w4MUQ2R3kxeG83MC9MSHVOTXc3ZUtaQmxaV2VFdEgyd0ZYY3ljdEhKUEQ4ck1USTJPaGtMSThadUllemtwZ0NEaGQ1UHk1ZmJvT1RyeUZiOFE2MWxmWjAiLCJtYWMiOiIzZjRiNGQ4ZjlkZjM5Y2E2NWMzMjRlZmI0ZTFhNDM2N2RkMGJmOTA2OTQ5NTUwOTQ0YWNlYjA0ZGNlZWQyNzVmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImVGaUJqRHBMK3AwamxVazI2M0R1U3c9PSIsInZhbHVlIjoiZHRpQVlDaXo2YldVVGliUmF2clpZWHFvYmxPOTJ5Y3VObFlpT1FxUGlGNXo4b3pFR1VNWkN0dSttREtJSGQ1NjM0cTRxSGtVVDFRdzhvb0l6R0FOQVdZOUh5RmJCa2JsRjFKWklLZWZHUkxscm5tdHdFMUZSVnlMM09KSGNjcloiLCJtYWMiOiIzYjM5MWM5NDk1YTMwYzkzZWQ1NjBkNDg4N2MwODk4ODQzZDFjNWM0ZDFjZmExOTAwN2Q1OWU5Y2MwZjU2NjQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-598363144\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1736267195 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59059</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"13 characters\">/teammappings</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"13 characters\">/teammappings</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/index.php/teammappings</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">153</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">153</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://127.0.0.1:8000/teammappings/Team%20888/create?teams=39</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ikc0ZVdqMmk0dXhQK1FPZmhiYWViUWc9PSIsInZhbHVlIjoiMUpxei9OM2RiSFIvWk8yM1VjdlQ2bTZOdUppdUF6MTRXaWZPSkQvN0w4MUQ2R3kxeG83MC9MSHVOTXc3ZUtaQmxaV2VFdEgyd0ZYY3ljdEhKUEQ4ck1USTJPaGtMSThadUllemtwZ0NEaGQ1UHk1ZmJvT1RyeUZiOFE2MWxmWjAiLCJtYWMiOiIzZjRiNGQ4ZjlkZjM5Y2E2NWMzMjRlZmI0ZTFhNDM2N2RkMGJmOTA2OTQ5NTUwOTQ0YWNlYjA0ZGNlZWQyNzVmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImVGaUJqRHBMK3AwamxVazI2M0R1U3c9PSIsInZhbHVlIjoiZHRpQVlDaXo2YldVVGliUmF2clpZWHFvYmxPOTJ5Y3VObFlpT1FxUGlGNXo4b3pFR1VNWkN0dSttREtJSGQ1NjM0cTRxSGtVVDFRdzhvb0l6R0FOQVdZOUh5RmJCa2JsRjFKWklLZWZHUkxscm5tdHdFMUZSVnlMM09KSGNjcloiLCJtYWMiOiIzYjM5MWM5NDk1YTMwYzkzZWQ1NjBkNDg4N2MwODk4ODQzZDFjNWM0ZDFjZmExOTAwN2Q1OWU5Y2MwZjU2NjQxIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584752.582</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584752</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1736267195\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1629990221 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A7qC5dKg3kSvtr41icWUi6S70mrKKeUAGVsmye8B</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PsfQwzGwhxLcTiW03wKjNrtJR5Amn59fF4M5EzyK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1629990221\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-22590016 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:25:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlBUWEZmZWlrWmEwdkhMU3FLU2syL3c9PSIsInZhbHVlIjoiWHRPRjU5Y1lRUzBKZzlvdDk4dGliSlBhcmJaVERpQThHWnpDbENsQlpzVURIQXlZclRhQ1ZzNDZ0c0FmSTAvRU00L3ByNnhVVUhrcCtPaFB5VDNMSjM5YlJLS3ZSTklGZGdrNFVRQ0dvakY3ZWo2ZXJzdEdUWmwyVG8zQzVCS0kiLCJtYWMiOiJkMzM5MWE3N2RkYjAxMGEzODVkOGVjNTg3ZDNiOTQ5NDEyNDQyZTdjYmNiNGM1ZWM4ODdhMTk5ZjBmYzk2OGVkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:25:56 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkFwNGtWL1hoVVdLWFVVeVFEeGNGQWc9PSIsInZhbHVlIjoib3Q1UHBlR2ZNMFVqWWpvR2tFUVRDOUluMkZXNzduQ3Y4WElOMmdjWWVCR0h3R2RjMlpHUGtsWEw3WDVQd0VaY0pRWXJVTnVJV2dqSGVDOVhRRFBtUURzbnhONFFVMGIzNUpFUlNSMlQvdXB1dFBjMnpqZWhKMXJkVDFkVktPdlgiLCJtYWMiOiJjMmFiZmIwYjg0NzY1ZTI5M2YxN2Q5NDY1OWQzMGIxYTA2MWQ3MWVmZmU0N2FkY2M5YzQ3YjhiY2Q3YmE3MzllIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:25:56 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlBUWEZmZWlrWmEwdkhMU3FLU2syL3c9PSIsInZhbHVlIjoiWHRPRjU5Y1lRUzBKZzlvdDk4dGliSlBhcmJaVERpQThHWnpDbENsQlpzVURIQXlZclRhQ1ZzNDZ0c0FmSTAvRU00L3ByNnhVVUhrcCtPaFB5VDNMSjM5YlJLS3ZSTklGZGdrNFVRQ0dvakY3ZWo2ZXJzdEdUWmwyVG8zQzVCS0kiLCJtYWMiOiJkMzM5MWE3N2RkYjAxMGEzODVkOGVjNTg3ZDNiOTQ5NDEyNDQyZTdjYmNiNGM1ZWM4ODdhMTk5ZjBmYzk2OGVkIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:25:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkFwNGtWL1hoVVdLWFVVeVFEeGNGQWc9PSIsInZhbHVlIjoib3Q1UHBlR2ZNMFVqWWpvR2tFUVRDOUluMkZXNzduQ3Y4WElOMmdjWWVCR0h3R2RjMlpHUGtsWEw3WDVQd0VaY0pRWXJVTnVJV2dqSGVDOVhRRFBtUURzbnhONFFVMGIzNUpFUlNSMlQvdXB1dFBjMnpqZWhKMXJkVDFkVktPdlgiLCJtYWMiOiJjMmFiZmIwYjg0NzY1ZTI5M2YxN2Q5NDY1OWQzMGIxYTA2MWQ3MWVmZmU0N2FkY2M5YzQ3YjhiY2Q3YmE3MzllIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:25:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-22590016\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1187209837 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A7qC5dKg3kSvtr41icWUi6S70mrKKeUAGVsmye8B</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://127.0.0.1:8000/teammappings/Team%20888/create?teams=39</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755584713</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1187209837\", {\"maxDepth\":0})</script>\n"}}