{"__meta": {"id": "Xbca419f0f46a9db03d33936868c0051f", "datetime": "2025-08-19 10:42:41", "utime": 1755571361.769011, "method": "GET", "uri": "/backlogTest/42", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 30, "messages": [{"message": "[10:42:41] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755571361.524096, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/backlogTest/42", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.609047, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.info: BacklogController@index started {\"project_id\":\"42\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755571361.609225, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.info: Project found {\"project\":{\"id\":42,\"team_name\":\"<PERSON><PERSON><PERSON>'s team\",\"proj_name\":\"Food Ordering System\",\"proj_desc\":\"A web-based platform that enables customers to browse menus, place orders, and make payments online while providing administrators with tools to manage menus, track orders, and monitor user activities efficiently.\",\"start_date\":\"2025-08-18\",\"end_date\":\"2026-01-25\",\"shareable_slug\":null,\"created_at\":\"2025-08-18T15:02:05.000000Z\",\"updated_at\":\"2025-08-18T15:02:05.000000Z\"}}", "message_html": null, "is_string": false, "label": "info", "time": 1755571361.626371, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.info: Explicit active sprint check completed {\"proj_name\":\"Food Ordering System\",\"active_sprint_found\":true,\"active_sprint_data\":{\"sprint_id\":35,\"sprint_name\":\"Sprint 2\",\"sprint_desc\":\"2\",\"start_sprint\":\"2025-08-19\",\"end_sprint\":\"2025-09-02\",\"active_sprint\":1,\"proj_name\":\"Food Ordering System\",\"users_name\":\"ivlyn\",\"created_at\":\"2025-08-19T02:36:31.000000Z\",\"updated_at\":\"2025-08-19T02:36:31.000000Z\"}}", "message_html": null, "is_string": false, "label": "info", "time": 1755571361.643387, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.info: Active sprint ID determined {\"active_sprint_id\":35}", "message_html": null, "is_string": false, "label": "info", "time": 1755571361.643543, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.info: Processing user stories with active sprint {\"active_sprint_id\":35}", "message_html": null, "is_string": false, "label": "info", "time": 1755571361.643639, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.info: All non-done user stories retrieved {\"total_user_stories\":2,\"user_story_ids\":[46,47],\"done_status_id\":208}", "message_html": null, "is_string": false, "label": "info", "time": 1755571361.679837, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: Processing user story {\"user_story_id\":46,\"user_story_sprint_id\":\"35\",\"active_sprint_id\":35}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.679941, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: User story is in active sprint - checking tasks {\"user_story_id\":46}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.68002, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: Tasks not in sprint check completed {\"user_story_id\":46,\"has_tasks_not_in_sprint\":false}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.69991, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: Processing user story {\"user_story_id\":47,\"user_story_sprint_id\":\"35\",\"active_sprint_id\":35}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.700015, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: User story is in active sprint - checking tasks {\"user_story_id\":47}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.700109, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: Tasks not in sprint check completed {\"user_story_id\":47,\"has_tasks_not_in_sprint\":false}", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.718946, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.info: User stories filtering completed with active sprint {\"filtered_user_stories_count\":0,\"filtered_user_story_ids\":[]}", "message_html": null, "is_string": false, "label": "info", "time": 1755571361.719069, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.info: Starting task retrieval for user stories", "message_html": null, "is_string": false, "label": "info", "time": 1755571361.719144, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.info: Task retrieval completed {\"total_user_stories_with_tasks\":0,\"total_tasks_retrieved\":0}", "message_html": null, "is_string": false, "label": "info", "time": 1755571361.736052, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.info: BacklogController@index completed successfully {\"project_id\":\"42\",\"active_sprint_id\":35,\"user_stories_count\":0,\"tasks_by_user_story_count\":0,\"note\":\"Filtered out user stories with Done status and tasks with done status\"}", "message_html": null, "is_string": false, "label": "info", "time": 1755571361.736152, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: Gate check for permission: addUserStory_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.744439, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.745899, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.745983, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: Gate check for permission: beginSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.751572, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.752668, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.752745, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: Gate check for permission: addToSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.75321, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.754204, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.75426, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: Gate check for permission: endSprint_backlog on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.75457, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.755595, "xdebug_link": null, "collector": "log"}, {"message": "[10:42:41] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755571361.75565, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755571361.177197, "end": 1755571361.769061, "duration": 0.5918641090393066, "duration_str": "592ms", "measures": [{"label": "Booting", "start": 1755571361.177197, "relative_start": 0, "end": 1755571361.49857, "relative_end": 1755571361.49857, "duration": 0.32137298583984375, "duration_str": "321ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755571361.498585, "relative_start": 0.32138800621032715, "end": 1755571361.769063, "relative_end": 1.9073486328125e-06, "duration": 0.2704780101776123, "duration_str": "270ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24090600, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "backlogTest.index (\\resources\\views\\backlogTest\\index.blade.php)", "param_count": 4, "params": ["project", "userStories", "tasksByUserStory", "activeSprint"], "type": "blade"}]}, "route": {"uri": "GET backlogTest/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\BacklogController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "backlogTest.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\BacklogController.php&line=19\">\\app\\Http\\Controllers\\BacklogController.php:19-239</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.00469, "accumulated_duration_str": "4.69ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 14.925}, {"sql": "select * from `projects` where `projects`.`id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 24}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:24", "connection": "sagile", "start_percent": 14.925, "width_percent": 10.021}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` = 1 limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 30}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:30", "connection": "sagile", "start_percent": 24.947, "width_percent": 11.301}, {"sql": "select * from `statuses` where `project_id` = '42' and `title` = 'Done' limit 1", "type": "query", "params": [], "bindings": ["42", "Done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 75}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:75", "connection": "sagile", "start_percent": 36.247, "width_percent": 13.646}, {"sql": "select * from `user_stories` where `proj_id` = '42' and `status_id` != 208", "type": "query", "params": [], "bindings": ["42", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 82}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:82", "connection": "sagile", "start_percent": 49.893, "width_percent": 13.433}, {"sql": "select exists(select * from `tasks` where `userstory_id` = 46 and (`sprint_id` != 35 or `sprint_id` is null)) as `exists`", "type": "query", "params": [], "bindings": ["46", "35"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 117}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:117", "connection": "sagile", "start_percent": 63.326, "width_percent": 10.661}, {"sql": "select exists(select * from `tasks` where `userstory_id` = 47 and (`sprint_id` != 35 or `sprint_id` is null)) as `exists`", "type": "query", "params": [], "bindings": ["47", "35"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 117}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:117", "connection": "sagile", "start_percent": 73.987, "width_percent": 10.021}, {"sql": "select * from `statuses` where `project_id` = '42' and `slug` = 'done' limit 1", "type": "query", "params": [], "bindings": ["42", "done"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\BacklogController.php", "line": 173}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Http\\Controllers\\BacklogController.php:173", "connection": "sagile", "start_percent": 84.009, "width_percent": 15.991}]}, "models": {"data": {"App\\UserStory": 2, "App\\Status": 2, "App\\Sprint": 1, "App\\Project": 1, "App\\User": 1}, "count": 7}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 4, "messages": [{"message": "[\n  ability => addUserStory_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">addUserStory_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571361.749932, "xdebug_link": null}, {"message": "[\n  ability => beginSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-432634656 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">beginSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432634656\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571361.752951, "xdebug_link": null}, {"message": "[\n  ability => addToSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1454016920 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">addToSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1454016920\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571361.754397, "xdebug_link": null}, {"message": "[\n  ability => endSprint_backlog,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1780459923 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">endSprint_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1780459923\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755571361.755801, "xdebug_link": null}]}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/backlogTest/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755570218\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/backlogTest/42", "status_code": "<pre class=sf-dump id=sf-dump-1299154435 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1299154435\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1217666955 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1217666955\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-249642401 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-249642401\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-151459765 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik0waFNFbzBYY2VHbkRyUG5nS2xPWlE9PSIsInZhbHVlIjoiSUdnakZtZWY1bnFmV2MzY0poYzRSS1Rybno3aWVla3FQbjJ4RytpbzhNUjhaaVExNjhxNlV5dEpkOFNibGhaUkdhZFZGRjRDejc5eGRZeGZBVzJKOFd5RVZVZlUyTnJzdUZjSzgzSXR1MzNqa3BWbE5rS25VM0ZsUFFENEF4RkMiLCJtYWMiOiI3NTI0YmFhZjY2ODgxMDgxYTY4YjQ2NDg0NDFmZGRjMDJlMzdjYzMwMTdhMThhYjY5OWI0MTljOGRiOWQyYTliIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkVIZHd0aUJBSUozR3d3Q25WZWJiZ1E9PSIsInZhbHVlIjoiVGN5cXNsdlpwTHRRcm83TkJWQS9DaCs5a0w1cGx0V2hLVHNTZWFkRkprUytiYmhBMFM1V09rTkV1WUE1dG5URm84YThnY1VpVnJTajFCTlI5REVidTRSN2xUdXhCb09OSXBJcFVnaWEreWxIbnVrUS9NcFJvYTNkNXhhc1luclUiLCJtYWMiOiI0MjIyYTUyZDViZGZmNzg2YjNhOGQ2YmQ4MmNhOGRjZmI1OWM3MGQ2OWJkYjg3ZDY0ZmRjZTgxNDMwN2E4NmQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-151459765\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-2095622424 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57717</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/index.php/backlogTest/42</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik0waFNFbzBYY2VHbkRyUG5nS2xPWlE9PSIsInZhbHVlIjoiSUdnakZtZWY1bnFmV2MzY0poYzRSS1Rybno3aWVla3FQbjJ4RytpbzhNUjhaaVExNjhxNlV5dEpkOFNibGhaUkdhZFZGRjRDejc5eGRZeGZBVzJKOFd5RVZVZlUyTnJzdUZjSzgzSXR1MzNqa3BWbE5rS25VM0ZsUFFENEF4RkMiLCJtYWMiOiI3NTI0YmFhZjY2ODgxMDgxYTY4YjQ2NDg0NDFmZGRjMDJlMzdjYzMwMTdhMThhYjY5OWI0MTljOGRiOWQyYTliIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkVIZHd0aUJBSUozR3d3Q25WZWJiZ1E9PSIsInZhbHVlIjoiVGN5cXNsdlpwTHRRcm83TkJWQS9DaCs5a0w1cGx0V2hLVHNTZWFkRkprUytiYmhBMFM1V09rTkV1WUE1dG5URm84YThnY1VpVnJTajFCTlI5REVidTRSN2xUdXhCb09OSXBJcFVnaWEreWxIbnVrUS9NcFJvYTNkNXhhc1luclUiLCJtYWMiOiI0MjIyYTUyZDViZGZmNzg2YjNhOGQ2YmQ4MmNhOGRjZmI1OWM3MGQ2OWJkYjg3ZDY0ZmRjZTgxNDMwN2E4NmQ2IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755571361.1772</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755571361</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095622424\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-379121491 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-379121491\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-706613051 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:42:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlRFNUpRL1d2QmQ0L0NZa3NxZWN0MGc9PSIsInZhbHVlIjoiekZ5U1pmRDVrMFg2RHUxUHhjUlhxbENYZ2JUa1ZRUDZEaFhKRkZ6VG9KN1BvMmR2ZmtuYlRoeUlNaVllUGFRT1pQNXNKUGQ1NmN6YWUwOVdDSVdlbHEyUkUzZjNSTHdMYld0b0JMWFkra1loZlAwdytpQ2xKM2lqTEJ3c1lrTFAiLCJtYWMiOiI1ZDBjMGMwNmI3MDc3MTA0YmQ0ZTcwNDFjOTU2NDZkMTkxOWE0OWYwYzAzMDUzMTg0ZDU5OTc0NWRhMzIyYTRmIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:42:41 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Ikx0US9yNFROcDhHOGRpc05OY0dCVGc9PSIsInZhbHVlIjoiaitkT0o2MzEySW1BM2dGaEV4Rk0zMi9KM012U2JZTHdoSkJvZjF4MFBwVC9BY1U5bjVNVVIrQUhlaHNGTFc0NFJLRVNHVW5xTlhSdjRuaTRWd2pyZTBkNDJNdjZ6K001V29VU1cvNU5oSGRoaHQ5SUZOVFk1ODNLSVZ1M1d2cUoiLCJtYWMiOiI1YmZiNGIwYTQ4ZjcyYWVhMTc3ZmE0YmIxMmZlNjJkYTUwMzYwNTczZDM0NmZjNjRlOThlMjVmMTg4MWRjZTM1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:42:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlRFNUpRL1d2QmQ0L0NZa3NxZWN0MGc9PSIsInZhbHVlIjoiekZ5U1pmRDVrMFg2RHUxUHhjUlhxbENYZ2JUa1ZRUDZEaFhKRkZ6VG9KN1BvMmR2ZmtuYlRoeUlNaVllUGFRT1pQNXNKUGQ1NmN6YWUwOVdDSVdlbHEyUkUzZjNSTHdMYld0b0JMWFkra1loZlAwdytpQ2xKM2lqTEJ3c1lrTFAiLCJtYWMiOiI1ZDBjMGMwNmI3MDc3MTA0YmQ0ZTcwNDFjOTU2NDZkMTkxOWE0OWYwYzAzMDUzMTg0ZDU5OTc0NWRhMzIyYTRmIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:42:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Ikx0US9yNFROcDhHOGRpc05OY0dCVGc9PSIsInZhbHVlIjoiaitkT0o2MzEySW1BM2dGaEV4Rk0zMi9KM012U2JZTHdoSkJvZjF4MFBwVC9BY1U5bjVNVVIrQUhlaHNGTFc0NFJLRVNHVW5xTlhSdjRuaTRWd2pyZTBkNDJNdjZ6K001V29VU1cvNU5oSGRoaHQ5SUZOVFk1ODNLSVZ1M1d2cUoiLCJtYWMiOiI1YmZiNGIwYTQ4ZjcyYWVhMTc3ZmE0YmIxMmZlNjJkYTUwMzYwNTczZDM0NmZjNjRlOThlMjVmMTg4MWRjZTM1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:42:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-706613051\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1279450871 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/backlogTest/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755570218</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1279450871\", {\"maxDepth\":0})</script>\n"}}