{"__meta": {"id": "Xbd42409f24c00125c10a7e50d58f9f79", "datetime": "2025-08-19 13:14:59", "utime": 1755580499.268778, "method": "GET", "uri": "/project/42/sprint-archives", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 44, "messages": [{"message": "[13:14:58] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755580498.847226, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:58] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/project/42/sprint-archives", "message_html": null, "is_string": false, "label": "debug", "time": 1755580498.908723, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Gate check for permission: viewKanbanArchive_sprintArchive on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.23648, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.237589, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.237646, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Gate check for permission: viewBurndownArchive_sprintArchive on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.242124, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.243695, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.24381, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Gate check for permission: viewKanbanArchive_sprintArchive on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.244331, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.245212, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.24527, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Gate check for permission: viewBurndownArchive_sprintArchive on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.245678, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.24648, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.246527, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Gate check for permission: viewKanbanArchive_sprintArchive on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.246874, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.24766, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.247707, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Gate check for permission: viewBurndownArchive_sprintArchive on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.248003, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.248775, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.248821, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Gate check for permission: viewKanbanArchive_sprintArchive on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.249187, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.25009, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.250146, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Gate check for permission: viewBurndownArchive_sprintArchive on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.250466, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.251245, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.251293, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Gate check for permission: viewKanbanArchive_sprintArchive on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.251627, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.252418, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.252464, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Gate check for permission: viewBurndownArchive_sprintArchive on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.252748, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.253504, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.253549, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Gate check for permission: viewKanbanArchive_sprintArchive on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.253862, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.254664, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.254711, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Gate check for permission: viewBurndownArchive_sprintArchive on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.254984, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.255728, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.255773, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Gate check for permission: viewKanbanArchive_sprintArchive on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.256133, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.256935, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.256982, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Gate check for permission: viewBurndownArchive_sprintArchive on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.257261, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.258025, "xdebug_link": null, "collector": "log"}, {"message": "[13:14:59] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755580499.258069, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755580498.558065, "end": 1755580499.268839, "duration": 0.7107739448547363, "duration_str": "711ms", "measures": [{"label": "Booting", "start": 1755580498.558065, "relative_start": 0, "end": 1755580498.827103, "relative_end": 1755580498.827103, "duration": 0.26903796195983887, "duration_str": "269ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755580498.827115, "relative_start": 0.26905012130737305, "end": 1755580499.268841, "relative_end": 2.1457672119140625e-06, "duration": 0.4417259693145752, "duration_str": "442ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24406624, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "sprint.archives.index (\\resources\\views\\sprint\\archives\\index.blade.php)", "param_count": 2, "params": ["project", "archivedSprints"], "type": "blade"}]}, "route": {"uri": "GET project/{proj_id}/sprint-archives", "middleware": "web", "controller": "App\\Http\\Controllers\\SprintArchiveController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "sprint.archives", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\SprintArchiveController.php&line=15\">\\app\\Http\\Controllers\\SprintArchiveController.php:15-27</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00495, "accumulated_duration_str": "4.95ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 11.717}, {"sql": "select * from `projects` where `projects`.`id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintArchiveController.php", "line": 17}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Http\\Controllers\\SprintArchiveController.php:17", "connection": "sagile", "start_percent": 11.717, "width_percent": 10.101}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` = 2", "type": "query", "params": [], "bindings": ["Food Ordering System", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintArchiveController.php", "line": 21}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\SprintArchiveController.php:21", "connection": "sagile", "start_percent": 21.818, "width_percent": 9.293}, {"sql": "select * from `sprint_archives` where `sprint_archives`.`sprint_id` in (37, 39, 40, 41, 42, 43, 44)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\SprintArchiveController.php", "line": 21}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0034100000000000003, "duration_str": "3.41ms", "stmt_id": "\\app\\Http\\Controllers\\SprintArchiveController.php:21", "connection": "sagile", "start_percent": 31.111, "width_percent": 68.889}]}, "models": {"data": {"App\\SprintArchive": 7, "App\\Sprint": 7, "App\\Project": 1, "App\\User": 1}, "count": 16}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 14, "messages": [{"message": "[\n  ability => viewKanbanArchive_sprintArchive,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">viewKanbanArchive_sprintArchive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755580499.240788, "xdebug_link": null}, {"message": "[\n  ability => viewBurndownArchive_sprintArchive,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1367851027 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"33 characters\">viewBurndownArchive_sprintArchive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1367851027\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755580499.244039, "xdebug_link": null}, {"message": "[\n  ability => viewKanbanArchive_sprintArchive,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2044282725 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">viewKanbanArchive_sprintArchive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2044282725\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755580499.245413, "xdebug_link": null}, {"message": "[\n  ability => viewBurndownArchive_sprintArchive,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1839754680 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"33 characters\">viewBurndownArchive_sprintArchive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1839754680\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755580499.246658, "xdebug_link": null}, {"message": "[\n  ability => viewKanbanArchive_sprintArchive,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1689215709 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">viewKanbanArchive_sprintArchive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1689215709\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755580499.247832, "xdebug_link": null}, {"message": "[\n  ability => viewBurndownArchive_sprintArchive,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1226929319 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"33 characters\">viewBurndownArchive_sprintArchive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226929319\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755580499.248946, "xdebug_link": null}, {"message": "[\n  ability => viewKanbanArchive_sprintArchive,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-104098279 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">viewKanbanArchive_sprintArchive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-104098279\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755580499.250288, "xdebug_link": null}, {"message": "[\n  ability => viewBurndownArchive_sprintArchive,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-741679029 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"33 characters\">viewBurndownArchive_sprintArchive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-741679029\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755580499.251418, "xdebug_link": null}, {"message": "[\n  ability => viewKanbanArchive_sprintArchive,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-697893926 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">viewKanbanArchive_sprintArchive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-697893926\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755580499.252585, "xdebug_link": null}, {"message": "[\n  ability => viewBurndownArchive_sprintArchive,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1384850485 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"33 characters\">viewBurndownArchive_sprintArchive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1384850485\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755580499.253666, "xdebug_link": null}, {"message": "[\n  ability => viewKanbanArchive_sprintArchive,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1328900056 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">viewKanbanArchive_sprintArchive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1328900056\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755580499.254827, "xdebug_link": null}, {"message": "[\n  ability => viewBurndownArchive_sprintArchive,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1713656398 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"33 characters\">viewBurndownArchive_sprintArchive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1713656398\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755580499.255888, "xdebug_link": null}, {"message": "[\n  ability => viewKanbanArchive_sprintArchive,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-117265486 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">viewKanbanArchive_sprintArchive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-117265486\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755580499.257103, "xdebug_link": null}, {"message": "[\n  ability => viewBurndownArchive_sprintArchive,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1087444641 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"33 characters\">viewBurndownArchive_sprintArchive</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1087444641\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755580499.258183, "xdebug_link": null}]}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/project/42/sprint-archives\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755570218\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/project/42/sprint-archives", "status_code": "<pre class=sf-dump id=sf-dump-537673664 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-537673664\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-52034687 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-52034687\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2131756354 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2131756354\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2096438998 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlNzWXdxRlo0bmowWGpiQ3hjRUgrc3c9PSIsInZhbHVlIjoiQUo5N2xCT3Z5bk52cm1Ia3U2TWg2SVI1VDN3QUVGVmVLTGF6TC9hYldjOS9LSmpIQkhGUWJtVWRZSmdCVjRGNmNYUWNmcG1oa3ZGWm5qbHpnMyt4bGN2NHd1cVRXczgwOHV4cWlrQ2VWSDZZU2E0b2RmalJiejRUSmRrR2pqb0YiLCJtYWMiOiJhODgyNDY2NTMyZGQ4MzBkYzgwNmU2ZjRkZmRmOTE5Mjk3MGE1ZTA5Mzg5ZjJlZWM5ZjA2ODU0MTg3NDA1ZmEzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlBDVTRiK3djeTVyUDRuZXJDZDZ5c2c9PSIsInZhbHVlIjoiR0NBNTZWeis5WVJXOHV5UHR4THljMmV4bWN4eWVvRENYQnZlTUVMOUU2KzluQUtmNGVsUXk0NXVwR2RVOXZsa2ZKV053eXordkV0S1Y3V05Dbm5XbHpZKzcyeDJjc1ZxNE85Yjh6cWhUbmNGM1FNWEIzaFlObEMrREtnOFZtTkgiLCJtYWMiOiJlNjkwNDE4NDJhMTZkNDhkZDFiMTUxYzQ5MzYxMzlmZGMxMWJjNzE2MjQ5MGNlNzk0NTIzNjNjZjZjOWYzODg0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2096438998\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1971753500 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59371</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/project/42/sprint-archives</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"27 characters\">/project/42/sprint-archives</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"37 characters\">/index.php/project/42/sprint-archives</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlNzWXdxRlo0bmowWGpiQ3hjRUgrc3c9PSIsInZhbHVlIjoiQUo5N2xCT3Z5bk52cm1Ia3U2TWg2SVI1VDN3QUVGVmVLTGF6TC9hYldjOS9LSmpIQkhGUWJtVWRZSmdCVjRGNmNYUWNmcG1oa3ZGWm5qbHpnMyt4bGN2NHd1cVRXczgwOHV4cWlrQ2VWSDZZU2E0b2RmalJiejRUSmRrR2pqb0YiLCJtYWMiOiJhODgyNDY2NTMyZGQ4MzBkYzgwNmU2ZjRkZmRmOTE5Mjk3MGE1ZTA5Mzg5ZjJlZWM5ZjA2ODU0MTg3NDA1ZmEzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlBDVTRiK3djeTVyUDRuZXJDZDZ5c2c9PSIsInZhbHVlIjoiR0NBNTZWeis5WVJXOHV5UHR4THljMmV4bWN4eWVvRENYQnZlTUVMOUU2KzluQUtmNGVsUXk0NXVwR2RVOXZsa2ZKV053eXordkV0S1Y3V05Dbm5XbHpZKzcyeDJjc1ZxNE85Yjh6cWhUbmNGM1FNWEIzaFlObEMrREtnOFZtTkgiLCJtYWMiOiJlNjkwNDE4NDJhMTZkNDhkZDFiMTUxYzQ5MzYxMzlmZGMxMWJjNzE2MjQ5MGNlNzk0NTIzNjNjZjZjOWYzODg0IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755580498.5581</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755580498</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1971753500\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1009375646 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1009375646\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-672304455 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:14:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImE5VWxIcUU5QzJLdFBDbU8rMHNMbnc9PSIsInZhbHVlIjoiZzRjUTRkRTRyZkJvaUhmUEwxMkJya2FZNEpUNGNTb3Jmckc5bjJ3bUNLZkpXb1JDVTMwSnE2Q1Aycm1jWFBIQkh3bGg2RVI3dHVJNFRXaGZBUUs4Uk9NbDIxaWdWa2IxalJMQmppNVBXS1dyakIyVmZYcXBSd3VMQkNiRVlxU2wiLCJtYWMiOiIzZjIzOGViZTRiNDIzNzYxODNmOTIyNDFjZjBlNWU2Zjg2MzNjMDAzYWIzNzkwOGMyMTEyNGRlYzBhYWVkZmZiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:14:59 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlluUzEzNDdTbXdlT2VTY1RjeHhDWlE9PSIsInZhbHVlIjoiZ3hZcnJiM1h2bjVPUHgyZk5LR3dCWHkvNTBIbnJmcURWcTczRW9tVkY4YmdnNGtSMVFiQ0NYbmlFOUlNMkY4a29QTWRKaTNNVnFGWkJ4QnhaUFFsRVVhVnR5dTJ4dXhJaytIRjdTYW5kdE5kWXJFQVFSRVJMdkNDRTg3Sk5saHkiLCJtYWMiOiJiNTk2MjhiNDUyODJjMjA2MzBkMTZjYTEwZmIxZGZhMDkxZTFlNGMxYjMzYTFiYTZjM2FjOGExZGEzYTM2ODE0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:14:59 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImE5VWxIcUU5QzJLdFBDbU8rMHNMbnc9PSIsInZhbHVlIjoiZzRjUTRkRTRyZkJvaUhmUEwxMkJya2FZNEpUNGNTb3Jmckc5bjJ3bUNLZkpXb1JDVTMwSnE2Q1Aycm1jWFBIQkh3bGg2RVI3dHVJNFRXaGZBUUs4Uk9NbDIxaWdWa2IxalJMQmppNVBXS1dyakIyVmZYcXBSd3VMQkNiRVlxU2wiLCJtYWMiOiIzZjIzOGViZTRiNDIzNzYxODNmOTIyNDFjZjBlNWU2Zjg2MzNjMDAzYWIzNzkwOGMyMTEyNGRlYzBhYWVkZmZiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:14:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlluUzEzNDdTbXdlT2VTY1RjeHhDWlE9PSIsInZhbHVlIjoiZ3hZcnJiM1h2bjVPUHgyZk5LR3dCWHkvNTBIbnJmcURWcTczRW9tVkY4YmdnNGtSMVFiQ0NYbmlFOUlNMkY4a29QTWRKaTNNVnFGWkJ4QnhaUFFsRVVhVnR5dTJ4dXhJaytIRjdTYW5kdE5kWXJFQVFSRVJMdkNDRTg3Sk5saHkiLCJtYWMiOiJiNTk2MjhiNDUyODJjMjA2MzBkMTZjYTEwZmIxZGZhMDkxZTFlNGMxYjMzYTFiYTZjM2FjOGExZGEzYTM2ODE0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:14:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-672304455\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-306972209 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">http://127.0.0.1:8000/project/42/sprint-archives</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755570218</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-306972209\", {\"maxDepth\":0})</script>\n"}}