{"__meta": {"id": "X5333c00a8335362d72b356a858744cf9", "datetime": "2025-08-19 14:28:15", "utime": 1755584895.591898, "method": "GET", "uri": "/logout", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[14:28:15] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584895.487371, "xdebug_link": null, "collector": "log"}, {"message": "[14:28:15] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/logout", "message_html": null, "is_string": false, "label": "debug", "time": 1755584895.565204, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584895.125977, "end": 1755584895.591929, "duration": 0.46595191955566406, "duration_str": "466ms", "measures": [{"label": "Booting", "start": 1755584895.125977, "relative_start": 0, "end": 1755584895.460504, "relative_end": 1755584895.460504, "duration": 0.33452701568603516, "duration_str": "335ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584895.460517, "relative_start": 0.33453989028930664, "end": 1755584895.591932, "relative_end": 3.0994415283203125e-06, "duration": 0.13141512870788574, "duration_str": "131ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23295864, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET logout", "middleware": "web", "controller": "\\App\\Http\\Controllers\\Auth\\LoginController@logout", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php&line=166\">\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php:166-181</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.0005200000000000001, "accumulated_duration_str": "520μs", "statements": [{"sql": "select * from `users` where `id` = 31 limit 1", "type": "query", "params": [], "bindings": ["31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "load.permissions", "line": 22}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WYLFG6nx9g9L9TGC9DkrMmVONrCUfO9cbwe27HPT", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/logout\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/logout", "status_code": "<pre class=sf-dump id=sf-dump-723687301 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-723687301\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-513706972 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-513706972\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1303862912 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/project-assignments/46</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkZRc2VkS0gxUitMZytKbklKdmZ0VUE9PSIsInZhbHVlIjoibi82TXd1ZnMxeEFwWjJTdkZmTE5OYnhqbU9kRXR2M0pYdDMrbGxpUS9WeFMrUHQzSC9DNGpUNGNaZVpHZHpSS1hYc0ZVTTlReDhjZ3B0M1BLcnBQdkVMY0tVWjhjUDdIQStZUS94ZlFPNnZDdVk0RnMrQ2FJd3hyVXc2WXkvem8iLCJtYWMiOiI2ZTM5NDViMDRmMDBlN2JmMjEyMzAzZWQ4NmU1NmQxZDlhYTUwMjMxY2U3ZTQwN2Y0N2I1N2UxZTY5YzFjNWNiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImJ2b1IzYks1eHAwUFM2TGoydnA0Y3c9PSIsInZhbHVlIjoiUHNrQUdrLzNsaGl6bk0ySGQ2ak5ZaFp0SFNFUUEzM3FEM2pCWm1vVGxTL09xT0dNbUU0bllwOFJpUXNYdXNMVnJDZXdRckEwVjcrZ2dCa1hoTFdvanBVUFhkNnZMNm4yZUF6ZzlhNXhiaDVIK2hpN3RtT05pSllvKzd5TUkxakoiLCJtYWMiOiIwODEwM2U5ODAxZmExZGY1MTM0MzQyNzBkNTkzZDE5MTY3MDAxNjQyNWRiNzNiNWUwYWU3NGIzMjU2Y2JmMjI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1303862912\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-174545684 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55211</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"7 characters\">/logout</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"7 characters\">/logout</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/index.php/logout</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/project-assignments/46</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkZRc2VkS0gxUitMZytKbklKdmZ0VUE9PSIsInZhbHVlIjoibi82TXd1ZnMxeEFwWjJTdkZmTE5OYnhqbU9kRXR2M0pYdDMrbGxpUS9WeFMrUHQzSC9DNGpUNGNaZVpHZHpSS1hYc0ZVTTlReDhjZ3B0M1BLcnBQdkVMY0tVWjhjUDdIQStZUS94ZlFPNnZDdVk0RnMrQ2FJd3hyVXc2WXkvem8iLCJtYWMiOiI2ZTM5NDViMDRmMDBlN2JmMjEyMzAzZWQ4NmU1NmQxZDlhYTUwMjMxY2U3ZTQwN2Y0N2I1N2UxZTY5YzFjNWNiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImJ2b1IzYks1eHAwUFM2TGoydnA0Y3c9PSIsInZhbHVlIjoiUHNrQUdrLzNsaGl6bk0ySGQ2ak5ZaFp0SFNFUUEzM3FEM2pCWm1vVGxTL09xT0dNbUU0bllwOFJpUXNYdXNMVnJDZXdRckEwVjcrZ2dCa1hoTFdvanBVUFhkNnZMNm4yZUF6ZzlhNXhiaDVIK2hpN3RtT05pSllvKzd5TUkxakoiLCJtYWMiOiIwODEwM2U5ODAxZmExZGY1MTM0MzQyNzBkNTkzZDE5MTY3MDAxNjQyNWRiNzNiNWUwYWU3NGIzMjU2Y2JmMjI5IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584895.126</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584895</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-174545684\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-664970253 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1HKQYsRSD2rF4MTIIeFUcWjlXS08bZuFgB96uqwf</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SVxqICKAMHCRL0LJ0lOUn4HjhWjUN7Mp0yrlMes7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-664970253\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1249347455 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:28:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlBPZHpZL2dTMTl6YmJTR3dPdkpZdXc9PSIsInZhbHVlIjoiODh2YUJ1cC8vOFdLMHkyYWpxMjhXQ1ZHaUVYOUlxdjNIMHBmN2pmRkdINTBrVWRhOUNQYUthZU9zd01mRmhRWk56VGNvYkQ0RUhLeDhYc2ZTd0h6MnJQcndVcjhFT3kvK0ZOQmxzRUpLdCtPYzdyTFord3ZmRkxZZjEzdGlQRUciLCJtYWMiOiI5ZDc4ZjE0YzMyNzkzZTA2ZmFkMTljOThiYzgzYTk3YjcwMjY2Yjc1ZjQzYjVjYTk2MmJkMGE0YjhiOTI5MjcwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:28:15 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImJoRnE5WlRhZmFEQVlJTWFyYWcweFE9PSIsInZhbHVlIjoiUFBtTzFOOEdlSEk1eXBoS2kzQWRibUFsRW9oZmY4c0hnMTRkcmEyZHRFamVyOVQ5cElkWlVaQzNsOUpHUEhBZW41cEJvN3dWVWhwbzE3SGNYYk9VMGwrZzhkY3JROEdUalNhOFdiMVpscTk1bWZoTkcwa0R6YUcvUmVlOTRubVkiLCJtYWMiOiJjODU3YWVlMzE1MGRhZDc2YjgyN2RlODI5Y2ZiZmMzOWM1ZDgxOGMwZmM0MTNlNmI5ZDNiZjU1NzM3MzE1MjM1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:28:15 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlBPZHpZL2dTMTl6YmJTR3dPdkpZdXc9PSIsInZhbHVlIjoiODh2YUJ1cC8vOFdLMHkyYWpxMjhXQ1ZHaUVYOUlxdjNIMHBmN2pmRkdINTBrVWRhOUNQYUthZU9zd01mRmhRWk56VGNvYkQ0RUhLeDhYc2ZTd0h6MnJQcndVcjhFT3kvK0ZOQmxzRUpLdCtPYzdyTFord3ZmRkxZZjEzdGlQRUciLCJtYWMiOiI5ZDc4ZjE0YzMyNzkzZTA2ZmFkMTljOThiYzgzYTk3YjcwMjY2Yjc1ZjQzYjVjYTk2MmJkMGE0YjhiOTI5MjcwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:28:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImJoRnE5WlRhZmFEQVlJTWFyYWcweFE9PSIsInZhbHVlIjoiUFBtTzFOOEdlSEk1eXBoS2kzQWRibUFsRW9oZmY4c0hnMTRkcmEyZHRFamVyOVQ5cElkWlVaQzNsOUpHUEhBZW41cEJvN3dWVWhwbzE3SGNYYk9VMGwrZzhkY3JROEdUalNhOFdiMVpscTk1bWZoTkcwa0R6YUcvUmVlOTRubVkiLCJtYWMiOiJjODU3YWVlMzE1MGRhZDc2YjgyN2RlODI5Y2ZiZmMzOWM1ZDgxOGMwZmM0MTNlNmI5ZDNiZjU1NzM3MzE1MjM1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:28:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1249347455\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-711598164 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WYLFG6nx9g9L9TGC9DkrMmVONrCUfO9cbwe27HPT</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://127.0.0.1:8000/logout</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-711598164\", {\"maxDepth\":0})</script>\n"}}