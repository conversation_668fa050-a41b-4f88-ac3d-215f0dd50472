{"__meta": {"id": "Xa6d496b576c62a441504789a771e8b52", "datetime": "2025-08-19 10:23:50", "utime": 1755570230.331124, "method": "GET", "uri": "/42/kanbanBoard", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 5, "messages": [{"message": "[10:23:49] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755570229.951452, "xdebug_link": null, "collector": "log"}, {"message": "[10:23:50] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/42/kanbanBoard", "message_html": null, "is_string": false, "label": "debug", "time": 1755570230.013416, "xdebug_link": null, "collector": "log"}, {"message": "[10:23:50] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755570230.256427, "xdebug_link": null, "collector": "log"}, {"message": "[10:23:50] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570230.257431, "xdebug_link": null, "collector": "log"}, {"message": "[10:23:50] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755570230.257481, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755570229.66425, "end": 1755570230.331157, "duration": 0.6669070720672607, "duration_str": "667ms", "measures": [{"label": "Booting", "start": 1755570229.66425, "relative_start": 0, "end": 1755570229.932201, "relative_end": 1755570229.932201, "duration": 0.26795101165771484, "duration_str": "268ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755570229.932212, "relative_start": 0.2679622173309326, "end": 1755570230.33116, "relative_end": 3.0994415283203125e-06, "duration": 0.39894795417785645, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24034912, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "kanban.index (\\resources\\views\\kanban\\index.blade.php)", "param_count": 2, "params": ["project", "hasActiveSprint"], "type": "blade"}, {"name": "inc.kanbanStyle (\\resources\\views\\inc\\kanbanStyle.blade.php)", "param_count": 6, "params": ["__env", "app", "menuData", "errors", "project", "hasActiveSprint"], "type": "blade"}, {"name": "inc.kanban.kanban-script-js (\\resources\\views\\inc\\kanban\\kanban-script-js.blade.php)", "param_count": 6, "params": ["__env", "app", "menuData", "errors", "project", "hasActiveSprint"], "type": "blade"}, {"name": "inc.kanban.comments-script-js (\\resources\\views\\inc\\kanban\\comments-script-js.blade.php)", "param_count": 6, "params": ["__env", "app", "menuData", "errors", "project", "hasActiveSprint"], "type": "blade"}]}, "route": {"uri": "GET {proj_id}/kanbanBoard", "middleware": "web", "controller": "App\\Http\\Controllers\\TaskController@kanbanIndex", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "sprint.kanbanPage", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TaskController.php&line=86\">\\app\\Http\\Controllers\\TaskController.php:86-176</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00438, "accumulated_duration_str": "4.38ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 10.274}, {"sql": "select * from `projects` where `id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 88}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:88", "connection": "sagile", "start_percent": 10.274, "width_percent": 9.589}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` = 1 limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 95}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00296, "duration_str": "2.96ms", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:95", "connection": "sagile", "start_percent": 19.863, "width_percent": 67.58}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` is null and `start_sprint` <= '2025-08-19 10:23:50' and `end_sprint` >= '2025-08-19 10:23:50' limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System", "2025-08-19 10:23:50", "2025-08-19 10:23:50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 104}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:104", "connection": "sagile", "start_percent": 87.443, "width_percent": 12.557}]}, "models": {"data": {"App\\Project": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755570230.260534, "xdebug_link": null}]}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/42/kanbanBoard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755570218\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/42/kanbanBoard", "status_code": "<pre class=sf-dump id=sf-dump-62113252 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-62113252\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1526317981 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1526317981\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1528679621 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1528679621\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1749252140 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InRWbzNUVjIzZUtCTVlQbzRSZXlsTkE9PSIsInZhbHVlIjoiRkV5QXNDaW0yQkFTQll3T2pqeGQwNFlwcDVEWERQVVdBRExDRU1FbFdmZ2p2THFIMFFOL09MV1p2ZWx2ZC9BU2V5K0VLRHlZSy91eG01WVZBeUp3Y3J3QjlhbVdMNklIZGlvVTJKRHR2VmRxU3B6dkxMZDBjallISStJRWlUb3giLCJtYWMiOiJkMjA2NTE2MzA3Nzk0ZDRhYWRhNTNlN2JjNDc3ZTI1OGIxOWEyMTc0ZmFjMDAzZGQwY2VlMmRkY2ZjNGU1OGQ1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InhjSGdNTXY4dHBIZU9nYjNyeUVEOFE9PSIsInZhbHVlIjoiYXhQQ2hUSnV6d25JMERNNVI3eVBleDFycGZVN2wxVUFReXIxNWRoOW14eFNyUDVsZDBlb011VVl5alZiU0tpa0IrSHVKR1BTUlRQYXZURVJ6bGZlZHVPWTdKNWRpS3c0TnZzdGZIU2U5QUl6WDZXZzRUS2ZTT0VUNm5xbnFUZXEiLCJtYWMiOiI0MTM2NmIxN2M1YmI4ZDczODBjNzJhZGJlNjgyM2JiMDZlZDA3OGRiMWQxNGQzMWU2NDhlMGMwMGJhN2ZlNjExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1749252140\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1434530346 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52584</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/42/kanbanBoard</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/42/kanbanBoard</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/index.php/42/kanbanBoard</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InRWbzNUVjIzZUtCTVlQbzRSZXlsTkE9PSIsInZhbHVlIjoiRkV5QXNDaW0yQkFTQll3T2pqeGQwNFlwcDVEWERQVVdBRExDRU1FbFdmZ2p2THFIMFFOL09MV1p2ZWx2ZC9BU2V5K0VLRHlZSy91eG01WVZBeUp3Y3J3QjlhbVdMNklIZGlvVTJKRHR2VmRxU3B6dkxMZDBjallISStJRWlUb3giLCJtYWMiOiJkMjA2NTE2MzA3Nzk0ZDRhYWRhNTNlN2JjNDc3ZTI1OGIxOWEyMTc0ZmFjMDAzZGQwY2VlMmRkY2ZjNGU1OGQ1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InhjSGdNTXY4dHBIZU9nYjNyeUVEOFE9PSIsInZhbHVlIjoiYXhQQ2hUSnV6d25JMERNNVI3eVBleDFycGZVN2wxVUFReXIxNWRoOW14eFNyUDVsZDBlb011VVl5alZiU0tpa0IrSHVKR1BTUlRQYXZURVJ6bGZlZHVPWTdKNWRpS3c0TnZzdGZIU2U5QUl6WDZXZzRUS2ZTT0VUNm5xbnFUZXEiLCJtYWMiOiI0MTM2NmIxN2M1YmI4ZDczODBjNzJhZGJlNjgyM2JiMDZlZDA3OGRiMWQxNGQzMWU2NDhlMGMwMGJhN2ZlNjExIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755570229.6642</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755570229</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1434530346\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1412507546 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1412507546\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-713643024 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:23:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImhHYVVRaG9KRTVxNlJ2YW1aOWZpOUE9PSIsInZhbHVlIjoic1JDelRSMU9yRVZKMVUrQTFKcUhPRnJGTXQwWG11OVdaZjhyem5XY25sZ1c3TzhRK3dmSU9kUDVuOXV6bSt1b244UTlOTGJvU0tSUDZaQk9RNkgvSlFvTU9rVXplK3VnNUErQzRXQVNLWGNqTUZWQ3FIWitBVEhQN1FTd1FsYVUiLCJtYWMiOiIxNzI1NjgyZjllYmI3YTRmMGQ2NGI0YTUwMzk2MGQ4NjE1ZTNhZWJkN2QzNmNiYzk3YTdlYWU4OTA4OWMwMjgxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:23:50 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImFtR1pBQnk3R2RNNnBNeTJ6cmp0MEE9PSIsInZhbHVlIjoiR1RBanBITU44bkttaEZqNzZMTHIyaUpOTU9nTGxheWpqaDdwQ2VnNHd0NzNnN29DTU52Q2dudk12VC9yUnR4U0ZuOU9kRyswMTBOZDJDZGFEdXNuZ3Q3RmEzQUtoTTJCdGh0RG9aRFE2NDVLNnN0UGk4akczV0t5WXRabFBGNmgiLCJtYWMiOiJjMzg2OWNiOTFiNzI1ZDdjOWMzNDA3ZjljNWQwYWJiZjA2OGEzMDAwYTcxYTNlZDU4NTgzMDVjYjJjYjRkMzBhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:23:50 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImhHYVVRaG9KRTVxNlJ2YW1aOWZpOUE9PSIsInZhbHVlIjoic1JDelRSMU9yRVZKMVUrQTFKcUhPRnJGTXQwWG11OVdaZjhyem5XY25sZ1c3TzhRK3dmSU9kUDVuOXV6bSt1b244UTlOTGJvU0tSUDZaQk9RNkgvSlFvTU9rVXplK3VnNUErQzRXQVNLWGNqTUZWQ3FIWitBVEhQN1FTd1FsYVUiLCJtYWMiOiIxNzI1NjgyZjllYmI3YTRmMGQ2NGI0YTUwMzk2MGQ4NjE1ZTNhZWJkN2QzNmNiYzk3YTdlYWU4OTA4OWMwMjgxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:23:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImFtR1pBQnk3R2RNNnBNeTJ6cmp0MEE9PSIsInZhbHVlIjoiR1RBanBITU44bkttaEZqNzZMTHIyaUpOTU9nTGxheWpqaDdwQ2VnNHd0NzNnN29DTU52Q2dudk12VC9yUnR4U0ZuOU9kRyswMTBOZDJDZGFEdXNuZ3Q3RmEzQUtoTTJCdGh0RG9aRFE2NDVLNnN0UGk4akczV0t5WXRabFBGNmgiLCJtYWMiOiJjMzg2OWNiOTFiNzI1ZDdjOWMzNDA3ZjljNWQwYWJiZjA2OGEzMDAwYTcxYTNlZDU4NTgzMDVjYjJjYjRkMzBhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:23:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-713643024\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-385853486 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/42/kanbanBoard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755570218</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-385853486\", {\"maxDepth\":0})</script>\n"}}