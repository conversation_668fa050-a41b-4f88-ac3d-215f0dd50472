{"__meta": {"id": "Xae7f56d6e4cb51bee4e035675403e924", "datetime": "2025-08-19 10:59:44", "utime": 1755572384.817288, "method": "GET", "uri": "/tvt/show/42", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[10:59:44] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572384.361327, "xdebug_link": null, "collector": "log"}, {"message": "[10:59:44] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/tvt/show/42", "message_html": null, "is_string": false, "label": "debug", "time": 1755572384.536293, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572383.320739, "end": 1755572384.817334, "duration": 1.4965949058532715, "duration_str": "1.5s", "measures": [{"label": "Booting", "start": 1755572383.320739, "relative_start": 0, "end": 1755572384.299072, "relative_end": 1755572384.299072, "duration": 0.9783329963684082, "duration_str": "978ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572384.299093, "relative_start": 0.9783539772033691, "end": 1755572384.817342, "relative_end": 8.106231689453125e-06, "duration": 0.5182490348815918, "duration_str": "518ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25530200, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 17, "templates": [{"name": "tvt.view (\\resources\\views\\tvt\\view.blade.php)", "param_count": 6, "params": ["title", "project", "results", "sprints", "generalNfrs", "specificNfrs"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\resources\\views\\vendor\\pagination\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 17, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 25, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 26, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 26, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 26, "params": ["__env", "app", "menuData", "errors", "title", "project", "results", "sprints", "generalNfrs", "specificNfrs", "__currentLoopData", "loop", "specificNfrName", "specificNfrId", "general<PERSON>fr<PERSON><PERSON>", "generalNfrId", "__empty_1", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET tvt/show/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\TVTController@show", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "tvt.show", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TVTController.php&line=26\">\\app\\Http\\Controllers\\TVTController.php:26-55</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01141, "accumulated_duration_str": "11.41ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 8.063}, {"sql": "select * from `projects` where `projects`.`id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Project.php", "line": 86}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\TVTController.php", "line": 29}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Project.php:86", "connection": "sagile", "start_percent": 8.063, "width_percent": 7.011}, {"sql": "select distinct `sprint_name` from `sprint` where exists (select * from `user_stories` where `sprint`.`sprint_id` = `user_stories`.`sprint_id` and `proj_id` = '42')", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Sprint.php", "line": 57}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TVTController.php", "line": 32}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Sprint.php:57", "connection": "sagile", "start_percent": 15.074, "width_percent": 6.573}, {"sql": "select `general_nfr`, `general_nfr_id` from `generalnfr`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\GeneralNFR.php", "line": 49}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\TVTController.php", "line": 33}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\GeneralNFR.php:49", "connection": "sagile", "start_percent": 21.648, "width_percent": 6.31}, {"sql": "select `specific_nfr`, `nfr_id` from `nfr`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\SpecificNFR.php", "line": 60}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\TVTController.php", "line": 34}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\app\\SpecificNFR.php:60", "connection": "sagile", "start_percent": 27.958, "width_percent": 5.083}, {"sql": "select count(*) as aggregate from `user_story_general_nfr` where exists (select * from `user_stories` where `user_story_general_nfr`.`user_story_id` = `user_stories`.`u_id` and `proj_id` = '42')", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\UserStoryGeneralNfr.php", "line": 182}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TVTController.php", "line": 44}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00764, "duration_str": "7.64ms", "stmt_id": "\\app\\UserStoryGeneralNfr.php:182", "connection": "sagile", "start_percent": 33.041, "width_percent": 66.959}]}, "models": {"data": {"App\\Project": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/tvt/show/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/tvt/show/42", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1566957489 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1566957489\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-175302078 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-175302078\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1844449830 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://127.0.0.1:8000/tvt</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImFWVko5M1l5ZzdEbGNydHBnUWhQZ3c9PSIsInZhbHVlIjoiT0FXMlZTcVR6Y3Z4c2VRUDdnK1ExemlKUUhDd01nV2k1VUNXNXBvT0ZhNXdicG4rRm0xcG1xZmV1K0gyeG8wbTVTUG1SaFFNNDdFcGptT2pnS2thRDB1aUFsaVRoVFF3aHB2TEtwdVNQMTMxYXFEcmJCZzhleFlmMUg2dFFZZHUiLCJtYWMiOiI5OTkxNmZlZDJkZmI5Mjg3YmM1YTc2ZTU3ZTZkYWQwY2RlM2E5NTIyODY0MTk2NWJmMjA3ZGEzMDdjZDlhZDEzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InBHOVIvaVBBQzdGejRRLzZuR1F5YlE9PSIsInZhbHVlIjoidE4wYW9qNGxla1BUWkVVTmNXMURaU1BxM2J6OStwRTZtVE1OZkJGeTFkOHZmZjZQcnNCWkIzVHg4OVZwYWkxYzAvTnc5Z2Uyd0o2enZVVWRIRTAyTVRTeS9PUHJsdjRVWkJ4S2hwdkZtbWNhUGhiWEtJNWY5WFhyeUpzVnlxZnYiLCJtYWMiOiI3ZTA3ZjBkMjVmMTQ1NzllYzU5MWQ2MmYxNzBlODFmNGY2Yzk1NTE5YmQyZmJhNTI2OTE4NGE2ZmI4YTJlYWIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1844449830\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1326267906 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52927</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/tvt/show/42</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/tvt/show/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/index.php/tvt/show/42</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://127.0.0.1:8000/tvt</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImFWVko5M1l5ZzdEbGNydHBnUWhQZ3c9PSIsInZhbHVlIjoiT0FXMlZTcVR6Y3Z4c2VRUDdnK1ExemlKUUhDd01nV2k1VUNXNXBvT0ZhNXdicG4rRm0xcG1xZmV1K0gyeG8wbTVTUG1SaFFNNDdFcGptT2pnS2thRDB1aUFsaVRoVFF3aHB2TEtwdVNQMTMxYXFEcmJCZzhleFlmMUg2dFFZZHUiLCJtYWMiOiI5OTkxNmZlZDJkZmI5Mjg3YmM1YTc2ZTU3ZTZkYWQwY2RlM2E5NTIyODY0MTk2NWJmMjA3ZGEzMDdjZDlhZDEzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InBHOVIvaVBBQzdGejRRLzZuR1F5YlE9PSIsInZhbHVlIjoidE4wYW9qNGxla1BUWkVVTmNXMURaU1BxM2J6OStwRTZtVE1OZkJGeTFkOHZmZjZQcnNCWkIzVHg4OVZwYWkxYzAvTnc5Z2Uyd0o2enZVVWRIRTAyTVRTeS9PUHJsdjRVWkJ4S2hwdkZtbWNhUGhiWEtJNWY5WFhyeUpzVnlxZnYiLCJtYWMiOiI3ZTA3ZjBkMjVmMTQ1NzllYzU5MWQ2MmYxNzBlODFmNGY2Yzk1NTE5YmQyZmJhNTI2OTE4NGE2ZmI4YTJlYWIzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572383.3207</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572383</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1326267906\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-479131114 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-479131114\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1379819489 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:59:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlpBczlKNEtYMlJ4NW5Cd3VJYmNzVVE9PSIsInZhbHVlIjoia0FMNUZtcDdPWS8wV2RpVEdXcG5LTGFRZzVxaDhLVWVCL1BCK0swYjhDdlhGR0VkMTFhaDFuR000Q29RaTBMSXg1UGtVS1BOR2MrOXZzSnYvVTFYanE2RnNhU3E5ek4xWWtnYlZLVzdYSUFyMjdESjRpakNrTXRQOUZNOGhFSWQiLCJtYWMiOiJlOThhMTVhOWVmMmI4MzYwOTFjOGZjNjMzOWQyMmQ1OTc5N2M1YmI3ZmQ2ZDY5N2ZlNGJiZmY0OGRkNTU2MzAyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:59:44 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjluSFovbUQySzFvZDlGZzhZaENYSGc9PSIsInZhbHVlIjoieklTem90RFV5OUplQjJCeXd0RHJlMk83WjNuTVYyRVZmRXMrSFU1V2E0MTdNMERmY1VSejBlalZUbjlSMEhMSStDK2s2SElEdHRxb3RzMkJKSUZUU0NCVmtrd1E0a0U2cCt2MjNPY3RaY3J6YWtPYjFENEVPTEQrQXJVYkNXRVEiLCJtYWMiOiI0YzQxODgyOWI5MTdkNWIxOWI3YmZhMGUyOGMzZDFmOTRjMTY5MWIxYzc2N2MzMzI0MGExZTU0ZDU4MTE4OTNiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:59:44 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlpBczlKNEtYMlJ4NW5Cd3VJYmNzVVE9PSIsInZhbHVlIjoia0FMNUZtcDdPWS8wV2RpVEdXcG5LTGFRZzVxaDhLVWVCL1BCK0swYjhDdlhGR0VkMTFhaDFuR000Q29RaTBMSXg1UGtVS1BOR2MrOXZzSnYvVTFYanE2RnNhU3E5ek4xWWtnYlZLVzdYSUFyMjdESjRpakNrTXRQOUZNOGhFSWQiLCJtYWMiOiJlOThhMTVhOWVmMmI4MzYwOTFjOGZjNjMzOWQyMmQ1OTc5N2M1YmI3ZmQ2ZDY5N2ZlNGJiZmY0OGRkNTU2MzAyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:59:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjluSFovbUQySzFvZDlGZzhZaENYSGc9PSIsInZhbHVlIjoieklTem90RFV5OUplQjJCeXd0RHJlMk83WjNuTVYyRVZmRXMrSFU1V2E0MTdNMERmY1VSejBlalZUbjlSMEhMSStDK2s2SElEdHRxb3RzMkJKSUZUU0NCVmtrd1E0a0U2cCt2MjNPY3RaY3J6YWtPYjFENEVPTEQrQXJVYkNXRVEiLCJtYWMiOiI0YzQxODgyOWI5MTdkNWIxOWI3YmZhMGUyOGMzZDFmOTRjMTY5MWIxYzc2N2MzMzI0MGExZTU0ZDU4MTE4OTNiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:59:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1379819489\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1323580812 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/tvt/show/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323580812\", {\"maxDepth\":0})</script>\n"}}