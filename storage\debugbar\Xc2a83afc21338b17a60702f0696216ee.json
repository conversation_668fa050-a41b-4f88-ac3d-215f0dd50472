{"__meta": {"id": "Xc2a83afc21338b17a60702f0696216ee", "datetime": "2025-08-19 11:08:14", "utime": 1755572894.585672, "method": "POST", "uri": "/upload-radar-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[11:08:14] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572894.482167, "xdebug_link": null, "collector": "log"}, {"message": "[11:08:14] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/upload-radar-chart", "message_html": null, "is_string": false, "label": "debug", "time": 1755572894.571976, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572893.984424, "end": 1755572894.585705, "duration": 0.601280927658081, "duration_str": "601ms", "measures": [{"label": "Booting", "start": 1755572893.984424, "relative_start": 0, "end": 1755572894.438733, "relative_end": 1755572894.438733, "duration": 0.45430898666381836, "duration_str": "454ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572894.438749, "relative_start": 0.45432496070861816, "end": 1755572894.585708, "relative_end": 2.86102294921875e-06, "duration": 0.1469588279724121, "duration_str": "147ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23492264, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST upload-radar-chart", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@uploadRadarChart", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=141\">\\app\\Http\\Controllers\\CIGController.php:141-151</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00058, "accumulated_duration_str": "580μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/radar-data/42\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"path_info": "/upload-radar-chart", "status_code": "<pre class=sf-dump id=sf-dump-742559789 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-742559789\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1722366257 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"73282 characters\">data:image/png;base64,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</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722366257\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1882352221 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">73294</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlZSUW15VS9FamF2YWhCOFhreFk5b2c9PSIsInZhbHVlIjoiWXcyRnpvNDMveE5ZWjZIUkU0NkVaNG5LbGFiT3kyOXRMMnZVbU1RV25xNkMzU29WY050eHIzaUduVnRTRXVUVCtYaTc4a2NaWktlaDMwMVJIMXhNSk0zbHhGZ1h0emlIdU85bUtJelF2SUdzRDdEZEZFRUJkd2ZxZ0hFQXJ0RXQiLCJtYWMiOiI2Y2FlZjgxMDkzODBjODAwZDBmMGUwNTcwYTcwNjk4Y2UxNjFhODc2MGVmMGUxNWEyNWJkNGI3ZDcwODRlOGNhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IllDSFkwZG1GR216bHRpcmtaa1ozOWc9PSIsInZhbHVlIjoiQTQrRmdjRUtMb2NRaHoyQ3hoVlZSTWtYNDN0U1VscTJyQk1vTWpVSHh4d2NDb2pkMCtxK2JtQVBsUzY3YVhxOWYvS2p5S3c3aHkzSEtNNEg4REN5QURZS0pOTkhiVXo2WUdvbnUzek8reE1BdTd3UVBqWmRqczk1REtwTDhDU3UiLCJtYWMiOiI4YzNjNzUwOWYyMTc2YWNkMzgxNjRhNDc0MDU4M2E3NDA2MDFjYTRjYTUwYzA5YmNlYWY2OWRlMzY4NDNmYzMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1882352221\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1983206099 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57052</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/index.php/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">73294</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">73294</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/cig/create/42</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlZSUW15VS9FamF2YWhCOFhreFk5b2c9PSIsInZhbHVlIjoiWXcyRnpvNDMveE5ZWjZIUkU0NkVaNG5LbGFiT3kyOXRMMnZVbU1RV25xNkMzU29WY050eHIzaUduVnRTRXVUVCtYaTc4a2NaWktlaDMwMVJIMXhNSk0zbHhGZ1h0emlIdU85bUtJelF2SUdzRDdEZEZFRUJkd2ZxZ0hFQXJ0RXQiLCJtYWMiOiI2Y2FlZjgxMDkzODBjODAwZDBmMGUwNTcwYTcwNjk4Y2UxNjFhODc2MGVmMGUxNWEyNWJkNGI3ZDcwODRlOGNhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IllDSFkwZG1GR216bHRpcmtaa1ozOWc9PSIsInZhbHVlIjoiQTQrRmdjRUtMb2NRaHoyQ3hoVlZSTWtYNDN0U1VscTJyQk1vTWpVSHh4d2NDb2pkMCtxK2JtQVBsUzY3YVhxOWYvS2p5S3c3aHkzSEtNNEg4REN5QURZS0pOTkhiVXo2WUdvbnUzek8reE1BdTd3UVBqWmRqczk1REtwTDhDU3UiLCJtYWMiOiI4YzNjNzUwOWYyMTc2YWNkMzgxNjRhNDc0MDU4M2E3NDA2MDFjYTRjYTUwYzA5YmNlYWY2OWRlMzY4NDNmYzMyIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572893.9844</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572893</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1983206099\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1298942789 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1298942789\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-30516043 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 03:08:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkNzaXlhVTE0Rng1ZVplZkRKWUhmZGc9PSIsInZhbHVlIjoiN2lWRDUxNWZKVTNlMTdMYWttQit1dkxLbkl4ZVVhR1NwRkNEbjFPWjBDYW1Jci9KVXJqRWYzZkRqbHRiWDU3Y0l4UVhpeEVhQmU2eXZIQlRCTzA2aVRYQ0ZRTUxUd3Z0OTMwbEhMTWhVZk1Xc2g2Rm9UeDhMQVNqVFcrR3VOM0EiLCJtYWMiOiIxMWQ0NzFmYTA5ZDY0YTY4ZjI4Zjc2YzU0ODM1OTVjODc3OTQ4Yjc5ZjJiNGQ5NjI5YmUyN2Y4MmU3ZWY0MTgwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:14 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Iml5d25Ib1BQbm1sMUM3d29CMGkrQ0E9PSIsInZhbHVlIjoidFFuamNER1JQRUVZeGIzZHlTVG9TTld6ajJXNk1UeGc4MkpTNmJEay9RMUtCcm1jT0p4YWcrQ3NSbHhUK2RHSFhhU1IxSmplV2dCUE1BNkRDM05jSGFEQWpleHM5QVVXNWZHam9HR29JYjFnODZ1dnJEbjF2SGpJL2lwcG55WGwiLCJtYWMiOiJiMzVmZmVjMzFiZmUyMWQyNzlmNDU5ZDJhZTY3MmE1N2Y1MTk0MWJiZmUyNWRhMjY4YmIwODMzZDk2YjViMmE2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:14 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkNzaXlhVTE0Rng1ZVplZkRKWUhmZGc9PSIsInZhbHVlIjoiN2lWRDUxNWZKVTNlMTdMYWttQit1dkxLbkl4ZVVhR1NwRkNEbjFPWjBDYW1Jci9KVXJqRWYzZkRqbHRiWDU3Y0l4UVhpeEVhQmU2eXZIQlRCTzA2aVRYQ0ZRTUxUd3Z0OTMwbEhMTWhVZk1Xc2g2Rm9UeDhMQVNqVFcrR3VOM0EiLCJtYWMiOiIxMWQ0NzFmYTA5ZDY0YTY4ZjI4Zjc2YzU0ODM1OTVjODc3OTQ4Yjc5ZjJiNGQ5NjI5YmUyN2Y4MmU3ZWY0MTgwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Iml5d25Ib1BQbm1sMUM3d29CMGkrQ0E9PSIsInZhbHVlIjoidFFuamNER1JQRUVZeGIzZHlTVG9TTld6ajJXNk1UeGc4MkpTNmJEay9RMUtCcm1jT0p4YWcrQ3NSbHhUK2RHSFhhU1IxSmplV2dCUE1BNkRDM05jSGFEQWpleHM5QVVXNWZHam9HR29JYjFnODZ1dnJEbjF2SGpJL2lwcG55WGwiLCJtYWMiOiJiMzVmZmVjMzFiZmUyMWQyNzlmNDU5ZDJhZTY3MmE1N2Y1MTk0MWJiZmUyNWRhMjY4YmIwODMzZDk2YjViMmE2IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:08:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-30516043\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-770736141 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/radar-data/42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-770736141\", {\"maxDepth\":0})</script>\n"}}