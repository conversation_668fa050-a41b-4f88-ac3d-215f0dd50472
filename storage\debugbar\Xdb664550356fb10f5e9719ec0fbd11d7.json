{"__meta": {"id": "Xdb664550356fb10f5e9719ec0fbd11d7", "datetime": "2025-08-19 14:28:47", "utime": 1755584927.241387, "method": "GET", "uri": "/project-assignments", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[14:28:46] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584926.823001, "xdebug_link": null, "collector": "log"}, {"message": "[14:28:47] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/project-assignments", "message_html": null, "is_string": false, "label": "debug", "time": 1755584927.085671, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584926.282374, "end": 1755584927.241413, "duration": 0.9590392112731934, "duration_str": "959ms", "measures": [{"label": "Booting", "start": 1755584926.282374, "relative_start": 0, "end": 1755584926.792125, "relative_end": 1755584926.792125, "duration": 0.5097510814666748, "duration_str": "510ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584926.792136, "relative_start": 0.5097620487213135, "end": 1755584927.241415, "relative_end": 1.9073486328125e-06, "duration": 0.4492790699005127, "duration_str": "449ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25527592, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 14, "templates": [{"name": "project-assignments.index (\\resources\\views\\project-assignments\\index.blade.php)", "param_count": 3, "params": ["projects", "projectAssignments", "managedTeams"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "projects", "projectAssignments", "managedTeams", "__empty_1", "__currentLoopData", "project", "loop", "assignments", "assignedCount", "assignment", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET project-assignments", "middleware": "web", "controller": "App\\Http\\Controllers\\ProjectAssignmentController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "project-assignments.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProjectAssignmentController.php&line=18\">\\app\\Http\\Controllers\\ProjectAssignmentController.php:18-52</a>"}, "queries": {"nb_statements": 20, "nb_failed_statements": 0, "accumulated_duration": 0.01143, "accumulated_duration_str": "11.43ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 6.124}, {"sql": "select exists(select * from `user_role` where `user_id` = 30 and `role_id` = 0) as `exists`", "type": "query", "params": [], "bindings": ["30", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "\\app\\Traits\\HasCachedPermissions.php", "line": 279}, {"index": 10, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 13, "namespace": null, "name": "\\app\\Traits\\HasCachedPermissions.php", "line": 275}, {"index": 14, "namespace": null, "name": "\\app\\User.php", "line": 202}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Traits\\HasCachedPermissions.php:279", "connection": "sagile", "start_percent": 6.124, "width_percent": 6.824}, {"sql": "select `team_name`, `role_name`, `project_id` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": "middleware", "name": "load.permissions", "line": 81}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 16, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00074, "duration_str": "740μs", "stmt_id": "middleware::load.permissions:81", "connection": "sagile", "start_percent": 12.948, "width_percent": 6.474}, {"sql": "select `role_id` from `user_role` where `user_id` = 30", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": "middleware", "name": "load.permissions", "line": 87}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 16, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "middleware::load.permissions:87", "connection": "sagile", "start_percent": 19.423, "width_percent": 3.587}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Team Manager' and `project_id` is null limit 1", "type": "query", "params": [], "bindings": ["Team Manager"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 23.01, "width_percent": 6.387}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Project Manager' and `project_id` = 42 limit 1", "type": "query", "params": [], "bindings": ["Project Manager", "42"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 29.396, "width_percent": 3.762}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Team Manager' and `project_id` is null limit 1", "type": "query", "params": [], "bindings": ["Team Manager"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 33.158, "width_percent": 3.587}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Project Manager' and `project_id` = 46 limit 1", "type": "query", "params": [], "bindings": ["Project Manager", "46"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 36.745, "width_percent": 4.112}, {"sql": "select `permission`.`key` from `permission_role` inner join `permission` on `permission`.`id` = `permission_role`.`permission_id` where `permission_role`.`role_id` = 39", "type": "query", "params": [], "bindings": ["39"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": "middleware", "name": "load.permissions", "line": 121}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 16, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "middleware::load.permissions:121", "connection": "sagile", "start_percent": 40.857, "width_percent": 7.524}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Project Manager' and `project_id` = 47 limit 1", "type": "query", "params": [], "bindings": ["Project Manager", "47"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 48.381, "width_percent": 3.762}, {"sql": "select `permission`.`key` from `permission_role` inner join `permission` on `permission`.`id` = `permission_role`.`permission_id` where `permission_role`.`role_id` = 41", "type": "query", "params": [], "bindings": ["41"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": "middleware", "name": "load.permissions", "line": 121}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 16, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "middleware::load.permissions:121", "connection": "sagile", "start_percent": 52.143, "width_percent": 4.549}, {"sql": "select `role_id`, `role_name`, `project_id` from `roles` where `role_name` = 'Project Manager' and `project_id` = 47 limit 1", "type": "query", "params": [], "bindings": ["Project Manager", "47"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "middleware", "name": "load.permissions", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 18, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "middleware::load.permissions:115", "connection": "sagile", "start_percent": 56.693, "width_percent": 3.937}, {"sql": "select `permission`.`key` from `permission_role` inner join `permission` on `permission`.`id` = `permission_role`.`permission_id` where `permission_role`.`role_id` = 41", "type": "query", "params": [], "bindings": ["41"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": "middleware", "name": "load.permissions", "line": 121}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 391}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 418}, {"index": 16, "namespace": "middleware", "name": "load.permissions", "line": 76}, {"index": 17, "namespace": "middleware", "name": "load.permissions", "line": 26}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "middleware::load.permissions:121", "connection": "sagile", "start_percent": 60.63, "width_percent": 6.124}, {"sql": "select `team_name` from `teammappings` where `project_id` is null and `username` = 'ivlyn' and `invitation_status` = 'accepted'", "type": "query", "params": [], "bindings": ["ivlyn", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 26}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:26", "connection": "sagile", "start_percent": 66.754, "width_percent": 5.074}, {"sql": "select * from `projects` where `team_name` in ('iv<PERSON>\\'s team', 'Team 888')", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team", "Team 888"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 29}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:29", "connection": "sagile", "start_percent": 71.829, "width_percent": 7.349}, {"sql": "select * from `teammappings` where `project_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 34}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:34", "connection": "sagile", "start_percent": 79.178, "width_percent": 4.024}, {"sql": "select * from `users` where `users`.`username` in ('UAT_1', 'ammarjmldnout', 'ivlyn', 'tay')", "type": "query", "params": [], "bindings": ["UAT_1", "ammarjmldnout", "ivlyn", "tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 34}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:34", "connection": "sagile", "start_percent": 83.202, "width_percent": 4.024}, {"sql": "select * from `projects` where `projects`.`id` in (41, 42, 46, 47)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 34}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:34", "connection": "sagile", "start_percent": 87.227, "width_percent": 3.85}, {"sql": "select * from `teams` where `teams`.`team_name` in ('Team 888', 'iv<PERSON>\\'s team', 'uatTestTeam1')", "type": "query", "params": [], "bindings": ["Team 888", "ivlyn&#039;s team", "uatTestTeam1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 34}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:34", "connection": "sagile", "start_percent": 91.076, "width_percent": 4.462}, {"sql": "select `team_name` from `teammappings` where `project_id` is null and `username` = 'ivlyn' and `role_name` = 'Team Manager' and `invitation_status` = 'accepted'", "type": "query", "params": [], "bindings": ["ivlyn", "Team Manager", "accepted"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProjectAssignmentController.php", "line": 48}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Http\\Controllers\\ProjectAssignmentController.php:48", "connection": "sagile", "start_percent": 95.538, "width_percent": 4.462}]}, "models": {"data": {"App\\Team": 2, "App\\TeamMapping": 9, "App\\Project": 4, "App\\User": 5}, "count": 20}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "j69KvKRJ480m4dMfFPg0rRxXnFo31vWsOPunt4tq", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/project-assignments\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/project-assignments", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1908138676 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1908138676\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1768175947 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1768175947\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2020617677 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjE5UWdVRm5ieEhxVldxYkppSXlDNnc9PSIsInZhbHVlIjoia0xSdW53SVJGTmVtSmp6VFpGYXJ3Lzd2cVlwVFdQYmo2d2JSeXZiUXZ3akFHRnUrQkVHVmNHQVY3d2hyU3V0RVBJZ0NCY3FMMGtYQ1REVWp1bTg2THoyZHNnTEVzY0ltT0cvRHpIMUU0b0V5dzJDM08ySVZIMUg4Z2JOZ3Exd0YiLCJtYWMiOiJlNGE3MmFmMjNlYTZjYTc4YjJkNTdiMzNkZDU5NmZmMDM5MzU5NzdiNWZiODc3NWEyMWQzNzQxNTc4NzhjNjliIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkFWTkRSbzZTMUwwdEJ6bmRpQzFoOXc9PSIsInZhbHVlIjoia0JwbWd3aVFXMDJpc3VoK1hGTldDTWxrOFFWajlwSE9obXVoKzQvSmh2T2JtU09sUS9JTXFYSml5WFBiUzY5WUZHUU85VFJPYzhsY1RxMlBaUnRPOWtZTnMyRDA5NXZxMllGbDJtNWZJVFBYL3pRdC9wVmpLUHFYL1FCd1QyWDIiLCJtYWMiOiIxNjhhNWE0OGFiNGU3M2FkMzMyMzhmNWYxMTgzMjAwMTgwMDAyZjcxMTk2MmIxYTE0OGZiZjVjYjc2N2VlZTNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2020617677\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-188265228 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">63287</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/project-assignments</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"20 characters\">/project-assignments</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"30 characters\">/index.php/project-assignments</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjE5UWdVRm5ieEhxVldxYkppSXlDNnc9PSIsInZhbHVlIjoia0xSdW53SVJGTmVtSmp6VFpGYXJ3Lzd2cVlwVFdQYmo2d2JSeXZiUXZ3akFHRnUrQkVHVmNHQVY3d2hyU3V0RVBJZ0NCY3FMMGtYQ1REVWp1bTg2THoyZHNnTEVzY0ltT0cvRHpIMUU0b0V5dzJDM08ySVZIMUg4Z2JOZ3Exd0YiLCJtYWMiOiJlNGE3MmFmMjNlYTZjYTc4YjJkNTdiMzNkZDU5NmZmMDM5MzU5NzdiNWZiODc3NWEyMWQzNzQxNTc4NzhjNjliIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkFWTkRSbzZTMUwwdEJ6bmRpQzFoOXc9PSIsInZhbHVlIjoia0JwbWd3aVFXMDJpc3VoK1hGTldDTWxrOFFWajlwSE9obXVoKzQvSmh2T2JtU09sUS9JTXFYSml5WFBiUzY5WUZHUU85VFJPYzhsY1RxMlBaUnRPOWtZTnMyRDA5NXZxMllGbDJtNWZJVFBYL3pRdC9wVmpLUHFYL1FCd1QyWDIiLCJtYWMiOiIxNjhhNWE0OGFiNGU3M2FkMzMyMzhmNWYxMTgzMjAwMTgwMDAyZjcxMTk2MmIxYTE0OGZiZjVjYjc2N2VlZTNiIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584926.2824</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584926</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-188265228\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2050665117 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">j69KvKRJ480m4dMfFPg0rRxXnFo31vWsOPunt4tq</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0RBr6QsuV8E1F5EFbFz8ORnbThS49hTgTSbqklTh</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050665117\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-769539583 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:28:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im02eEIvRzNCUTJvck1NamwyQ25EalE9PSIsInZhbHVlIjoidURpakQrcjVaeElCblJkdTFGNEpLTEdLbHdtaGl1S3R1TXBnTHZ3cHZJK1JmWk0xTzFhQ3YrbFEwM3QxbzV5cWVQTXFuYjVDTEtVRTJEQnRXdkZVZGc4dXM4VWcyeHBjMTVyYlVoSXhDNmdQUXJ2amdSVCs3SXdSSFNVM1h6NXAiLCJtYWMiOiIzZGRiYmUwMjM3MTVkMDAzYWI5OGY4ZWVmYjQ2YzM2MzI5NzI3NGZmMDc1NDdiNDc3OTVjMGIxNzcyNGMzZmNjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:28:47 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlVIc2Rzd2dnZW5lU3h5Q0d0UEgrcVE9PSIsInZhbHVlIjoiazVQVldiWVlkc1c4VU85NTdSbEVzUi83RngyQk5xZk1aTmlud2hxNFZUcEFDU0xpYWVVR2dYQ0dGNnByTzJBZ2ZHWklaZXkyMkdxK3lXNzZxQ1FkQWdqMEdRM1lYaGFiYzBibEVLb1hxTlNxcVN0Ry9GUUlOSHd5Y2lMU3ovS1QiLCJtYWMiOiIxNTYwMmMxOTA5OTBjOTY0OGVhMThiNzc0ODllZWY3NDg2MzE3ODMxODI5ODE2ZGFkNjRiZWM4OTc0NGM0N2FiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:28:47 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im02eEIvRzNCUTJvck1NamwyQ25EalE9PSIsInZhbHVlIjoidURpakQrcjVaeElCblJkdTFGNEpLTEdLbHdtaGl1S3R1TXBnTHZ3cHZJK1JmWk0xTzFhQ3YrbFEwM3QxbzV5cWVQTXFuYjVDTEtVRTJEQnRXdkZVZGc4dXM4VWcyeHBjMTVyYlVoSXhDNmdQUXJ2amdSVCs3SXdSSFNVM1h6NXAiLCJtYWMiOiIzZGRiYmUwMjM3MTVkMDAzYWI5OGY4ZWVmYjQ2YzM2MzI5NzI3NGZmMDc1NDdiNDc3OTVjMGIxNzcyNGMzZmNjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:28:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlVIc2Rzd2dnZW5lU3h5Q0d0UEgrcVE9PSIsInZhbHVlIjoiazVQVldiWVlkc1c4VU85NTdSbEVzUi83RngyQk5xZk1aTmlud2hxNFZUcEFDU0xpYWVVR2dYQ0dGNnByTzJBZ2ZHWklaZXkyMkdxK3lXNzZxQ1FkQWdqMEdRM1lYaGFiYzBibEVLb1hxTlNxcVN0Ry9GUUlOSHd5Y2lMU3ovS1QiLCJtYWMiOiIxNTYwMmMxOTA5OTBjOTY0OGVhMThiNzc0ODllZWY3NDg2MzE3ODMxODI5ODE2ZGFkNjRiZWM4OTc0NGM0N2FiIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:28:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-769539583\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-911191062 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">j69KvKRJ480m4dMfFPg0rRxXnFo31vWsOPunt4tq</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/project-assignments</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911191062\", {\"maxDepth\":0})</script>\n"}}