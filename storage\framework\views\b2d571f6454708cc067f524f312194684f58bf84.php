<?php
    // Make sure we have a proper project object for permission checks
    $projectObj = $project instanceof \App\Project ? $project : \App\Project::find($project['id'] ?? null);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Project Statuses</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f4f6f9;
            padding: 20px;
            margin: 0;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .table {
            background-color: white;
            border-radius: 0.25rem;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .alert {
            margin-bottom: 1rem;
        }
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        .drag-handle {
            cursor: grab;
            color: #999;
            width: 20px;
            text-align: center;
            user-select: none;
        }
        .dragging {
            opacity: 0.5;
            background: #f8f9fa;
            cursor: grabbing !important;
        }
        .dragging .drag-handle {
            cursor: grabbing;
        }
        .status-row.drop-target {
            border-top: 2px solid #0d6efd;
        }
        .status-row.drop-target-bottom {
            border-bottom: 2px solid #0d6efd;
        }
        /* Notification Styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            z-index: 1001;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }
        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
        }
        .notification i {
            font-size: 1.2em;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo e(session('success')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0">Project Statuses</h4>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('add_status', $projectObj)): ?>
                    <a href="<?php echo e(route('statuses.create', $project['id'])); ?>" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i> Add Status
                    </a>
                    <?php endif; ?>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="40px"></th>
                                <th>Status Name</th>
                                <th>Slug</th>
                                <th>Order</th>
                                <?php if(Auth::user()->can('edit_status', $projectObj) || Auth::user()->can('delete_status', $projectObj)): ?>
                                <th>Actions</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody id="status-tbody">
                            <?php $__empty_1 = true; $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr data-status-id="<?php echo e($status->id); ?>" draggable="true" class="status-row">
                                    <td class="drag-handle">::</td>
                                    <td><?php echo e($status->title); ?></td>
                                    <td><?php echo e($status->slug); ?></td>
                                    <td><?php echo e($status->order); ?></td>
                                    <?php if(Auth::user()->can('edit_status', $projectObj) || Auth::user()->can('delete_status', $projectObj)): ?>
                                    <td>
                                        <div class="action-buttons">
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit_status', $projectObj)): ?>
                                            <a href="<?php echo e(route('statuses.edit', $status)); ?>" 
                                               class="btn btn-primary btn-sm">
                                                <i class="fas fa-edit me-1"></i> Edit
                                            </a>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete_status', $projectObj)): ?>
                                            <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo e($status->id); ?>">
                                                <i class="fas fa-trash me-1"></i> Delete
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <?php endif; ?>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="<?php echo e((Auth::user()->can('edit_status', $projectObj) || Auth::user()->can('delete_status', $projectObj)) ? 4 : 3); ?>" class="text-center">No status added yet</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modals -->
    <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $modalStatus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete_status', $projectObj)): ?>
        <div class="modal fade" id="deleteModal<?php echo e($modalStatus->id); ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo e($modalStatus->id); ?>" aria-hidden="true" data-bs-backdrop="false">
            <div class="modal-dialog shadow-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteModalLabel<?php echo e($modalStatus->id); ?>">Confirm Delete</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        Are you sure you want to delete the status "<?php echo e($modalStatus->title); ?>"? This action cannot be undone.
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <form action="<?php echo e(route('statuses.destroy', $modalStatus)); ?>" method="get">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-danger">Delete Status</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tbody = document.getElementById('status-tbody');
            let draggingElement = null;

            // Add drag event listeners to all rows
            document.querySelectorAll('.status-row').forEach(row => {
                row.addEventListener('dragstart', handleDragStart);
                row.addEventListener('dragend', handleDragEnd);
                row.addEventListener('dragover', handleDragOver);
                row.addEventListener('dragenter', handleDragEnter);
                row.addEventListener('dragleave', handleDragLeave);
                row.addEventListener('drop', handleDrop);
            });

            function handleDragStart(e) {
                draggingElement = this;
                this.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
                
                // Add delay to change cursor
                setTimeout(() => {
                    document.body.style.cursor = 'grabbing';
                }, 0);
            }

            function handleDragEnd(e) {
                document.body.style.cursor = '';
                draggingElement.classList.remove('dragging');
                // Remove all drop target indicators
                document.querySelectorAll('.status-row').forEach(row => {
                    row.classList.remove('drop-target', 'drop-target-bottom');
                });
                draggingElement = null;
            }

            function handleDragOver(e) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
                
                if (!draggingElement || draggingElement === this) return;
                
                const rect = this.getBoundingClientRect();
                const midY = rect.top + rect.height / 2;
                
                // Remove existing indicators
                this.classList.remove('drop-target', 'drop-target-bottom');
                
                // Add indicator based on mouse position
                if (e.clientY < midY) {
                    this.classList.add('drop-target');
                } else {
                    this.classList.add('drop-target-bottom');
                }
            }

            function handleDragEnter(e) {
                e.preventDefault();
            }

            function handleDragLeave(e) {
                this.classList.remove('drop-target', 'drop-target-bottom');
            }

            function handleDrop(e) {
                e.preventDefault();
                if (draggingElement === this) return;

                const rect = this.getBoundingClientRect();
                const midY = rect.top + rect.height / 2;
                
                if (e.clientY < midY) {
                    this.parentNode.insertBefore(draggingElement, this);
                } else {
                    this.parentNode.insertBefore(draggingElement, this.nextSibling);
                }

                updateOrder();
            }

            function updateOrder() {
                const rows = [...document.querySelectorAll('.status-row')];
                const orderData = rows.map((row, index) => ({
                    id: row.dataset.statusId,
                    order: index + 1
                }));

                fetch(`/statuses/reorder`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({ orders: orderData })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update the order numbers displayed in the table
                        rows.forEach((row, index) => {
                            row.querySelector('td:nth-child(4)').textContent = index + 1;
                        });
                        showNotification('Status order updated successfully!', 'success');
                    } else {
                        showNotification('Failed to update status order', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Error updating status order', 'error');
                });
            }

            function showNotification(message, type = 'success') {
                // Create notification element
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.innerHTML = `
                    <div class="notification-content">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                        <span>${message}</span>
                    </div>
                `;
                
                // Add dynamic styles
                notification.style.background = type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6';
                
                document.body.appendChild(notification);
                
                // Animate in
                setTimeout(() => {
                    notification.style.opacity = '1';
                    notification.style.transform = 'translateX(0)';
                }, 100);
                
                // Remove after 3 seconds
                setTimeout(() => {
                    notification.style.opacity = '0';
                    notification.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }
        });
    </script>
</body>
</html>

<?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/status/project.blade.php ENDPATH**/ ?>