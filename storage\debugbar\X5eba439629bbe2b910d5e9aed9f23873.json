{"__meta": {"id": "X5eba439629bbe2b910d5e9aed9f23873", "datetime": "2025-08-19 14:20:55", "utime": 1755584455.64177, "method": "GET", "uri": "/projects/45", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 45, "messages": [{"message": "[14:20:55] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755584455.453158, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_kanban on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.576531, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.57802, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_burndown on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.584176, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.585272, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_backlog on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.585647, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.586658, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_userstory on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.586967, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.588087, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_forum on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.588385, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.589453, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_bugtracking on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.589745, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.590805, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_roles on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.591206, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.592485, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_status on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.592846, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.593923, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_kanban on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.594247, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.59525, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_details on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.595477, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.596365, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_kanban on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.596661, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.597695, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_burndown on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.598101, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.599139, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_backlog on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.599495, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.600561, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_userstory on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.600898, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.601919, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_forum on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.602244, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.603194, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_bugtracking on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.603436, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.604433, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_roles on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.604717, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.605477, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_status on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.605718, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.606429, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_kanban on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.60666, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.6075, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: view_details on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.607749, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.608489, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: edit_details on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.608813, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.609535, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: Gate check for permission: delete_details on project: 45 (SAgile) for user: <PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.609768, "xdebug_link": null, "collector": "log"}, {"message": "[14:20:55] LOG.debug: User is admin - access granted", "message_html": null, "is_string": false, "label": "debug", "time": 1755584455.610519, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755584455.100433, "end": 1755584455.641829, "duration": 0.541395902633667, "duration_str": "541ms", "measures": [{"label": "Booting", "start": 1755584455.100433, "relative_start": 0, "end": 1755584455.429836, "relative_end": 1755584455.429836, "duration": 0.3294029235839844, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755584455.429851, "relative_start": 0.3294179439544678, "end": 1755584455.64183, "relative_end": 9.5367431640625e-07, "duration": 0.21197891235351562, "duration_str": "212ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24104936, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 10, "templates": [{"name": "project.details (\\resources\\views\\project\\details.blade.php)", "param_count": 1, "params": ["project"], "type": "blade"}, {"name": "inc.breadcrumbStyle (\\resources\\views\\inc\\breadcrumbStyle.blade.php)", "param_count": 5, "params": ["__env", "app", "menuData", "errors", "project"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 5, "params": ["__env", "app", "menuData", "errors", "project"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 13, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "project", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET projects/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\ProductFeatureController@details", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "projects.details", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ProductFeatureController.php&line=133\">\\app\\Http\\Controllers\\ProductFeatureController.php:133-153</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0016100000000000003, "accumulated_duration_str": "1.61ms", "statements": [{"sql": "select * from `users` where `id` = 31 limit 1", "type": "query", "params": [], "bindings": ["31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 34.161}, {"sql": "select `team_name` from `teammappings` where `username` = 'tay'", "type": "query", "params": [], "bindings": ["tay"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 140}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:140", "connection": "sagile", "start_percent": 34.161, "width_percent": 31.677}, {"sql": "select * from `projects` where `id` = '45' and `team_name` in ('Team 888', 'Team 888', 'Team AD', 'Team AD') limit 1", "type": "query", "params": [], "bindings": ["45", "Team 888", "Team 888", "Team AD", "Team AD"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\ProductFeatureController.php", "line": 146}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\app\\Http\\Controllers\\ProductFeatureController.php:146", "connection": "sagile", "start_percent": 65.839, "width_percent": 34.161}]}, "models": {"data": {"App\\Project": 1, "App\\User": 1}, "count": 2}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 22, "messages": [{"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.582144, "xdebug_link": null}, {"message": "[\n  ability => view_burndown,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1160997097 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_burndown</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1160997097\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.585442, "xdebug_link": null}, {"message": "[\n  ability => view_backlog,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2017783191 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2017783191\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.58679, "xdebug_link": null}, {"message": "[\n  ability => view_userstory,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1284513297 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1284513297\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.588215, "xdebug_link": null}, {"message": "[\n  ability => view_forum,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1068646903 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_forum</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1068646903\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.589578, "xdebug_link": null}, {"message": "[\n  ability => view_bugtracking,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2004537012 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_bugtracking</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2004537012\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.590971, "xdebug_link": null}, {"message": "[\n  ability => view_roles,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-267761207 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-267761207\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.592641, "xdebug_link": null}, {"message": "[\n  ability => view_status,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1230257436 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1230257436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.594056, "xdebug_link": null}, {"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1576032831 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1576032831\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.595347, "xdebug_link": null}, {"message": "[\n  ability => view_details,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1266061025 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1266061025\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.596491, "xdebug_link": null}, {"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1944915942 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1944915942\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.597847, "xdebug_link": null}, {"message": "[\n  ability => view_burndown,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1230199987 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">view_burndown</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1230199987\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.599268, "xdebug_link": null}, {"message": "[\n  ability => view_backlog,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2046436936 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_backlog</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2046436936\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.600686, "xdebug_link": null}, {"message": "[\n  ability => view_userstory,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1938212080 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">view_userstory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1938212080\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.602038, "xdebug_link": null}, {"message": "[\n  ability => view_forum,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-651167109 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_forum</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-651167109\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.603285, "xdebug_link": null}, {"message": "[\n  ability => view_bugtracking,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-647447034 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">view_bugtracking</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-647447034\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.60455, "xdebug_link": null}, {"message": "[\n  ability => view_roles,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1458830690 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">view_roles</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1458830690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.605566, "xdebug_link": null}, {"message": "[\n  ability => view_status,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1371749573 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_status</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1371749573\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.606514, "xdebug_link": null}, {"message": "[\n  ability => view_kanban,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-674598701 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">view_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-674598701\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.607603, "xdebug_link": null}, {"message": "[\n  ability => view_details,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-762951705 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">view_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-762951705\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.608574, "xdebug_link": null}, {"message": "[\n  ability => edit_details,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-595299627 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">edit_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-595299627\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.60962, "xdebug_link": null}, {"message": "[\n  ability => delete_details,\n  result => true,\n  user => 31,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1650037900 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">delete_details</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1650037900\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755584455.610603, "xdebug_link": null}]}, "session": {"_token": "p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/projects/45\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "31", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755584266\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/projects/45", "status_code": "<pre class=sf-dump id=sf-dump-980504982 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-980504982\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1142051113 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1142051113\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1643750866 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1643750866\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1372659779 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InVTZDVNb0dpMmJTRGJ0YU93bDdhNUE9PSIsInZhbHVlIjoiTzUrWkgzdTRvUDRJdXNIbWlLWlloWDVROHJFb3BWdkU3V0s5NWFSZ2xpZVpnVkxSR0kwQ3d0d1NCdTBOaDVnZjZ2MFlkRlFrMWN2TjNFL2hGaU1oNlQvVklUWVJGL2dQSWVXQ0wrUGRzNDgzbU5tU1psckhUQmFNNG9WOThJUWYiLCJtYWMiOiIwMTFlZDk5ZTBlMmYxMTI1YjkxYmYwM2NkMDYyMGQ4ZmM0MGI0MDBlZWFjOGYzMzc5YzVmMzE0OTE3ZDUzYzBkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImVTZ3F0UDRNMm1qWHhpNHhvUFF6RGc9PSIsInZhbHVlIjoiU0hNdUNkTnlWZDI0dHZzRHVENXdyUkRPRXhRTGdyZVZoN0hIb2x6OVdabmloc2tNRkQ5NWp3L3ZsY1Y3NGJpOGF6N2dMTDN5b000ZzI2QS9yTFkxSjJFRVE1KzVxQWpmMzl4aXBpTkVQNjZvUFRYejZrNFIweklyOEFzTGgyTVgiLCJtYWMiOiIxNmJiNjcwOTc1NjgyNjFkMzAxZjRmYmNhODA0ZWJlNWI0MjU1ZDBjYWYwYTNmNmJiZTBhY2MzODRiM2IzMmYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1372659779\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1813098467 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">63580</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/projects/45</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"12 characters\">/projects/45</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/index.php/projects/45</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/projects</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InVTZDVNb0dpMmJTRGJ0YU93bDdhNUE9PSIsInZhbHVlIjoiTzUrWkgzdTRvUDRJdXNIbWlLWlloWDVROHJFb3BWdkU3V0s5NWFSZ2xpZVpnVkxSR0kwQ3d0d1NCdTBOaDVnZjZ2MFlkRlFrMWN2TjNFL2hGaU1oNlQvVklUWVJGL2dQSWVXQ0wrUGRzNDgzbU5tU1psckhUQmFNNG9WOThJUWYiLCJtYWMiOiIwMTFlZDk5ZTBlMmYxMTI1YjkxYmYwM2NkMDYyMGQ4ZmM0MGI0MDBlZWFjOGYzMzc5YzVmMzE0OTE3ZDUzYzBkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImVTZ3F0UDRNMm1qWHhpNHhvUFF6RGc9PSIsInZhbHVlIjoiU0hNdUNkTnlWZDI0dHZzRHVENXdyUkRPRXhRTGdyZVZoN0hIb2x6OVdabmloc2tNRkQ5NWp3L3ZsY1Y3NGJpOGF6N2dMTDN5b000ZzI2QS9yTFkxSjJFRVE1KzVxQWpmMzl4aXBpTkVQNjZvUFRYejZrNFIweklyOEFzTGgyTVgiLCJtYWMiOiIxNmJiNjcwOTc1NjgyNjFkMzAxZjRmYmNhODA0ZWJlNWI0MjU1ZDBjYWYwYTNmNmJiZTBhY2MzODRiM2IzMmYzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755584455.1004</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755584455</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1813098467\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-399009617 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">CfapZDSBofQwHxJsJhdmHULLiISKd6PUgVWjQqhr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-399009617\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:20:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im91d09pV0xXZVAzbUEvV2VhLzBWSVE9PSIsInZhbHVlIjoiT0YvazZjQW5ITmhNY0F5MnVxUUd0eUpwSVJyODFuNTFWc2R4L25CcjZUb3ByOXJmQjE5RUk2Z2p3VTBOWXlJQmRraHQwcHdjc0dHQ0N2ZmpDamZUYnNmNmdyZDMrWXV0ODdRZUdmdXh3OWp6cldvbWF5NXVKaGhyQTZ5cmlvdGUiLCJtYWMiOiI2OGVjYTMzYjQwMjdjZGI3Mzk5ZWI4NDAxZGZlODJmOTg0NDc3YmQxMWE5ZmJmMmU4ZjA2NDYwMWMzNmZlODM1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:20:55 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IndjRWNNaWdVVWlMM1FQdVY0bVNhZVE9PSIsInZhbHVlIjoic245MlY2SFBwN05QWjVLaHA3Y3g3bk9uU0pINEhBRlZWQ0FIYUtFNmEzUERwWnRIdTh2VXhGajdnQlRKeThGM3NjSUNQYWQrTzdxQzFSQVBYbFYrTzZEV0JlbjZqUTNCdzNuZlFkdVBqRVRmRVc0RnJGaG9BUlU4N29CbFFxazEiLCJtYWMiOiJhMmNlMGE3MzhhOTM4OTQ1N2RlM2YzZWQ2YTY0MWNmYWFhNjQyMTY3YTgxMjQyZTQ4ZWRmYzFhNDNhOTM5NWU0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:20:55 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im91d09pV0xXZVAzbUEvV2VhLzBWSVE9PSIsInZhbHVlIjoiT0YvazZjQW5ITmhNY0F5MnVxUUd0eUpwSVJyODFuNTFWc2R4L25CcjZUb3ByOXJmQjE5RUk2Z2p3VTBOWXlJQmRraHQwcHdjc0dHQ0N2ZmpDamZUYnNmNmdyZDMrWXV0ODdRZUdmdXh3OWp6cldvbWF5NXVKaGhyQTZ5cmlvdGUiLCJtYWMiOiI2OGVjYTMzYjQwMjdjZGI3Mzk5ZWI4NDAxZGZlODJmOTg0NDc3YmQxMWE5ZmJmMmU4ZjA2NDYwMWMzNmZlODM1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:20:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IndjRWNNaWdVVWlMM1FQdVY0bVNhZVE9PSIsInZhbHVlIjoic245MlY2SFBwN05QWjVLaHA3Y3g3bk9uU0pINEhBRlZWQ0FIYUtFNmEzUERwWnRIdTh2VXhGajdnQlRKeThGM3NjSUNQYWQrTzdxQzFSQVBYbFYrTzZEV0JlbjZqUTNCdzNuZlFkdVBqRVRmRVc0RnJGaG9BUlU4N29CbFFxazEiLCJtYWMiOiJhMmNlMGE3MzhhOTM4OTQ1N2RlM2YzZWQ2YTY0MWNmYWFhNjQyMTY3YTgxMjQyZTQ4ZWRmYzFhNDNhOTM5NWU0IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:20:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1721889180 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">p1Uz4Ln9RIHZfxGQiT9EJOgs76zEs7JhxJcduGuY</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>31</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755584266</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721889180\", {\"maxDepth\":0})</script>\n"}}