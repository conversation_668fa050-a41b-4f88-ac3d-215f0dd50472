{"__meta": {"id": "Xaa1e768b2b84b067cac26da082a7db7f", "datetime": "2025-08-19 13:33:58", "utime": 1755581638.216635, "method": "GET", "uri": "/forum/43", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[13:33:58] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755581638.114231, "xdebug_link": null, "collector": "log"}, {"message": "[13:33:58] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/forum/43", "message_html": null, "is_string": false, "label": "debug", "time": 1755581638.17286, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755581637.832121, "end": 1755581638.216662, "duration": 0.38454103469848633, "duration_str": "385ms", "measures": [{"label": "Booting", "start": 1755581637.832121, "relative_start": 0, "end": 1755581638.094992, "relative_end": 1755581638.094992, "duration": 0.26287102699279785, "duration_str": "263ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755581638.095004, "relative_start": 0.26288318634033203, "end": 1755581638.216664, "relative_end": 2.1457672119140625e-06, "duration": 0.12165999412536621, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23827312, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "forum.index (\\resources\\views\\forum\\index.blade.php)", "param_count": 3, "params": ["forumPosts", "selectedCate<PERSON><PERSON>", "projectId"], "type": "blade"}, {"name": "pagination::tailwind (\\resources\\views\\vendor\\pagination\\tailwind.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "layouts.detailsPartialLoad (\\resources\\views\\layouts\\detailsPartialLoad.blade.php)", "param_count": 7, "params": ["__env", "app", "menuData", "errors", "forumPosts", "selectedCate<PERSON><PERSON>", "projectId"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 15, "params": ["__env", "app", "menuData", "errors", "forumPosts", "selectedCate<PERSON><PERSON>", "projectId", "isMenu", "contentNavbar", "containerNav", "isNavbar", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "forumPosts", "selectedCate<PERSON><PERSON>", "projectId", "isMenu", "contentNavbar", "containerNav", "isNavbar", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "forumPosts", "selectedCate<PERSON><PERSON>", "projectId", "isMenu", "contentNavbar", "containerNav", "isNavbar", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "forumPosts", "selectedCate<PERSON><PERSON>", "projectId", "isMenu", "contentNavbar", "containerNav", "isNavbar", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET forum/{projectId}", "middleware": "web", "controller": "App\\Http\\Controllers\\ForumController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "forum.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\ForumController.php&line=13\">\\app\\Http\\Controllers\\ForumController.php:13-31</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00088, "accumulated_duration_str": "880μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 50}, {"sql": "select count(*) as aggregate from `forums` where `project_id` = '43'", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\ForumController.php", "line": 23}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Http\\Controllers\\ForumController.php:23", "connection": "sagile", "start_percent": 50, "width_percent": 50}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/forum/43\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/forum/43", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-576823975 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-576823975\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-232440482 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-232440482\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1872670066 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkVSSGNmUmxlbjlsWkJQRG1WeDVrd2c9PSIsInZhbHVlIjoiWGdweGs3QW04aGl5YjMxNkxzTXlpc3BvM3dGU2F3cWlpSnM5dEVuUlBoa0djdTgrenRHb2dLSHV2ZkV2K1c1WEU4WnhTbE5PUTczeVJYUGUxb1lBYzYwN09jS290QkFOMHdIWXU2eWNUOC9ldGREOFNjQi9WRk1FOW5leEtmNVIiLCJtYWMiOiJmNDZmYjRjODM4ODUwY2ZiYjdkMWNmZWVhNmQwNTYwYjk0OTNkNTIyYmJiODRlN2ZhMTE4ZTE0YTg0YTBjYjBmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkZBemYvL0N4dHNCNDhnVVRGdE1GSnc9PSIsInZhbHVlIjoiMGVEQ0JGSW1TVytRNkI2V25XQXo3ZW9SdURkTmxrTlQwbUFZK2RyaHJ2d0UyNGRKY3VuRG1EM2JrNUdhcU00UVBKT2VpMVV0VTNLd3loTGU5TWFZdEx5dzl5cnMyR25iazUrR0dpSjFGUWJHQjg2bFltOVEySm1pcXFmbDY5eWsiLCJtYWMiOiI1ZDQ5N2RjMWQzMmU1NTI2YzAzYWU2N2UyZjJhYWQyMGNlMTMwOTMwYjExNzAzOTNmN2YzN2NiYTY5NjFmMDU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1872670066\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-490902261 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60145</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/forum/43</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/forum/43</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/index.php/forum/43</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/43</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkVSSGNmUmxlbjlsWkJQRG1WeDVrd2c9PSIsInZhbHVlIjoiWGdweGs3QW04aGl5YjMxNkxzTXlpc3BvM3dGU2F3cWlpSnM5dEVuUlBoa0djdTgrenRHb2dLSHV2ZkV2K1c1WEU4WnhTbE5PUTczeVJYUGUxb1lBYzYwN09jS290QkFOMHdIWXU2eWNUOC9ldGREOFNjQi9WRk1FOW5leEtmNVIiLCJtYWMiOiJmNDZmYjRjODM4ODUwY2ZiYjdkMWNmZWVhNmQwNTYwYjk0OTNkNTIyYmJiODRlN2ZhMTE4ZTE0YTg0YTBjYjBmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkZBemYvL0N4dHNCNDhnVVRGdE1GSnc9PSIsInZhbHVlIjoiMGVEQ0JGSW1TVytRNkI2V25XQXo3ZW9SdURkTmxrTlQwbUFZK2RyaHJ2d0UyNGRKY3VuRG1EM2JrNUdhcU00UVBKT2VpMVV0VTNLd3loTGU5TWFZdEx5dzl5cnMyR25iazUrR0dpSjFGUWJHQjg2bFltOVEySm1pcXFmbDY5eWsiLCJtYWMiOiI1ZDQ5N2RjMWQzMmU1NTI2YzAzYWU2N2UyZjJhYWQyMGNlMTMwOTMwYjExNzAzOTNmN2YzN2NiYTY5NjFmMDU5IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755581637.8321</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755581637</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-490902261\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-589651515 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GKxRuD042xMQ4k3v5EHCZLsF1sfAXXnsz4rkTCog</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-589651515\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1373561024 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 05:33:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ii9vajlNSkRrcWtmVzdEb0JTQXpGakE9PSIsInZhbHVlIjoiK2V1b0RHdWhNRFZ5SEJyQnZlbzhlNkhPVmpBN2tyZFZGQ2dOM04xSk9vam8xSjBvMzVRV255bC8vOWN5d2kwb0lwODNYNnQrMGY2UTdIRmZFZzVJL0xheUZ5TVk0ZUFnZmNORW43d0tyc2EzWDhOMWdkcS81VU4xci9vTnBwUkwiLCJtYWMiOiIxODRlMmRmMDRiZDNlNDBjZDI2MGM4NzlhNWVhNmZhZDEzOGFlYWNlN2JjZWU3NjgzMGU5NTZkZmFlMDZkNjUwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:33:58 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjE0QkhaYmNDWjFDN3JLdWJpcHJmcXc9PSIsInZhbHVlIjoiZ1NPMWYyMkNMcHJuYWVNaS9sVWNsTnBjaDh3TFZRczIyMWwva2t0eHE0NjM3K2N5QVdwZk1vY0NDclNEbEJiNkU1ZW5DOVl0S0crRkNHTjZxSlkwakdFak5IeG9ucUpBYkFtZ3JjT2pEV2JVQVFhaXQ4akNCcWtibGtCZThkMHYiLCJtYWMiOiIxZTJhY2Y5MTFiNGNhOTdjNTY0N2M4ZjE2OGVmNGU5Zjg4ZDM3MjY4NTQ1ZjFkYzIyMTY5ZDIzNTZmMTJlNjE1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:33:58 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ii9vajlNSkRrcWtmVzdEb0JTQXpGakE9PSIsInZhbHVlIjoiK2V1b0RHdWhNRFZ5SEJyQnZlbzhlNkhPVmpBN2tyZFZGQ2dOM04xSk9vam8xSjBvMzVRV255bC8vOWN5d2kwb0lwODNYNnQrMGY2UTdIRmZFZzVJL0xheUZ5TVk0ZUFnZmNORW43d0tyc2EzWDhOMWdkcS81VU4xci9vTnBwUkwiLCJtYWMiOiIxODRlMmRmMDRiZDNlNDBjZDI2MGM4NzlhNWVhNmZhZDEzOGFlYWNlN2JjZWU3NjgzMGU5NTZkZmFlMDZkNjUwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:33:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjE0QkhaYmNDWjFDN3JLdWJpcHJmcXc9PSIsInZhbHVlIjoiZ1NPMWYyMkNMcHJuYWVNaS9sVWNsTnBjaDh3TFZRczIyMWwva2t0eHE0NjM3K2N5QVdwZk1vY0NDclNEbEJiNkU1ZW5DOVl0S0crRkNHTjZxSlkwakdFak5IeG9ucUpBYkFtZ3JjT2pEV2JVQVFhaXQ4akNCcWtibGtCZThkMHYiLCJtYWMiOiIxZTJhY2Y5MTFiNGNhOTdjNTY0N2M4ZjE2OGVmNGU5Zjg4ZDM3MjY4NTQ1ZjFkYzIyMTY5ZDIzNTZmMTJlNjE1IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 07:33:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1373561024\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1786316837 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dKocF2yn7fDQrGrffou2LjBG15SedrZKMLgZ8AQZ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/forum/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1786316837\", {\"maxDepth\":0})</script>\n"}}