{"__meta": {"id": "X8cb81c3eb2a0a8bce1518c47c5d7aa61", "datetime": "2025-08-19 10:56:59", "utime": 1755572219.84867, "method": "POST", "uri": "/upload-radar-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[10:56:59] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572219.729166, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:59] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/upload-radar-chart", "message_html": null, "is_string": false, "label": "debug", "time": 1755572219.834182, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572219.257888, "end": 1755572219.848694, "duration": 0.5908060073852539, "duration_str": "591ms", "measures": [{"label": "Booting", "start": 1755572219.257888, "relative_start": 0, "end": 1755572219.696556, "relative_end": 1755572219.696556, "duration": 0.43866801261901855, "duration_str": "439ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572219.696575, "relative_start": 0.4386868476867676, "end": 1755572219.848697, "relative_end": 2.86102294921875e-06, "duration": 0.15212202072143555, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23402120, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST upload-radar-chart", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@uploadRadarChart", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=141\">\\app\\Http\\Controllers\\CIGController.php:141-151</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00065, "accumulated_duration_str": "650μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/radar-data/42/36\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"path_info": "/upload-radar-chart", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-329584960 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-329584960\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-434182755 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"27978 characters\">data:image/png;base64,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</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-434182755\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-878014228 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">27990</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjlyZWx0VldFVmgxV2FTa3NwMjFiMGc9PSIsInZhbHVlIjoiQno4SVBCeE9Ta2RnYUxFbnBzejhZSTZNWWFxd0dRVjBVbUFmeHgvTVBOSS9mWnRGY2E0YXJRZUpOaitlcDlqdWY1NEwrQmRGSmVYcEovelpDdnpnT0Rpcy94am1oMTZ0bEtzUmpmWXhjRGFmWThtaGRlY0N2QjladFBkSVBhZmoiLCJtYWMiOiI1ZWNiNjE0MDc1ZTY1ZDM2NjRlMWNlYjNmNGJiYTQwYWY3YzE1NjVhY2ZjNTU1NmQxMTg3OGIzMzk0NzU0ZTg2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IktnQ3N6ZzFYRHl3bWI5MXN2RkhWZFE9PSIsInZhbHVlIjoiak1rcHQwT3BhOFB6NE1EdlhUK3c1UDA0TWoxVjdIbFU2bFI1d1MvbmVva1lhdVVyaEV0S0VHRzRpU0tKdjNrc09scEhwSXZ0Rk9CN0NkekNMM1FoVUNvSmc2TkJZV1p2Nlh6NCtnbTMyYWpSbUdvTnZpWms5VVMxc1c4RmpvTkYiLCJtYWMiOiJmYjc4MWM2ZDMyNDMxMGM0MGFiMDJlNTA1YjhkYTkwMDBiMmI0MjljZTM1MzI5MTBhMzE3Y2U1N2JiNDgzNGQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-878014228\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1131721192 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52072</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/index.php/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">27990</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">27990</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjlyZWx0VldFVmgxV2FTa3NwMjFiMGc9PSIsInZhbHVlIjoiQno4SVBCeE9Ta2RnYUxFbnBzejhZSTZNWWFxd0dRVjBVbUFmeHgvTVBOSS9mWnRGY2E0YXJRZUpOaitlcDlqdWY1NEwrQmRGSmVYcEovelpDdnpnT0Rpcy94am1oMTZ0bEtzUmpmWXhjRGFmWThtaGRlY0N2QjladFBkSVBhZmoiLCJtYWMiOiI1ZWNiNjE0MDc1ZTY1ZDM2NjRlMWNlYjNmNGJiYTQwYWY3YzE1NjVhY2ZjNTU1NmQxMTg3OGIzMzk0NzU0ZTg2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IktnQ3N6ZzFYRHl3bWI5MXN2RkhWZFE9PSIsInZhbHVlIjoiak1rcHQwT3BhOFB6NE1EdlhUK3c1UDA0TWoxVjdIbFU2bFI1d1MvbmVva1lhdVVyaEV0S0VHRzRpU0tKdjNrc09scEhwSXZ0Rk9CN0NkekNMM1FoVUNvSmc2TkJZV1p2Nlh6NCtnbTMyYWpSbUdvTnZpWms5VVMxc1c4RmpvTkYiLCJtYWMiOiJmYjc4MWM2ZDMyNDMxMGM0MGFiMDJlNTA1YjhkYTkwMDBiMmI0MjljZTM1MzI5MTBhMzE3Y2U1N2JiNDgzNGQ1IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572219.2579</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572219</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1131721192\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-856069305 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-856069305\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-705625853 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:56:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjJlZHpMQ05XTHdpMEFYWHBMSTIrS2c9PSIsInZhbHVlIjoiaHB3amY2bEtLODg3eVRNZEtyL3NrVVNJU2VWbnkyQUlrNTZseFdBWStoM1lWdzJBSnJmOE51aXJwMHd6UGt1eGMyYVNLcHpBS29DdTZOTHlhUEFMSC9aTXhyRXVHNGNYejd4RnJMN2hEbUpIR3lOeFhjWG1UUVlMTStMb3FHV0giLCJtYWMiOiJkNjI4NzYyZTFkNTBjOGE5MzUzNjdmZmI4NjY4NzBjM2MwZDA4ZDNlMDFiZmYyOGI1Y2VjYjc1NTE4MGY0OTkyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:59 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImUxeHdGRVFITmJOMDlTZlM3YVh1THc9PSIsInZhbHVlIjoick5LQUNxSW5KK0x5WUhxZGtKN2hiVUI2NFpHTWphcW5ER2FWVlJ5YjBTNC9MNWNUSkhWTGhEaDBnK2FlekJCT2dEWW9xM2h2a3dWYWJjYkhJSVhscWs0czMxZGVpVExlRDVhM3h3TCs5S0JpUUJWSmlVR0lQdCtvUkRIYTQ4bTQiLCJtYWMiOiI2ZjQ2YWVkMzY3YjUzMDA3NTE2Yzg1ODczNWM4NTVkNDhlODBiODI3ZjUzNDY5OTJiMjU3MTQ5MmRhMGFlYjNmIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:59 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjJlZHpMQ05XTHdpMEFYWHBMSTIrS2c9PSIsInZhbHVlIjoiaHB3amY2bEtLODg3eVRNZEtyL3NrVVNJU2VWbnkyQUlrNTZseFdBWStoM1lWdzJBSnJmOE51aXJwMHd6UGt1eGMyYVNLcHpBS29DdTZOTHlhUEFMSC9aTXhyRXVHNGNYejd4RnJMN2hEbUpIR3lOeFhjWG1UUVlMTStMb3FHV0giLCJtYWMiOiJkNjI4NzYyZTFkNTBjOGE5MzUzNjdmZmI4NjY4NzBjM2MwZDA4ZDNlMDFiZmYyOGI1Y2VjYjc1NTE4MGY0OTkyIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImUxeHdGRVFITmJOMDlTZlM3YVh1THc9PSIsInZhbHVlIjoick5LQUNxSW5KK0x5WUhxZGtKN2hiVUI2NFpHTWphcW5ER2FWVlJ5YjBTNC9MNWNUSkhWTGhEaDBnK2FlekJCT2dEWW9xM2h2a3dWYWJjYkhJSVhscWs0czMxZGVpVExlRDVhM3h3TCs5S0JpUUJWSmlVR0lQdCtvUkRIYTQ4bTQiLCJtYWMiOiI2ZjQ2YWVkMzY3YjUzMDA3NTE2Yzg1ODczNWM4NTVkNDhlODBiODI3ZjUzNDY5OTJiMjU3MTQ5MmRhMGFlYjNmIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:56:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-705625853\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-239005703 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/radar-data/42/36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-239005703\", {\"maxDepth\":0})</script>\n"}}