{"__meta": {"id": "Xb7d332ac3514f4d68c4ca001e3f2f066", "datetime": "2025-08-19 14:01:16", "utime": 1755583276.449412, "method": "GET", "uri": "/43/kanbanBoard", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 89, "messages": [{"message": "[14:01:16] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755583276.222506, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/43/kanbanBoard", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.292642, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: addLane_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.356639, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.357672, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.357725, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: editLane_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.36214, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.362975, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.363026, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: deleteLane_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.363291, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.364093, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.36414, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: addTask_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.364375, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.365123, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.365169, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: editLane_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.366327, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.367156, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.367212, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: deleteLane_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.367452, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.368274, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.368321, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: addTask_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.368547, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.369291, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.369338, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: editLane_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.369655, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.37039, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.370435, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: deleteLane_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.370653, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.371382, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.371427, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: addTask_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.371645, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.372442, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.372487, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.3728, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.373551, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.373605, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.373836, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.374624, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.374669, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: editTask_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.374917, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.375825, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.375872, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: deleteTask_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.376102, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.37686, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.376908, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: addComment_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.392407, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.393377, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.393433, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.393774, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.394628, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.394681, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.394989, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.395967, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.396016, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: editTask_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.396378, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.397165, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.397212, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: deleteTask_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.397468, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.39824, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.398287, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: addComment_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.410093, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.411028, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.411082, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: editLane_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.411437, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.412215, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.412264, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: deleteLane_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.412526, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.413296, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.413343, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: addTask_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.413577, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.414328, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.414376, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.414698, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.415487, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.415538, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.41587, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.416704, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.416752, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: editTask_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.417033, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.417791, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.417849, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: deleteTask_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.418151, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.418983, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.419029, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: addComment_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.431511, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.432412, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.432466, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Gate check for permission: updateTaskStatus_kanban on project: 43 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.432956, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team Team 888: Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.43374, "xdebug_link": null, "collector": "log"}, {"message": "[14:01:16] LOG.debug: Using team role map for team Team 888", "message_html": null, "is_string": false, "label": "debug", "time": 1755583276.43379, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755583275.901899, "end": 1755583276.449523, "duration": 0.547623872756958, "duration_str": "548ms", "measures": [{"label": "Booting", "start": 1755583275.901899, "relative_start": 0, "end": 1755583276.200597, "relative_end": 1755583276.200597, "duration": 0.29869794845581055, "duration_str": "299ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755583276.200619, "relative_start": 0.2987198829650879, "end": 1755583276.449525, "relative_end": 2.1457672119140625e-06, "duration": 0.24890613555908203, "duration_str": "249ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24502152, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "kanban.index (\\resources\\views\\kanban\\index.blade.php)", "param_count": 7, "params": ["statuses", "tasksByStatus", "sprint", "project", "sort_date", "isSprintOverdue", "hasActiveSprint"], "type": "blade"}, {"name": "inc.kanbanStyle (\\resources\\views\\inc\\kanbanStyle.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "statuses", "tasksByStatus", "sprint", "project", "sort_date", "isSprintOverdue", "hasActiveSprint"], "type": "blade"}, {"name": "inc.kanban.kanban-script-js (\\resources\\views\\inc\\kanban\\kanban-script-js.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "statuses", "tasksByStatus", "sprint", "project", "sort_date", "isSprintOverdue", "hasActiveSprint", "__currentLoopData", "status", "loop", "taskList", "task"], "type": "blade"}, {"name": "inc.kanban.comments-script-js (\\resources\\views\\inc\\kanban\\comments-script-js.blade.php)", "param_count": 16, "params": ["__env", "app", "menuData", "errors", "statuses", "tasksByStatus", "sprint", "project", "sort_date", "isSprintOverdue", "hasActiveSprint", "__currentLoopData", "status", "loop", "taskList", "task"], "type": "blade"}]}, "route": {"uri": "GET {proj_id}/kanbanBoard", "middleware": "web", "controller": "App\\Http\\Controllers\\TaskController@kanbanIndex", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "sprint.kanbanPage", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TaskController.php&line=86\">\\app\\Http\\Controllers\\TaskController.php:86-176</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.00423, "accumulated_duration_str": "4.23ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 13.948}, {"sql": "select * from `projects` where `id` = '43' limit 1", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 88}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:88", "connection": "sagile", "start_percent": 13.948, "width_percent": 11.111}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System' and `active_sprint` = 1 limit 1", "type": "query", "params": [], "bindings": ["Food Ordering System", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 95}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:95", "connection": "sagile", "start_percent": 25.059, "width_percent": 11.584}, {"sql": "select * from `statuses` where `project_id` = '43' order by `order` asc", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 137}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:137", "connection": "sagile", "start_percent": 36.643, "width_percent": 13.475}, {"sql": "select * from `tasks` where `proj_id` = '43' and `sprint_id` = 46 order by `order` asc", "type": "query", "params": [], "bindings": ["43", "46"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 140}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:140", "connection": "sagile", "start_percent": 50.118, "width_percent": 13.239}, {"sql": "select * from `taskComment` where `taskComment`.`task_id` = 122 and `taskComment`.`task_id` is not null", "type": "query", "params": [], "bindings": ["122"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "b1591381181a1d3992389b86a4585ff2ce5f4ff1", "line": 115}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 24, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "view::b1591381181a1d3992389b86a4585ff2ce5f4ff1:115", "connection": "sagile", "start_percent": 63.357, "width_percent": 15.13}, {"sql": "select * from `taskComment` where `taskComment`.`task_id` = 121 and `taskComment`.`task_id` is not null", "type": "query", "params": [], "bindings": ["121"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "b1591381181a1d3992389b86a4585ff2ce5f4ff1", "line": 115}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 24, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "view::b1591381181a1d3992389b86a4585ff2ce5f4ff1:115", "connection": "sagile", "start_percent": 78.487, "width_percent": 10.875}, {"sql": "select * from `taskComment` where `taskComment`.`task_id` = 120 and `taskComment`.`task_id` is not null", "type": "query", "params": [], "bindings": ["120"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "b1591381181a1d3992389b86a4585ff2ce5f4ff1", "line": 115}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}, {"index": 24, "namespace": null, "name": "\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php", "line": 37}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "view::b1591381181a1d3992389b86a4585ff2ce5f4ff1:115", "connection": "sagile", "start_percent": 89.362, "width_percent": 10.638}]}, "models": {"data": {"App\\Task": 3, "App\\Status": 4, "App\\Sprint": 1, "App\\Project": 1, "App\\User": 1}, "count": 10}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 29, "messages": [{"message": "[\n  ability => addLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1791699456 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">addLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1791699456\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.361212, "xdebug_link": null}, {"message": "[\n  ability => editLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-130823068 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">editLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-130823068\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.363161, "xdebug_link": null}, {"message": "[\n  ability => deleteLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2134245660 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">deleteLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2134245660\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.364255, "xdebug_link": null}, {"message": "[\n  ability => addTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-437356129 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">addTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-437356129\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.365281, "xdebug_link": null}, {"message": "[\n  ability => editLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1136780181 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">editLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1136780181\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.367328, "xdebug_link": null}, {"message": "[\n  ability => deleteLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-474033213 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">deleteLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-474033213\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.368431, "xdebug_link": null}, {"message": "[\n  ability => addTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1229079510 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">addTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1229079510\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.369445, "xdebug_link": null}, {"message": "[\n  ability => editLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1989477216 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">editLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1989477216\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.370541, "xdebug_link": null}, {"message": "[\n  ability => deleteLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1674288722 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">deleteLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1674288722\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.37153, "xdebug_link": null}, {"message": "[\n  ability => addTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1448329670 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">addTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1448329670\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.3726, "xdebug_link": null}, {"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1593942245 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1593942245\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.37372, "xdebug_link": null}, {"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-913556658 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-913556658\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.374778, "xdebug_link": null}, {"message": "[\n  ability => editTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-642039909 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">editTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-642039909\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.375984, "xdebug_link": null}, {"message": "[\n  ability => deleteTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-346186042 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">deleteTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-346186042\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.37702, "xdebug_link": null}, {"message": "[\n  ability => addComment_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-46589768 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">addComment_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-46589768\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.393592, "xdebug_link": null}, {"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1059518535 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059518535\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.39484, "xdebug_link": null}, {"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-190936895 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190936895\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.396153, "xdebug_link": null}, {"message": "[\n  ability => editTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1139586247 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">editTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1139586247\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.397339, "xdebug_link": null}, {"message": "[\n  ability => deleteTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1690025877 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">deleteTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1690025877\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.39841, "xdebug_link": null}, {"message": "[\n  ability => addComment_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-173600047 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">addComment_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-173600047\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.411242, "xdebug_link": null}, {"message": "[\n  ability => editLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1656558329 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">editLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1656558329\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.412395, "xdebug_link": null}, {"message": "[\n  ability => deleteLane_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-53381214 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">deleteLane_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-53381214\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.413457, "xdebug_link": null}, {"message": "[\n  ability => addTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1316600866 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">addTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1316600866\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.41449, "xdebug_link": null}, {"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-589696169 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-589696169\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.415696, "xdebug_link": null}, {"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-841804919 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-841804919\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.416886, "xdebug_link": null}, {"message": "[\n  ability => editTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-945255396 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">editTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-945255396\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.417996, "xdebug_link": null}, {"message": "[\n  ability => deleteTask_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2020155092 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">deleteTask_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2020155092\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.41914, "xdebug_link": null}, {"message": "[\n  ability => addComment_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1530795338 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">addComment_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1530795338\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.432632, "xdebug_link": null}, {"message": "[\n  ability => updateTaskStatus_kanban,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-702015849 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">updateTaskStatus_kanban</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702015849\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755583276.433924, "xdebug_link": null}]}, "session": {"_token": "0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/43/kanbanBoard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755581799\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/43/kanbanBoard", "status_code": "<pre class=sf-dump id=sf-dump-1638225266 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1638225266\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1478305172 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1478305172\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-222027888 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-222027888\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1214146664 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IktVbHNNL2ZWTURSZS9wc055dHprNVE9PSIsInZhbHVlIjoiZzY5QzMySjgzOVY0UVh6bTlkemRGaDVodStoa3Vva3gxMnh2RmhIOEV4T2V4ZmYxWG1BUklxUUJBOW1uWVcrVHlJbWxIUTBJc2VKc1Ztd05ULzZYZnl1QWhWZXg2ZFdRMm8ySXBubHdNYTBIT2k4NisraisrNitTMUREYkl6ZUEiLCJtYWMiOiIyZjY1ZWM2ZDMyMGU0YjQwNDU3ODBjNmVjZGE5MWZlZTQyNmRkNWNkZDBkODgyOWM2ZWYxMmE4MDc4ZTlhMTI2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik9SL0pWQzdNRnRUcWc0WU8xNUZ2MFE9PSIsInZhbHVlIjoiWlgvTlpqQSs3VnkyVGw1MFNZTVNaNUZEcjQ4eUlEeXppRUxJMW5pQlkwZVlyNjl0WHRlaFZsVy84SWNUbWxVWVZ4ZUhXdHJPK3FYUm9hdGRkVi9ObEZMWnd2T2pFWDk3eHBnRzY5WHBUY0RwM2hvR2RCWTlhU2F3NmtFK3doZFgiLCJtYWMiOiIwZDVhZTkyZjBiZjlmZmE4OWVlM2ZkYmM3YzQ2NjYxYmQxNjhlYWU1MTQ3NTZhNDE3ODFjZTdlMjU4YTM0YjlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1214146664\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-2112472197 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55772</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/43/kanbanBoard</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"15 characters\">/43/kanbanBoard</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/index.php/43/kanbanBoard</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/projects/43</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IktVbHNNL2ZWTURSZS9wc055dHprNVE9PSIsInZhbHVlIjoiZzY5QzMySjgzOVY0UVh6bTlkemRGaDVodStoa3Vva3gxMnh2RmhIOEV4T2V4ZmYxWG1BUklxUUJBOW1uWVcrVHlJbWxIUTBJc2VKc1Ztd05ULzZYZnl1QWhWZXg2ZFdRMm8ySXBubHdNYTBIT2k4NisraisrNitTMUREYkl6ZUEiLCJtYWMiOiIyZjY1ZWM2ZDMyMGU0YjQwNDU3ODBjNmVjZGE5MWZlZTQyNmRkNWNkZDBkODgyOWM2ZWYxMmE4MDc4ZTlhMTI2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik9SL0pWQzdNRnRUcWc0WU8xNUZ2MFE9PSIsInZhbHVlIjoiWlgvTlpqQSs3VnkyVGw1MFNZTVNaNUZEcjQ4eUlEeXppRUxJMW5pQlkwZVlyNjl0WHRlaFZsVy84SWNUbWxVWVZ4ZUhXdHJPK3FYUm9hdGRkVi9ObEZMWnd2T2pFWDk3eHBnRzY5WHBUY0RwM2hvR2RCWTlhU2F3NmtFK3doZFgiLCJtYWMiOiIwZDVhZTkyZjBiZjlmZmE4OWVlM2ZkYmM3YzQ2NjYxYmQxNjhlYWU1MTQ3NTZhNDE3ODFjZTdlMjU4YTM0YjlhIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755583275.9019</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755583275</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2112472197\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1035037979 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sJsZVyNti3aBaTmYcYNDvPUlZjMRPBfWfAuqfNPK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035037979\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-539125106 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 06:01:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImRHSjU0dlJSQjcvcFc2QXdGNWZ4TGc9PSIsInZhbHVlIjoiZWFTTEZ0WlBFUlZFY2Q0MkJBSlE4TkpUbWJEQVNwMmxSNEJwWjVnVllIS05Dcm9yOHdqaWtNRzFPSDlnMU5VdWZ6cmh5SUlydXY3NEFKOXF1UDhqZ2FDeDh0KzNWd3d6cnJXZmpUdlR5WUFqU0ROMk1aeDM2ZjczU3ZmQVZTc1UiLCJtYWMiOiI3NDgxYzg2MTY1MmE0ZTkwOTkxODMxMmVjYzA1NWM4Yzk3Mzg3ZTQwNWQzMzk0N2E0ZWIzNmVkNjRlMTFmNzhjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:01:16 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Inh6UzRacTV2dmhBUm5SK2RTd0tHWlE9PSIsInZhbHVlIjoicTVGNmFJNkR0bVorRGs2NTJTMVpyWFl5aFlNekNNQlp0bXVwdmlxWUVZU3UvTUx3U09QN2F4aDJwanpuSXRBc0xVbytvMFd6SWJNUHcrYksvUEdUaExSQlpSc09hcFFOdGd5UzAxZFgzNXdmSzBCMEIzMm1PRjA0ZWthSUZXYngiLCJtYWMiOiJhMWMzZjkxNWNkNWIxNzgxYTJmM2JkZmJlMGQwNDE2Y2FhMWE2ZjUxNDkxN2VhZTQ3N2U0NjE5ODRmN2MyNWQxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:01:16 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImRHSjU0dlJSQjcvcFc2QXdGNWZ4TGc9PSIsInZhbHVlIjoiZWFTTEZ0WlBFUlZFY2Q0MkJBSlE4TkpUbWJEQVNwMmxSNEJwWjVnVllIS05Dcm9yOHdqaWtNRzFPSDlnMU5VdWZ6cmh5SUlydXY3NEFKOXF1UDhqZ2FDeDh0KzNWd3d6cnJXZmpUdlR5WUFqU0ROMk1aeDM2ZjczU3ZmQVZTc1UiLCJtYWMiOiI3NDgxYzg2MTY1MmE0ZTkwOTkxODMxMmVjYzA1NWM4Yzk3Mzg3ZTQwNWQzMzk0N2E0ZWIzNmVkNjRlMTFmNzhjIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:01:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Inh6UzRacTV2dmhBUm5SK2RTd0tHWlE9PSIsInZhbHVlIjoicTVGNmFJNkR0bVorRGs2NTJTMVpyWFl5aFlNekNNQlp0bXVwdmlxWUVZU3UvTUx3U09QN2F4aDJwanpuSXRBc0xVbytvMFd6SWJNUHcrYksvUEdUaExSQlpSc09hcFFOdGd5UzAxZFgzNXdmSzBCMEIzMm1PRjA0ZWthSUZXYngiLCJtYWMiOiJhMWMzZjkxNWNkNWIxNzgxYTJmM2JkZmJlMGQwNDE2Y2FhMWE2ZjUxNDkxN2VhZTQ3N2U0NjE5ODRmN2MyNWQxIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 08:01:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-539125106\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-959288610 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CQ8ts1Aea7js2Vy9E5qiYdNnEFX3bAyZuiuESdE</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/43/kanbanBoard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755581799</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-959288610\", {\"maxDepth\":0})</script>\n"}}