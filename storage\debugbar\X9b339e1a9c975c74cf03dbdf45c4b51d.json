{"__meta": {"id": "X9b339e1a9c975c74cf03dbdf45c4b51d", "datetime": "2025-08-19 11:01:11", "utime": 1755572471.837657, "method": "GET", "uri": "/task/50", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 29, "messages": [{"message": "[11:01:11] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572471.531808, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/task/50", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.663879, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Gate check for permission: viewComments_task on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.799727, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.801419, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.801505, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Gate check for permission: viewComments_task on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.807787, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.809079, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.809156, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Gate check for permission: edit_task on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.80961, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.810841, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.810914, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Gate check for permission: delete_task on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.811323, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.812521, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.812595, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Gate check for permission: viewComments_task on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.813064, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.814256, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.814327, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Gate check for permission: edit_task on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.814744, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.815967, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.816037, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Gate check for permission: delete_task on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.816434, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.817631, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.817703, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Gate check for permission: add_task on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.818101, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.819287, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.819361, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Gate check for permission: viewCalendar_task on project: 42 (Food Ordering System) for user: <PERSON><PERSON><PERSON>", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.819745, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: TeamName for user <PERSON><PERSON><PERSON> in team iv<PERSON>'s team: i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.820925, "xdebug_link": null, "collector": "log"}, {"message": "[11:01:11] LOG.debug: Using team role map for team i<PERSON><PERSON>'s team", "message_html": null, "is_string": false, "label": "debug", "time": 1755572471.820995, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572471.021415, "end": 1755572471.837714, "duration": 0.8162989616394043, "duration_str": "816ms", "measures": [{"label": "Booting", "start": 1755572471.021415, "relative_start": 0, "end": 1755572471.491011, "relative_end": 1755572471.491011, "duration": 0.46959590911865234, "duration_str": "470ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572471.491028, "relative_start": 0.46961307525634766, "end": 1755572471.837716, "relative_end": 2.1457672119140625e-06, "duration": 0.34668803215026855, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 24174928, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "tasks.index (\\resources\\views\\tasks\\index.blade.php)", "param_count": 7, "params": ["userstory_id", "userstory", "tasks", "statuses", "title", "project", "pros"], "type": "blade"}]}, "route": {"uri": "GET task/{u_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\TaskController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "tasks.index", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TaskController.php&line=23\">\\app\\Http\\Controllers\\TaskController.php:23-50</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.011829999999999999, "accumulated_duration_str": "11.83ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00751, "duration_str": "7.51ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 63.483}, {"sql": "select `team_name` from `teammappings` where `username` = 'ivlyn'", "type": "query", "params": [], "bindings": ["ivlyn"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 27}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:27", "connection": "sagile", "start_percent": 63.483, "width_percent": 6.424}, {"sql": "select * from `projects` where `team_name` in ('iv<PERSON>\\'s team', 'iv<PERSON>\\'s team')", "type": "query", "params": [], "bindings": ["ivlyn&#039;s team", "ivlyn&#039;s team"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 28}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00107, "duration_str": "1.07ms", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:28", "connection": "sagile", "start_percent": 69.907, "width_percent": 9.045}, {"sql": "select * from `tasks` where `userstory_id` = '50'", "type": "query", "params": [], "bindings": ["50"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 31}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:31", "connection": "sagile", "start_percent": 78.952, "width_percent": 4.649}, {"sql": "select * from `user_stories` where `u_id` = '50' limit 1", "type": "query", "params": [], "bindings": ["50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 34}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:34", "connection": "sagile", "start_percent": 83.601, "width_percent": 4.818}, {"sql": "select * from `projects` where `projects`.`id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 37}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00074, "duration_str": "740μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:37", "connection": "sagile", "start_percent": 88.419, "width_percent": 6.255}, {"sql": "select * from `statuses` where `project_id` = '42'", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 40}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\app\\Http\\Controllers\\TaskController.php:40", "connection": "sagile", "start_percent": 94.675, "width_percent": 5.325}]}, "models": {"data": {"App\\Status": 4, "App\\UserStory": 1, "App\\Task": 2, "App\\Project": 2, "App\\User": 1}, "count": 10}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 9, "messages": [{"message": "[\n  ability => viewComments_task,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">viewComments_task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755572471.80638, "xdebug_link": null}, {"message": "[\n  ability => viewComments_task,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-344104185 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">viewComments_task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-344104185\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755572471.809341, "xdebug_link": null}, {"message": "[\n  ability => edit_task,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-528252137 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit_task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-528252137\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755572471.81109, "xdebug_link": null}, {"message": "[\n  ability => delete_task,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1127700934 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1127700934\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755572471.812766, "xdebug_link": null}, {"message": "[\n  ability => viewComments_task,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1840160440 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">viewComments_task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1840160440\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755572471.814509, "xdebug_link": null}, {"message": "[\n  ability => edit_task,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2101004480 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit_task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2101004480\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755572471.816209, "xdebug_link": null}, {"message": "[\n  ability => delete_task,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-395552210 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete_task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-395552210\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755572471.817872, "xdebug_link": null}, {"message": "[\n  ability => add_task,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-77735536 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">add_task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-77735536\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755572471.819531, "xdebug_link": null}, {"message": "[\n  ability => viewCalendar_task,\n  result => true,\n  user => 30,\n  arguments => [0 => Object(App\\Project)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1386235186 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">viewCalendar_task</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[0 =&gt; Object(App\\Project)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1386235186\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1755572471.821163, "xdebug_link": null}]}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/task/50\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => 1755570218\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/task/50", "status_code": "<pre class=sf-dump id=sf-dump-1771316133 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1771316133\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-562757851 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-562757851\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1635971099 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1635971099\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1072008502 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/task/50/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik1nb05qTU5SWkUwRHEvVHc4Z3JNTlE9PSIsInZhbHVlIjoiREc3N1pKb3BLL1pHSm16ZFFXY09mN1Y2VnhwZlZickN4TXV5aTBIQnppK0VDdWdzeXRnYlRwTWk5NVJVTjFKM1BEaFZ3OGhIQ3hKTEYyQVo4UmtpVmRwQU9pYXFjeG80Mk9yU252V3E4WnRSRE93cmpHN3QxN2ZRUGNlanQ0MEQiLCJtYWMiOiI4ZDZkYzVhNDY5Njg2NWMyMGYxN2Y2YTVlNWVhMDg5YTA1MmZhMmU3ZWRjZWQ3ZmEzZWQ2MWIyZWU1ZDM5NTU1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ii9Xa1N2ZHhxeWhYR2JZc1NWcmNmbVE9PSIsInZhbHVlIjoiVmFDWHZyYlNUS2QxODFzdUtnOWcyNy83Q1BtSE9JTXM5bERGRzI4MGM4b3BkcjNGT3Z0Z3NQMm45SjR2bUpheTE5dTJLcGtPcGJmTk1GTSsxZ3VDV1F6QjVwNWVpUU5zWGFzd1krSTZXNm53SzJaek9xQ05ZM3ROZFM3b0VqWFYiLCJtYWMiOiJjN2E0NmNiNWI2YjIzNGJhMzBlMjdlZWUyMDE1M2JkZGUwOTAwNGRjNmY3MjU2YjkzNmU4MTk0NTliMDkwYzRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1072008502\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-536843618 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59354</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"8 characters\">/task/50</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"8 characters\">/task/50</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"18 characters\">/index.php/task/50</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/task/50/create</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik1nb05qTU5SWkUwRHEvVHc4Z3JNTlE9PSIsInZhbHVlIjoiREc3N1pKb3BLL1pHSm16ZFFXY09mN1Y2VnhwZlZickN4TXV5aTBIQnppK0VDdWdzeXRnYlRwTWk5NVJVTjFKM1BEaFZ3OGhIQ3hKTEYyQVo4UmtpVmRwQU9pYXFjeG80Mk9yU252V3E4WnRSRE93cmpHN3QxN2ZRUGNlanQ0MEQiLCJtYWMiOiI4ZDZkYzVhNDY5Njg2NWMyMGYxN2Y2YTVlNWVhMDg5YTA1MmZhMmU3ZWRjZWQ3ZmEzZWQ2MWIyZWU1ZDM5NTU1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ii9Xa1N2ZHhxeWhYR2JZc1NWcmNmbVE9PSIsInZhbHVlIjoiVmFDWHZyYlNUS2QxODFzdUtnOWcyNy83Q1BtSE9JTXM5bERGRzI4MGM4b3BkcjNGT3Z0Z3NQMm45SjR2bUpheTE5dTJLcGtPcGJmTk1GTSsxZ3VDV1F6QjVwNWVpUU5zWGFzd1krSTZXNm53SzJaek9xQ05ZM3ROZFM3b0VqWFYiLCJtYWMiOiJjN2E0NmNiNWI2YjIzNGJhMzBlMjdlZWUyMDE1M2JkZGUwOTAwNGRjNmY3MjU2YjkzNmU4MTk0NTliMDkwYzRiIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572471.0214</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572471</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-536843618\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-749223318 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-749223318\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-621923843 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 03:01:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkRHTTBCSkNNcDg4TEp3aWRPSWpMbXc9PSIsInZhbHVlIjoiM0QyTEFNNnByTGVnODh5NEFLMjRvRHJIL2tOdS9sSkZ1Z20rRDd2ZWErQ2FiWHJCMnVXejREdXlxbTVoWXc2SGtWRTVHcmlwOCszOElUTkdtTmFTUmZCTWdrdGJYODYzZzFQUlhUbFRJa2hUZHA3Qkg0QzJvdERuaG9vZmdNTXIiLCJtYWMiOiJjZjIxZTY5NDkwNDUyZGY3M2IwZmU3ZjM4MTc3NzQxZjM0Nzg3MWZkODU5NTM5MTc4M2I2OTM2YTEzYmQ4NTkwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:01:11 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InJpc1VpQjhPYlpUNFNiK09zcUpCcEE9PSIsInZhbHVlIjoiL0dHelFodWFuRTlST3pkaHI1VTJGZEN3V01UbVY4ZGpnaFgyNnNXVk1UejBwckk0b3lCL3JwK0NZczhTa09qTWJtUHFrRE5QRVZlajVCNm9SS3hvbFk3TUNUZ1ZLWk5jOEJ3aEhsRWl2YUNrZEtpaW9tK2dnbG5JQ0lsVmxVYnIiLCJtYWMiOiI0YmIxZmUyYTRiNzAzNGJmNDEyM2UzNDRkNmY3MDM1NzJmOWNiZWM1NWYzMDM4MGVmMmYwOTUzYWVjYjg1MDc5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:01:11 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkRHTTBCSkNNcDg4TEp3aWRPSWpMbXc9PSIsInZhbHVlIjoiM0QyTEFNNnByTGVnODh5NEFLMjRvRHJIL2tOdS9sSkZ1Z20rRDd2ZWErQ2FiWHJCMnVXejREdXlxbTVoWXc2SGtWRTVHcmlwOCszOElUTkdtTmFTUmZCTWdrdGJYODYzZzFQUlhUbFRJa2hUZHA3Qkg0QzJvdERuaG9vZmdNTXIiLCJtYWMiOiJjZjIxZTY5NDkwNDUyZGY3M2IwZmU3ZjM4MTc3NzQxZjM0Nzg3MWZkODU5NTM5MTc4M2I2OTM2YTEzYmQ4NTkwIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:01:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InJpc1VpQjhPYlpUNFNiK09zcUpCcEE9PSIsInZhbHVlIjoiL0dHelFodWFuRTlST3pkaHI1VTJGZEN3V01UbVY4ZGpnaFgyNnNXVk1UejBwckk0b3lCL3JwK0NZczhTa09qTWJtUHFrRE5QRVZlajVCNm9SS3hvbFk3TUNUZ1ZLWk5jOEJ3aEhsRWl2YUNrZEtpaW9tK2dnbG5JQ0lsVmxVYnIiLCJtYWMiOiI0YmIxZmUyYTRiNzAzNGJmNDEyM2UzNDRkNmY3MDM1NzJmOWNiZWM1NWYzMDM4MGVmMmYwOTUzYWVjYjg1MDc5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 05:01:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-621923843\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1312133206 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/task/50</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1755570218</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1312133206\", {\"maxDepth\":0})</script>\n"}}