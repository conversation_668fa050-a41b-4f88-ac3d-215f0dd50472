
<?php echo $__env->make('inc.success', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('inc.style', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->startSection('navbar'); ?>
  <?php echo $__env->make('inc.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<?php echo $__env->make('inc.title', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<br>
<?php if($errors->any()): ?>
    <div class="alert alert-danger">
        <ul>
            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li><?php echo e($error); ?></li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
<?php endif; ?>
<script>
    function confirmAction(event) {
        event.preventDefault();
        if (confirm("Are you sure you want to proceed?")) {
            event.target.closest('form').submit();
        }
    }
</script>
<?php if($user->user_type == 1): ?> 
<form action="<?php echo e(route('nfr.storeSpecific', ['general_nfr_id' => $general_nfr_id])); ?>" method="POST" onsubmit="confirmAction(event)">
    <?php echo csrf_field(); ?>
    <label for="specific_nfr">Specific Requirement:</label>
    <input type="text" name="specific_nfr"  class="form-control" required>
    <br><br>
    
    <label for="specific_nfr_desc">Description:</label>
    <input type="text" name="specific_nfr_desc"  class="form-control">
    <br><br>

    <button type="submit" class="btn btn-success">Submit</button>
    <a href="<?php echo e(route('nfr.show', ['general_nfr_id' => $general_nfr_id])); ?>" class="btn btn-secondary">Cancel</a>
</form>
<?php else: ?>
<div>You do not have access to this page.</div>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\SAgilePMT_UTM\resources\views/nfr/createSpecific.blade.php ENDPATH**/ ?>