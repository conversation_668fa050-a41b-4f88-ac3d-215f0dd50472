{"__meta": {"id": "X8a26c214fdbdcfadc6f0d7b90ceb6d57", "datetime": "2025-08-18 23:43:19", "utime": 1755531799.805068, "method": "GET", "uri": "/cig/create/42?filter_sprint_id=34", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[23:43:19] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755531799.637474, "xdebug_link": null, "collector": "log"}, {"message": "[23:43:19] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/cig/create/42", "message_html": null, "is_string": false, "label": "debug", "time": 1755531799.697966, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755531799.314909, "end": 1755531799.805089, "duration": 0.49018001556396484, "duration_str": "490ms", "measures": [{"label": "Booting", "start": 1755531799.314909, "relative_start": 0, "end": 1755531799.615126, "relative_end": 1755531799.615126, "duration": 0.3002169132232666, "duration_str": "300ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755531799.615143, "relative_start": 0.3002340793609619, "end": 1755531799.805091, "relative_end": 1.9073486328125e-06, "duration": 0.18994784355163574, "duration_str": "190ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25531216, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 17, "templates": [{"name": "cig.create (\\resources\\views\\cig\\create.blade.php)", "param_count": 7, "params": ["title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 11, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 14, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 22, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 23, "params": ["__env", "app", "menuData", "errors", "title", "proj_id", "sprints", "userStories", "paginatedUserStories", "selectedSprintId", "radarData", "__currentLoopData", "sprint", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET cig/create/{proj_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@create", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "cig.create", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=81\">\\app\\Http\\Controllers\\CIGController.php:81-108</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00236, "accumulated_duration_str": "2.36ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00049, "duration_str": "490μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 20.763}, {"sql": "select * from `projects` where `projects`.`id` = '42' limit 1", "type": "query", "params": [], "bindings": ["42"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Project.php", "line": 86}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 84}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Project.php:86", "connection": "sagile", "start_percent": 20.763, "width_percent": 18.22}, {"sql": "select * from `sprint` where `proj_name` = 'Food Ordering System'", "type": "query", "params": [], "bindings": ["Food Ordering System"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Sprint.php", "line": 62}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 85}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Sprint.php:62", "connection": "sagile", "start_percent": 38.983, "width_percent": 17.797}, {"sql": "select * from `user_stories` where `proj_id` = '42' and `sprint_id` = '34'", "type": "query", "params": [], "bindings": ["42", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 36}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 91}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Http\\Controllers\\CIGController.php:36", "connection": "sagile", "start_percent": 56.78, "width_percent": 20.339}, {"sql": "select count(*) as aggregate from `user_stories` where `proj_id` = '42' and `sprint_id` = '34' and exists (select * from `user_story_general_nfr` where `user_stories`.`u_id` = `user_story_general_nfr`.`user_story_id`)", "type": "query", "params": [], "bindings": ["42", "34"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\UserStory.php", "line": 68}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\CIGController.php", "line": 95}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\UserStory.php:68", "connection": "sagile", "start_percent": 77.119, "width_percent": 22.881}]}, "models": {"data": {"App\\Sprint": 1, "App\\Project": 1, "App\\User": 1}, "count": 3}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/cig/create/42?filter_sprint_id=34\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/cig/create/42", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-792716823 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>filter_sprint_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-792716823\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1883223371 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>filter_sprint_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">34</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1883223371\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-43104498 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkdTSS9qRUhrVEZNcGM0UUNhU1hZK2c9PSIsInZhbHVlIjoiWFlVQldyclZGQWFFMEQ5VHI3cDFRekVybFlLQXFibjVSbFBqTVordTdjVTZsNXNmbDQvZnBDOStTWkpEVUlWYnhFVnBNVTNrMUkyNW5rTjdKekJ5cWNRcGJzZUlGaHpOYzZORjhoMU1jN0htMWt6Rk9aQld5MUM5UU80Rks3Y3QiLCJtYWMiOiI2N2I0ZDg3ZTNhMjM4MTBmOTE3NWUzMWFkNWM4Zjk3N2IyMTJjNWE3Yjk1OTk2NTVjYmE1MDYzMGQ3MjFlMGUxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ilg0QVN1Vm1xc0dkR1FCYnMzWXRGTlE9PSIsInZhbHVlIjoiN1QzWGxsck9SOU9CdnNCVEpNSnlnQ21OM0IxQW5hRnIrM3JheFZ5VkNpaHhMK0h0cjBBQVI3TTJmUk9Ub1JXbHBvdDlXVEZMOC8zUnBYSmc1ck5DMGc4YkluYk9ySHNqcjJkWmF5UFJON29Kajh5cEN5K0g3bmpzNlNOL2U2SE4iLCJtYWMiOiIyMzc0NjJiZGJjMzc1YWNhYjVkNjg2ZjRmMTI3ZjYzY2Q1ZWVhMjljOTk4MWRmMDhjZjcxNWZkMGEwNjIwNTc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-43104498\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1998192153 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59655</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"34 characters\">/cig/create/42?filter_sprint_id=34</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"14 characters\">/cig/create/42</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/index.php/cig/create/42</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"19 characters\">filter_sprint_id=34</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkdTSS9qRUhrVEZNcGM0UUNhU1hZK2c9PSIsInZhbHVlIjoiWFlVQldyclZGQWFFMEQ5VHI3cDFRekVybFlLQXFibjVSbFBqTVordTdjVTZsNXNmbDQvZnBDOStTWkpEVUlWYnhFVnBNVTNrMUkyNW5rTjdKekJ5cWNRcGJzZUlGaHpOYzZORjhoMU1jN0htMWt6Rk9aQld5MUM5UU80Rks3Y3QiLCJtYWMiOiI2N2I0ZDg3ZTNhMjM4MTBmOTE3NWUzMWFkNWM4Zjk3N2IyMTJjNWE3Yjk1OTk2NTVjYmE1MDYzMGQ3MjFlMGUxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ilg0QVN1Vm1xc0dkR1FCYnMzWXRGTlE9PSIsInZhbHVlIjoiN1QzWGxsck9SOU9CdnNCVEpNSnlnQ21OM0IxQW5hRnIrM3JheFZ5VkNpaHhMK0h0cjBBQVI3TTJmUk9Ub1JXbHBvdDlXVEZMOC8zUnBYSmc1ck5DMGc4YkluYk9ySHNqcjJkWmF5UFJON29Kajh5cEN5K0g3bmpzNlNOL2U2SE4iLCJtYWMiOiIyMzc0NjJiZGJjMzc1YWNhYjVkNjg2ZjRmMTI3ZjYzY2Q1ZWVhMjljOTk4MWRmMDhjZjcxNWZkMGEwNjIwNTc1IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755531799.3149</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755531799</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1998192153\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1198324692 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198324692\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1524120118 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:43:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlJ3a05DYnZwTjVVanp5QzEwSlg5cnc9PSIsInZhbHVlIjoiYzBrRCtmaTRQYUJhSi9RbjVnY1ZBT0I2b2pkM1l0bDV1SDYzMVNnQUY0ck9NUVlPTDZlcXBZTVVQcDNvcFhQdDdRNHFXa1RxbzdkK0M4ODV2S3dYeGl5aUMyM0RXTlljNUNPTnBuQmx1YlRXK0ZBUVdxdjBWZm03OG80RGx0Z0MiLCJtYWMiOiI0ZGU3MDJmMzA2YTM5MWMwODVhY2Q3OTUwMWNiYWRlMmJjZWQ4NzIxOWRjMDdmYTg4MmYxZTUwNTVlNDQ4ZGRlIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:19 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InQwai9SQStFYUV6Z3ZtOHhkSERYa3c9PSIsInZhbHVlIjoidStJTHhMSm1FNUYrZXBjVlJiNlRyUlE4SWZTUkV0MkhoVGNOK2dPLzllMjFGTVNnQ1VOZmVjdGJoOE5mSDluMlpTaEM0TkJOK3QxZnNHb0VESzdRSmphc1ZzcjNSWm9CUFZESGwwT2dZMmNtSjhlSWhwbHpIYkE5OW9ad3pJcisiLCJtYWMiOiI4NDBkYjNmNDM1YzZiYTkyMTI4NWU4MTVmMGVlZWViYzkxN2Y1MTcyZTU3YjNlZTEwMTUzOTVlNmRjZjY2ZDIyIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:19 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlJ3a05DYnZwTjVVanp5QzEwSlg5cnc9PSIsInZhbHVlIjoiYzBrRCtmaTRQYUJhSi9RbjVnY1ZBT0I2b2pkM1l0bDV1SDYzMVNnQUY0ck9NUVlPTDZlcXBZTVVQcDNvcFhQdDdRNHFXa1RxbzdkK0M4ODV2S3dYeGl5aUMyM0RXTlljNUNPTnBuQmx1YlRXK0ZBUVdxdjBWZm03OG80RGx0Z0MiLCJtYWMiOiI0ZGU3MDJmMzA2YTM5MWMwODVhY2Q3OTUwMWNiYWRlMmJjZWQ4NzIxOWRjMDdmYTg4MmYxZTUwNTVlNDQ4ZGRlIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InQwai9SQStFYUV6Z3ZtOHhkSERYa3c9PSIsInZhbHVlIjoidStJTHhMSm1FNUYrZXBjVlJiNlRyUlE4SWZTUkV0MkhoVGNOK2dPLzllMjFGTVNnQ1VOZmVjdGJoOE5mSDluMlpTaEM0TkJOK3QxZnNHb0VESzdRSmphc1ZzcjNSWm9CUFZESGwwT2dZMmNtSjhlSWhwbHpIYkE5OW9ad3pJcisiLCJtYWMiOiI4NDBkYjNmNDM1YzZiYTkyMTI4NWU4MTVmMGVlZWViYzkxN2Y1MTcyZTU3YjNlZTEwMTUzOTVlNmRjZjY2ZDIyIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:43:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1524120118\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-5120343 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-5120343\", {\"maxDepth\":0})</script>\n"}}