{"__meta": {"id": "X703693db8c6ff9e0536c15563bee775a", "datetime": "2025-08-18 23:35:04", "utime": 1755531304.173507, "method": "GET", "uri": "/tasks/103/comment/34", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[23:35:03] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755531303.642465, "xdebug_link": null, "collector": "log"}, {"message": "[23:35:03] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/tasks/103/comment/34", "message_html": null, "is_string": false, "label": "debug", "time": 1755531303.760696, "xdebug_link": null, "collector": "log"}, {"message": "[23:35:03] LOG.warning: Since 2.61.0, it's deprecated to compare a date to null, meaning of such comparison is ambiguous and will no longer be possible in 3.0.0, you should explicitly pass 'now' or make an other check to eliminate null values. in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Comparison.php on line 1119", "message_html": null, "is_string": false, "label": "warning", "time": 1755531303.80631, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755531303.065623, "end": 1755531304.173547, "duration": 1.107923984527588, "duration_str": "1.11s", "measures": [{"label": "Booting", "start": 1755531303.065623, "relative_start": 0, "end": 1755531303.60316, "relative_end": 1755531303.60316, "duration": 0.5375368595123291, "duration_str": "538ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755531303.603176, "relative_start": 0.537553071975708, "end": 1755531304.173551, "relative_end": 4.0531158447265625e-06, "duration": 0.5703749656677246, "duration_str": "570ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 25620072, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 18, "templates": [{"name": "tasks.viewCommentList (\\resources\\views\\tasks\\viewCommentList.blade.php)", "param_count": 6, "params": ["title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue"], "type": "blade"}, {"name": "inc.success (\\resources\\views\\inc\\success.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue"], "type": "blade"}, {"name": "inc.style (\\resources\\views\\inc\\style.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue"], "type": "blade"}, {"name": "inc.navbar (\\resources\\views\\inc\\navbar.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue"], "type": "blade"}, {"name": "inc.title (\\resources\\views\\inc\\title.blade.php)", "param_count": 10, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\resources\\views\\vendor\\pagination\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "layouts.app2 (\\resources\\views\\layouts\\app2.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "loop"], "type": "blade"}, {"name": "inc.apex-charts-css (\\resources\\views\\inc\\apex-charts-css.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "loop"], "type": "blade"}, {"name": "inc.apex-charts-js (\\resources\\views\\inc\\apex-charts-js.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "loop"], "type": "blade"}, {"name": "inc.dashboard-analytics-js (\\resources\\views\\inc\\dashboard-analytics-js.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "loop"], "type": "blade"}, {"name": "layouts.contentNavbarLayout (\\resources\\views\\layouts\\contentNavbarLayout.blade.php)", "param_count": 12, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "loop"], "type": "blade"}, {"name": "layouts.sections.menu.verticalMenu (\\resources\\views\\layouts\\sections\\menu\\verticalMenu.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.navbar.navbar (\\resources\\views\\layouts\\sections\\navbar\\navbar.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.footer.footer (\\resources\\views\\layouts\\sections\\footer\\footer.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.commonMaster (\\resources\\views\\layouts\\commonMaster.blade.php)", "param_count": 20, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container"], "type": "blade"}, {"name": "layouts.sections.styles (\\resources\\views\\layouts\\sections\\styles.blade.php)", "param_count": 21, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scriptsIncludes (\\resources\\views\\layouts\\sections\\scriptsIncludes.blade.php)", "param_count": 21, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}, {"name": "layouts.sections.scripts (\\resources\\views\\layouts\\sections\\scripts.blade.php)", "param_count": 21, "params": ["__env", "app", "menuData", "errors", "title", "task", "comments", "uniqueCreators", "isTaskOverdue", "isSprintOverdue", "__currentLoopData", "loop", "contentNavbar", "containerNav", "isNavbar", "isMenu", "isFlex", "<PERSON><PERSON>ooter", "navbarDetached", "container", "layoutHtmlClasses"], "type": "blade"}]}, "route": {"uri": "GET tasks/{task_id}/comment/{sprint_id}", "middleware": "web", "controller": "App\\Http\\Controllers\\TaskController@viewCommentList", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "tasks.viewCommentList", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\TaskController.php&line=291\">\\app\\Http\\Controllers\\TaskController.php:291-344</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00391, "accumulated_duration_str": "3.91ms", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 18.67}, {"sql": "select * from `tasks` where `tasks`.`id` = '103' limit 1", "type": "query", "params": [], "bindings": ["103"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Task.php", "line": 43}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 300}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\app\\Task.php:43", "connection": "sagile", "start_percent": 18.67, "width_percent": 17.647}, {"sql": "select * from `sprint` where `sprint_id` = 34 limit 1", "type": "query", "params": [], "bindings": ["34"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Sprint.php", "line": 46}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 306}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\app\\Sprint.php:46", "connection": "sagile", "start_percent": 36.317, "width_percent": 17.903}, {"sql": "select `created_by` from `taskComment` where `task_id` = '103'", "type": "query", "params": [], "bindings": ["103"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\taskComment.php", "line": 39}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 314}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0011799999999999998, "duration_str": "1.18ms", "stmt_id": "\\app\\taskComment.php:39", "connection": "sagile", "start_percent": 54.22, "width_percent": 30.179}, {"sql": "select count(*) as aggregate from `taskComment` where `task_id` = '103'", "type": "query", "params": [], "bindings": ["103"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\taskComment.php", "line": 80}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\TaskController.php", "line": 321}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00061, "duration_str": "610μs", "stmt_id": "\\app\\taskComment.php:80", "connection": "sagile", "start_percent": 84.399, "width_percent": 15.601}]}, "models": {"data": {"App\\Sprint": 1, "App\\Task": 1, "App\\User": 1}, "count": 3}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/tasks/103/comment/34\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/home\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/tasks/103/comment/34", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2099229977 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2099229977\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1964559362 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1964559362\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2092684073 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/task/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IldKMFV4VHhwRUdCWlhzRENnYmxjU2c9PSIsInZhbHVlIjoieEl4VCthTE8wemFqZ3NDN2hkUjk1b09wOTBoZC8vM2M2NFpqNW5zY3BjRmhDMGwxKzJtc2Z1TzR1NXl5WTdYaTN0Ymo1dzAyWkJhaTJETEo3RU9IVlA3ZnpQQituaGhKNGZlZjM4bWNnT1B5a0dSeG9Dd0Q2bWRyeHozRTBPYk4iLCJtYWMiOiI3MjE0ZmI3Mjg5MGRiYmQ3NDQwOWYwNmUwZTkxOTBmYWNkNDZmYWI0OGJiMzNmYTY4MDhkNmI5YmEyOTFkYzc3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImtRekhlWFE1cmgvdmtIbnVXaGRHS1E9PSIsInZhbHVlIjoid2JwbTI3NElaSzIwRUkrM3d6YVVXM0YxRDhkQUszZmZvY1JzU3doUVMxekhKRXpkQlJRVlI4cFlBV1JWZWxUeDZXdm5wbFpLN0xKbTkwSHh3TjQ1VytwZVdIYk82ME1yUWc1cmkrZGoyY3czdjNtOExTQi9zUCt6SFB2alNSYUkiLCJtYWMiOiI2NmRhODU1M2FlM2QyMjc2NmE1ZmJiZDA0NTVmZWJjN2UxNDc5OGIyYmQ5YjlhY2NiYTJmYjc1YWJhMzdlMDM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2092684073\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1085362243 data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">50651</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/tasks/103/comment/34</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/tasks/103/comment/34</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/index.php/tasks/103/comment/34</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"6 characters\">iframe</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/task/45</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IldKMFV4VHhwRUdCWlhzRENnYmxjU2c9PSIsInZhbHVlIjoieEl4VCthTE8wemFqZ3NDN2hkUjk1b09wOTBoZC8vM2M2NFpqNW5zY3BjRmhDMGwxKzJtc2Z1TzR1NXl5WTdYaTN0Ymo1dzAyWkJhaTJETEo3RU9IVlA3ZnpQQituaGhKNGZlZjM4bWNnT1B5a0dSeG9Dd0Q2bWRyeHozRTBPYk4iLCJtYWMiOiI3MjE0ZmI3Mjg5MGRiYmQ3NDQwOWYwNmUwZTkxOTBmYWNkNDZmYWI0OGJiMzNmYTY4MDhkNmI5YmEyOTFkYzc3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImtRekhlWFE1cmgvdmtIbnVXaGRHS1E9PSIsInZhbHVlIjoid2JwbTI3NElaSzIwRUkrM3d6YVVXM0YxRDhkQUszZmZvY1JzU3doUVMxekhKRXpkQlJRVlI4cFlBV1JWZWxUeDZXdm5wbFpLN0xKbTkwSHh3TjQ1VytwZVdIYk82ME1yUWc1cmkrZGoyY3czdjNtOExTQi9zUCt6SFB2alNSYUkiLCJtYWMiOiI2NmRhODU1M2FlM2QyMjc2NmE1ZmJiZDA0NTVmZWJjN2UxNDc5OGIyYmQ5YjlhY2NiYTJmYjc1YWJhMzdlMDM0IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755531303.0656</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755531303</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1085362243\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-505710951 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3ncPjqusJbCIpWuOHmzvayfYSOYCObc3MzirZJ0n</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-505710951\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1597989187 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 18 Aug 2025 15:35:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImRXWUhOQ1MvSDZ2ZklrbDJ3VE9BR3c9PSIsInZhbHVlIjoiWnIvQXFHVUo3WkdNR3Z1U3QxM2NCMHByRHBUT05NVjdsMzRQZ0FBbU1iczFwdkU3TEFtMTdtS3p3MTZKdWd6WEFxdzVudHUrdEVqcEZMRTZ4VDhnT2xOR0FrcmlkelZGeFBxMnU4ZEVMdXErY1R0dUxRZHY0Nno5bGxYNjFrOWciLCJtYWMiOiI5OTQ5Y2FlZjRlYjAyYTM4YTA2NmZjMjIwZWM0MTVhNjAwMThhMDJiMTMxNzhlMDNmMWI5Y2IzMWI4MmU1YjdjIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:35:04 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InkzUEl3SGFvS2QrYWZTUjhsL3Zaa2c9PSIsInZhbHVlIjoiWFIwbVJKcFp5WWFzVWJML2hlYWladFJwNEtvMUdpRHpSVFV6ZDF1UERiS2lKZG1aSGpXdVBhbWlvNTNrWk56NGhJUitKc294clpiS3pWcXdxTTdWbk91SGJINTM3ZkRlNTNXdi9DU3VBWFVTbldQbERoZUtlS2syZW82UllvWGoiLCJtYWMiOiIyNDIyMWVlYmEyNjNjMmMwNzM5MGY1MDg3MzMwMjIyODljMTVmNzQ1NTBiNWZhMTA2ZTFkM2I2YjE1Y2QzMzVmIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:35:04 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImRXWUhOQ1MvSDZ2ZklrbDJ3VE9BR3c9PSIsInZhbHVlIjoiWnIvQXFHVUo3WkdNR3Z1U3QxM2NCMHByRHBUT05NVjdsMzRQZ0FBbU1iczFwdkU3TEFtMTdtS3p3MTZKdWd6WEFxdzVudHUrdEVqcEZMRTZ4VDhnT2xOR0FrcmlkelZGeFBxMnU4ZEVMdXErY1R0dUxRZHY0Nno5bGxYNjFrOWciLCJtYWMiOiI5OTQ5Y2FlZjRlYjAyYTM4YTA2NmZjMjIwZWM0MTVhNjAwMThhMDJiMTMxNzhlMDNmMWI5Y2IzMWI4MmU1YjdjIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:35:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InkzUEl3SGFvS2QrYWZTUjhsL3Zaa2c9PSIsInZhbHVlIjoiWFIwbVJKcFp5WWFzVWJML2hlYWladFJwNEtvMUdpRHpSVFV6ZDF1UERiS2lKZG1aSGpXdVBhbWlvNTNrWk56NGhJUitKc294clpiS3pWcXdxTTdWbk91SGJINTM3ZkRlNTNXdi9DU3VBWFVTbldQbERoZUtlS2syZW82UllvWGoiLCJtYWMiOiIyNDIyMWVlYmEyNjNjMmMwNzM5MGY1MDg3MzMwMjIyODljMTVmNzQ1NTBiNWZhMTA2ZTFkM2I2YjE1Y2QzMzVmIiwidGFnIjoiIn0%3D; expires=Mon, 18-Aug-2025 17:35:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1597989187\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1622573723 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6753mqMsbHrkw4v4Bse67o28cYSCjqzh2yYb4QI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/tasks/103/comment/34</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://127.0.0.1:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1622573723\", {\"maxDepth\":0})</script>\n"}}