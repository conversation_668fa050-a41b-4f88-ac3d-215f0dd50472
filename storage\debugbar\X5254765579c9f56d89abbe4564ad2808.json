{"__meta": {"id": "X5254765579c9f56d89abbe4564ad2808", "datetime": "2025-08-19 10:57:01", "utime": 1755572221.220778, "method": "POST", "uri": "/upload-radar-chart", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[10:57:01] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp\\htdocs\\SAgilePMT_UTM\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1755572221.097559, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:01] LOG.debug: LoadUserPermissions: Extracted project ID: null from URL: http://127.0.0.1:8000/upload-radar-chart", "message_html": null, "is_string": false, "label": "debug", "time": 1755572221.207691, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755572220.605446, "end": 1755572221.220803, "duration": 0.6153569221496582, "duration_str": "615ms", "measures": [{"label": "Booting", "start": 1755572220.605446, "relative_start": 0, "end": 1755572221.066674, "relative_end": 1755572221.066674, "duration": 0.4612278938293457, "duration_str": "461ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1755572221.066685, "relative_start": 0.4612388610839844, "end": 1755572221.220806, "relative_end": 2.86102294921875e-06, "duration": 0.15412092208862305, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 23393928, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST upload-radar-chart", "middleware": "web", "controller": "App\\Http\\Controllers\\CIGController@uploadRadarChart", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\SAgilePMT_UTM\\app\\Http\\Controllers\\CIGController.php&line=141\">\\app\\Http\\Controllers\\CIGController.php:141-151</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00063, "accumulated_duration_str": "630μs", "statements": [{"sql": "select * from `users` where `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "sagile", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\User": 1}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/radar-data/42/36\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "30", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]"}, "request": {"path_info": "/upload-radar-chart", "status_code": "<pre class=sf-dump id=sf-dump-502207781 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-502207781\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"23550 characters\">data:image/png;base64,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</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1774977695 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">23562</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjJlZHpMQ05XTHdpMEFYWHBMSTIrS2c9PSIsInZhbHVlIjoiaHB3amY2bEtLODg3eVRNZEtyL3NrVVNJU2VWbnkyQUlrNTZseFdBWStoM1lWdzJBSnJmOE51aXJwMHd6UGt1eGMyYVNLcHpBS29DdTZOTHlhUEFMSC9aTXhyRXVHNGNYejd4RnJMN2hEbUpIR3lOeFhjWG1UUVlMTStMb3FHV0giLCJtYWMiOiJkNjI4NzYyZTFkNTBjOGE5MzUzNjdmZmI4NjY4NzBjM2MwZDA4ZDNlMDFiZmYyOGI1Y2VjYjc1NTE4MGY0OTkyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImUxeHdGRVFITmJOMDlTZlM3YVh1THc9PSIsInZhbHVlIjoick5LQUNxSW5KK0x5WUhxZGtKN2hiVUI2NFpHTWphcW5ER2FWVlJ5YjBTNC9MNWNUSkhWTGhEaDBnK2FlekJCT2dEWW9xM2h2a3dWYWJjYkhJSVhscWs0czMxZGVpVExlRDVhM3h3TCs5S0JpUUJWSmlVR0lQdCtvUkRIYTQ4bTQiLCJtYWMiOiI2ZjQ2YWVkMzY3YjUzMDA3NTE2Yzg1ODczNWM4NTVkNDhlODBiODI3ZjUzNDY5OTJiMjU3MTQ5MmRhMGFlYjNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1774977695\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1146737811 data-indent-pad=\"  \"><span class=sf-dump-note>array:35</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"36 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59280</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"46 characters\">C:\\xampp\\htdocs\\SAgilePMT_UTM\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"19 characters\">/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"29 characters\">/index.php/upload-radar-chart</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">23562</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">23562</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/cig/create/42?filter_sprint_id=36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"68 characters\">en-US,en-GB;q=0.9,en;q=0.8,ms;q=0.7,zh-GB;q=0.6,zh;q=0.5,zh-CN;q=0.4</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjJlZHpMQ05XTHdpMEFYWHBMSTIrS2c9PSIsInZhbHVlIjoiaHB3amY2bEtLODg3eVRNZEtyL3NrVVNJU2VWbnkyQUlrNTZseFdBWStoM1lWdzJBSnJmOE51aXJwMHd6UGt1eGMyYVNLcHpBS29DdTZOTHlhUEFMSC9aTXhyRXVHNGNYejd4RnJMN2hEbUpIR3lOeFhjWG1UUVlMTStMb3FHV0giLCJtYWMiOiJkNjI4NzYyZTFkNTBjOGE5MzUzNjdmZmI4NjY4NzBjM2MwZDA4ZDNlMDFiZmYyOGI1Y2VjYjc1NTE4MGY0OTkyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImUxeHdGRVFITmJOMDlTZlM3YVh1THc9PSIsInZhbHVlIjoick5LQUNxSW5KK0x5WUhxZGtKN2hiVUI2NFpHTWphcW5ER2FWVlJ5YjBTNC9MNWNUSkhWTGhEaDBnK2FlekJCT2dEWW9xM2h2a3dWYWJjYkhJSVhscWs0czMxZGVpVExlRDVhM3h3TCs5S0JpUUJWSmlVR0lQdCtvUkRIYTQ4bTQiLCJtYWMiOiI2ZjQ2YWVkMzY3YjUzMDA3NTE2Yzg1ODczNWM4NTVkNDhlODBiODI3ZjUzNDY5OTJiMjU3MTQ5MmRhMGFlYjNmIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1755572220.6054</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1755572220</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1146737811\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-705461565 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ULedHCbhxbwyqt3p2wLzUWVHMKi2beV7aQofvoiT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-705461565\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-421804562 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 02:57:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlNBTzhMbmZFcm02ZjlJdTUxYUozZkE9PSIsInZhbHVlIjoiTDQ4cTZtUnRmVGhFcEZ2bDJzMUp1N29wY1drellZRFc5c3JtMGovdjJibjFOZ2Ura3luVUVQYk5Mc2lNRGQvZkhrWm1CVlpNMm1qdXZBY1MwL1lCaE1nTlp5Q0FGMUlyM1dtNnI5QVhjMVhXeTFLWTJWUDVRQzR3Y3ptOHRNZGQiLCJtYWMiOiI0ZTZlMjEwMmE3MWQyNzhkZTczYzgxNzk4MDkyN2YzYzRmZDhhOGRmMzNmOTlmMzk4NzA0ZDY5N2I3ODJmYTU5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:57:01 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImRIVmxoTFpOTmdkOTRyb1JkN3JLUEE9PSIsInZhbHVlIjoiNmM0cXAyZllVRm0xdjVETzYrWDZsNUczVDhQZ2FZM0xQUjJ2NUs5MmMrUnIxM2p4cUlVb3RjNTRSRFF2c0R2ZExVT2V4V0FCdDUvTVZZVVVOSWRRSDEvMnRuMXB4K2lVMnY2Qk5vM2FraUxjdzBSeVpiemdNWHJUZnZFOUxBcTIiLCJtYWMiOiJjOTk3NjM0MDlmYmYwNTIyMjQyOGJkYTE2ZmNjZGM1ZjlkMWQzN2NhMTBhYjU2NGY4ZGE4ZTE1NWRkZmI3NjVhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:57:01 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlNBTzhMbmZFcm02ZjlJdTUxYUozZkE9PSIsInZhbHVlIjoiTDQ4cTZtUnRmVGhFcEZ2bDJzMUp1N29wY1drellZRFc5c3JtMGovdjJibjFOZ2Ura3luVUVQYk5Mc2lNRGQvZkhrWm1CVlpNMm1qdXZBY1MwL1lCaE1nTlp5Q0FGMUlyM1dtNnI5QVhjMVhXeTFLWTJWUDVRQzR3Y3ptOHRNZGQiLCJtYWMiOiI0ZTZlMjEwMmE3MWQyNzhkZTczYzgxNzk4MDkyN2YzYzRmZDhhOGRmMzNmOTlmMzk4NzA0ZDY5N2I3ODJmYTU5IiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:57:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImRIVmxoTFpOTmdkOTRyb1JkN3JLUEE9PSIsInZhbHVlIjoiNmM0cXAyZllVRm0xdjVETzYrWDZsNUczVDhQZ2FZM0xQUjJ2NUs5MmMrUnIxM2p4cUlVb3RjNTRSRFF2c0R2ZExVT2V4V0FCdDUvTVZZVVVOSWRRSDEvMnRuMXB4K2lVMnY2Qk5vM2FraUxjdzBSeVpiemdNWHJUZnZFOUxBcTIiLCJtYWMiOiJjOTk3NjM0MDlmYmYwNTIyMjQyOGJkYTE2ZmNjZGM1ZjlkMWQzN2NhMTBhYjU2NGY4ZGE4ZTE1NWRkZmI3NjVhIiwidGFnIjoiIn0%3D; expires=Tue, 19-Aug-2025 04:57:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-421804562\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1989508682 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">eT3CpOUZiadwzQFfQ3YqjTt560pWf1oEuL55iJOo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/radar-data/42/36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>30</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1989508682\", {\"maxDepth\":0})</script>\n"}}